# MythEVE 技术实施详细方案

## 🔧 阶段A: 真实EVE SSO集成 - 技术细节

### A1. EVE SSO配置验证

#### 检查清单
```bash
# 1. 验证环境变量
echo $EVE_CLIENT_ID
echo $EVE_CLIENT_SECRET
echo $EVE_CALLBACK_URL

# 2. 测试EVE SSO端点连通性
curl -I https://login.eveonline.com/v2/oauth/authorize

# 3. 验证回调URL可达性
curl -I http://localhost:8000/auth/callback
```

#### 配置文件更新
```python
# src/infrastructure/config/settings.py
class EVESSOSettings:
    CLIENT_ID: str = Field(..., env="EVE_CLIENT_ID")
    CLIENT_SECRET: str = Field(..., env="EVE_CLIENT_SECRET") 
    CALLBACK_URL: str = Field("http://localhost:8000/auth/callback", env="EVE_CALLBACK_URL")
    SCOPES: List[str] = [
        "esi-assets.read_assets.v1",
        "esi-skills.read_skills.v1",
        "esi-wallet.read_character_wallet.v1",
        "esi-location.read_location.v1",
        "esi-location.read_ship_type.v1"
    ]
```

### A2. OAuth流程实现

#### 核心文件修改
1. **更新EVE SSO客户端**
```python
# src/infrastructure/esi/sso_client.py
class EVESSOClient:
    async def generate_login_url(self, scopes: List[str]) -> Tuple[str, str]:
        # 实现真实的授权URL生成
        state = secrets.token_urlsafe(32)
        params = {
            "response_type": "code",
            "redirect_uri": self.callback_url,
            "client_id": self.client_id,
            "scope": " ".join(scopes),
            "state": state
        }
        url = f"{self.auth_url}?{urlencode(params)}"
        return url, state
```

2. **实现token交换**
```python
async def exchange_code_for_tokens(self, code: str, state: str) -> Dict[str, Any]:
    # 实现授权码换取访问令牌
    data = {
        "grant_type": "authorization_code",
        "code": code,
        "client_id": self.client_id,
        "client_secret": self.client_secret
    }
    # HTTP请求和JWT解析逻辑
```

#### 预计工作量
- **开发时间**: 2-3天
- **测试时间**: 1天
- **文档更新**: 0.5天

### A3. 用户会话管理

#### 数据库表设计
```sql
-- 用户表
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    eve_character_id BIGINT UNIQUE NOT NULL,
    character_name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 会话表
CREATE TABLE user_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    session_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 访问令牌表
CREATE TABLE access_tokens (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    access_token TEXT NOT NULL,
    refresh_token TEXT,
    expires_at TIMESTAMP NOT NULL,
    scopes TEXT[],
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 会话管理服务
```python
# src/application/session.py
class SessionService:
    async def create_user_session(self, character_data: Dict) -> str:
        # 创建或更新用户记录
        # 生成会话令牌
        # 存储会话信息
        pass
    
    async def validate_session(self, session_token: str) -> Optional[Dict]:
        # 验证会话有效性
        # 返回用户信息
        pass
```

---

## 🗄️ 阶段B: 数据库模型完善 - 技术细节

### B1. 核心数据表设计

#### 完整数据库架构
```sql
-- 角色表扩展
CREATE TABLE characters (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    character_id BIGINT UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    corporation_id BIGINT,
    corporation_name VARCHAR(255),
    alliance_id BIGINT,
    alliance_name VARCHAR(255),
    security_status DECIMAL(3,2),
    birthday TIMESTAMP,
    race_id INTEGER,
    bloodline_id INTEGER,
    ancestry_id INTEGER,
    gender VARCHAR(10),
    description TEXT,
    title VARCHAR(255),
    total_sp BIGINT DEFAULT 0,
    unallocated_sp BIGINT DEFAULT 0,
    last_sync TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 技能表
CREATE TABLE character_skills (
    id SERIAL PRIMARY KEY,
    character_id BIGINT REFERENCES characters(character_id),
    skill_id INTEGER NOT NULL,
    skill_name VARCHAR(255),
    level INTEGER NOT NULL,
    skillpoints_in_skill BIGINT NOT NULL,
    trained_skill_level INTEGER NOT NULL,
    last_sync TIMESTAMP,
    UNIQUE(character_id, skill_id)
);

-- 资产表
CREATE TABLE character_assets (
    id SERIAL PRIMARY KEY,
    character_id BIGINT REFERENCES characters(character_id),
    item_id BIGINT UNIQUE NOT NULL,
    type_id INTEGER NOT NULL,
    type_name VARCHAR(255),
    quantity INTEGER NOT NULL,
    location_id BIGINT,
    location_name VARCHAR(255),
    location_type VARCHAR(50),
    is_singleton BOOLEAN DEFAULT FALSE,
    last_sync TIMESTAMP
);
```

### B2. 数据库迁移系统

#### Alembic配置
```python
# alembic/env.py
from src.infrastructure.database.models import Base
target_metadata = Base.metadata

# 迁移脚本示例
# alembic/versions/001_initial_tables.py
def upgrade():
    op.create_table('users', ...)
    op.create_table('characters', ...)
    # ...

def downgrade():
    op.drop_table('characters')
    op.drop_table('users')
```

#### 数据库初始化脚本
```python
# scripts/init_database.py
async def init_database():
    # 创建所有表
    # 插入初始数据
    # 创建索引
    # 设置权限
```

### B3. Repository模式实现

#### 基础Repository
```python
# src/infrastructure/database/repositories/base.py
class BaseRepository:
    def __init__(self, db: Session):
        self.db = db
    
    async def create(self, obj: BaseModel) -> BaseModel:
        # 通用创建方法
    
    async def get_by_id(self, id: int) -> Optional[BaseModel]:
        # 通用查询方法
    
    async def update(self, id: int, data: Dict) -> BaseModel:
        # 通用更新方法
```

#### 专用Repository
```python
# src/infrastructure/database/repositories/character.py
class CharacterRepository(BaseRepository):
    async def get_by_character_id(self, character_id: int) -> Optional[Character]:
        # 角色特定查询
    
    async def get_user_characters(self, user_id: int) -> List[Character]:
        # 用户角色列表
    
    async def sync_character_data(self, character_id: int, data: Dict):
        # 角色数据同步
```

---

## ⚡ 阶段C: 缓存系统集成 - 技术细节

### C1. Redis集成

#### Redis配置
```python
# src/infrastructure/cache/redis_client.py
class RedisClient:
    def __init__(self):
        self.redis = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            db=settings.REDIS_DB,
            decode_responses=True
        )
    
    async def get(self, key: str) -> Optional[str]:
        return await self.redis.get(key)
    
    async def set(self, key: str, value: str, expire: int = 3600):
        return await self.redis.setex(key, expire, value)
```

#### 缓存键命名规范
```python
# 缓存键格式
CACHE_KEYS = {
    "character_info": "char:{character_id}:info",
    "character_skills": "char:{character_id}:skills",
    "character_assets": "char:{character_id}:assets",
    "user_characters": "user:{user_id}:characters",
    "esi_response": "esi:{endpoint}:{params_hash}"
}
```

### C2. 多级缓存架构

#### 缓存装饰器
```python
# src/infrastructure/cache/decorators.py
def cached(key_pattern: str, expire: int = 3600, levels: List[str] = ["memory", "redis"]):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 1. 检查内存缓存
            # 2. 检查Redis缓存
            # 3. 执行原函数
            # 4. 更新缓存
            pass
        return wrapper
    return decorator

# 使用示例
@cached("char:{character_id}:info", expire=1800)
async def get_character_info(character_id: int):
    # 实际的数据获取逻辑
```

---

## 📋 实施时间表

### 第1周
- **周一-周二**: A1 EVE SSO配置验证
- **周三-周四**: A2 OAuth流程实现
- **周五**: A3 用户会话管理基础

### 第2周
- **周一**: A3 用户会话管理完善 + 测试
- **周二-周三**: B1 核心数据表设计和实现
- **周四**: B2 数据库迁移系统
- **周五**: B3 Repository模式实现

### 第3周 (可选)
- **周一-周二**: C1 Redis集成
- **周三-周四**: C2 多级缓存架构
- **周五**: 整体测试和优化

---

## 🧪 测试策略

### 单元测试
```python
# tests/test_sso_client.py
class TestEVESSOClient:
    async def test_generate_login_url(self):
        # 测试登录URL生成
    
    async def test_exchange_code_for_tokens(self):
        # 测试token交换
```

### 集成测试
```python
# tests/integration/test_auth_flow.py
class TestAuthFlow:
    async def test_complete_oauth_flow(self):
        # 测试完整的OAuth流程
    
    async def test_session_management(self):
        # 测试会话管理
```

### 性能测试
```python
# tests/performance/test_cache_performance.py
class TestCachePerformance:
    async def test_cache_hit_rate(self):
        # 测试缓存命中率
    
    async def test_response_time_improvement(self):
        # 测试响应时间改善
```

---

## 🚀 部署准备

### 环境配置
```bash
# .env.production
EVE_CLIENT_ID=your_client_id
EVE_CLIENT_SECRET=your_client_secret
EVE_CALLBACK_URL=https://yourdomain.com/auth/callback
DATABASE_URL=postgresql://user:pass@localhost/mytheve
REDIS_URL=redis://localhost:6379/0
```

### Docker配置
```dockerfile
# Dockerfile
FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["uvicorn", "src.presentation.api.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 部署脚本
```bash
# scripts/deploy.sh
#!/bin/bash
# 数据库迁移
alembic upgrade head
# 启动应用
uvicorn src.presentation.api.main:app --host 0.0.0.0 --port 8000
```
