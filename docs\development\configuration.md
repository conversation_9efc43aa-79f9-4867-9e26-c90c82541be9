# EVE Online Assistant 配置指南

## 📋 配置文件位置

### 主配置文件：`.env`
项目根目录下的 `.env` 文件是**主要的配置文件**，包含所有环境变量配置。

### 配置文件结构
```
EVE Online Assistant/
├── .env                    # 主配置文件 ⭐
├── .env.example           # 配置模板
├── src/infrastructure/config/
│   └── settings.py        # 配置类定义
└── scripts/
    └── validate_config.py # 配置验证脚本
```

## 🔧 必需配置项

### EVE SSO 配置
这些是**必须配置**的项目，用于EVE Online SSO认证：

```env
# EVE SSO配置 - 从EVE Developer Portal获取
EVE_SSO_CLIENT_ID="0643ec5d929e4a5094df698fc5780f0a"
EVE_SSO_CLIENT_SECRET="amw1eI9d77cH3riEFoyh4VaQsZ49JFptjcLKBteQ"
EVE_SSO_CALLBACK_URL="https://d537-175-168-188-176.ngrok-free.app/callback"
EVE_SSO_SCOPES="esi-assets.read_assets.v1"
EVE_SSO_STATE_TIMEOUT=300
```

### ESI API 配置
```env
# ESI API配置
ESI_BASE_URL="https://esi.evetech.net"
ESI_USER_AGENT="MyEVEInventoryTool/1.3 (By Your Name)"
ESI_TIMEOUT=30
ESI_MAX_RETRIES=3
```

### 应用基础配置
```env
# 应用配置
APP_NAME="EVE Online Assistant"
APP_VERSION="0.1.0"
DEBUG=true
SECRET_KEY="your-secret-key-here"
```

## 🗄️ 数据库配置

### 开发环境（SQLite）
```env
DATABASE_URL="sqlite:///./eve_assistant.db"
DATABASE_ECHO=false
```

### 生产环境（PostgreSQL）
```env
DATABASE_URL="postgresql://username:password@localhost:5432/eve_assistant"
DATABASE_ECHO=false
```

## 🚀 缓存配置

### Redis 配置
```env
REDIS_URL="redis://localhost:6379/0"
```

### 智能缓存TTL配置
```env
# 按数据类型分层的缓存配置
CACHE_TTL_REALTIME=30      # 实时数据：位置、在线状态
CACHE_TTL_FREQUENT=300     # 频繁数据：市场订单、邮件
CACHE_TTL_REGULAR=3600     # 常规数据：技能、资产
CACHE_TTL_DAILY=86400      # 每日数据：角色信息、公司信息
CACHE_TTL_COMPUTED=1800    # 计算结果：分析数据

# 智能缓存优化
CACHE_OPTIMIZATION_ENABLED=true
CACHE_MAX_MEMORY_USAGE=512
CACHE_EVICTION_POLICY=lru
```

## 📦 存储配置

```env
# 存储配置
STORAGE_BASE_PATH=./data
STORAGE_ENABLE_COMPRESSION=true
STORAGE_AUTO_CLEANUP=true
STORAGE_BACKUP_ENABLED=true
```

## ⚡ 性能配置

### API限流配置
```env
# ESI API限流配置
RATE_LIMIT_REQUESTS_PER_SECOND=10
RATE_LIMIT_BURST=20
```

### 数据同步配置
```env
# 同步间隔配置（秒）
SYNC_INTERVAL_REALTIME=30      # 实时数据同步
SYNC_INTERVAL_FREQUENT=300     # 频繁数据同步
SYNC_INTERVAL_REGULAR=3600     # 常规数据同步
SYNC_INTERVAL_DAILY=86400      # 每日数据同步

# 同步功能开关
SYNC_CHARACTER_LOCATION=true
SYNC_CHARACTER_ONLINE=true
SYNC_CHARACTER_SKILLS=true
SYNC_CHARACTER_ASSETS=true
SYNC_CHARACTER_WALLET=true
SYNC_MARKET_ORDERS=true
SYNC_INDUSTRY_JOBS=true
SYNC_CORPORATION_INFO=true
```

## 🔐 安全配置

### JWT配置
```env
JWT_ALGORITHM="HS256"
JWT_EXPIRE_MINUTES=30
JWT_REFRESH_EXPIRE_DAYS=7
```

### CORS配置
```env
CORS_ORIGINS=http://localhost:3000,http://localhost:8000
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=*
CORS_ALLOW_HEADERS=*
```

## 📝 日志配置

```env
LOG_LEVEL="INFO"
LOG_FORMAT="json"
```

## 🔧 如何配置您的应用

### 1. 复制配置模板
```bash
cp .env.example .env
```

### 2. 编辑 `.env` 文件
使用任何文本编辑器打开 `.env` 文件，填入您的配置：

```env
# 必需配置 - 请替换为您的实际值
EVE_SSO_CLIENT_ID="您的Client ID"
EVE_SSO_CLIENT_SECRET="您的Client Secret"
EVE_SSO_CALLBACK_URL="您的回调URL"
ESI_USER_AGENT="您的应用名称/版本 (联系信息)"
```

### 3. 验证配置
运行配置验证脚本：
```bash
python scripts/validate_config.py
```

### 4. 测试连接
启动应用并测试：
```bash
python -m uvicorn src.presentation.api.main:app --reload
```

## 🌍 环境特定配置

### 开发环境
```env
DEBUG=true
DATABASE_URL="sqlite:///./eve_assistant.db"
REDIS_URL="redis://localhost:6379/0"
LOG_LEVEL="DEBUG"
```

### 生产环境
```env
DEBUG=false
DATABASE_URL="***********************************/eve_assistant"
REDIS_URL="redis://prod-redis:6379/0"
LOG_LEVEL="INFO"
SECRET_KEY="strong-production-secret-key"
```

## 🔍 配置验证

### 自动验证
系统启动时会自动验证关键配置项。

### 手动验证
```bash
# 运行完整配置验证
python scripts/validate_config.py

# 快速配置检查
python -c "from src.infrastructure.config import settings; print('配置加载成功!')"
```

### 验证检查项
- ✅ EVE SSO配置完整性
- ✅ ESI API配置正确性
- ✅ 数据库连接可用性
- ✅ Redis缓存连接
- ✅ 存储路径权限
- ✅ 限流配置合理性

## 🚨 常见配置问题

### 1. EVE SSO配置错误
```
❌ EVE_SSO_CLIENT_ID 使用默认值
解决：在EVE Developer Portal创建应用，获取真实的Client ID
```

### 2. 回调URL不匹配
```
❌ 回调URL与EVE Developer Portal配置不一致
解决：确保.env中的回调URL与开发者门户中的完全一致
```

### 3. User-Agent格式错误
```
❌ ESI_USER_AGENT 格式不正确
解决：使用格式 "AppName/Version (Contact Info)"
```

### 4. 权限范围错误
```
❌ EVE_SSO_SCOPES 权限不足
解决：根据功能需求添加必要的ESI权限范围
```

## 📚 配置参考

### EVE Developer Portal
- 网址：https://developers.eveonline.com/
- 创建应用获取Client ID和Secret
- 配置回调URL

### ESI API文档
- 网址：https://esi.evetech.net/ui/
- 查看可用的API端点
- 了解权限范围要求

### 权限范围参考
常用的ESI权限范围：
```
esi-assets.read_assets.v1           # 读取资产
esi-characters.read_contacts.v1     # 读取联系人
esi-characters.read_location.v1     # 读取位置
esi-skills.read_skills.v1           # 读取技能
esi-wallet.read_character_wallet.v1 # 读取钱包
esi-markets.read_character_orders.v1 # 读取市场订单
```

## 🎯 配置最佳实践

1. **安全性**：生产环境使用强密钥，不要提交敏感信息到版本控制
2. **性能**：根据实际使用情况调整缓存TTL和限流参数
3. **监控**：启用日志和性能监控，便于问题排查
4. **备份**：定期备份配置文件和数据库
5. **测试**：配置更改后运行验证脚本确保正确性

通过正确配置这些参数，您的EVE Online Assistant将能够正常连接EVE Online API并提供完整的功能。
