"""
角色领域服务
"""
from datetime import datetime, timedelta
from typing import Dict, List, Optional

from ..shared.base_entity import DomainService
from ..shared.value_objects import CharacterId, SkillPoints
from ..shared.exceptions import BusinessRuleViolationError, DomainValidationError
from .entities import Character
from .value_objects import Skill, SkillQueue, Attributes
from .repositories import CharacterRepository


class SkillTrainingService(DomainService):
    """技能训练服务"""
    
    def __init__(self, character_repository: CharacterRepository):
        self._character_repository = character_repository
    
    def calculate_training_time(self, 
                              character: Character,
                              skill_id: int,
                              target_level: int,
                              attributes: Optional[Attributes] = None) -> timedelta:
        """计算技能训练时间"""
        if target_level < 1 or target_level > 5:
            raise DomainValidationError("目标等级必须在1-5之间")
        
        current_skill = character.get_skill_by_id(skill_id)
        current_level = current_skill.trained_skill_level if current_skill else 0
        current_sp = current_skill.skillpoints_in_skill.value if current_skill else 0
        
        if target_level <= current_level:
            raise DomainValidationError("目标等级必须高于当前等级")
        
        # 使用角色当前属性或提供的属性
        char_attributes = attributes or character.attributes
        
        # 计算所需技能点
        required_sp = self._calculate_required_sp_for_level(skill_id, target_level)
        sp_needed = required_sp - current_sp
        
        # 计算训练速度（SP/分钟）
        training_speed = self._calculate_training_speed(skill_id, char_attributes)
        
        # 计算训练时间（分钟）
        training_minutes = sp_needed / training_speed
        
        return timedelta(minutes=training_minutes)
    
    def can_add_to_queue(self, character: Character, skill_id: int, target_level: int) -> bool:
        """检查是否可以添加到技能队列"""
        # 检查队列长度限制
        if len(character.skill_queue) >= 50:  # EVE Online的队列限制
            return False
        
        # 检查是否已在队列中
        for queue_item in character.skill_queue:
            if queue_item.skill_id == skill_id:
                return False
        
        # 检查前置技能（简化版本）
        return self._check_prerequisites(character, skill_id, target_level)
    
    def optimize_training_order(self, 
                               character: Character,
                               desired_skills: List[tuple[int, int]]) -> List[tuple[int, int]]:
        """优化技能训练顺序"""
        # 简化的优化算法，实际应该考虑前置技能依赖
        optimized = []
        remaining_skills = desired_skills.copy()
        
        while remaining_skills:
            # 找到可以立即训练的技能
            trainable = []
            for skill_id, level in remaining_skills:
                if self._check_prerequisites(character, skill_id, level):
                    trainable.append((skill_id, level))
            
            if not trainable:
                # 如果没有可训练的技能，选择第一个
                trainable = [remaining_skills[0]]
            
            # 选择训练时间最短的技能
            best_skill = min(trainable, 
                           key=lambda x: self.calculate_training_time(character, x[0], x[1]))
            
            optimized.append(best_skill)
            remaining_skills.remove(best_skill)
            
            # 模拟训练完成，更新角色技能
            # 这里应该创建一个临时的角色副本来模拟
        
        return optimized
    
    def _calculate_required_sp_for_level(self, skill_id: int, level: int) -> int:
        """计算指定等级所需的总技能点"""
        # 简化的计算，实际应该从技能数据库获取
        base_sp = 250
        multiplier = self._get_skill_multiplier(skill_id)
        
        total_sp = 0
        for i in range(1, level + 1):
            level_sp = base_sp * multiplier * (2 ** (i - 1))
            total_sp += level_sp
        
        return total_sp
    
    def _calculate_training_speed(self, skill_id: int, attributes: Attributes) -> float:
        """计算技能训练速度（SP/分钟）"""
        # 获取技能的主要和次要属性
        primary_attr, secondary_attr = self._get_skill_attributes(skill_id)
        
        # 从角色属性中获取对应值
        primary_value = getattr(attributes, primary_attr.lower())
        secondary_value = getattr(attributes, secondary_attr.lower())
        
        # 计算训练速度
        speed = primary_value + (secondary_value / 2.0)
        
        return speed
    
    def _get_skill_multiplier(self, skill_id: int) -> int:
        """获取技能倍数"""
        # 简化版本，实际应该从技能数据库获取
        # 不同技能有不同的训练倍数（1x, 2x, 3x等）
        return 1
    
    def _get_skill_attributes(self, skill_id: int) -> tuple[str, str]:
        """获取技能的主要和次要属性"""
        # 简化版本，实际应该从技能数据库获取
        # 返回属性名称，如 ("Intelligence", "Memory")
        return ("Intelligence", "Memory")
    
    def _check_prerequisites(self, character: Character, skill_id: int, level: int) -> bool:
        """检查技能前置条件"""
        # 简化版本，实际应该检查复杂的前置技能树
        return True


class CharacterAnalysisService(DomainService):
    """角色分析服务"""
    
    def calculate_character_value(self, character: Character) -> Dict[str, float]:
        """计算角色价值"""
        total_sp = character.get_total_skill_points().value
        
        # 简化的价值计算
        sp_value = total_sp * 0.5  # 每SP价值0.5 ISK（示例）
        
        return {
            "total_skill_points": total_sp,
            "estimated_sp_value": sp_value,
            "wallet_balance": character.wallet_balance.amount,
            "total_estimated_value": sp_value + character.wallet_balance.amount
        }
    
    def get_training_recommendations(self, character: Character) -> List[Dict[str, any]]:
        """获取训练建议"""
        recommendations = []
        
        # 分析角色当前技能分布
        skill_categories = self._categorize_skills(character)
        
        # 基于技能分布给出建议
        for category, skills in skill_categories.items():
            if len(skills) < 5:  # 如果某个类别技能较少
                recommendations.append({
                    "type": "skill_category",
                    "category": category,
                    "reason": f"建议增强{category}类技能",
                    "priority": "medium"
                })
        
        return recommendations
    
    def compare_characters(self, char1: Character, char2: Character) -> Dict[str, any]:
        """比较两个角色"""
        char1_sp = char1.get_total_skill_points().value
        char2_sp = char2.get_total_skill_points().value
        
        return {
            "character1": {
                "name": char1.name,
                "total_sp": char1_sp,
                "wallet": char1.wallet_balance.amount
            },
            "character2": {
                "name": char2.name,
                "total_sp": char2_sp,
                "wallet": char2.wallet_balance.amount
            },
            "comparison": {
                "sp_difference": char1_sp - char2_sp,
                "wallet_difference": char1.wallet_balance.amount - char2.wallet_balance.amount,
                "stronger_character": char1.name if char1_sp > char2_sp else char2.name
            }
        }
    
    def _categorize_skills(self, character: Character) -> Dict[str, List[Skill]]:
        """将技能按类别分组"""
        categories = {
            "combat": [],
            "industry": [],
            "trade": [],
            "exploration": [],
            "social": []
        }
        
        # 简化的分类逻辑
        for skill in character.skills.values():
            # 实际应该根据技能ID查询技能数据库来确定类别
            category = self._get_skill_category(skill.skill_id)
            if category in categories:
                categories[category].append(skill)
        
        return categories
    
    def _get_skill_category(self, skill_id: int) -> str:
        """获取技能类别"""
        # 简化版本，实际应该从技能数据库获取
        return "combat"


class CharacterSecurityService(DomainService):
    """角色安全服务"""
    
    def is_character_safe_for_high_sec(self, character: Character) -> bool:
        """检查角色是否适合在高安活动"""
        return character.security_status.value >= -2.0
    
    def can_enter_high_sec(self, character: Character) -> bool:
        """检查角色是否可以进入高安"""
        return character.security_status.value >= -5.0
    
    def calculate_security_status_recovery_time(self, character: Character, target_status: float) -> Optional[timedelta]:
        """计算安全等级恢复时间"""
        current_status = character.security_status.value
        
        if current_status >= target_status:
            return timedelta(0)
        
        if current_status < -10.0:
            return None  # 无法恢复
        
        # 简化的恢复时间计算
        status_diff = target_status - current_status
        recovery_days = status_diff * 30  # 每0.1安全等级需要3天恢复
        
        return timedelta(days=recovery_days)
