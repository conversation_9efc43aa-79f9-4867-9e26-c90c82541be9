#!/usr/bin/env python3
"""
测试服务器状态的脚本
"""
import requests
import time

def test_backend():
    """测试后端服务器"""
    try:
        print("🔍 测试后端服务器 (http://localhost:8000)...")
        
        # 测试健康检查
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ 后端健康检查通过")
            print(f"   响应: {response.json()}")
        else:
            print(f"❌ 后端健康检查失败: {response.status_code}")
            return False
            
        # 测试EVE登录API
        login_data = {"scopes": ["esi-characters.read_characters.v1"]}
        response = requests.post("http://localhost:8000/auth/login", json=login_data, timeout=5)
        if response.status_code == 200:
            print("✅ EVE登录API测试通过")
            result = response.json()
            if result.get("success") and "login_url" in result.get("data", {}):
                print(f"   登录URL: {result['data']['login_url'][:50]}...")
            else:
                print(f"   响应: {result}")
        else:
            print(f"❌ EVE登录API测试失败: {response.status_code}")
            print(f"   响应: {response.text}")
            
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务器")
        return False
    except Exception as e:
        print(f"❌ 后端测试失败: {e}")
        return False

def test_frontend():
    """测试前端服务器"""
    try:
        print("\n🔍 测试前端服务器 (http://localhost:3000)...")
        
        response = requests.get("http://localhost:3000", timeout=5)
        if response.status_code == 200:
            print("✅ 前端服务器运行正常")
            return True
        else:
            print(f"❌ 前端服务器响应异常: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到前端服务器")
        return False
    except Exception as e:
        print(f"❌ 前端测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始测试服务器状态...\n")
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(5)
    
    backend_ok = test_backend()
    frontend_ok = test_frontend()
    
    print("\n📊 测试结果:")
    print(f"   后端服务器: {'✅ 正常' if backend_ok else '❌ 异常'}")
    print(f"   前端服务器: {'✅ 正常' if frontend_ok else '❌ 异常'}")
    
    if backend_ok and frontend_ok:
        print("\n🎉 所有服务器运行正常！")
        print("   你可以访问: http://localhost:3000")
        print("   API文档: http://localhost:8000/docs")
    else:
        print("\n⚠️  部分服务器有问题，请检查启动日志")

if __name__ == "__main__":
    main()
