#!/usr/bin/env python3
"""
EVE SSO配置验证脚本
验证EVE开发者应用配置是否正确
"""
import sys
import os
import requests
import json
from urllib.parse import urlencode
from datetime import datetime

# 添加项目路径
sys.path.insert(0, '.')

from src.infrastructure.config import settings

def verify_eve_sso_config():
    """验证EVE SSO配置"""
    print("🔐 EVE SSO配置验证")
    print("=" * 50)
    
    # 1. 检查环境变量
    print("\n1. 检查环境变量...")
    
    required_vars = [
        'EVE_SSO_CLIENT_ID',
        'EVE_SSO_CLIENT_SECRET', 
        'EVE_SSO_CALLBACK_URL',
        'EVE_SSO_BASE_URL'
    ]
    
    missing_vars = []
    for var in required_vars:
        value = getattr(settings, var.lower(), None)
        if value:
            if 'SECRET' in var:
                print(f"   ✅ {var}: {'*' * 20}")
            else:
                print(f"   ✅ {var}: {value}")
        else:
            print(f"   ❌ {var}: 未配置")
            missing_vars.append(var)
    
    if missing_vars:
        print(f"\n❌ 缺少必要的环境变量: {', '.join(missing_vars)}")
        return False
    
    # 2. 测试EVE SSO端点连通性
    print("\n2. 测试EVE SSO端点连通性...")

    endpoints = [
        f"{settings.eve_sso_base_url}/v2/oauth/authorize",
        f"{settings.eve_sso_base_url}/v2/oauth/token",
        "https://esi.evetech.net/verify/"
    ]

    for endpoint in endpoints:
        try:
            response = requests.head(endpoint, timeout=10)
            if response.status_code in [200, 405, 400]:  # 405和400也是正常的，说明端点存在
                print(f"   ✅ {endpoint}: 可访问 (HTTP {response.status_code})")
            else:
                print(f"   ⚠️  {endpoint}: HTTP {response.status_code}")
        except Exception as e:
            print(f"   ❌ {endpoint}: 连接失败 - {e}")
            return False
    
    # 3. 验证回调URL格式
    print("\n3. 验证回调URL格式...")
    
    callback_url = settings.eve_sso_callback_url
    if callback_url.startswith('https://'):
        print(f"   ✅ 回调URL格式正确: {callback_url}")
    elif callback_url.startswith('http://localhost'):
        print(f"   ⚠️  回调URL使用localhost: {callback_url}")
        print("      注意: 生产环境需要使用HTTPS")
    else:
        print(f"   ❌ 回调URL格式不正确: {callback_url}")
        return False
    
    # 4. 生成测试登录URL
    print("\n4. 生成测试登录URL...")
    
    try:
        test_scopes = [
            "esi-assets.read_assets.v1",
            "esi-skills.read_skills.v1",
            "esi-wallet.read_character_wallet.v1"
        ]
        
        params = {
            "response_type": "code",
            "redirect_uri": callback_url,
            "client_id": settings.eve_sso_client_id,
            "scope": " ".join(test_scopes),
            "state": "test_state_12345"
        }
        
        login_url = f"{settings.eve_sso_base_url}/v2/oauth/authorize?{urlencode(params)}"
        
        print(f"   ✅ 测试登录URL生成成功")
        print(f"   🔗 URL长度: {len(login_url)} 字符")
        print(f"   🔗 完整URL: {login_url[:100]}...")
        
        # 保存完整URL到文件
        with open("test_login_url.txt", "w") as f:
            f.write(f"EVE SSO测试登录URL\n")
            f.write(f"生成时间: {datetime.now().isoformat()}\n")
            f.write(f"完整URL:\n{login_url}\n")
        
        print(f"   📄 完整URL已保存到: test_login_url.txt")
        
    except Exception as e:
        print(f"   ❌ 生成登录URL失败: {e}")
        return False
    
    # 5. 验证ngrok状态 (如果使用)
    if 'ngrok' in callback_url:
        print("\n5. 验证ngrok状态...")
        try:
            response = requests.get("http://localhost:4040/api/tunnels", timeout=5)
            if response.status_code == 200:
                data = response.json()
                tunnels = data.get('tunnels', [])
                if tunnels:
                    for tunnel in tunnels:
                        public_url = tunnel.get('public_url', '')
                        if callback_url.startswith(public_url.replace('http://', 'https://')):
                            print(f"   ✅ ngrok隧道正常: {public_url}")
                            break
                    else:
                        print(f"   ⚠️  ngrok隧道URL不匹配")
                else:
                    print(f"   ❌ 没有活动的ngrok隧道")
            else:
                print(f"   ❌ ngrok API不可访问")
        except Exception as e:
            print(f"   ⚠️  无法检查ngrok状态: {e}")
            print("      请确保ngrok正在运行")
    
    print("\n" + "=" * 50)
    print("🎉 EVE SSO配置验证完成！")
    print("✅ 所有检查项目通过")
    print("🚀 可以开始实施真实EVE SSO集成")
    print("=" * 50)
    
    return True

if __name__ == "__main__":
    success = verify_eve_sso_config()
    
    if success:
        print("\n📋 下一步行动:")
        print("1. 检查test_login_url.txt中的登录URL")
        print("2. 可以开始实施OAuth流程")
        print("3. 准备用户会话管理")
    else:
        print("\n⚠️  请先解决配置问题再继续")
    
    sys.exit(0 if success else 1)
