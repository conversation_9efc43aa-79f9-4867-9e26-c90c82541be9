"""
ESI API数据模型
"""
from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, validator


class ESICharacterInfo(BaseModel):
    """ESI角色信息模型"""
    character_id: int = Field(..., alias="character_id")
    name: str
    description: Optional[str] = ""
    corporation_id: int
    alliance_id: Optional[int] = None
    faction_id: Optional[int] = None
    race_id: int
    bloodline_id: int
    ancestry_id: Optional[int] = None
    gender: str
    security_status: Optional[float] = 0.0
    birthday: datetime
    title: Optional[str] = ""
    
    class Config:
        allow_population_by_field_name = True


class ESICharacterAttributes(BaseModel):
    """ESI角色属性模型"""
    charisma: int
    intelligence: int
    memory: int
    perception: int
    willpower: int
    bonus_remaps: Optional[int] = 0
    last_remap_date: Optional[datetime] = None
    accrued_remap_cooldown_date: Optional[datetime] = None


class ESISkill(BaseModel):
    """ESI技能模型"""
    skill_id: int
    skillpoints_in_skill: int
    trained_skill_level: int
    active_skill_level: int


class ESISkillQueue(BaseModel):
    """ESI技能队列模型"""
    skill_id: int
    finished_level: int
    queue_position: int
    training_start_sp: Optional[int] = None
    level_end_sp: Optional[int] = None
    level_start_sp: Optional[int] = None
    start_date: Optional[datetime] = None
    finish_date: Optional[datetime] = None


class ESICharacterLocation(BaseModel):
    """ESI角色位置模型"""
    solar_system_id: int
    station_id: Optional[int] = None
    structure_id: Optional[int] = None


class ESICharacterOnline(BaseModel):
    """ESI角色在线状态模型"""
    online: bool
    last_login: Optional[datetime] = None
    last_logout: Optional[datetime] = None
    logins: Optional[int] = None


class ESICharacterShip(BaseModel):
    """ESI角色当前飞船模型"""
    ship_type_id: int
    ship_item_id: int
    ship_name: str


class ESIWalletJournal(BaseModel):
    """ESI钱包日志模型"""
    id: int
    date: datetime
    ref_type: str
    first_party_id: Optional[int] = None
    first_party_type: Optional[str] = None
    second_party_id: Optional[int] = None
    second_party_type: Optional[str] = None
    amount: Optional[float] = None
    balance: Optional[float] = None
    reason: Optional[str] = ""
    tax_receiver_id: Optional[int] = None
    tax: Optional[float] = None
    context_id: Optional[int] = None
    context_id_type: Optional[str] = None
    description: str


class ESIAsset(BaseModel):
    """ESI资产模型"""
    item_id: int
    type_id: int
    quantity: int
    location_id: int
    location_type: str
    location_flag: str
    is_singleton: bool
    is_blueprint_copy: Optional[bool] = None


class ESIMarketOrder(BaseModel):
    """ESI市场订单模型"""
    order_id: int
    type_id: int
    location_id: int
    volume_total: int
    volume_remain: int
    min_volume: int
    price: float
    is_buy_order: bool
    duration: int
    issued: datetime
    range: str


class ESIMarketHistory(BaseModel):
    """ESI市场历史模型"""
    date: str
    order_count: int
    volume: int
    highest: float
    average: float
    lowest: float


class ESIIndustryJob(BaseModel):
    """ESI工业任务模型"""
    job_id: int
    installer_id: int
    facility_id: int
    station_id: Optional[int] = None
    activity_id: int
    blueprint_id: int
    blueprint_type_id: int
    blueprint_location_id: int
    output_location_id: int
    runs: int
    cost: Optional[float] = None
    licensed_runs: Optional[int] = None
    probability: Optional[float] = None
    product_type_id: Optional[int] = None
    status: str
    time_in_seconds: int
    start_date: datetime
    end_date: datetime
    pause_date: Optional[datetime] = None
    completed_date: Optional[datetime] = None
    completed_character_id: Optional[int] = None
    successful_runs: Optional[int] = None


class ESICorporationInfo(BaseModel):
    """ESI公司信息模型"""
    corporation_id: int = Field(..., alias="corporation_id")
    name: str
    ticker: str
    member_count: int
    description: Optional[str] = ""
    tax_rate: float
    date_founded: Optional[datetime] = None
    creator_id: int
    ceo_id: int
    alliance_id: Optional[int] = None
    faction_id: Optional[int] = None
    home_station_id: Optional[int] = None
    shares: Optional[int] = None
    url: Optional[str] = ""
    war_eligible: Optional[bool] = None
    
    class Config:
        allow_population_by_field_name = True


class ESIAllianceInfo(BaseModel):
    """ESI联盟信息模型"""
    alliance_id: int = Field(..., alias="alliance_id")
    name: str
    ticker: str
    creator_id: int
    creator_corporation_id: int
    executor_corporation_id: Optional[int] = None
    date_founded: datetime
    faction_id: Optional[int] = None
    
    class Config:
        allow_population_by_field_name = True


class ESIUniverseType(BaseModel):
    """ESI宇宙物品类型模型"""
    type_id: int
    name: str
    description: str
    published: bool
    group_id: int
    category_id: int
    market_group_id: Optional[int] = None
    mass: Optional[float] = None
    volume: Optional[float] = None
    capacity: Optional[float] = None
    portion_size: Optional[int] = None
    radius: Optional[float] = None
    graphic_id: Optional[int] = None
    icon_id: Optional[int] = None
    sound_id: Optional[int] = None


class ESIUniverseSystem(BaseModel):
    """ESI宇宙星系模型"""
    system_id: int
    name: str
    constellation_id: int
    security_status: float
    security_class: Optional[str] = None
    star_id: Optional[int] = None
    stargates: Optional[List[int]] = []
    stations: Optional[List[int]] = []
    planets: Optional[List[Dict[str, Any]]] = []


class ESIError(BaseModel):
    """ESI错误响应模型"""
    error: str
    error_description: Optional[str] = None
    sso_status: Optional[int] = None
    timeout: Optional[int] = None
