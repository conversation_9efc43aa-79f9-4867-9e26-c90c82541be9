#!/usr/bin/env python3
import sys
sys.path.insert(0, '.')

print('Testing server startup...')
try:
    from src.presentation.api.main import app
    print('SUCCESS: App imported')
    
    import uvicorn
    print('Starting server on port 8000...')
    print('Visit http://localhost:8000/health/status to test')
    
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
    
except Exception as e:
    print(f'ERROR: {e}')
    import traceback
    traceback.print_exc()
