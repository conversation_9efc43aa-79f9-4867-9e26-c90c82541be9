"""
ESI API HTTP客户端
"""
import asyncio
import json
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urljoin

import httpx
from httpx import AsyncClient, Response

from ..config import settings
from ..config.logging import get_logger
from ...domain.shared.exceptions import (
    ESIException, ESIAuthenticationError, ESIAuthorizationError,
    ESIRateLimitError, ESIServerError, ESITimeoutError, ESIDataError
)

logger = get_logger(__name__)


class ESIClient:
    """ESI API客户端"""
    
    def __init__(self):
        self.base_url = settings.esi_base_url
        self.user_agent = settings.esi_user_agent
        self.timeout = settings.esi_timeout
        self.max_retries = settings.esi_max_retries
        self.retry_delay = settings.esi_retry_delay
        self.backoff_factor = settings.esi_backoff_factor
        
        # HTTP客户端配置
        self._client: Optional[AsyncClient] = None
        self._rate_limiter = RateLimiter()
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self._ensure_client()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
    
    async def _ensure_client(self):
        """确保HTTP客户端已初始化"""
        if self._client is None:
            self._client = AsyncClient(
                timeout=httpx.Timeout(self.timeout),
                headers={
                    "User-Agent": self.user_agent,
                    "Accept": "application/json",
                },
                follow_redirects=True
            )
    
    async def close(self):
        """关闭HTTP客户端"""
        if self._client:
            await self._client.aclose()
            self._client = None
    
    async def get(self, 
                  endpoint: str,
                  params: Optional[Dict[str, Any]] = None,
                  headers: Optional[Dict[str, str]] = None,
                  token: Optional[str] = None) -> Dict[str, Any]:
        """发送GET请求"""
        return await self._request("GET", endpoint, params=params, headers=headers, token=token)
    
    async def post(self,
                   endpoint: str,
                   data: Optional[Dict[str, Any]] = None,
                   params: Optional[Dict[str, Any]] = None,
                   headers: Optional[Dict[str, str]] = None,
                   token: Optional[str] = None) -> Dict[str, Any]:
        """发送POST请求"""
        return await self._request("POST", endpoint, data=data, params=params, headers=headers, token=token)
    
    async def _request(self,
                      method: str,
                      endpoint: str,
                      data: Optional[Dict[str, Any]] = None,
                      params: Optional[Dict[str, Any]] = None,
                      headers: Optional[Dict[str, str]] = None,
                      token: Optional[str] = None) -> Dict[str, Any]:
        """发送HTTP请求"""
        await self._ensure_client()
        
        # 构建完整URL
        url = urljoin(self.base_url, endpoint.lstrip('/'))
        
        # 准备请求头
        request_headers = {}
        if headers:
            request_headers.update(headers)
        
        if token:
            request_headers["Authorization"] = f"Bearer {token}"
        
        # 限流控制
        await self._rate_limiter.acquire()
        
        # 重试逻辑
        last_exception = None
        for attempt in range(self.max_retries + 1):
            try:
                logger.debug(
                    "发送ESI请求",
                    method=method,
                    url=url,
                    attempt=attempt + 1,
                    max_attempts=self.max_retries + 1
                )
                
                response = await self._client.request(
                    method=method,
                    url=url,
                    params=params,
                    json=data if data else None,
                    headers=request_headers
                )
                
                return await self._handle_response(response)
                
            except (ESIRateLimitError, ESIServerError) as e:
                last_exception = e
                if attempt < self.max_retries:
                    delay = self.retry_delay * (self.backoff_factor ** attempt)
                    logger.warning(
                        "ESI请求失败，准备重试",
                        error=str(e),
                        attempt=attempt + 1,
                        retry_delay=delay
                    )
                    await asyncio.sleep(delay)
                    continue
                else:
                    logger.error("ESI请求重试次数已用完", error=str(e))
                    raise
            
            except Exception as e:
                logger.error("ESI请求发生未预期错误", error=str(e), exc_info=True)
                raise ESIException(f"Unexpected error during ESI request: {str(e)}")
        
        # 如果所有重试都失败了
        if last_exception:
            raise last_exception
        
        raise ESIException("All retry attempts failed")
    
    async def _handle_response(self, response: Response) -> Dict[str, Any]:
        """处理HTTP响应"""
        # 更新限流器状态
        self._rate_limiter.update_from_response(response)
        
        # 检查HTTP状态码
        if response.status_code == 200:
            try:
                return response.json()
            except json.JSONDecodeError as e:
                logger.error("ESI响应JSON解析失败", response_text=response.text)
                raise ESIDataError(f"Invalid JSON response: {str(e)}")
        
        elif response.status_code == 304:
            # Not Modified - 缓存有效
            return {}
        
        elif response.status_code == 400:
            raise ESIException(f"Bad request: {response.text}")
        
        elif response.status_code == 401:
            raise ESIAuthenticationError("Authentication failed")
        
        elif response.status_code == 403:
            raise ESIAuthorizationError("Insufficient permissions")
        
        elif response.status_code == 404:
            raise ESIException(f"Resource not found: {response.url}")
        
        elif response.status_code == 420:
            # ESI特有的限流状态码
            retry_after = int(response.headers.get("Retry-After", 60))
            raise ESIRateLimitError(retry_after)
        
        elif 500 <= response.status_code < 600:
            raise ESIServerError(response.status_code, f"Server error: {response.text}")
        
        else:
            raise ESIException(f"Unexpected status code {response.status_code}: {response.text}")


class RateLimiter:
    """ESI API限流器"""
    
    def __init__(self):
        self.requests_per_second = settings.rate_limit_requests_per_second
        self.burst_limit = settings.rate_limit_burst
        self.error_limit_remain = 100  # ESI错误限制剩余
        self.error_limit_reset = datetime.utcnow()
        self._semaphore = asyncio.Semaphore(self.burst_limit)
        self._last_request_time = 0.0
    
    async def acquire(self):
        """获取请求许可"""
        async with self._semaphore:
            # 检查错误限制
            if self.error_limit_remain <= 0:
                wait_time = (self.error_limit_reset - datetime.utcnow()).total_seconds()
                if wait_time > 0:
                    logger.warning(f"ESI错误限制已达上限，等待 {wait_time:.1f} 秒")
                    await asyncio.sleep(wait_time)
            
            # 控制请求频率
            current_time = asyncio.get_event_loop().time()
            time_since_last = current_time - self._last_request_time
            min_interval = 1.0 / self.requests_per_second
            
            if time_since_last < min_interval:
                sleep_time = min_interval - time_since_last
                await asyncio.sleep(sleep_time)
            
            self._last_request_time = asyncio.get_event_loop().time()
    
    def update_from_response(self, response: Response):
        """从响应头更新限流状态"""
        # 更新错误限制状态
        if "X-ESI-Error-Limit-Remain" in response.headers:
            self.error_limit_remain = int(response.headers["X-ESI-Error-Limit-Remain"])
        
        if "X-ESI-Error-Limit-Reset" in response.headers:
            reset_timestamp = int(response.headers["X-ESI-Error-Limit-Reset"])
            self.error_limit_reset = datetime.fromtimestamp(reset_timestamp)
        
        logger.debug(
            "更新ESI限流状态",
            error_limit_remain=self.error_limit_remain,
            error_limit_reset=self.error_limit_reset
        )
