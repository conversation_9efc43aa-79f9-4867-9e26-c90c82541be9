#!/usr/bin/env python3
"""
调试服务器错误
"""
import requests
import json
import time
import traceback

def debug_server_errors():
    print("🔍 服务器错误深度调试")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    
    # 1. 检查服务器基本状态
    print("\n1. 检查服务器基本状态...")
    try:
        response = requests.get(f"{base_url}/health/status", timeout=5)
        print(f"   健康检查状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   服务状态: {data.get('status')}")
            print(f"   数据库状态: {data.get('database', {}).get('status')}")
        else:
            print(f"   健康检查失败: {response.text}")
    except Exception as e:
        print(f"   ❌ 服务器连接失败: {e}")
        return False
    
    # 2. 测试认证端点
    print("\n2. 测试认证端点...")
    endpoints = [
        "/auth/status",
        "/auth/eve/login", 
        "/auth/callback"
    ]
    
    for endpoint in endpoints:
        try:
            print(f"\n   测试 {endpoint}:")
            if endpoint == "/auth/eve/login":
                # POST请求
                response = requests.post(
                    f"{base_url}{endpoint}",
                    json={"scopes": ["esi-assets.read_assets.v1"]},
                    timeout=10
                )
            else:
                # GET请求
                response = requests.get(f"{base_url}{endpoint}", timeout=5)
            
            print(f"     状态码: {response.status_code}")
            print(f"     响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"     响应数据: {json.dumps(data, indent=2, ensure_ascii=False)[:200]}...")
                except:
                    print(f"     响应文本: {response.text[:200]}...")
            else:
                print(f"     错误响应: {response.text}")
                
        except Exception as e:
            print(f"     ❌ 请求失败: {e}")
            traceback.print_exc()
    
    # 3. 测试数据库连接
    print("\n3. 测试数据库连接...")
    try:
        # 通过API测试数据库
        response = requests.get(f"{base_url}/health/database", timeout=5)
        print(f"   数据库健康检查状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   数据库连接: {data.get('status')}")
        else:
            print(f"   数据库检查失败: {response.text}")
    except Exception as e:
        print(f"   ❌ 数据库检查失败: {e}")
    
    # 4. 检查前端资源
    print("\n4. 检查前端资源...")
    frontend_endpoints = [
        "/",
        "/static/css/main.css",
        "/static/js/main.js"
    ]
    
    for endpoint in frontend_endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            print(f"   {endpoint}: HTTP {response.status_code}")
            if response.status_code != 200:
                print(f"     错误: {response.text[:100]}...")
        except Exception as e:
            print(f"   {endpoint}: 连接失败 - {e}")
    
    # 5. 检查具体错误信息
    print("\n5. 检查具体错误信息...")
    try:
        # 尝试获取详细的错误信息
        response = requests.get(f"{base_url}/api/debug/errors", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"   错误日志: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"   无法获取错误日志: HTTP {response.status_code}")
    except Exception as e:
        print(f"   错误日志获取失败: {e}")
    
    return True

def check_import_errors():
    """检查模块导入错误"""
    print("\n" + "=" * 60)
    print("🔍 检查模块导入错误")
    print("=" * 60)
    
    try:
        print("\n1. 测试基础模块导入...")
        import sys
        sys.path.insert(0, '.')
        
        # 测试配置导入
        print("   导入配置...")
        from src.infrastructure.config import settings
        print(f"   ✅ 配置导入成功: {settings.app_name}")
        
        # 测试数据库导入
        print("   导入数据库...")
        from src.infrastructure.persistence.database import Base, engine
        print(f"   ✅ 数据库导入成功: {Base}")
        
        # 测试认证服务导入
        print("   导入认证服务...")
        from src.application.auth_simple import SimpleAuthenticationService
        print(f"   ✅ 认证服务导入成功")
        
        # 测试ESI客户端导入
        print("   导入ESI客户端...")
        from src.infrastructure.esi.auth import EVESSOClient
        print(f"   ✅ ESI客户端导入成功")
        
        # 测试路由导入
        print("   导入路由...")
        from src.presentation.api.routers.auth import router
        print(f"   ✅ 路由导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        traceback.print_exc()
        return False

def check_database_connection():
    """检查数据库连接"""
    print("\n" + "=" * 60)
    print("🗄️ 检查数据库连接")
    print("=" * 60)
    
    try:
        import sys
        sys.path.insert(0, '.')
        
        from src.infrastructure.persistence.database import engine, SessionLocal
        from sqlalchemy import text
        
        print("\n1. 测试数据库引擎...")
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            print(f"   ✅ 数据库引擎连接成功: {result.fetchone()}")
        
        print("\n2. 测试会话...")
        with SessionLocal() as session:
            result = session.execute(text("SELECT COUNT(*) FROM users"))
            count = result.fetchone()[0]
            print(f"   ✅ 数据库会话成功，用户数: {count}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚨 MythEVE 错误深度分析")
    print("=" * 60)
    
    # 检查模块导入
    import_ok = check_import_errors()
    
    # 检查数据库连接
    db_ok = check_database_connection()
    
    # 检查服务器错误
    server_ok = debug_server_errors()
    
    print("\n" + "=" * 60)
    print("📋 错误分析总结")
    print("=" * 60)
    print(f"模块导入: {'✅ 正常' if import_ok else '❌ 异常'}")
    print(f"数据库连接: {'✅ 正常' if db_ok else '❌ 异常'}")
    print(f"服务器状态: {'✅ 正常' if server_ok else '❌ 异常'}")
    
    if not (import_ok and db_ok and server_ok):
        print("\n⚠️  发现问题，需要进一步调试")
    else:
        print("\n🎉 所有检查通过，问题可能在前端或特定功能")
    
    print("=" * 60)
