"""
速率限制中间件
"""
import time
from typing import Dict, Optional, Tuple
from collections import defaultdict, deque
from dataclasses import dataclass
from fastapi import Request, Response, HTTPException, status
from starlette.middleware.base import BaseHTTPMiddleware

from ..config import settings
from ..config.logging import get_logger

logger = get_logger(__name__)


@dataclass
class RateLimitRule:
    """速率限制规则"""
    requests: int  # 允许的请求数
    window: int    # 时间窗口（秒）
    burst: int     # 突发请求数
    
    def __str__(self) -> str:
        return f"{self.requests}/{self.window}s (burst: {self.burst})"


class TokenBucket:
    """令牌桶算法实现"""
    
    def __init__(self, capacity: int, refill_rate: float):
        self.capacity = capacity
        self.tokens = capacity
        self.refill_rate = refill_rate
        self.last_refill = time.time()
    
    def consume(self, tokens: int = 1) -> bool:
        """消费令牌"""
        self._refill()
        
        if self.tokens >= tokens:
            self.tokens -= tokens
            return True
        
        return False
    
    def _refill(self):
        """补充令牌"""
        now = time.time()
        elapsed = now - self.last_refill
        
        # 计算应该添加的令牌数
        tokens_to_add = elapsed * self.refill_rate
        self.tokens = min(self.capacity, self.tokens + tokens_to_add)
        self.last_refill = now


class SlidingWindowCounter:
    """滑动窗口计数器"""
    
    def __init__(self, window_size: int):
        self.window_size = window_size
        self.requests = deque()
    
    def add_request(self) -> int:
        """添加请求并返回当前窗口内的请求数"""
        now = time.time()
        self.requests.append(now)
        
        # 移除过期的请求
        cutoff = now - self.window_size
        while self.requests and self.requests[0] < cutoff:
            self.requests.popleft()
        
        return len(self.requests)


class RateLimitingMiddleware(BaseHTTPMiddleware):
    """速率限制中间件"""
    
    def __init__(self, app,
                 default_rule: Optional[RateLimitRule] = None,
                 per_endpoint_rules: Optional[Dict[str, RateLimitRule]] = None,
                 per_user_rules: Optional[Dict[str, RateLimitRule]] = None,
                 enable_burst: bool = True,
                 storage_backend: str = "memory"):
        super().__init__(app)
        
        # 默认规则
        self.default_rule = default_rule or RateLimitRule(
            requests=settings.rate_limit_requests_per_second * 60,  # 每分钟
            window=60,
            burst=settings.rate_limit_burst
        )
        
        # 端点特定规则
        self.per_endpoint_rules = per_endpoint_rules or {
            "/api/auth/login": RateLimitRule(5, 300, 10),  # 登录限制更严格
            "/api/auth/register": RateLimitRule(3, 3600, 5),  # 注册限制
            "/api/auth/refresh": RateLimitRule(10, 60, 20),  # 刷新令牌
            "/api/esi/*": RateLimitRule(100, 60, 150),  # ESI API调用
        }
        
        # 用户特定规则
        self.per_user_rules = per_user_rules or {}
        
        self.enable_burst = enable_burst
        self.storage_backend = storage_backend
        
        # 内存存储
        self._ip_buckets: Dict[str, TokenBucket] = {}
        self._user_buckets: Dict[str, TokenBucket] = {}
        self._endpoint_counters: Dict[str, Dict[str, SlidingWindowCounter]] = defaultdict(dict)
        
        # 清理任务
        self._last_cleanup = time.time()
        self._cleanup_interval = 300  # 5分钟清理一次
    
    async def dispatch(self, request: Request, call_next):
        # 获取客户端标识
        client_id = self._get_client_id(request)
        endpoint = self._get_endpoint_key(request)
        user_id = self._get_user_id(request)
        
        # 检查速率限制
        try:
            self._check_rate_limits(client_id, endpoint, user_id, request)
        except HTTPException as e:
            return Response(
                content=e.detail,
                status_code=e.status_code,
                headers=self._get_rate_limit_headers(client_id, endpoint)
            )
        
        # 处理请求
        response = await call_next(request)
        
        # 添加速率限制头部
        rate_limit_headers = self._get_rate_limit_headers(client_id, endpoint)
        for key, value in rate_limit_headers.items():
            response.headers[key] = value
        
        # 定期清理
        self._periodic_cleanup()
        
        return response
    
    def _get_client_id(self, request: Request) -> str:
        """获取客户端标识"""
        # 优先使用真实IP
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # 使用客户端IP
        return request.client.host if request.client else "unknown"
    
    def _get_endpoint_key(self, request: Request) -> str:
        """获取端点键"""
        path = request.url.path
        method = request.method
        
        # 检查是否有特定端点规则
        for pattern in self.per_endpoint_rules.keys():
            if pattern.endswith("*"):
                if path.startswith(pattern[:-1]):
                    return pattern
            elif path == pattern:
                return pattern
        
        return f"{method}:{path}"
    
    def _get_user_id(self, request: Request) -> Optional[str]:
        """获取用户ID"""
        # 从请求状态中获取用户ID（由认证中间件设置）
        return getattr(request.state, "user_id", None)
    
    def _check_rate_limits(self, client_id: str, endpoint: str, 
                          user_id: Optional[str], request: Request) -> None:
        """检查速率限制"""
        # 1. 检查IP级别限制
        self._check_ip_rate_limit(client_id)
        
        # 2. 检查端点级别限制
        self._check_endpoint_rate_limit(client_id, endpoint)
        
        # 3. 检查用户级别限制
        if user_id:
            self._check_user_rate_limit(user_id)
    
    def _check_ip_rate_limit(self, client_id: str) -> None:
        """检查IP级别速率限制"""
        if client_id not in self._ip_buckets:
            self._ip_buckets[client_id] = TokenBucket(
                capacity=self.default_rule.burst,
                refill_rate=self.default_rule.requests / self.default_rule.window
            )
        
        bucket = self._ip_buckets[client_id]
        if not bucket.consume():
            logger.warning(f"IP速率限制触发: {client_id}")
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded for IP address"
            )
    
    def _check_endpoint_rate_limit(self, client_id: str, endpoint: str) -> None:
        """检查端点级别速率限制"""
        rule = self.per_endpoint_rules.get(endpoint, self.default_rule)
        
        if endpoint not in self._endpoint_counters[client_id]:
            self._endpoint_counters[client_id][endpoint] = SlidingWindowCounter(rule.window)
        
        counter = self._endpoint_counters[client_id][endpoint]
        current_requests = counter.add_request()
        
        if current_requests > rule.requests:
            logger.warning(f"端点速率限制触发: {client_id} -> {endpoint}")
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=f"Rate limit exceeded for endpoint: {endpoint}"
            )
    
    def _check_user_rate_limit(self, user_id: str) -> None:
        """检查用户级别速率限制"""
        rule = self.per_user_rules.get(user_id, self.default_rule)
        
        if user_id not in self._user_buckets:
            self._user_buckets[user_id] = TokenBucket(
                capacity=rule.burst,
                refill_rate=rule.requests / rule.window
            )
        
        bucket = self._user_buckets[user_id]
        if not bucket.consume():
            logger.warning(f"用户速率限制触发: {user_id}")
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded for user"
            )
    
    def _get_rate_limit_headers(self, client_id: str, endpoint: str) -> Dict[str, str]:
        """获取速率限制头部"""
        headers = {}
        
        # IP级别限制信息
        if client_id in self._ip_buckets:
            bucket = self._ip_buckets[client_id]
            headers["X-RateLimit-Limit"] = str(self.default_rule.requests)
            headers["X-RateLimit-Remaining"] = str(int(bucket.tokens))
            headers["X-RateLimit-Reset"] = str(int(time.time() + self.default_rule.window))
        
        # 端点级别限制信息
        rule = self.per_endpoint_rules.get(endpoint, self.default_rule)
        if endpoint in self._endpoint_counters.get(client_id, {}):
            counter = self._endpoint_counters[client_id][endpoint]
            remaining = max(0, rule.requests - len(counter.requests))
            headers["X-RateLimit-Endpoint-Limit"] = str(rule.requests)
            headers["X-RateLimit-Endpoint-Remaining"] = str(remaining)
        
        return headers
    
    def _periodic_cleanup(self) -> None:
        """定期清理过期数据"""
        now = time.time()
        if now - self._last_cleanup < self._cleanup_interval:
            return
        
        # 清理过期的令牌桶
        expired_ips = []
        for ip, bucket in self._ip_buckets.items():
            if now - bucket.last_refill > 3600:  # 1小时未使用
                expired_ips.append(ip)
        
        for ip in expired_ips:
            del self._ip_buckets[ip]
        
        # 清理过期的计数器
        for client_id in list(self._endpoint_counters.keys()):
            expired_endpoints = []
            for endpoint, counter in self._endpoint_counters[client_id].items():
                if not counter.requests:  # 空计数器
                    expired_endpoints.append(endpoint)
            
            for endpoint in expired_endpoints:
                del self._endpoint_counters[client_id][endpoint]
            
            if not self._endpoint_counters[client_id]:
                del self._endpoint_counters[client_id]
        
        self._last_cleanup = now
        
        if expired_ips or expired_endpoints:
            logger.debug(f"清理了 {len(expired_ips)} 个IP桶和相关计数器")


def setup_rate_limiting_middleware(app):
    """设置速率限制中间件"""
    app.add_middleware(RateLimitingMiddleware)
    
    logger.info(
        "速率限制中间件已配置",
        default_limit=f"{settings.rate_limit_requests_per_second * 60}/min",
        burst_limit=settings.rate_limit_burst
    )
