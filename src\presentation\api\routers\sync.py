"""
数据同步路由
"""
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from typing import Dict, Any, List

from ....application.sync import DataSyncService, SyncType
from ....infrastructure.config.logging import get_logger
from ..dependencies import (
    get_sync_service, get_current_user, require_admin, 
    strict_rate_limiter
)
from ..schemas.sync import (
    SyncStatusResponse, SyncRequest, SyncResponse,
    CharacterSyncRequest, CharacterSyncResponse
)

logger = get_logger(__name__)

router = APIRouter()


@router.get("/status", response_model=SyncStatusResponse)
async def get_sync_status(
    sync_service: DataSyncService = Depends(get_sync_service),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取同步状态"""
    try:
        status = await sync_service.get_sync_status()
        
        return SyncStatusResponse(**status)
        
    except Exception as e:
        logger.error("获取同步状态失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get sync status")


@router.post("/character/{character_id}", response_model=CharacterSyncResponse)
async def sync_character(
    character_id: int,
    request: CharacterSyncRequest,
    background_tasks: BackgroundTasks,
    sync_service: DataSyncService = Depends(get_sync_service),
    current_user: Dict[str, Any] = Depends(get_current_user),
    _: None = Depends(strict_rate_limiter)
):
    """同步指定角色数据"""
    try:
        # 解析同步类型
        sync_types = []
        if request.sync_types:
            for sync_type_str in request.sync_types:
                try:
                    sync_types.append(SyncType(sync_type_str))
                except ValueError:
                    raise HTTPException(
                        status_code=400, 
                        detail=f"Invalid sync type: {sync_type_str}"
                    )
        
        # 如果是强制刷新或者没有指定类型，使用所有类型
        if request.force_refresh or not sync_types:
            sync_types = [SyncType.REALTIME, SyncType.FREQUENT, SyncType.REGULAR]
        
        # 执行同步
        if request.background:
            # 后台同步
            background_tasks.add_task(
                sync_service.sync_character_data,
                character_id,
                sync_types
            )
            
            return CharacterSyncResponse(
                character_id=character_id,
                sync_types=[st.value for st in sync_types],
                status="queued",
                message="Sync task queued for background processing"
            )
        else:
            # 同步执行
            result = await sync_service.sync_character_data(character_id, sync_types)
            
            return CharacterSyncResponse(
                character_id=character_id,
                sync_types=[st.value for st in sync_types],
                status="completed" if "error" not in result else "failed",
                results=result.get("results", {}),
                sync_time=result.get("sync_time"),
                message="Sync completed successfully" if "error" not in result else result.get("error")
            )
        
    except Exception as e:
        logger.error("同步角色数据失败", character_id=character_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to sync character")


@router.post("/start", response_model=SyncResponse)
async def start_sync_scheduler(
    background_tasks: BackgroundTasks,
    sync_service: DataSyncService = Depends(get_sync_service),
    current_user: Dict[str, Any] = Depends(require_admin)
):
    """启动同步调度器"""
    try:
        # 在后台启动同步调度器
        background_tasks.add_task(sync_service.start_sync_scheduler)
        
        return SyncResponse(
            success=True,
            message="Sync scheduler started successfully"
        )
        
    except Exception as e:
        logger.error("启动同步调度器失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to start sync scheduler")


@router.post("/stop", response_model=SyncResponse)
async def stop_sync_scheduler(
    sync_service: DataSyncService = Depends(get_sync_service),
    current_user: Dict[str, Any] = Depends(require_admin)
):
    """停止同步调度器"""
    try:
        # 这里需要实现停止同步调度器的逻辑
        # 临时返回成功响应
        
        return SyncResponse(
            success=True,
            message="Sync scheduler stopped successfully"
        )
        
    except Exception as e:
        logger.error("停止同步调度器失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to stop sync scheduler")


@router.post("/manual", response_model=SyncResponse)
async def manual_sync(
    request: SyncRequest,
    background_tasks: BackgroundTasks,
    sync_service: DataSyncService = Depends(get_sync_service),
    current_user: Dict[str, Any] = Depends(require_admin)
):
    """手动触发同步"""
    try:
        # 解析同步类型
        sync_type = SyncType(request.sync_type)
        
        # 根据同步类型执行相应的同步逻辑
        if sync_type == SyncType.REALTIME:
            background_tasks.add_task(sync_service._sync_realtime_data)
        elif sync_type == SyncType.FREQUENT:
            background_tasks.add_task(sync_service._sync_frequent_data)
        elif sync_type == SyncType.REGULAR:
            background_tasks.add_task(sync_service._sync_regular_data)
        elif sync_type == SyncType.DAILY:
            background_tasks.add_task(sync_service._sync_daily_data)
        
        return SyncResponse(
            success=True,
            message=f"Manual {sync_type.value} sync triggered successfully"
        )
        
    except ValueError:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid sync type: {request.sync_type}"
        )
    except Exception as e:
        logger.error("手动同步失败", sync_type=request.sync_type, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to trigger manual sync")


@router.get("/history")
async def get_sync_history(
    limit: int = 100,
    offset: int = 0,
    sync_service: DataSyncService = Depends(get_sync_service),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取同步历史"""
    try:
        # 这里需要实现同步历史查询逻辑
        # 临时返回空历史
        
        return {
            "history": [],
            "total": 0,
            "limit": limit,
            "offset": offset
        }
        
    except Exception as e:
        logger.error("获取同步历史失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get sync history")


@router.get("/config")
async def get_sync_config(
    sync_service: DataSyncService = Depends(get_sync_service),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取同步配置"""
    try:
        return {
            "sync_intervals": {
                "realtime": sync_service.sync_intervals[SyncType.REALTIME],
                "frequent": sync_service.sync_intervals[SyncType.FREQUENT],
                "regular": sync_service.sync_intervals[SyncType.REGULAR],
                "daily": sync_service.sync_intervals[SyncType.DAILY]
            },
            "sync_features": sync_service.sync_features,
            "available_sync_types": [st.value for st in SyncType]
        }
        
    except Exception as e:
        logger.error("获取同步配置失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get sync config")


@router.post("/config")
async def update_sync_config(
    config: Dict[str, Any],
    sync_service: DataSyncService = Depends(get_sync_service),
    current_user: Dict[str, Any] = Depends(require_admin)
):
    """更新同步配置"""
    try:
        # 这里需要实现同步配置更新逻辑
        # 临时返回成功响应
        
        return {
            "success": True,
            "message": "Sync configuration updated successfully",
            "updated_config": config
        }
        
    except Exception as e:
        logger.error("更新同步配置失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to update sync config")
