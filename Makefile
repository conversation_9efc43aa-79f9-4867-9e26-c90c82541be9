# EVE Online Assistant - Makefile

.PHONY: help install dev-install test lint format clean run migrate docker-build docker-run

# 默认目标
help:
	@echo "EVE Online Assistant - 可用命令:"
	@echo ""
	@echo "  install      - 安装生产依赖"
	@echo "  dev-install  - 安装开发依赖"
	@echo "  test         - 运行测试"
	@echo "  test-cov     - 运行测试并生成覆盖率报告"
	@echo "  lint         - 运行代码检查"
	@echo "  format       - 格式化代码"
	@echo "  clean        - 清理临时文件"
	@echo "  run          - 启动开发服务器"
	@echo "  migrate      - 运行数据库迁移"
	@echo "  docker-build - 构建Docker镜像"
	@echo "  docker-run   - 运行Docker容器"
	@echo ""

# 安装依赖
install:
	pip install -r requirements.txt

dev-install:
	pip install -e ".[dev]"
	pre-commit install

# 测试
test:
	pytest

test-cov:
	pytest --cov=src --cov-report=html --cov-report=term-missing

# 代码质量
lint:
	flake8 src tests
	mypy src
	bandit -r src

format:
	black src tests
	isort src tests

# 清理
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf build/
	rm -rf dist/
	rm -rf .coverage
	rm -rf htmlcov/
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/

# 运行
run:
	uvicorn src.presentation.api.main:app --reload --host 0.0.0.0 --port 8000

# 数据库
migrate:
	alembic upgrade head

migrate-create:
	alembic revision --autogenerate -m "$(MSG)"

migrate-downgrade:
	alembic downgrade -1

# Docker
docker-build:
	docker build -t eve-assistant .

docker-run:
	docker-compose up -d

docker-stop:
	docker-compose down

# 开发工具
shell:
	python -c "from src.infrastructure.persistence.database import SessionLocal; from src.infrastructure.config import settings; print('EVE Assistant Shell'); print(f'Database: {settings.database_url}'); import IPython; IPython.start_ipython()"

# 数据库工具
db-reset:
	alembic downgrade base
	alembic upgrade head

db-seed:
	python scripts/seed_data.py

# 代码统计
stats:
	@echo "代码统计:"
	@find src -name "*.py" | xargs wc -l | tail -1
	@echo "测试统计:"
	@find tests -name "*.py" | xargs wc -l | tail -1
