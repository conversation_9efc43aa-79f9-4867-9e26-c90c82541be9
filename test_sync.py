#!/usr/bin/env python3
"""
数据同步功能测试脚本
"""
import requests
import time
import json

def test_sync():
    base_url = "http://localhost:8000"
    
    print("🔄 数据同步功能测试")
    print("=" * 50)
    
    # 等待服务器启动
    print("等待服务器启动...")
    time.sleep(3)
    
    # 测试1: 获取同步状态
    print("\n1. 测试获取同步状态...")
    try:
        response = requests.get(f"{base_url}/sync/status", timeout=5)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 同步状态端点正常")
            print(f"   响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        elif response.status_code == 401:
            print("⚠️  需要认证（预期行为）")
        else:
            print(f"❌ 同步状态失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 同步状态连接失败: {e}")
        return False
    
    # 测试2: 同步角色数据
    print("\n2. 测试同步角色数据...")
    try:
        test_character_id = 123456789
        response = requests.post(
            f"{base_url}/sync/character/{test_character_id}",
            params={"sync_types": ["character", "skills"]},
            timeout=10
        )
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 角色同步端点正常")
            print(f"   响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        elif response.status_code == 401:
            print("⚠️  需要认证（预期行为）")
        else:
            print(f"❌ 角色同步失败: HTTP {response.status_code}")
            print(f"   错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 角色同步连接失败: {e}")
    
    # 测试3: 获取同步历史
    print("\n3. 测试获取同步历史...")
    try:
        response = requests.get(f"{base_url}/sync/history?limit=5", timeout=5)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 同步历史端点正常")
            print(f"   响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        elif response.status_code == 401:
            print("⚠️  需要认证（预期行为）")
        else:
            print(f"❌ 同步历史失败: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ 同步历史连接失败: {e}")
    
    # 测试4: 获取同步类型
    print("\n4. 测试获取同步类型...")
    try:
        response = requests.get(f"{base_url}/sync/types", timeout=5)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 同步类型端点正常")
            print(f"   响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ 同步类型失败: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ 同步类型连接失败: {e}")
    
    # 测试5: 服务状态
    print("\n5. 测试同步服务状态...")
    try:
        response = requests.get(f"{base_url}/sync/service-status", timeout=5)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 服务状态端点正常")
            print(f"   响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ 服务状态失败: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ 服务状态连接失败: {e}")
    
    return True

if __name__ == "__main__":
    success = test_sync()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 数据同步功能测试通过！")
        print("✅ 数据同步路由已恢复")
    else:
        print("❌ 数据同步功能测试失败")
        print("⚠️  需要进一步调试")
    print("=" * 50)
