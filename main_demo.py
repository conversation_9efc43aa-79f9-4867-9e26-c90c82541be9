"""
EVE Assistant - 快速演示版本
简化的FastAPI应用，用于演示前后端集成
"""
import os
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from fastapi import FastAPI, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, EmailStr
import jwt
import uvicorn

# 配置
SECRET_KEY = "your-secret-key-here"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# 创建FastAPI应用
app = FastAPI(
    title="EVE Assistant API",
    description="EVE Online 管理助手 API",
    version="1.0.0"
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 安全配置
security = HTTPBearer()

# 数据模型
class LoginRequest(BaseModel):
    email: EmailStr
    password: str
    rememberMe: bool = False

class RegisterRequest(BaseModel):
    email: EmailStr
    username: str
    password: str
    confirmPassword: str
    agreeToTerms: bool

class User(BaseModel):
    id: str
    email: str
    username: str
    isActive: bool = True
    isVerified: bool = True
    createdAt: str
    lastLoginAt: Optional[str] = None
    characterCount: int = 0
    mainCharacterId: Optional[int] = None

class TokenResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    user: User

class ApiResponse(BaseModel):
    success: bool
    data: Optional[Any] = None
    message: Optional[str] = None
    error: Optional[str] = None

# 模拟数据库
fake_users_db = {
    "<EMAIL>": {
        "id": "user_123",
        "email": "<EMAIL>",
        "username": "demo_user",
        "hashed_password": "fake_hashed_password",
        "isActive": True,
        "isVerified": True,
        "createdAt": "2024-01-01T00:00:00Z",
        "lastLoginAt": None,
        "characterCount": 2,
        "mainCharacterId": 12345
    }
}

# 工具函数
def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        email: str = payload.get("sub")
        if email is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
        return email
    except jwt.PyJWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

def get_current_user(email: str = Depends(verify_token)):
    user = fake_users_db.get(email)
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    return user

# API路由
@app.get("/")
async def root():
    return {"message": "EVE Assistant API is running!"}

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.0.0"
    }

@app.post("/auth/login", response_model=ApiResponse)
async def login(login_data: LoginRequest):
    """用户登录"""
    user = fake_users_db.get(login_data.email)
    
    if not user or login_data.password != "demo123":
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid email or password"
        )
    
    # 创建访问令牌
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user["email"]}, expires_delta=access_token_expires
    )
    
    # 更新最后登录时间
    user["lastLoginAt"] = datetime.utcnow().isoformat()
    
    # 创建用户对象
    user_obj = User(
        id=user["id"],
        email=user["email"],
        username=user["username"],
        isActive=user["isActive"],
        isVerified=user["isVerified"],
        createdAt=user["createdAt"],
        lastLoginAt=user["lastLoginAt"],
        characterCount=user["characterCount"],
        mainCharacterId=user["mainCharacterId"]
    )
    
    return ApiResponse(
        success=True,
        data={
            "access_token": access_token,
            "refresh_token": "fake_refresh_token",
            "token_type": "bearer",
            "expires_in": ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            "user": user_obj.dict()
        },
        message="Login successful"
    )

@app.post("/auth/register", response_model=ApiResponse)
async def register(register_data: RegisterRequest):
    """用户注册"""
    if register_data.email in fake_users_db:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )
    
    if register_data.password != register_data.confirmPassword:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Passwords do not match"
        )
    
    if not register_data.agreeToTerms:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Must agree to terms and conditions"
        )
    
    # 创建新用户
    new_user = {
        "id": f"user_{len(fake_users_db) + 1}",
        "email": register_data.email,
        "username": register_data.username,
        "hashed_password": "fake_hashed_password",
        "isActive": True,
        "isVerified": False,  # 需要邮箱验证
        "createdAt": datetime.utcnow().isoformat(),
        "lastLoginAt": None,
        "characterCount": 0,
        "mainCharacterId": None
    }
    
    fake_users_db[register_data.email] = new_user
    
    return ApiResponse(
        success=True,
        message="Registration successful. Please check your email for verification."
    )

@app.get("/auth/me", response_model=ApiResponse)
async def get_current_user_info(current_user: dict = Depends(get_current_user)):
    """获取当前用户信息"""
    user_obj = User(
        id=current_user["id"],
        email=current_user["email"],
        username=current_user["username"],
        isActive=current_user["isActive"],
        isVerified=current_user["isVerified"],
        createdAt=current_user["createdAt"],
        lastLoginAt=current_user["lastLoginAt"],
        characterCount=current_user["characterCount"],
        mainCharacterId=current_user["mainCharacterId"]
    )
    
    return ApiResponse(
        success=True,
        data=user_obj.dict()
    )

@app.post("/auth/logout", response_model=ApiResponse)
async def logout(current_user: dict = Depends(get_current_user)):
    """用户登出"""
    return ApiResponse(
        success=True,
        message="Logout successful"
    )

@app.post("/auth/eve/login", response_model=ApiResponse)
async def initiate_eve_login():
    """发起EVE SSO登录"""
    # 模拟EVE SSO登录URL
    login_url = "https://login.eveonline.com/v2/oauth/authorize?response_type=code&redirect_uri=http://localhost:3000/auth/callback&client_id=demo&scope=esi-characters.read_characters.v1&state=demo_state"
    
    return ApiResponse(
        success=True,
        data={
            "login_url": login_url,
            "state": "demo_state",
            "expires_in": 300,
            "scopes": ["esi-characters.read_characters.v1"]
        }
    )

@app.post("/auth/eve/callback", response_model=ApiResponse)
async def complete_eve_login():
    """完成EVE SSO登录"""
    return ApiResponse(
        success=True,
        data={
            "character_id": 12345,
            "character_name": "Demo Character",
            "scopes": ["esi-characters.read_characters.v1"]
        },
        message="EVE character linked successfully"
    )

# 模拟数据端点
@app.get("/dashboard/stats", response_model=ApiResponse)
async def get_dashboard_stats(current_user: dict = Depends(get_current_user)):
    """获取仪表板统计数据"""
    stats = {
        "totalAssetValue": 1500000000,
        "assetValueTrend": 5.2,
        "totalSkillPoints": 45000000,
        "activeOrders": 12,
        "industryJobs": 3,
        "characterCount": current_user["characterCount"],
        "lastUpdateTime": datetime.utcnow().isoformat()
    }
    
    return ApiResponse(success=True, data=stats)

@app.get("/characters", response_model=ApiResponse)
async def get_characters(current_user: dict = Depends(get_current_user)):
    """获取角色列表"""
    characters = [
        {
            "id": 12345,
            "name": "Demo Character",
            "corporation": "Demo Corp",
            "alliance": "Demo Alliance",
            "totalSkillPoints": 25000000,
            "isOnline": True,
            "location": "Jita IV - Moon 4 - Caldari Navy Assembly Plant",
            "ship": "Rifter",
            "portraitUrl": "https://images.evetech.net/characters/12345/portrait?size=256",
            "securityStatus": 2.5,
            "wallet": 500000000,
            "unallocatedSkillPoints": 150000
        },
        {
            "id": 67890,
            "name": "Alt Character",
            "corporation": "NPC Corp",
            "alliance": None,
            "totalSkillPoints": 20000000,
            "isOnline": False,
            "location": "Amarr VIII (Oris) - Emperor Family Academy",
            "ship": "Punisher",
            "portraitUrl": "https://images.evetech.net/characters/67890/portrait?size=256",
            "securityStatus": 0.8,
            "wallet": 100000000,
            "unallocatedSkillPoints": 0
        }
    ]
    
    return ApiResponse(success=True, data=characters)

if __name__ == "__main__":
    print("🚀 启动EVE Assistant演示服务器...")
    print("📱 前端地址: http://localhost:3000")
    print("🔧 API地址: http://localhost:8000")
    print("📖 API文档: http://localhost:8000/docs")
    print("\n演示账户:")
    print("📧 邮箱: <EMAIL>")
    print("🔑 密码: demo123")
    
    uvicorn.run(
        "main_demo:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
