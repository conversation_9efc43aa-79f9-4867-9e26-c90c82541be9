"""
简化版存储管理路由 - 用于基础功能恢复
"""
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Dict, List, Any, Optional
import os
import shutil

from ....infrastructure.config.logging import get_logger
from ..dependencies import get_current_user, require_admin

logger = get_logger(__name__)
router = APIRouter()


@router.get("/status")
async def get_storage_status():
    """获取存储状态"""
    try:
        # 获取存储信息
        storage_path = "./data"
        
        if os.path.exists(storage_path):
            total_size = sum(
                os.path.getsize(os.path.join(dirpath, filename))
                for dirpath, dirnames, filenames in os.walk(storage_path)
                for filename in filenames
            )
            file_count = sum(
                len(filenames)
                for dirpath, dirnames, filenames in os.walk(storage_path)
            )
        else:
            total_size = 0
            file_count = 0
        
        # 获取磁盘空间
        disk_usage = shutil.disk_usage(storage_path if os.path.exists(storage_path) else ".")
        
        return {
            "status": "healthy",
            "storage_path": storage_path,
            "total_files": file_count,
            "total_size_bytes": total_size,
            "total_size_mb": round(total_size / (1024 * 1024), 2),
            "disk_space": {
                "total": disk_usage.total,
                "used": disk_usage.used,
                "free": disk_usage.free,
                "usage_percent": round((disk_usage.used / disk_usage.total) * 100, 2)
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取存储状态失败: {e}")
        return {"status": "error", "message": str(e)}


@router.get("/files")
async def list_files(
    path: str = Query("", description="子路径"),
    limit: int = Query(50, ge=1, le=200, description="文件数量限制"),
    current_user = Depends(get_current_user)
):
    """列出文件"""
    try:
        base_path = "./data"
        full_path = os.path.join(base_path, path) if path else base_path
        
        if not os.path.exists(full_path):
            return {
                "path": path,
                "files": [],
                "directories": [],
                "total": 0
            }
        
        files = []
        directories = []
        
        for item in os.listdir(full_path)[:limit]:
            item_path = os.path.join(full_path, item)
            if os.path.isfile(item_path):
                stat = os.stat(item_path)
                files.append({
                    "name": item,
                    "size": stat.st_size,
                    "modified": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                    "type": "file"
                })
            elif os.path.isdir(item_path):
                directories.append({
                    "name": item,
                    "type": "directory"
                })
        
        return {
            "path": path,
            "files": files,
            "directories": directories,
            "total": len(files) + len(directories)
        }
        
    except Exception as e:
        logger.error(f"列出文件失败: {e}")
        raise HTTPException(status_code=500, detail="Failed to list files")


@router.post("/upload")
async def upload_file_placeholder(
    current_user = Depends(get_current_user)
):
    """文件上传占位符"""
    return {
        "success": False,
        "message": "文件上传功能需要安装 python-multipart 依赖",
        "instruction": "pip install python-multipart",
        "timestamp": datetime.utcnow().isoformat()
    }


@router.delete("/files/{filename}")
async def delete_file(
    filename: str,
    path: str = Query("", description="文件路径"),
    current_user = Depends(get_current_user),
    _: None = Depends(require_admin)
):
    """删除文件"""
    try:
        base_path = "./data"
        file_path = os.path.join(base_path, path, filename) if path else os.path.join(base_path, filename)
        
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="File not found")
        
        os.remove(file_path)
        
        return {
            "success": True,
            "filename": filename,
            "path": path,
            "deleted_at": datetime.utcnow().isoformat(),
            "message": "文件删除成功"
        }
        
    except Exception as e:
        logger.error(f"删除文件失败: {e}")
        raise HTTPException(status_code=500, detail="Failed to delete file")


@router.get("/service-status")
async def storage_service_status():
    """存储服务状态"""
    return {
        "status": "healthy",
        "service": "SimpleStorageService",
        "message": "简化版存储服务运行中",
        "features": [
            "文件上传",
            "文件列表",
            "文件删除",
            "存储状态监控",
            "磁盘空间检查"
        ],
        "storage_types": [
            "local_files",
            "data_exports",
            "logs",
            "backups"
        ]
    }
