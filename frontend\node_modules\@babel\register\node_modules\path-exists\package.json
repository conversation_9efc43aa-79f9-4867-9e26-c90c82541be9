{"name": "path-exists", "version": "3.0.0", "description": "Check if a path exists", "license": "MIT", "repository": "sindresorhus/path-exists", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["path", "exists", "exist", "file", "filepath", "fs", "filesystem", "file-system", "access", "stat"], "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"esnext": true}}