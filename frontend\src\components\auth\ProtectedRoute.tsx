import React, { ReactNode } from 'react'
import { Navigate, useLocation } from 'react-router-dom'

import { useAuth } from '@/hooks/useAuth'
import { LoadingScreen } from '@/components/ui/LoadingScreen'

interface ProtectedRouteProps {
  children: ReactNode
  requireVerification?: boolean
  requireCharacters?: boolean
  fallback?: ReactNode
}

export function ProtectedRoute({ 
  children, 
  requireVerification = false,
  requireCharacters = false,
  fallback 
}: ProtectedRouteProps) {
  const { isAuthenticated, isLoading, user } = useAuth()
  const location = useLocation()

  // 加载中显示加载屏幕
  if (isLoading) {
    return <LoadingScreen tip="验证身份中..." />
  }

  // 未认证重定向到登录页
  if (!isAuthenticated) {
    return (
      <Navigate 
        to="/auth/login" 
        state={{ from: location }} 
        replace 
      />
    )
  }

  // 需要邮箱验证但未验证
  if (requireVerification && !user?.isVerified) {
    if (fallback) {
      return <>{fallback}</>
    }
    return (
      <Navigate 
        to="/auth/verify-email" 
        state={{ from: location }} 
        replace 
      />
    )
  }

  // 需要角色但没有角色
  if (requireCharacters && (!user?.characterCount || user.characterCount === 0)) {
    if (fallback) {
      return <>{fallback}</>
    }
    return (
      <Navigate 
        to="/characters/bind" 
        state={{ from: location }} 
        replace 
      />
    )
  }

  return <>{children}</>
}

// 权限保护路由
interface PermissionProtectedRouteProps {
  children: ReactNode
  permissions: string[]
  requireAll?: boolean
  fallback?: ReactNode
}

export function PermissionProtectedRoute({
  children,
  permissions,
  requireAll = true,
  fallback
}: PermissionProtectedRouteProps) {
  const { user } = useAuth()

  // 这里需要实现权限检查逻辑
  // 暂时简化实现
  const hasPermission = !!user

  if (!hasPermission) {
    if (fallback) {
      return <>{fallback}</>
    }
    
    return (
      <Navigate 
        to="/403" 
        replace 
      />
    )
  }

  return <>{children}</>
}

// 角色保护路由
interface CharacterProtectedRouteProps {
  children: ReactNode
  characterId?: number
  fallback?: ReactNode
}

export function CharacterProtectedRoute({
  children,
  characterId,
  fallback
}: CharacterProtectedRouteProps) {
  const { user } = useAuth()

  // 检查是否有权限访问指定角色
  const hasCharacterAccess = () => {
    if (!characterId) return true
    if (!user) return false
    
    // 这里需要实现角色访问权限检查
    // 暂时简化实现
    return true
  }

  if (!hasCharacterAccess()) {
    if (fallback) {
      return <>{fallback}</>
    }
    
    return (
      <Navigate 
        to="/403" 
        replace 
      />
    )
  }

  return <>{children}</>
}

// 管理员保护路由
interface AdminProtectedRouteProps {
  children: ReactNode
  fallback?: ReactNode
}

export function AdminProtectedRoute({
  children,
  fallback
}: AdminProtectedRouteProps) {
  const { user } = useAuth()

  // 检查是否是管理员
  const isAdmin = () => {
    // 这里需要实现管理员权限检查
    // 暂时简化实现
    return !!user
  }

  if (!isAdmin()) {
    if (fallback) {
      return <>{fallback}</>
    }
    
    return (
      <Navigate 
        to="/403" 
        replace 
      />
    )
  }

  return <>{children}</>
}

// 游客路由（仅未登录用户可访问）
interface GuestRouteProps {
  children: ReactNode
  redirectTo?: string
}

export function GuestRoute({ 
  children, 
  redirectTo = '/dashboard' 
}: GuestRouteProps) {
  const { isAuthenticated, isLoading } = useAuth()

  // 加载中显示加载屏幕
  if (isLoading) {
    return <LoadingScreen tip="检查登录状态..." />
  }

  // 已登录重定向到指定页面
  if (isAuthenticated) {
    return <Navigate to={redirectTo} replace />
  }

  return <>{children}</>
}
