"""
RBAC权限管理应用服务
"""
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

from ...domain.rbac.aggregates import Role, Permission, UserRole
from ...domain.rbac.services import RBACDomainService, PermissionChecker
from ...domain.rbac.value_objects import (
    RoleId, PermissionId, RoleName, PermissionName, PermissionScope,
    PermissionContext, ResourceType, ActionType
)
from ...domain.rbac.exceptions import (
    RoleNotFoundError, PermissionNotFoundError, InsufficientPermissionError
)
from ...domain.shared.value_objects import UserId
from ...infrastructure.config.logging import get_logger

logger = get_logger(__name__)


class RBACApplicationService:
    """RBAC权限管理应用服务"""
    
    def __init__(self,
                 rbac_domain_service: RBACDomainService,
                 permission_checker: PermissionChecker,
                 unit_of_work,
                 event_bus):
        self._rbac_service = rbac_domain_service
        self._permission_checker = permission_checker
        self._unit_of_work = unit_of_work
        self._event_bus = event_bus
    
    async def initialize_system(self) -> Dict[str, Any]:
        """初始化RBAC系统"""
        async with self._unit_of_work:
            try:
                self._rbac_service.initialize_system_roles_and_permissions()
                
                logger.info("RBAC系统初始化完成")
                
                return {
                    "success": True,
                    "message": "RBAC system initialized successfully"
                }
                
            except Exception as e:
                logger.error("RBAC系统初始化失败", error=str(e))
                raise
    
    async def create_role(self, admin_user_id: UserId, name: str, 
                         description: str) -> Dict[str, Any]:
        """创建角色"""
        async with self._unit_of_work:
            # 检查管理员权限
            admin_context = PermissionContext(
                user_id=admin_user_id,
                resource_type="system",
                action_type="admin"
            )
            self._permission_checker.require_permission(admin_user_id, admin_context)
            
            # 创建角色
            role = Role.create(
                name=RoleName(name),
                description=description
            )
            
            await self._rbac_service.role_repository.save(role)
            
            # 发布领域事件
            events = role.get_domain_events()
            for event in events:
                await self._event_bus.publish(event)
            
            logger.info(
                "角色创建成功",
                role_id=str(role.role_id),
                name=name,
                created_by=str(admin_user_id)
            )
            
            return {
                "success": True,
                "role_id": str(role.role_id),
                "name": name,
                "description": description
            }
    
    async def create_permission(self, admin_user_id: UserId, name: str,
                               description: str, resource_type: str,
                               action_type: str) -> Dict[str, Any]:
        """创建权限"""
        async with self._unit_of_work:
            # 检查管理员权限
            admin_context = PermissionContext(
                user_id=admin_user_id,
                resource_type="system",
                action_type="admin"
            )
            self._permission_checker.require_permission(admin_user_id, admin_context)
            
            # 创建权限范围
            scope = PermissionScope(
                resource_type=ResourceType(resource_type),
                action_type=ActionType(action_type)
            )
            
            # 创建权限
            permission = Permission.create(
                name=PermissionName(name),
                description=description,
                scope=scope
            )
            
            await self._rbac_service.permission_repository.save(permission)
            
            # 发布领域事件
            events = permission.get_domain_events()
            for event in events:
                await self._event_bus.publish(event)
            
            logger.info(
                "权限创建成功",
                permission_id=str(permission.permission_id),
                name=name,
                created_by=str(admin_user_id)
            )
            
            return {
                "success": True,
                "permission_id": str(permission.permission_id),
                "name": name,
                "description": description,
                "resource_type": resource_type,
                "action_type": action_type
            }
    
    async def assign_role_to_user(self, admin_user_id: UserId, user_id: UserId,
                                 role_id: str, expires_in_days: Optional[int] = None) -> Dict[str, Any]:
        """为用户分配角色"""
        async with self._unit_of_work:
            # 检查管理员权限
            admin_context = PermissionContext(
                user_id=admin_user_id,
                resource_type="user",
                action_type="manage"
            )
            self._permission_checker.require_permission(admin_user_id, admin_context)
            
            # 验证角色存在
            role = await self._rbac_service.role_repository.get_by_id(RoleId(role_id))
            if not role:
                raise RoleNotFoundError(f"Role {role_id} not found")
            
            # 获取或创建用户角色
            user_role = await self._rbac_service.user_role_repository.get_by_user_id(user_id)
            if not user_role:
                user_role = UserRole.create(user_id)
            
            # 计算过期时间
            expires_at = None
            if expires_in_days:
                expires_at = datetime.utcnow() + timedelta(days=expires_in_days)
            
            # 分配角色
            user_role.assign_role(role.role_id, admin_user_id, expires_at)
            
            await self._rbac_service.user_role_repository.save(user_role)
            
            # 发布领域事件
            events = user_role.get_domain_events()
            for event in events:
                await self._event_bus.publish(event)
            
            logger.info(
                "用户角色分配成功",
                user_id=str(user_id),
                role_id=role_id,
                assigned_by=str(admin_user_id),
                expires_at=expires_at.isoformat() if expires_at else None
            )
            
            return {
                "success": True,
                "user_id": str(user_id),
                "role_id": role_id,
                "role_name": role.name.value,
                "expires_at": expires_at.isoformat() if expires_at else None
            }
    
    async def revoke_role_from_user(self, admin_user_id: UserId, user_id: UserId,
                                   role_id: str) -> Dict[str, Any]:
        """撤销用户角色"""
        async with self._unit_of_work:
            # 检查管理员权限
            admin_context = PermissionContext(
                user_id=admin_user_id,
                resource_type="user",
                action_type="manage"
            )
            self._permission_checker.require_permission(admin_user_id, admin_context)
            
            # 获取用户角色
            user_role = await self._rbac_service.user_role_repository.get_by_user_id(user_id)
            if not user_role:
                return {"success": False, "message": "User role not found"}
            
            # 撤销角色
            user_role.revoke_role(RoleId(role_id), admin_user_id)
            
            await self._rbac_service.user_role_repository.save(user_role)
            
            # 发布领域事件
            events = user_role.get_domain_events()
            for event in events:
                await self._event_bus.publish(event)
            
            logger.info(
                "用户角色撤销成功",
                user_id=str(user_id),
                role_id=role_id,
                revoked_by=str(admin_user_id)
            )
            
            return {
                "success": True,
                "user_id": str(user_id),
                "role_id": role_id,
                "message": "Role revoked successfully"
            }
    
    async def grant_permission_to_role(self, admin_user_id: UserId, role_id: str,
                                      permission_id: str) -> Dict[str, Any]:
        """为角色授予权限"""
        async with self._unit_of_work:
            # 检查管理员权限
            admin_context = PermissionContext(
                user_id=admin_user_id,
                resource_type="system",
                action_type="admin"
            )
            self._permission_checker.require_permission(admin_user_id, admin_context)
            
            # 获取角色和权限
            role = await self._rbac_service.role_repository.get_by_id(RoleId(role_id))
            if not role:
                raise RoleNotFoundError(f"Role {role_id} not found")
            
            permission = await self._rbac_service.permission_repository.get_by_id(PermissionId(permission_id))
            if not permission:
                raise PermissionNotFoundError(f"Permission {permission_id} not found")
            
            # 授予权限
            role.grant_permission(permission.permission_id)
            
            await self._rbac_service.role_repository.save(role)
            
            # 发布领域事件
            events = role.get_domain_events()
            for event in events:
                await self._event_bus.publish(event)
            
            logger.info(
                "角色权限授予成功",
                role_id=role_id,
                permission_id=permission_id,
                granted_by=str(admin_user_id)
            )
            
            return {
                "success": True,
                "role_id": role_id,
                "permission_id": permission_id,
                "message": "Permission granted successfully"
            }
    
    async def check_user_permission(self, user_id: UserId, resource_type: str,
                                   action_type: str, resource_id: str = None,
                                   character_id: int = None) -> Dict[str, Any]:
        """检查用户权限"""
        context = PermissionContext(
            user_id=user_id,
            resource_type=resource_type,
            action_type=action_type,
            resource_id=resource_id,
            character_id=character_id
        )
        
        has_permission = self._permission_checker.check_permission(user_id, context)
        
        return {
            "user_id": str(user_id),
            "resource_type": resource_type,
            "action_type": action_type,
            "resource_id": resource_id,
            "character_id": character_id,
            "has_permission": has_permission
        }
    
    async def get_user_roles(self, user_id: UserId) -> Dict[str, Any]:
        """获取用户角色"""
        user_role = await self._rbac_service.user_role_repository.get_by_user_id(user_id)
        if not user_role:
            return {
                "user_id": str(user_id),
                "roles": []
            }
        
        # 清理过期角色
        user_role.cleanup_expired_assignments()
        
        active_roles = user_role.get_active_roles()
        roles_info = []
        
        for role_id in active_roles:
            role = await self._rbac_service.role_repository.get_by_id(role_id)
            if role:
                assignment = user_role.get_role_assignment(role_id)
                roles_info.append({
                    "role_id": str(role_id),
                    "name": role.name.value,
                    "description": role.description,
                    "assigned_at": assignment.assigned_at if assignment else None,
                    "expires_at": assignment.expires_at if assignment else None
                })
        
        return {
            "user_id": str(user_id),
            "roles": roles_info
        }
    
    async def get_user_permissions(self, user_id: UserId) -> Dict[str, Any]:
        """获取用户权限"""
        effective_permissions = self._rbac_service.get_user_effective_permissions(user_id)
        
        permissions_info = []
        for permission_id in effective_permissions:
            permission = await self._rbac_service.permission_repository.get_by_id(permission_id)
            if permission:
                permissions_info.append({
                    "permission_id": str(permission_id),
                    "name": permission.name.value,
                    "description": permission.description,
                    "resource_type": permission.scope.resource_type.value,
                    "action_type": permission.scope.action_type.value
                })
        
        return {
            "user_id": str(user_id),
            "permissions": permissions_info
        }
    
    async def list_roles(self, admin_user_id: UserId) -> Dict[str, Any]:
        """列出所有角色"""
        # 检查管理员权限
        admin_context = PermissionContext(
            user_id=admin_user_id,
            resource_type="system",
            action_type="read"
        )
        self._permission_checker.require_permission(admin_user_id, admin_context)
        
        roles = await self._rbac_service.role_repository.get_all_active()
        
        roles_info = []
        for role in roles:
            roles_info.append({
                "role_id": str(role.role_id),
                "name": role.name.value,
                "description": role.description,
                "is_system_role": role.is_system_role,
                "permission_count": len(role.permissions),
                "created_at": role.created_at.isoformat()
            })
        
        return {
            "roles": roles_info,
            "total": len(roles_info)
        }
    
    async def list_permissions(self, admin_user_id: UserId) -> Dict[str, Any]:
        """列出所有权限"""
        # 检查管理员权限
        admin_context = PermissionContext(
            user_id=admin_user_id,
            resource_type="system",
            action_type="read"
        )
        self._permission_checker.require_permission(admin_user_id, admin_context)
        
        permissions = await self._rbac_service.permission_repository.get_all_active()
        
        permissions_info = []
        for permission in permissions:
            permissions_info.append({
                "permission_id": str(permission.permission_id),
                "name": permission.name.value,
                "description": permission.description,
                "resource_type": permission.scope.resource_type.value,
                "action_type": permission.scope.action_type.value,
                "created_at": permission.created_at.isoformat()
            })
        
        return {
            "permissions": permissions_info,
            "total": len(permissions_info)
        }
