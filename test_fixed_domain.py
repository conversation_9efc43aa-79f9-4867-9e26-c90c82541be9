#!/usr/bin/env python3
"""
测试固定域名配置
"""
import os
import subprocess
import time
from dotenv import load_dotenv

def test_fixed_domain_config():
    """测试固定域名配置"""
    print("🔧 测试固定域名配置")
    print("=" * 50)
    
    # 加载环境变量
    load_dotenv()
    
    fixed_domain = os.getenv('NGROK_FIXED_DOMAIN')
    callback_url = os.getenv('EVE_SSO_CALLBACK_URL')
    
    print(f"📋 配置检查:")
    print(f"   固定域名: {fixed_domain}")
    print(f"   回调URL: {callback_url}")
    
    # 验证配置一致性
    if fixed_domain and callback_url:
        if fixed_domain in callback_url:
            print("   ✅ 配置一致性检查通过")
        else:
            print("   ❌ 配置不一致！")
            print(f"      固定域名: {fixed_domain}")
            print(f"      回调URL中的域名: {callback_url}")
            return False
    else:
        print("   ⚠️  配置不完整")
        return False
    
    print()
    
    # 测试ngrok命令构建
    print("🔗 测试ngrok命令构建:")
    
    if fixed_domain:
        ngrok_cmd = ['ngrok', 'http', '8000', '--domain', fixed_domain]
        print(f"   命令: {' '.join(ngrok_cmd)}")
        print("   ✅ 使用固定域名命令")
    else:
        ngrok_cmd = ['ngrok', 'http', '8000']
        print(f"   命令: {' '.join(ngrok_cmd)}")
        print("   ⚠️  使用随机域名命令")
    
    print()
    
    # 测试ngrok配置文件
    print("📄 测试ngrok配置文件:")
    
    try:
        result = subprocess.run(['ngrok', 'config', 'check'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("   ✅ ngrok配置文件有效")
            print(f"   📋 输出: {result.stdout.strip()}")
        else:
            print("   ❌ ngrok配置文件无效")
            print(f"   📋 错误: {result.stderr.strip()}")
            return False
            
    except Exception as e:
        print(f"   ❌ 检查ngrok配置失败: {e}")
        return False
    
    print()
    
    # 建议下一步操作
    print("🎯 下一步操作建议:")
    print("1. 确认域名在ngrok仪表板中已创建:")
    print("   https://dashboard.ngrok.com/cloud-edge/domains")
    print()
    print("2. 更新EVE开发者门户回调URL:")
    print("   https://developers.eveonline.com/")
    print(f"   回调URL: {callback_url}")
    print()
    print("3. 运行production-start.py测试:")
    print("   python scripts/deployment/production-start.py")
    
    return True

def main():
    """主函数"""
    if test_fixed_domain_config():
        print("\n🎉 固定域名配置检查完成！")
        return 0
    else:
        print("\n❌ 固定域名配置有问题，请检查")
        return 1

if __name__ == "__main__":
    exit(main())
