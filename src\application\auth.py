"""
认证应用服务
"""
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, Optional, List
import secrets
import hashlib

from ..domain.shared.value_objects import CharacterId
from ..domain.shared.exceptions import AuthenticationError, AuthorizationError
from ..infrastructure.esi import EVESSOClient, ESI_SCOPES
from ..infrastructure.config import settings
from ..infrastructure.config.logging import get_logger

logger = get_logger(__name__)


class AuthenticationService:
    """认证应用服务"""
    
    def __init__(self, 
                 user_repository,
                 token_repository,
                 character_repository,
                 sso_client: EVESSOClient):
        self.user_repository = user_repository
        self.token_repository = token_repository
        self.character_repository = character_repository
        self.sso_client = sso_client
        
        # 认证状态缓存
        self._auth_states: Dict[str, Dict[str, Any]] = {}
    
    async def initiate_eve_login(self, 
                                scopes: List[str] = None,
                                redirect_url: str = None) -> Dict[str, Any]:
        """发起EVE SSO登录"""
        try:
            if scopes is None:
                # 默认权限范围
                scopes = [
                    ESI_SCOPES["character_info"],
                    ESI_SCOPES["character_location"],
                    ESI_SCOPES["character_online"],
                    ESI_SCOPES["character_skills"],
                    ESI_SCOPES["character_wallet"],
                    ESI_SCOPES["character_assets"]
                ]
            
            # 生成登录URL和状态
            login_url, state = self.sso_client.generate_login_url(scopes)
            
            # 缓存认证状态
            self._auth_states[state] = {
                "scopes": scopes,
                "redirect_url": redirect_url,
                "created_at": datetime.utcnow(),
                "expires_at": datetime.utcnow() + timedelta(minutes=10)
            }
            
            logger.info("EVE SSO登录发起成功", state=state, scopes=scopes)
            
            return {
                "login_url": login_url,
                "state": state,
                "scopes": scopes
            }
            
        except Exception as e:
            logger.error("发起EVE SSO登录失败", error=str(e))
            raise AuthenticationError(f"Failed to initiate EVE login: {str(e)}")
    
    async def handle_eve_callback(self, 
                                 authorization_code: str, 
                                 state: str) -> Dict[str, Any]:
        """处理EVE SSO回调"""
        try:
            # 验证状态
            if state not in self._auth_states:
                raise AuthenticationError("Invalid or expired authentication state")
            
            auth_state = self._auth_states[state]
            
            # 检查状态是否过期
            if datetime.utcnow() > auth_state["expires_at"]:
                del self._auth_states[state]
                raise AuthenticationError("Authentication state expired")
            
            # 交换授权码获取令牌
            token_data = await self.sso_client.exchange_code_for_tokens(
                authorization_code, state
            )
            
            # 获取或创建角色
            character_id = int(token_data["character_id"])
            character = await self._get_or_create_character(character_id, token_data)
            
            # 保存令牌
            token = await self._save_token(character_id, token_data)
            
            # 获取或创建用户
            user = await self._get_or_create_user(character_id, token_data["character_name"])
            
            # 生成会话令牌
            session_token = self._generate_session_token(user.id, character_id)
            
            # 清理认证状态
            del self._auth_states[state]
            
            logger.info("EVE SSO认证成功", 
                       character_id=character_id, 
                       character_name=token_data["character_name"])
            
            return {
                "success": True,
                "user_id": user.id,
                "character_id": character_id,
                "character_name": token_data["character_name"],
                "session_token": session_token,
                "scopes": token_data["scopes"],
                "redirect_url": auth_state.get("redirect_url")
            }
            
        except Exception as e:
            logger.error("处理EVE SSO回调失败", error=str(e))
            raise AuthenticationError(f"Failed to handle EVE callback: {str(e)}")
    
    async def refresh_character_token(self, character_id: int) -> Dict[str, Any]:
        """刷新角色令牌"""
        try:
            # 获取现有令牌
            token = await self.token_repository.get_active_token_by_character(character_id)
            if not token:
                raise AuthenticationError("No active token found for character")
            
            # 刷新令牌
            refreshed_data = await self.sso_client.refresh_access_token(token.refresh_token)
            
            # 更新令牌
            token.access_token = refreshed_data["access_token"]
            token.refresh_token = refreshed_data.get("refresh_token", token.refresh_token)
            token.expires_at = refreshed_data["expires_at"]
            token.last_refresh_at = datetime.utcnow()
            token.refresh_count += 1
            token.refresh_error_count = 0
            token.refresh_error_message = None
            
            await self.token_repository.save(token)
            
            logger.info("角色令牌刷新成功", character_id=character_id)
            
            return {
                "success": True,
                "character_id": character_id,
                "expires_at": token.expires_at.isoformat()
            }
            
        except Exception as e:
            logger.error("刷新角色令牌失败", character_id=character_id, error=str(e))
            
            # 更新错误信息
            if 'token' in locals():
                token.refresh_error_count += 1
                token.refresh_error_message = str(e)
                await self.token_repository.save(token)
            
            raise AuthenticationError(f"Failed to refresh token: {str(e)}")
    
    async def revoke_character_token(self, character_id: int) -> Dict[str, Any]:
        """撤销角色令牌"""
        try:
            # 获取令牌
            token = await self.token_repository.get_active_token_by_character(character_id)
            if not token:
                return {"success": True, "message": "No active token found"}
            
            # 撤销令牌
            await self.sso_client.revoke_token(token.refresh_token, "refresh_token")
            
            # 标记令牌为非活跃
            token.is_active = False
            await self.token_repository.save(token)
            
            logger.info("角色令牌撤销成功", character_id=character_id)
            
            return {
                "success": True,
                "character_id": character_id,
                "message": "Token revoked successfully"
            }
            
        except Exception as e:
            logger.error("撤销角色令牌失败", character_id=character_id, error=str(e))
            raise AuthenticationError(f"Failed to revoke token: {str(e)}")
    
    async def validate_session_token(self, session_token: str) -> Optional[Dict[str, Any]]:
        """验证会话令牌"""
        try:
            # 这里需要实现JWT令牌验证逻辑
            # 解析令牌，验证签名，检查过期时间等
            
            # 临时实现：简单的令牌格式验证
            if not session_token or len(session_token) < 32:
                return None
            
            # 实际实现中应该解析JWT令牌
            # payload = jwt.decode(session_token, settings.secret_key, algorithms=["HS256"])
            # user_id = payload.get("user_id")
            # character_id = payload.get("character_id")
            
            # 临时返回示例数据
            return {
                "valid": True,
                "user_id": 1,
                "character_id": 123456789
            }
            
        except Exception as e:
            logger.error("验证会话令牌失败", error=str(e))
            return None
    
    async def get_character_permissions(self, character_id: int) -> List[str]:
        """获取角色权限"""
        try:
            token = await self.token_repository.get_active_token_by_character(character_id)
            if not token:
                return []
            
            # 解析权限范围
            import json
            scopes = json.loads(token.scopes) if token.scopes else []
            
            return scopes
            
        except Exception as e:
            logger.error("获取角色权限失败", character_id=character_id, error=str(e))
            return []
    
    async def check_character_permission(self, character_id: int, required_scope: str) -> bool:
        """检查角色权限"""
        try:
            scopes = await self.get_character_permissions(character_id)
            return required_scope in scopes
            
        except Exception as e:
            logger.error("检查角色权限失败", 
                        character_id=character_id, 
                        required_scope=required_scope, 
                        error=str(e))
            return False
    
    async def get_user_characters(self, user_id: int) -> List[Dict[str, Any]]:
        """获取用户的角色列表"""
        try:
            tokens = await self.token_repository.get_tokens_by_user(user_id)
            
            characters = []
            for token in tokens:
                character = await self.character_repository.get_by_id(
                    CharacterId(token.character_id)
                )
                
                if character:
                    characters.append({
                        "character_id": character.character_id.value,
                        "character_name": character.name,
                        "corporation_id": character.corporation_id.value,
                        "alliance_id": character.alliance_id.value if character.alliance_id else None,
                        "is_online": character.is_online,
                        "token_expires_at": token.expires_at.isoformat(),
                        "token_active": token.is_active
                    })
            
            return characters
            
        except Exception as e:
            logger.error("获取用户角色列表失败", user_id=user_id, error=str(e))
            raise
    
    async def _get_or_create_character(self, character_id: int, token_data: Dict[str, Any]):
        """获取或创建角色"""
        char_id = CharacterId(character_id)
        character = await self.character_repository.get_by_id(char_id)
        
        if not character:
            # 创建新角色 - 这里需要从ESI获取完整信息
            # 临时创建基本角色信息
            from ..domain.character.entities import Character
            from ..domain.shared.value_objects import CorporationId
            
            character = Character(
                character_id=char_id,
                name=token_data["character_name"],
                corporation_id=CorporationId(1),  # 临时值
                birthday=datetime.utcnow(),
                race_id=1,
                bloodline_id=1,
                gender="Male"
            )
            
            await self.character_repository.save(character)
        
        return character
    
    async def _save_token(self, character_id: int, token_data: Dict[str, Any]):
        """保存令牌"""
        from ..infrastructure.persistence.models.auth import TokenModel
        import json
        
        # 检查是否已有令牌
        existing_token = await self.token_repository.get_active_token_by_character(character_id)
        
        if existing_token:
            # 更新现有令牌
            existing_token.access_token = token_data["access_token"]
            existing_token.refresh_token = token_data["refresh_token"]
            existing_token.expires_at = token_data["expires_at"]
            existing_token.scopes = json.dumps(token_data["scopes"])
            existing_token.last_used_at = datetime.utcnow()
            token = existing_token
        else:
            # 创建新令牌
            token = TokenModel(
                character_id=character_id,
                access_token=token_data["access_token"],
                refresh_token=token_data["refresh_token"],
                token_type=token_data.get("token_type", "Bearer"),
                scopes=json.dumps(token_data["scopes"]),
                expires_at=token_data["expires_at"],
                is_active=True
            )
        
        await self.token_repository.save(token)
        return token
    
    async def _get_or_create_user(self, character_id: int, character_name: str):
        """获取或创建用户"""
        from ..infrastructure.persistence.models.auth import UserModel
        
        # 查找现有用户
        user = await self.user_repository.get_by_main_character(character_id)
        
        if not user:
            # 创建新用户
            user = UserModel(
                username=f"eve_{character_id}",
                main_character_id=character_id,
                is_active=True,
                last_login=datetime.utcnow(),
                login_count=1
            )
        else:
            # 更新登录信息
            user.last_login = datetime.utcnow()
            user.login_count += 1
        
        await self.user_repository.save(user)
        return user
    
    def _generate_session_token(self, user_id: int, character_id: int) -> str:
        """生成会话令牌"""
        # 这里应该生成JWT令牌
        # 临时实现：生成简单的令牌
        import jwt
        
        payload = {
            "user_id": user_id,
            "character_id": character_id,
            "iat": datetime.utcnow(),
            "exp": datetime.utcnow() + timedelta(minutes=settings.jwt_expire_minutes)
        }
        
        token = jwt.encode(payload, settings.secret_key, algorithm=settings.jwt_algorithm)
        return token
