"""
SQLAlchemy数据库模型
"""
from .base import BaseModel
from .character import CharacterModel, SkillModel, SkillQueueModel, AttributesModel
from .asset import AssetModel
from .market import MarketOrderModel, MarketHistoryModel
from .industry import IndustryJobModel
from .corporation import CorporationModel, AllianceModel
from .universe import UniverseTypeModel, UniverseSystemModel
from .auth import UserModel, TokenModel

__all__ = [
    # 基础模型
    "BaseModel",
    
    # 角色相关
    "CharacterModel",
    "SkillModel", 
    "SkillQueueModel",
    "AttributesModel",
    
    # 资产相关
    "AssetModel",
    
    # 市场相关
    "MarketOrderModel",
    "MarketHistoryModel",
    
    # 工业相关
    "IndustryJobModel",
    
    # 公司相关
    "CorporationModel",
    "AllianceModel",
    
    # 宇宙相关
    "UniverseTypeModel",
    "UniverseSystemModel",
    
    # 认证相关
    "UserModel",
    "TokenModel",
]
