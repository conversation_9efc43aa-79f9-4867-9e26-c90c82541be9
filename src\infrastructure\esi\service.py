"""
ESI API服务
"""
from datetime import datetime
from typing import Dict, List, Optional, Any

from .client import ESIClient
from .models import (
    ESICharacterInfo, ESICharacterAttributes, ESISkill, ESISkillQueue,
    ESICharacterLocation, ESICharacterOnline, ESICharacterShip,
    ESIWalletJournal, ESIAsset, ESIMarketOrder, ESIMarketHistory,
    ESIIndustryJob, ESICorporationInfo, ESIAllianceInfo,
    ESIUniverseType, ESIUniverseSystem
)
from ..config.logging import get_logger
from ...domain.shared.exceptions import ESIException

logger = get_logger(__name__)


class ESIService:
    """ESI API服务"""
    
    def __init__(self, client: ESIClient):
        self.client = client
    
    # 角色相关API
    async def get_character_info(self, character_id: int) -> ESICharacterInfo:
        """获取角色基本信息"""
        endpoint = f"/v5/characters/{character_id}/"
        data = await self.client.get(endpoint)
        return ESICharacterInfo(**data)
    
    async def get_character_attributes(self, character_id: int, token: str) -> ESICharacterAttributes:
        """获取角色属性"""
        endpoint = f"/v1/characters/{character_id}/attributes/"
        data = await self.client.get(endpoint, token=token)
        return ESICharacterAttributes(**data)
    
    async def get_character_skills(self, character_id: int, token: str) -> List[ESISkill]:
        """获取角色技能"""
        endpoint = f"/v4/characters/{character_id}/skills/"
        data = await self.client.get(endpoint, token=token)
        return [ESISkill(**skill) for skill in data.get("skills", [])]
    
    async def get_character_skillqueue(self, character_id: int, token: str) -> List[ESISkillQueue]:
        """获取角色技能队列"""
        endpoint = f"/v2/characters/{character_id}/skillqueue/"
        data = await self.client.get(endpoint, token=token)
        return [ESISkillQueue(**item) for item in data]
    
    async def get_character_location(self, character_id: int, token: str) -> ESICharacterLocation:
        """获取角色位置"""
        endpoint = f"/v1/characters/{character_id}/location/"
        data = await self.client.get(endpoint, token=token)
        return ESICharacterLocation(**data)
    
    async def get_character_online(self, character_id: int, token: str) -> ESICharacterOnline:
        """获取角色在线状态"""
        endpoint = f"/v2/characters/{character_id}/online/"
        data = await self.client.get(endpoint, token=token)
        return ESICharacterOnline(**data)
    
    async def get_character_ship(self, character_id: int, token: str) -> ESICharacterShip:
        """获取角色当前飞船"""
        endpoint = f"/v1/characters/{character_id}/ship/"
        data = await self.client.get(endpoint, token=token)
        return ESICharacterShip(**data)
    
    async def get_character_wallet_balance(self, character_id: int, token: str) -> float:
        """获取角色钱包余额"""
        endpoint = f"/v1/characters/{character_id}/wallet/"
        data = await self.client.get(endpoint, token=token)
        return float(data)
    
    async def get_character_wallet_journal(self, 
                                         character_id: int, 
                                         token: str,
                                         page: int = 1) -> List[ESIWalletJournal]:
        """获取角色钱包日志"""
        endpoint = f"/v6/characters/{character_id}/wallet/journal/"
        params = {"page": page}
        data = await self.client.get(endpoint, params=params, token=token)
        return [ESIWalletJournal(**entry) for entry in data]
    
    async def get_character_assets(self, character_id: int, token: str) -> List[ESIAsset]:
        """获取角色资产"""
        endpoint = f"/v5/characters/{character_id}/assets/"
        data = await self.client.get(endpoint, token=token)
        return [ESIAsset(**asset) for asset in data]
    
    async def get_character_industry_jobs(self, 
                                        character_id: int, 
                                        token: str,
                                        include_completed: bool = False) -> List[ESIIndustryJob]:
        """获取角色工业任务"""
        endpoint = f"/v1/characters/{character_id}/industry/jobs/"
        params = {"include_completed": str(include_completed).lower()}
        data = await self.client.get(endpoint, params=params, token=token)
        return [ESIIndustryJob(**job) for job in data]
    
    # 公司相关API
    async def get_corporation_info(self, corporation_id: int) -> ESICorporationInfo:
        """获取公司信息"""
        endpoint = f"/v5/corporations/{corporation_id}/"
        data = await self.client.get(endpoint)
        return ESICorporationInfo(**data)
    
    async def get_corporation_members(self, corporation_id: int, token: str) -> List[int]:
        """获取公司成员列表"""
        endpoint = f"/v4/corporations/{corporation_id}/members/"
        data = await self.client.get(endpoint, token=token)
        return data
    
    async def get_corporation_assets(self, corporation_id: int, token: str) -> List[ESIAsset]:
        """获取公司资产"""
        endpoint = f"/v5/corporations/{corporation_id}/assets/"
        data = await self.client.get(endpoint, token=token)
        return [ESIAsset(**asset) for asset in data]
    
    # 联盟相关API
    async def get_alliance_info(self, alliance_id: int) -> ESIAllianceInfo:
        """获取联盟信息"""
        endpoint = f"/v4/alliances/{alliance_id}/"
        data = await self.client.get(endpoint)
        return ESIAllianceInfo(**data)
    
    async def get_alliance_corporations(self, alliance_id: int) -> List[int]:
        """获取联盟公司列表"""
        endpoint = f"/v2/alliances/{alliance_id}/corporations/"
        data = await self.client.get(endpoint)
        return data
    
    # 市场相关API
    async def get_market_orders(self, 
                              region_id: int,
                              type_id: Optional[int] = None,
                              order_type: str = "all") -> List[ESIMarketOrder]:
        """获取市场订单"""
        endpoint = f"/v1/markets/{region_id}/orders/"
        params = {"order_type": order_type}
        if type_id:
            params["type_id"] = type_id
        
        data = await self.client.get(endpoint, params=params)
        return [ESIMarketOrder(**order) for order in data]
    
    async def get_market_history(self, region_id: int, type_id: int) -> List[ESIMarketHistory]:
        """获取市场历史数据"""
        endpoint = f"/v1/markets/{region_id}/history/"
        params = {"type_id": type_id}
        data = await self.client.get(endpoint, params=params)
        return [ESIMarketHistory(**entry) for entry in data]
    
    async def get_character_market_orders(self, character_id: int, token: str) -> List[ESIMarketOrder]:
        """获取角色市场订单"""
        endpoint = f"/v2/characters/{character_id}/orders/"
        data = await self.client.get(endpoint, token=token)
        return [ESIMarketOrder(**order) for order in data]
    
    # 宇宙相关API
    async def get_universe_type(self, type_id: int) -> ESIUniverseType:
        """获取物品类型信息"""
        endpoint = f"/v3/universe/types/{type_id}/"
        data = await self.client.get(endpoint)
        return ESIUniverseType(**data)
    
    async def get_universe_system(self, system_id: int) -> ESIUniverseSystem:
        """获取星系信息"""
        endpoint = f"/v4/universe/systems/{system_id}/"
        data = await self.client.get(endpoint)
        return ESIUniverseSystem(**data)
    
    async def get_universe_names(self, ids: List[int]) -> List[Dict[str, Any]]:
        """批量获取名称"""
        endpoint = "/v3/universe/names/"
        data = await self.client.post(endpoint, data=ids)
        return data
    
    # 搜索API
    async def search_characters(self, search: str, strict: bool = False) -> List[int]:
        """搜索角色"""
        endpoint = "/v1/search/"
        params = {
            "categories": "character",
            "search": search,
            "strict": str(strict).lower()
        }
        data = await self.client.get(endpoint, params=params)
        return data.get("character", [])
    
    async def search_corporations(self, search: str, strict: bool = False) -> List[int]:
        """搜索公司"""
        endpoint = "/v1/search/"
        params = {
            "categories": "corporation",
            "search": search,
            "strict": str(strict).lower()
        }
        data = await self.client.get(endpoint, params=params)
        return data.get("corporation", [])
    
    # 批量操作
    async def get_multiple_character_info(self, character_ids: List[int]) -> List[ESICharacterInfo]:
        """批量获取角色信息"""
        results = []
        for character_id in character_ids:
            try:
                info = await self.get_character_info(character_id)
                results.append(info)
            except ESIException as e:
                logger.warning(f"获取角色 {character_id} 信息失败", error=str(e))
                continue
        return results
    
    async def get_multiple_universe_types(self, type_ids: List[int]) -> List[ESIUniverseType]:
        """批量获取物品类型信息"""
        results = []
        for type_id in type_ids:
            try:
                type_info = await self.get_universe_type(type_id)
                results.append(type_info)
            except ESIException as e:
                logger.warning(f"获取物品类型 {type_id} 信息失败", error=str(e))
                continue
        return results
