"""
简化版认证路由 - 用于基本应用启动
"""
from fastapi import APIRouter, HTTPException, status
from typing import Dict, Any, List, Optional
from pydantic import BaseModel

router = APIRouter()


class EVELoginRequest(BaseModel):
    """EVE SSO登录请求"""
    scopes: List[str]


class EVELoginResponse(BaseModel):
    """EVE SSO登录响应"""
    success: bool
    login_url: Optional[str] = None
    state: Optional[str] = None
    expires_in: Optional[int] = None
    scopes: Optional[List[str]] = None
    message: Optional[str] = None


@router.post("/login", response_model=EVELoginResponse)
async def initiate_eve_login(request: EVELoginRequest):
    """发起EVE SSO登录 - 简化版本"""
    try:
        # 简化版本：返回错误信息，提示用户功能正在开发中
        return EVELoginResponse(
            success=False,
            message="EVE SSO登录功能正在开发中，请稍后重试"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="EVE login initiation failed"
        )


@router.get("/callback")
async def eve_callback():
    """EVE SSO回调 - 简化版本"""
    return {"message": "EVE SSO回调功能正在开发中"}


@router.get("/status")
async def auth_status():
    """认证状态检查"""
    return {
        "authenticated": False,
        "message": "认证功能正在开发中"
    }


@router.post("/logout")
async def logout():
    """登出"""
    return {"message": "登出成功"}
