# Python DDD 实现指南

## 📋 概述

本文档提供了在Python环境下实现DDD架构的具体指南，确保PRD文档中的设计能够正确地转化为高质量的Python代码。

## 🏗️ Python DDD 核心模式

### 1. 聚合根实现

#### 标准模式
```python
from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any
from abc import ABC, abstractmethod

@dataclass
class AggregateRoot(ABC):
    """聚合根基类"""
    id: Any
    version: int = field(default=0, init=False)
    _events: List[DomainEvent] = field(default_factory=list, init=False)
    
    def __post_init__(self):
        self._validate_invariants()
    
    @abstractmethod
    def _validate_invariants(self) -> None:
        """验证聚合不变量"""
        pass
    
    def raise_event(self, event: DomainEvent) -> None:
        """引发领域事件"""
        self._events.append(event)
    
    def clear_events(self) -> List[DomainEvent]:
        """清除并返回事件"""
        events = self._events.copy()
        self._events.clear()
        return events

@dataclass
class Character(AggregateRoot):
    """角色聚合根"""
    character_id: CharacterId
    name: str
    skills: Dict[int, Skill] = field(default_factory=dict)
    wallet_balance: Money = field(default_factory=lambda: Money(Decimal('0')))
    
    def __post_init__(self):
        super().__post_init__()
        self.id = self.character_id
    
    def _validate_invariants(self) -> None:
        if not self.name or not self.name.strip():
            raise DomainValidationError("角色名称不能为空")
        if self.wallet_balance.amount < 0:
            raise DomainValidationError("钱包余额不能为负数")
```

### 2. 值对象实现

#### 使用frozen dataclass
```python
from dataclasses import dataclass
from decimal import Decimal
from typing import Self

@dataclass(frozen=True)
class Money:
    """货币值对象"""
    amount: Decimal
    currency: str = "ISK"
    
    def __post_init__(self):
        if self.amount < 0:
            raise DomainValidationError("金额不能为负数")
        if not self.currency:
            raise DomainValidationError("货币类型不能为空")
    
    def add(self, other: Self) -> Self:
        """加法运算"""
        if self.currency != other.currency:
            raise DomainValidationError("不同货币类型不能相加")
        return Money(self.amount + other.amount, self.currency)
    
    def __str__(self) -> str:
        return f"{self.amount:,.2f} {self.currency}"
```

### 3. 仓储模式实现

#### 使用Protocol定义接口
```python
from typing import Protocol, Optional, List, runtime_checkable

@runtime_checkable
class CharacterRepository(Protocol):
    """角色仓储接口"""
    
    async def get_by_id(self, character_id: CharacterId) -> Optional[Character]:
        """根据ID获取角色"""
        ...
    
    async def save(self, character: Character) -> None:
        """保存角色"""
        ...
    
    async def find_by_corporation(self, corp_id: CorporationId) -> List[Character]:
        """根据公司ID查找角色"""
        ...
    
    async def delete(self, character: Character) -> None:
        """删除角色"""
        ...

# 具体实现
class SqlCharacterRepository:
    """SQL角色仓储实现"""
    
    def __init__(self, session_factory: Callable[[], AsyncSession]):
        self._session_factory = session_factory
    
    async def get_by_id(self, character_id: CharacterId) -> Optional[Character]:
        async with self._session_factory() as session:
            stmt = select(CharacterModel).where(
                CharacterModel.character_id == character_id.value
            )
            result = await session.execute(stmt)
            model = result.scalar_one_or_none()
            
            if model is None:
                return None
            
            return self._to_entity(model)
```

### 4. 领域服务实现

#### 使用ABC定义服务接口
```python
from abc import ABC, abstractmethod

class DomainService(ABC):
    """领域服务基类"""
    pass

class SkillTrainingService(DomainService):
    """技能训练服务"""
    
    def __init__(self, character_repo: CharacterRepository):
        self._character_repo = character_repo
    
    async def calculate_training_time(
        self, 
        character: Character, 
        skill_id: int, 
        target_level: int
    ) -> timedelta:
        """计算技能训练时间"""
        current_skill = character.get_skill(skill_id)
        if current_skill is None:
            current_level = 0
            current_sp = SkillPoints(0)
        else:
            current_level = current_skill.level
            current_sp = current_skill.skill_points
        
        if current_level >= target_level:
            return timedelta(0)
        
        # 计算所需技能点
        required_sp = self._calculate_required_sp(skill_id, target_level)
        remaining_sp = required_sp.subtract(current_sp)
        
        # 计算训练速度（基于角色属性）
        training_speed = self._calculate_training_speed(character, skill_id)
        
        # 计算训练时间
        training_seconds = remaining_sp.value / training_speed
        return timedelta(seconds=training_seconds)
```

### 5. 应用服务实现

#### 异步应用服务模式
```python
from contextlib import asynccontextmanager

class CharacterApplicationService:
    """角色应用服务"""
    
    def __init__(
        self,
        character_repo: CharacterRepository,
        skill_training_service: SkillTrainingService,
        unit_of_work: UnitOfWork,
        event_bus: EventBus
    ):
        self._character_repo = character_repo
        self._skill_training_service = skill_training_service
        self._unit_of_work = unit_of_work
        self._event_bus = event_bus
    
    async def get_character_overview(
        self, 
        character_id: CharacterId
    ) -> CharacterOverviewDTO:
        """获取角色概览"""
        character = await self._character_repo.get_by_id(character_id)
        if character is None:
            raise CharacterNotFoundError(character_id)
        
        return CharacterOverviewDTO(
            character_id=character.character_id.value,
            name=character.name,
            total_sp=character.get_total_skill_points().value,
            wallet_balance=str(character.wallet_balance)
        )
    
    async def update_character_skills(
        self, 
        character_id: CharacterId, 
        skills_data: List[SkillDataDTO]
    ) -> None:
        """更新角色技能"""
        async with self._unit_of_work:
            character = await self._character_repo.get_by_id(character_id)
            if character is None:
                raise CharacterNotFoundError(character_id)
            
            # 更新技能
            for skill_data in skills_data:
                skill = Skill(
                    skill_id=skill_data.skill_id,
                    level=skill_data.level,
                    skill_points=SkillPoints(skill_data.skill_points)
                )
                character.update_skill(skill)
            
            await self._character_repo.save(character)
            
            # 发布事件
            events = character.clear_events()
            for event in events:
                await self._event_bus.publish(event)
```

## 🔧 Python特定的最佳实践

### 1. 类型注解使用

```python
from typing import TypeVar, Generic, Protocol, Optional, List, Dict, Any
from collections.abc import Sequence, Mapping

T = TypeVar('T', bound=AggregateRoot)
K = TypeVar('K')
V = TypeVar('V')

class Repository(Protocol, Generic[T, K]):
    async def get_by_id(self, id: K) -> Optional[T]: ...
    async def save(self, entity: T) -> None: ...
    async def find_all(self) -> Sequence[T]: ...
```

### 2. 异步上下文管理

```python
class UnitOfWork:
    """工作单元模式"""
    
    def __init__(self, session_factory: Callable[[], AsyncSession]):
        self._session_factory = session_factory
        self._session: Optional[AsyncSession] = None
    
    async def __aenter__(self):
        self._session = self._session_factory()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if exc_type is None:
            await self._session.commit()
        else:
            await self._session.rollback()
        await self._session.close()
```

### 3. 依赖注入配置

```python
from dependency_injector import containers, providers
from dependency_injector.wiring import Provide, inject

class Container(containers.DeclarativeContainer):
    """依赖注入容器"""
    
    # 配置
    config = providers.Configuration()
    
    # 数据库
    database = providers.Singleton(
        Database,
        url=config.database.url
    )
    
    # 仓储
    character_repository = providers.Factory(
        SqlCharacterRepository,
        session_factory=database.provided.session_factory
    )
    
    # 领域服务
    skill_training_service = providers.Factory(
        SkillTrainingService,
        character_repo=character_repository
    )
    
    # 应用服务
    character_service = providers.Factory(
        CharacterApplicationService,
        character_repo=character_repository,
        skill_training_service=skill_training_service
    )

# 在FastAPI中使用
@router.get("/characters/{character_id}")
@inject
async def get_character(
    character_id: int,
    service: CharacterApplicationService = Provide[Container.character_service]
):
    return await service.get_character_overview(CharacterId(character_id))
```

### 4. 错误处理策略

```python
class DomainException(Exception):
    """领域异常基类"""
    
    def __init__(self, message: str, error_code: str = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code

class CharacterNotFoundError(DomainException):
    """角色未找到错误"""
    
    def __init__(self, character_id: CharacterId):
        super().__init__(
            f"角色 {character_id} 未找到",
            error_code="CHARACTER_NOT_FOUND"
        )
        self.character_id = character_id

# 全局异常处理
@app.exception_handler(DomainException)
async def domain_exception_handler(request: Request, exc: DomainException):
    return JSONResponse(
        status_code=400,
        content={
            "error": {
                "code": exc.error_code,
                "message": exc.message,
                "type": "domain_error"
            }
        }
    )
```

## 📈 性能优化建议

### 1. 异步数据库操作
- 使用 SQLAlchemy 2.0 异步模式
- 连接池配置优化
- 查询优化和索引使用

### 2. 缓存策略
- Redis 异步客户端 (aioredis)
- 多级缓存架构
- 缓存失效策略

### 3. 并发控制
- asyncio 任务管理
- 信号量控制并发数
- 背压处理机制

---

**文档版本**: v1.0  
**最后更新**: 2025-01-09  
**负责人**: 技术架构团队
