# ngrok配置文件
# 使用方法: ngrok start --config ngrok.yml eve-api

version: "2"

# 在这里填入你的authtoken（从 https://dashboard.ngrok.com/get-started/your-authtoken 获取）
authtoken: 2twVZ9ClYDceG5Lr4A5fPXRfNCR_7qCUAvXzHaHXiGYQHkDt7

tunnels:
  eve-api:
    proto: http
    addr: 8000
    # 如果你有固定域名，取消下面这行的注释并填入你的域名
    hostname: diverse-monitor-model.ngrok-free.app
    
    # 可选配置
    inspect: true
    bind_tls: true
    
    # 请求头配置
    request_headers:
      add:
        - "X-Forwarded-Proto: https"
    
    # 响应头配置  
    response_headers:
      add:
        - "X-Custom-Header: EVE-Assistant"

# 日志配置
log_level: info
log_format: logfmt
log: ngrok.log
