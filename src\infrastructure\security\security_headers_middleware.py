"""
安全头部中间件
"""
from typing import Dict, Optional
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from ..config.logging import get_logger

logger = get_logger(__name__)


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """安全头部中间件"""
    
    def __init__(self, app,
                 force_https: bool = True,
                 hsts_max_age: int = 31536000,  # 1年
                 hsts_include_subdomains: bool = True,
                 hsts_preload: bool = True,
                 content_type_nosniff: bool = True,
                 x_frame_options: str = "DENY",
                 x_content_type_options: str = "nosniff",
                 referrer_policy: str = "strict-origin-when-cross-origin",
                 permissions_policy: Optional[str] = None,
                 custom_headers: Optional[Dict[str, str]] = None):
        super().__init__(app)
        
        self.force_https = force_https
        self.hsts_max_age = hsts_max_age
        self.hsts_include_subdomains = hsts_include_subdomains
        self.hsts_preload = hsts_preload
        self.content_type_nosniff = content_type_nosniff
        self.x_frame_options = x_frame_options
        self.x_content_type_options = x_content_type_options
        self.referrer_policy = referrer_policy
        self.permissions_policy = permissions_policy or self._default_permissions_policy()
        self.custom_headers = custom_headers or {}
        
        # CSP策略
        self.csp_policy = self._build_csp_policy()
    
    async def dispatch(self, request: Request, call_next):
        # 检查HTTPS重定向
        if self.force_https and request.url.scheme == "http":
            https_url = request.url.replace(scheme="https")
            return Response(
                status_code=301,
                headers={"Location": str(https_url)}
            )
        
        response = await call_next(request)
        
        # 添加安全头部
        self._add_security_headers(response, request)
        
        return response
    
    def _add_security_headers(self, response: Response, request: Request) -> None:
        """添加安全头部"""
        headers = response.headers
        
        # HSTS (HTTP Strict Transport Security)
        if request.url.scheme == "https":
            hsts_value = f"max-age={self.hsts_max_age}"
            if self.hsts_include_subdomains:
                hsts_value += "; includeSubDomains"
            if self.hsts_preload:
                hsts_value += "; preload"
            headers["Strict-Transport-Security"] = hsts_value
        
        # X-Frame-Options
        if self.x_frame_options:
            headers["X-Frame-Options"] = self.x_frame_options
        
        # X-Content-Type-Options
        if self.x_content_type_options:
            headers["X-Content-Type-Options"] = self.x_content_type_options
        
        # Referrer-Policy
        if self.referrer_policy:
            headers["Referrer-Policy"] = self.referrer_policy
        
        # Content Security Policy
        if self.csp_policy:
            headers["Content-Security-Policy"] = self.csp_policy
        
        # Permissions Policy
        if self.permissions_policy:
            headers["Permissions-Policy"] = self.permissions_policy
        
        # X-XSS-Protection (虽然已废弃，但为了兼容性)
        headers["X-XSS-Protection"] = "1; mode=block"
        
        # 自定义安全头部
        headers["X-Powered-By"] = "EVE-Assistant"
        headers["X-Content-Type-Options"] = "nosniff"
        headers["X-Download-Options"] = "noopen"
        headers["X-Permitted-Cross-Domain-Policies"] = "none"
        
        # 添加自定义头部
        for key, value in self.custom_headers.items():
            headers[key] = value
        
        # 移除可能泄露信息的头部
        headers.pop("Server", None)
        headers.pop("X-Powered-By", None)
    
    def _build_csp_policy(self) -> str:
        """构建CSP策略"""
        policy_parts = [
            "default-src 'self'",
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com",
            "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net",
            "font-src 'self' https://fonts.gstatic.com https://cdn.jsdelivr.net",
            "img-src 'self' data: https: blob:",
            "connect-src 'self' https://esi.evetech.net https://login.eveonline.com wss:",
            "media-src 'self'",
            "object-src 'none'",
            "base-uri 'self'",
            "form-action 'self'",
            "frame-ancestors 'none'",
            "upgrade-insecure-requests"
        ]
        
        return "; ".join(policy_parts)
    
    def _default_permissions_policy(self) -> str:
        """默认权限策略"""
        policies = [
            "accelerometer=()",
            "ambient-light-sensor=()",
            "autoplay=()",
            "battery=()",
            "camera=()",
            "cross-origin-isolated=()",
            "display-capture=()",
            "document-domain=()",
            "encrypted-media=()",
            "execution-while-not-rendered=()",
            "execution-while-out-of-viewport=()",
            "fullscreen=()",
            "geolocation=()",
            "gyroscope=()",
            "keyboard-map=()",
            "magnetometer=()",
            "microphone=()",
            "midi=()",
            "navigation-override=()",
            "payment=()",
            "picture-in-picture=()",
            "publickey-credentials-get=()",
            "screen-wake-lock=()",
            "sync-xhr=()",
            "usb=()",
            "web-share=()",
            "xr-spatial-tracking=()"
        ]
        
        return ", ".join(policies)


class CSPViolationReporter:
    """CSP违规报告处理器"""
    
    def __init__(self):
        self.logger = get_logger(f"{__name__}.CSPViolationReporter")
    
    async def handle_csp_report(self, request: Request) -> Response:
        """处理CSP违规报告"""
        try:
            report_data = await request.json()
            
            csp_report = report_data.get("csp-report", {})
            
            self.logger.warning(
                "CSP违规报告",
                document_uri=csp_report.get("document-uri"),
                violated_directive=csp_report.get("violated-directive"),
                blocked_uri=csp_report.get("blocked-uri"),
                source_file=csp_report.get("source-file"),
                line_number=csp_report.get("line-number"),
                column_number=csp_report.get("column-number"),
                user_agent=request.headers.get("user-agent")
            )
            
            # 这里可以添加更多的处理逻辑，比如：
            # - 存储到数据库
            # - 发送告警
            # - 统计分析
            
            return Response(status_code=204)  # No Content
            
        except Exception as e:
            self.logger.error("处理CSP违规报告失败", error=str(e))
            return Response(status_code=400)


def setup_security_headers_middleware(app):
    """设置安全头部中间件"""
    app.add_middleware(SecurityHeadersMiddleware)
    
    # 添加CSP违规报告端点
    csp_reporter = CSPViolationReporter()
    
    @app.post("/api/security/csp-report")
    async def csp_report_endpoint(request: Request):
        return await csp_reporter.handle_csp_report(request)
    
    logger.info("安全头部中间件已配置")
