#!/usr/bin/env python3
"""
依赖安装脚本
检查并安装EVE Online Assistant所需的依赖包
"""
import subprocess
import sys
import importlib


def check_and_install_package(package_name, import_name=None):
    """检查并安装包"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        print(f"✅ {package_name} 已安装")
        return True
    except ImportError:
        print(f"❌ {package_name} 未安装，正在安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
            print(f"✅ {package_name} 安装成功")
            return True
        except subprocess.CalledProcessError:
            print(f"❌ {package_name} 安装失败")
            return False


def main():
    """主函数"""
    print("🔍 检查EVE Online Assistant依赖...")
    
    # 核心依赖列表
    dependencies = [
        ("pydantic", "pydantic"),
        ("pydantic-settings", "pydantic_settings"),
        ("fastapi", "fastapi"),
        ("uvicorn[standard]", "uvicorn"),
        ("pyngrok", "pyngrok"),
        ("pyjwt", "jwt"),
        ("httpx", "httpx"),
        ("sqlalchemy", "sqlalchemy"),
        ("structlog", "structlog"),
        ("python-dotenv", "dotenv"),
    ]
    
    failed_packages = []
    
    for package_name, import_name in dependencies:
        if not check_and_install_package(package_name, import_name):
            failed_packages.append(package_name)
    
    print("\n" + "="*50)
    
    if failed_packages:
        print(f"❌ 以下包安装失败: {', '.join(failed_packages)}")
        print("请手动安装这些包:")
        for package in failed_packages:
            print(f"   pip install {package}")
        return 1
    else:
        print("🎉 所有依赖安装完成!")
        print("现在可以运行: python start.py")
        return 0


if __name__ == "__main__":
    sys.exit(main())
