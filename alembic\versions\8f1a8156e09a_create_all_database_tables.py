"""Create all database tables

Revision ID: 8f1a8156e09a
Revises: 
Create Date: 2025-08-20 10:42:49.075861

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8f1a8156e09a'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """升级数据库"""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('alliances',
    sa.Column('alliance_id', sa.BigInteger(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('ticker', sa.String(length=10), nullable=False),
    sa.Column('creator_id', sa.<PERSON>Integer(), nullable=False),
    sa.Column('creator_corporation_id', sa.<PERSON>Integer(), nullable=False),
    sa.Column('executor_corporation_id', sa.BigInteger(), nullable=True),
    sa.Column('date_founded', sa.DateTime(), nullable=False),
    sa.Column('faction_id', sa.Integer(), nullable=True),
    sa.Column('last_sync_at', sa.DateTime(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('alliance_id', 'id')
    )
    op.create_index('idx_alliance_creator', 'alliances', ['creator_corporation_id'], unique=False)
    op.create_index('idx_alliance_executor', 'alliances', ['executor_corporation_id'], unique=False)
    op.create_index(op.f('ix_alliances_id'), 'alliances', ['id'], unique=False)
    op.create_index(op.f('ix_alliances_name'), 'alliances', ['name'], unique=False)
    op.create_index(op.f('ix_alliances_ticker'), 'alliances', ['ticker'], unique=False)
    op.create_table('asset_locations',
    sa.Column('location_id', sa.BigInteger(), nullable=False),
    sa.Column('location_name', sa.String(length=255), nullable=True),
    sa.Column('location_type', sa.String(length=50), nullable=False),
    sa.Column('system_id', sa.Integer(), nullable=True),
    sa.Column('system_name', sa.String(length=255), nullable=True),
    sa.Column('region_id', sa.Integer(), nullable=True),
    sa.Column('region_name', sa.String(length=255), nullable=True),
    sa.Column('cached_at', sa.DateTime(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('location_id', 'id')
    )
    op.create_index('idx_location_region', 'asset_locations', ['region_id'], unique=False)
    op.create_index('idx_location_system', 'asset_locations', ['system_id', 'location_type'], unique=False)
    op.create_index(op.f('ix_asset_locations_id'), 'asset_locations', ['id'], unique=False)
    op.create_index(op.f('ix_asset_locations_region_id'), 'asset_locations', ['region_id'], unique=False)
    op.create_index(op.f('ix_asset_locations_system_id'), 'asset_locations', ['system_id'], unique=False)
    op.create_table('characters',
    sa.Column('character_id', sa.BigInteger(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('corporation_id', sa.BigInteger(), nullable=False),
    sa.Column('alliance_id', sa.BigInteger(), nullable=True),
    sa.Column('faction_id', sa.Integer(), nullable=True),
    sa.Column('race_id', sa.Integer(), nullable=False),
    sa.Column('bloodline_id', sa.Integer(), nullable=False),
    sa.Column('ancestry_id', sa.Integer(), nullable=True),
    sa.Column('gender', sa.String(length=10), nullable=False),
    sa.Column('birthday', sa.DateTime(), nullable=False),
    sa.Column('security_status', sa.Float(), nullable=True),
    sa.Column('title', sa.String(length=255), nullable=True),
    sa.Column('current_system_id', sa.Integer(), nullable=True),
    sa.Column('current_station_id', sa.BigInteger(), nullable=True),
    sa.Column('current_structure_id', sa.BigInteger(), nullable=True),
    sa.Column('is_online', sa.Boolean(), nullable=True),
    sa.Column('last_login', sa.DateTime(), nullable=True),
    sa.Column('last_logout', sa.DateTime(), nullable=True),
    sa.Column('current_ship_type_id', sa.Integer(), nullable=True),
    sa.Column('current_ship_item_id', sa.BigInteger(), nullable=True),
    sa.Column('current_ship_name', sa.String(length=255), nullable=True),
    sa.Column('wallet_balance', sa.Float(), nullable=True),
    sa.Column('last_sync_at', sa.DateTime(), nullable=True),
    sa.Column('sync_error_count', sa.Integer(), nullable=True),
    sa.Column('sync_error_message', sa.Text(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('character_id', 'id')
    )
    op.create_index('idx_character_corp_alliance', 'characters', ['corporation_id', 'alliance_id'], unique=False)
    op.create_index('idx_character_location', 'characters', ['current_system_id', 'current_station_id'], unique=False)
    op.create_index('idx_character_sync', 'characters', ['last_sync_at', 'sync_error_count'], unique=False)
    op.create_index(op.f('ix_characters_alliance_id'), 'characters', ['alliance_id'], unique=False)
    op.create_index(op.f('ix_characters_character_id'), 'characters', ['character_id'], unique=False)
    op.create_index(op.f('ix_characters_corporation_id'), 'characters', ['corporation_id'], unique=False)
    op.create_index(op.f('ix_characters_current_system_id'), 'characters', ['current_system_id'], unique=False)
    op.create_index(op.f('ix_characters_id'), 'characters', ['id'], unique=False)
    op.create_index(op.f('ix_characters_is_online'), 'characters', ['is_online'], unique=False)
    op.create_index(op.f('ix_characters_name'), 'characters', ['name'], unique=False)
    op.create_table('corporations',
    sa.Column('corporation_id', sa.BigInteger(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('ticker', sa.String(length=10), nullable=False),
    sa.Column('member_count', sa.Integer(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('tax_rate', sa.Float(), nullable=False),
    sa.Column('date_founded', sa.DateTime(), nullable=True),
    sa.Column('creator_id', sa.BigInteger(), nullable=False),
    sa.Column('ceo_id', sa.BigInteger(), nullable=False),
    sa.Column('alliance_id', sa.BigInteger(), nullable=True),
    sa.Column('faction_id', sa.Integer(), nullable=True),
    sa.Column('home_station_id', sa.BigInteger(), nullable=True),
    sa.Column('shares', sa.BigInteger(), nullable=True),
    sa.Column('url', sa.String(length=500), nullable=True),
    sa.Column('war_eligible', sa.Boolean(), nullable=True),
    sa.Column('last_sync_at', sa.DateTime(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('corporation_id', 'id')
    )
    op.create_index('idx_corp_alliance', 'corporations', ['alliance_id'], unique=False)
    op.create_index('idx_corp_ceo', 'corporations', ['ceo_id'], unique=False)
    op.create_index(op.f('ix_corporations_alliance_id'), 'corporations', ['alliance_id'], unique=False)
    op.create_index(op.f('ix_corporations_id'), 'corporations', ['id'], unique=False)
    op.create_index(op.f('ix_corporations_name'), 'corporations', ['name'], unique=False)
    op.create_index(op.f('ix_corporations_ticker'), 'corporations', ['ticker'], unique=False)
    op.create_table('market_history',
    sa.Column('region_id', sa.Integer(), nullable=False),
    sa.Column('type_id', sa.Integer(), nullable=False),
    sa.Column('date', sa.DateTime(), nullable=False),
    sa.Column('order_count', sa.Integer(), nullable=False),
    sa.Column('volume', sa.BigInteger(), nullable=False),
    sa.Column('highest', sa.Float(), nullable=False),
    sa.Column('average', sa.Float(), nullable=False),
    sa.Column('lowest', sa.Float(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('region_id', 'type_id', 'date', 'id')
    )
    op.create_index('idx_history_region_date', 'market_history', ['region_id', 'date'], unique=False)
    op.create_index('idx_history_type_date', 'market_history', ['type_id', 'date'], unique=False)
    op.create_index(op.f('ix_market_history_id'), 'market_history', ['id'], unique=False)
    op.create_table('universe_constellations',
    sa.Column('constellation_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('region_id', sa.Integer(), nullable=False),
    sa.Column('last_sync_at', sa.DateTime(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('constellation_id', 'id')
    )
    op.create_index('idx_constellation_region', 'universe_constellations', ['region_id'], unique=False)
    op.create_index(op.f('ix_universe_constellations_id'), 'universe_constellations', ['id'], unique=False)
    op.create_index(op.f('ix_universe_constellations_name'), 'universe_constellations', ['name'], unique=False)
    op.create_index(op.f('ix_universe_constellations_region_id'), 'universe_constellations', ['region_id'], unique=False)
    op.create_table('universe_regions',
    sa.Column('region_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('last_sync_at', sa.DateTime(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('region_id', 'id')
    )
    op.create_index(op.f('ix_universe_regions_id'), 'universe_regions', ['id'], unique=False)
    op.create_index(op.f('ix_universe_regions_name'), 'universe_regions', ['name'], unique=False)
    op.create_table('universe_systems',
    sa.Column('system_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('constellation_id', sa.Integer(), nullable=False),
    sa.Column('region_id', sa.Integer(), nullable=False),
    sa.Column('security_status', sa.Float(), nullable=False),
    sa.Column('security_class', sa.String(length=10), nullable=True),
    sa.Column('star_id', sa.Integer(), nullable=True),
    sa.Column('last_sync_at', sa.DateTime(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('system_id', 'id')
    )
    op.create_index('idx_system_constellation', 'universe_systems', ['constellation_id'], unique=False)
    op.create_index('idx_system_region', 'universe_systems', ['region_id'], unique=False)
    op.create_index('idx_system_security', 'universe_systems', ['security_status'], unique=False)
    op.create_index(op.f('ix_universe_systems_constellation_id'), 'universe_systems', ['constellation_id'], unique=False)
    op.create_index(op.f('ix_universe_systems_id'), 'universe_systems', ['id'], unique=False)
    op.create_index(op.f('ix_universe_systems_name'), 'universe_systems', ['name'], unique=False)
    op.create_index(op.f('ix_universe_systems_region_id'), 'universe_systems', ['region_id'], unique=False)
    op.create_index(op.f('ix_universe_systems_security_status'), 'universe_systems', ['security_status'], unique=False)
    op.create_table('universe_types',
    sa.Column('type_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('published', sa.Boolean(), nullable=False),
    sa.Column('group_id', sa.Integer(), nullable=False),
    sa.Column('category_id', sa.Integer(), nullable=False),
    sa.Column('market_group_id', sa.Integer(), nullable=True),
    sa.Column('mass', sa.Float(), nullable=True),
    sa.Column('volume', sa.Float(), nullable=True),
    sa.Column('capacity', sa.Float(), nullable=True),
    sa.Column('portion_size', sa.Integer(), nullable=True),
    sa.Column('radius', sa.Float(), nullable=True),
    sa.Column('graphic_id', sa.Integer(), nullable=True),
    sa.Column('icon_id', sa.Integer(), nullable=True),
    sa.Column('sound_id', sa.Integer(), nullable=True),
    sa.Column('last_sync_at', sa.DateTime(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('type_id', 'id')
    )
    op.create_index('idx_type_group_category', 'universe_types', ['group_id', 'category_id'], unique=False)
    op.create_index('idx_type_market_group', 'universe_types', ['market_group_id'], unique=False)
    op.create_index('idx_type_published', 'universe_types', ['published', 'category_id'], unique=False)
    op.create_index(op.f('ix_universe_types_category_id'), 'universe_types', ['category_id'], unique=False)
    op.create_index(op.f('ix_universe_types_group_id'), 'universe_types', ['group_id'], unique=False)
    op.create_index(op.f('ix_universe_types_id'), 'universe_types', ['id'], unique=False)
    op.create_index(op.f('ix_universe_types_market_group_id'), 'universe_types', ['market_group_id'], unique=False)
    op.create_index(op.f('ix_universe_types_name'), 'universe_types', ['name'], unique=False)
    op.create_table('users',
    sa.Column('username', sa.String(length=255), nullable=False),
    sa.Column('email', sa.String(length=255), nullable=True),
    sa.Column('hashed_password', sa.String(length=255), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_superuser', sa.Boolean(), nullable=True),
    sa.Column('main_character_id', sa.BigInteger(), nullable=True),
    sa.Column('last_login', sa.DateTime(), nullable=True),
    sa.Column('login_count', sa.Integer(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_user_active', 'users', ['is_active'], unique=False)
    op.create_index('idx_user_main_char', 'users', ['main_character_id'], unique=False)
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_is_active'), 'users', ['is_active'], unique=False)
    op.create_index(op.f('ix_users_main_character_id'), 'users', ['main_character_id'], unique=False)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    op.create_table('asset_value_history',
    sa.Column('character_id', sa.BigInteger(), nullable=False),
    sa.Column('date', sa.DateTime(), nullable=False),
    sa.Column('total_value', sa.Float(), nullable=False),
    sa.Column('liquid_value', sa.Float(), nullable=False),
    sa.Column('ship_value', sa.Float(), nullable=False),
    sa.Column('module_value', sa.Float(), nullable=False),
    sa.Column('commodity_value', sa.Float(), nullable=False),
    sa.Column('total_items', sa.Integer(), nullable=False),
    sa.Column('unique_types', sa.Integer(), nullable=False),
    sa.Column('locations', sa.Integer(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['character_id'], ['characters.character_id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_value_history_date', 'asset_value_history', ['character_id', 'date'], unique=False)
    op.create_index(op.f('ix_asset_value_history_character_id'), 'asset_value_history', ['character_id'], unique=False)
    op.create_index(op.f('ix_asset_value_history_date'), 'asset_value_history', ['date'], unique=False)
    op.create_index(op.f('ix_asset_value_history_id'), 'asset_value_history', ['id'], unique=False)
    op.create_table('assets',
    sa.Column('item_id', sa.BigInteger(), nullable=False),
    sa.Column('character_id', sa.BigInteger(), nullable=True),
    sa.Column('corporation_id', sa.BigInteger(), nullable=True),
    sa.Column('type_id', sa.Integer(), nullable=False),
    sa.Column('quantity', sa.BigInteger(), nullable=False),
    sa.Column('location_id', sa.BigInteger(), nullable=False),
    sa.Column('location_type', sa.String(length=50), nullable=False),
    sa.Column('location_flag', sa.String(length=50), nullable=False),
    sa.Column('is_singleton', sa.Boolean(), nullable=False),
    sa.Column('is_blueprint_copy', sa.Boolean(), nullable=True),
    sa.Column('estimated_value', sa.Float(), nullable=True),
    sa.Column('last_price_update', sa.DateTime(), nullable=True),
    sa.Column('last_sync_at', sa.DateTime(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['character_id'], ['characters.character_id'], ),
    sa.PrimaryKeyConstraint('item_id', 'id')
    )
    op.create_index('idx_asset_location', 'assets', ['location_id', 'location_type'], unique=False)
    op.create_index('idx_asset_owner_type', 'assets', ['character_id', 'type_id'], unique=False)
    op.create_index('idx_asset_sync', 'assets', ['last_sync_at'], unique=False)
    op.create_index('idx_asset_value', 'assets', ['estimated_value'], unique=False)
    op.create_index(op.f('ix_assets_character_id'), 'assets', ['character_id'], unique=False)
    op.create_index(op.f('ix_assets_corporation_id'), 'assets', ['corporation_id'], unique=False)
    op.create_index(op.f('ix_assets_id'), 'assets', ['id'], unique=False)
    op.create_index(op.f('ix_assets_location_id'), 'assets', ['location_id'], unique=False)
    op.create_index(op.f('ix_assets_type_id'), 'assets', ['type_id'], unique=False)
    op.create_table('character_attributes',
    sa.Column('character_id', sa.BigInteger(), nullable=False),
    sa.Column('charisma', sa.Integer(), nullable=False),
    sa.Column('intelligence', sa.Integer(), nullable=False),
    sa.Column('memory', sa.Integer(), nullable=False),
    sa.Column('perception', sa.Integer(), nullable=False),
    sa.Column('willpower', sa.Integer(), nullable=False),
    sa.Column('bonus_remaps', sa.Integer(), nullable=True),
    sa.Column('last_remap_date', sa.DateTime(), nullable=True),
    sa.Column('accrued_remap_cooldown_date', sa.DateTime(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['character_id'], ['characters.character_id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('character_id')
    )
    op.create_index(op.f('ix_character_attributes_id'), 'character_attributes', ['id'], unique=False)
    op.create_table('character_clones',
    sa.Column('character_id', sa.BigInteger(), nullable=False),
    sa.Column('clone_id', sa.BigInteger(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('location_id', sa.BigInteger(), nullable=False),
    sa.Column('clone_type', sa.String(length=20), nullable=False),
    sa.Column('implants', sa.Text(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['character_id'], ['characters.character_id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('character_id', 'clone_id', name='uq_character_clone')
    )
    op.create_index(op.f('ix_character_clones_character_id'), 'character_clones', ['character_id'], unique=False)
    op.create_index(op.f('ix_character_clones_id'), 'character_clones', ['id'], unique=False)
    op.create_table('character_mails',
    sa.Column('character_id', sa.BigInteger(), nullable=False),
    sa.Column('mail_id', sa.BigInteger(), nullable=False),
    sa.Column('subject', sa.String(length=500), nullable=False),
    sa.Column('from_id', sa.BigInteger(), nullable=False),
    sa.Column('timestamp', sa.DateTime(), nullable=False),
    sa.Column('is_read', sa.Boolean(), nullable=True),
    sa.Column('recipients', sa.Text(), nullable=True),
    sa.Column('body', sa.Text(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['character_id'], ['characters.character_id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('mail_id')
    )
    op.create_index('idx_mail_read_status', 'character_mails', ['character_id', 'is_read'], unique=False)
    op.create_index('idx_mail_timestamp', 'character_mails', ['character_id', 'timestamp'], unique=False)
    op.create_index(op.f('ix_character_mails_character_id'), 'character_mails', ['character_id'], unique=False)
    op.create_index(op.f('ix_character_mails_id'), 'character_mails', ['id'], unique=False)
    op.create_index(op.f('ix_character_mails_timestamp'), 'character_mails', ['timestamp'], unique=False)
    op.create_table('character_skill_queue',
    sa.Column('character_id', sa.BigInteger(), nullable=False),
    sa.Column('skill_id', sa.Integer(), nullable=False),
    sa.Column('queue_position', sa.Integer(), nullable=False),
    sa.Column('finished_level', sa.Integer(), nullable=False),
    sa.Column('training_start_sp', sa.BigInteger(), nullable=True),
    sa.Column('level_end_sp', sa.BigInteger(), nullable=True),
    sa.Column('level_start_sp', sa.BigInteger(), nullable=True),
    sa.Column('start_date', sa.DateTime(), nullable=True),
    sa.Column('finish_date', sa.DateTime(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['character_id'], ['characters.character_id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_skill_queue_position', 'character_skill_queue', ['character_id', 'queue_position'], unique=False)
    op.create_index('idx_skill_queue_time', 'character_skill_queue', ['start_date', 'finish_date'], unique=False)
    op.create_index(op.f('ix_character_skill_queue_character_id'), 'character_skill_queue', ['character_id'], unique=False)
    op.create_index(op.f('ix_character_skill_queue_id'), 'character_skill_queue', ['id'], unique=False)
    op.create_index(op.f('ix_character_skill_queue_skill_id'), 'character_skill_queue', ['skill_id'], unique=False)
    op.create_table('character_skills',
    sa.Column('character_id', sa.BigInteger(), nullable=False),
    sa.Column('skill_id', sa.Integer(), nullable=False),
    sa.Column('skillpoints_in_skill', sa.BigInteger(), nullable=False),
    sa.Column('trained_skill_level', sa.Integer(), nullable=False),
    sa.Column('active_skill_level', sa.Integer(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['character_id'], ['characters.character_id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('character_id', 'skill_id', name='uq_character_skill')
    )
    op.create_index('idx_skill_level', 'character_skills', ['skill_id', 'trained_skill_level'], unique=False)
    op.create_index(op.f('ix_character_skills_character_id'), 'character_skills', ['character_id'], unique=False)
    op.create_index(op.f('ix_character_skills_id'), 'character_skills', ['id'], unique=False)
    op.create_index(op.f('ix_character_skills_skill_id'), 'character_skills', ['skill_id'], unique=False)
    op.create_table('industry_jobs',
    sa.Column('job_id', sa.BigInteger(), nullable=False),
    sa.Column('character_id', sa.BigInteger(), nullable=True),
    sa.Column('corporation_id', sa.BigInteger(), nullable=True),
    sa.Column('installer_id', sa.BigInteger(), nullable=False),
    sa.Column('facility_id', sa.BigInteger(), nullable=False),
    sa.Column('station_id', sa.BigInteger(), nullable=True),
    sa.Column('activity_id', sa.Integer(), nullable=False),
    sa.Column('blueprint_id', sa.BigInteger(), nullable=False),
    sa.Column('blueprint_type_id', sa.Integer(), nullable=False),
    sa.Column('blueprint_location_id', sa.BigInteger(), nullable=False),
    sa.Column('output_location_id', sa.BigInteger(), nullable=False),
    sa.Column('runs', sa.Integer(), nullable=False),
    sa.Column('cost', sa.Float(), nullable=True),
    sa.Column('licensed_runs', sa.Integer(), nullable=True),
    sa.Column('probability', sa.Float(), nullable=True),
    sa.Column('product_type_id', sa.Integer(), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('time_in_seconds', sa.Integer(), nullable=False),
    sa.Column('start_date', sa.DateTime(), nullable=False),
    sa.Column('end_date', sa.DateTime(), nullable=False),
    sa.Column('pause_date', sa.DateTime(), nullable=True),
    sa.Column('completed_date', sa.DateTime(), nullable=True),
    sa.Column('completed_character_id', sa.BigInteger(), nullable=True),
    sa.Column('successful_runs', sa.Integer(), nullable=True),
    sa.Column('last_sync_at', sa.DateTime(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['character_id'], ['characters.character_id'], ),
    sa.PrimaryKeyConstraint('job_id', 'id')
    )
    op.create_index('idx_industry_activity', 'industry_jobs', ['activity_id', 'blueprint_type_id'], unique=False)
    op.create_index('idx_industry_facility', 'industry_jobs', ['facility_id', 'start_date'], unique=False)
    op.create_index('idx_industry_status_date', 'industry_jobs', ['status', 'end_date'], unique=False)
    op.create_index(op.f('ix_industry_jobs_activity_id'), 'industry_jobs', ['activity_id'], unique=False)
    op.create_index(op.f('ix_industry_jobs_blueprint_type_id'), 'industry_jobs', ['blueprint_type_id'], unique=False)
    op.create_index(op.f('ix_industry_jobs_character_id'), 'industry_jobs', ['character_id'], unique=False)
    op.create_index(op.f('ix_industry_jobs_corporation_id'), 'industry_jobs', ['corporation_id'], unique=False)
    op.create_index(op.f('ix_industry_jobs_end_date'), 'industry_jobs', ['end_date'], unique=False)
    op.create_index(op.f('ix_industry_jobs_facility_id'), 'industry_jobs', ['facility_id'], unique=False)
    op.create_index(op.f('ix_industry_jobs_id'), 'industry_jobs', ['id'], unique=False)
    op.create_index(op.f('ix_industry_jobs_product_type_id'), 'industry_jobs', ['product_type_id'], unique=False)
    op.create_index(op.f('ix_industry_jobs_start_date'), 'industry_jobs', ['start_date'], unique=False)
    op.create_index(op.f('ix_industry_jobs_status'), 'industry_jobs', ['status'], unique=False)
    op.create_table('market_orders',
    sa.Column('order_id', sa.BigInteger(), nullable=False),
    sa.Column('character_id', sa.BigInteger(), nullable=True),
    sa.Column('type_id', sa.Integer(), nullable=False),
    sa.Column('location_id', sa.BigInteger(), nullable=False),
    sa.Column('region_id', sa.Integer(), nullable=False),
    sa.Column('volume_total', sa.Integer(), nullable=False),
    sa.Column('volume_remain', sa.Integer(), nullable=False),
    sa.Column('min_volume', sa.Integer(), nullable=False),
    sa.Column('price', sa.Float(), nullable=False),
    sa.Column('is_buy_order', sa.Boolean(), nullable=False),
    sa.Column('duration', sa.Integer(), nullable=False),
    sa.Column('issued', sa.DateTime(), nullable=False),
    sa.Column('range', sa.String(length=20), nullable=False),
    sa.Column('last_sync_at', sa.DateTime(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['character_id'], ['characters.character_id'], ),
    sa.PrimaryKeyConstraint('order_id', 'id')
    )
    op.create_index('idx_market_location', 'market_orders', ['location_id', 'type_id'], unique=False)
    op.create_index('idx_market_price_type', 'market_orders', ['type_id', 'is_buy_order', 'price'], unique=False)
    op.create_index('idx_market_type_region', 'market_orders', ['type_id', 'region_id'], unique=False)
    op.create_index(op.f('ix_market_orders_character_id'), 'market_orders', ['character_id'], unique=False)
    op.create_index(op.f('ix_market_orders_id'), 'market_orders', ['id'], unique=False)
    op.create_index(op.f('ix_market_orders_is_buy_order'), 'market_orders', ['is_buy_order'], unique=False)
    op.create_index(op.f('ix_market_orders_issued'), 'market_orders', ['issued'], unique=False)
    op.create_index(op.f('ix_market_orders_location_id'), 'market_orders', ['location_id'], unique=False)
    op.create_index(op.f('ix_market_orders_price'), 'market_orders', ['price'], unique=False)
    op.create_index(op.f('ix_market_orders_region_id'), 'market_orders', ['region_id'], unique=False)
    op.create_index(op.f('ix_market_orders_type_id'), 'market_orders', ['type_id'], unique=False)
    op.create_table('tokens',
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('character_id', sa.BigInteger(), nullable=False),
    sa.Column('access_token', sa.Text(), nullable=False),
    sa.Column('refresh_token', sa.Text(), nullable=False),
    sa.Column('token_type', sa.String(length=20), nullable=True),
    sa.Column('scopes', sa.Text(), nullable=False),
    sa.Column('expires_at', sa.DateTime(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('last_used_at', sa.DateTime(), nullable=True),
    sa.Column('use_count', sa.Integer(), nullable=True),
    sa.Column('last_refresh_at', sa.DateTime(), nullable=True),
    sa.Column('refresh_count', sa.Integer(), nullable=True),
    sa.Column('refresh_error_count', sa.Integer(), nullable=True),
    sa.Column('refresh_error_message', sa.Text(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['character_id'], ['characters.character_id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_token_character', 'tokens', ['character_id', 'is_active'], unique=False)
    op.create_index('idx_token_expires', 'tokens', ['expires_at', 'is_active'], unique=False)
    op.create_index('idx_token_refresh', 'tokens', ['last_refresh_at', 'refresh_error_count'], unique=False)
    op.create_index(op.f('ix_tokens_character_id'), 'tokens', ['character_id'], unique=False)
    op.create_index(op.f('ix_tokens_expires_at'), 'tokens', ['expires_at'], unique=False)
    op.create_index(op.f('ix_tokens_id'), 'tokens', ['id'], unique=False)
    op.create_index(op.f('ix_tokens_is_active'), 'tokens', ['is_active'], unique=False)
    op.create_index(op.f('ix_tokens_user_id'), 'tokens', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """降级数据库"""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_tokens_user_id'), table_name='tokens')
    op.drop_index(op.f('ix_tokens_is_active'), table_name='tokens')
    op.drop_index(op.f('ix_tokens_id'), table_name='tokens')
    op.drop_index(op.f('ix_tokens_expires_at'), table_name='tokens')
    op.drop_index(op.f('ix_tokens_character_id'), table_name='tokens')
    op.drop_index('idx_token_refresh', table_name='tokens')
    op.drop_index('idx_token_expires', table_name='tokens')
    op.drop_index('idx_token_character', table_name='tokens')
    op.drop_table('tokens')
    op.drop_index(op.f('ix_market_orders_type_id'), table_name='market_orders')
    op.drop_index(op.f('ix_market_orders_region_id'), table_name='market_orders')
    op.drop_index(op.f('ix_market_orders_price'), table_name='market_orders')
    op.drop_index(op.f('ix_market_orders_location_id'), table_name='market_orders')
    op.drop_index(op.f('ix_market_orders_issued'), table_name='market_orders')
    op.drop_index(op.f('ix_market_orders_is_buy_order'), table_name='market_orders')
    op.drop_index(op.f('ix_market_orders_id'), table_name='market_orders')
    op.drop_index(op.f('ix_market_orders_character_id'), table_name='market_orders')
    op.drop_index('idx_market_type_region', table_name='market_orders')
    op.drop_index('idx_market_price_type', table_name='market_orders')
    op.drop_index('idx_market_location', table_name='market_orders')
    op.drop_table('market_orders')
    op.drop_index(op.f('ix_industry_jobs_status'), table_name='industry_jobs')
    op.drop_index(op.f('ix_industry_jobs_start_date'), table_name='industry_jobs')
    op.drop_index(op.f('ix_industry_jobs_product_type_id'), table_name='industry_jobs')
    op.drop_index(op.f('ix_industry_jobs_id'), table_name='industry_jobs')
    op.drop_index(op.f('ix_industry_jobs_facility_id'), table_name='industry_jobs')
    op.drop_index(op.f('ix_industry_jobs_end_date'), table_name='industry_jobs')
    op.drop_index(op.f('ix_industry_jobs_corporation_id'), table_name='industry_jobs')
    op.drop_index(op.f('ix_industry_jobs_character_id'), table_name='industry_jobs')
    op.drop_index(op.f('ix_industry_jobs_blueprint_type_id'), table_name='industry_jobs')
    op.drop_index(op.f('ix_industry_jobs_activity_id'), table_name='industry_jobs')
    op.drop_index('idx_industry_status_date', table_name='industry_jobs')
    op.drop_index('idx_industry_facility', table_name='industry_jobs')
    op.drop_index('idx_industry_activity', table_name='industry_jobs')
    op.drop_table('industry_jobs')
    op.drop_index(op.f('ix_character_skills_skill_id'), table_name='character_skills')
    op.drop_index(op.f('ix_character_skills_id'), table_name='character_skills')
    op.drop_index(op.f('ix_character_skills_character_id'), table_name='character_skills')
    op.drop_index('idx_skill_level', table_name='character_skills')
    op.drop_table('character_skills')
    op.drop_index(op.f('ix_character_skill_queue_skill_id'), table_name='character_skill_queue')
    op.drop_index(op.f('ix_character_skill_queue_id'), table_name='character_skill_queue')
    op.drop_index(op.f('ix_character_skill_queue_character_id'), table_name='character_skill_queue')
    op.drop_index('idx_skill_queue_time', table_name='character_skill_queue')
    op.drop_index('idx_skill_queue_position', table_name='character_skill_queue')
    op.drop_table('character_skill_queue')
    op.drop_index(op.f('ix_character_mails_timestamp'), table_name='character_mails')
    op.drop_index(op.f('ix_character_mails_id'), table_name='character_mails')
    op.drop_index(op.f('ix_character_mails_character_id'), table_name='character_mails')
    op.drop_index('idx_mail_timestamp', table_name='character_mails')
    op.drop_index('idx_mail_read_status', table_name='character_mails')
    op.drop_table('character_mails')
    op.drop_index(op.f('ix_character_clones_id'), table_name='character_clones')
    op.drop_index(op.f('ix_character_clones_character_id'), table_name='character_clones')
    op.drop_table('character_clones')
    op.drop_index(op.f('ix_character_attributes_id'), table_name='character_attributes')
    op.drop_table('character_attributes')
    op.drop_index(op.f('ix_assets_type_id'), table_name='assets')
    op.drop_index(op.f('ix_assets_location_id'), table_name='assets')
    op.drop_index(op.f('ix_assets_id'), table_name='assets')
    op.drop_index(op.f('ix_assets_corporation_id'), table_name='assets')
    op.drop_index(op.f('ix_assets_character_id'), table_name='assets')
    op.drop_index('idx_asset_value', table_name='assets')
    op.drop_index('idx_asset_sync', table_name='assets')
    op.drop_index('idx_asset_owner_type', table_name='assets')
    op.drop_index('idx_asset_location', table_name='assets')
    op.drop_table('assets')
    op.drop_index(op.f('ix_asset_value_history_id'), table_name='asset_value_history')
    op.drop_index(op.f('ix_asset_value_history_date'), table_name='asset_value_history')
    op.drop_index(op.f('ix_asset_value_history_character_id'), table_name='asset_value_history')
    op.drop_index('idx_value_history_date', table_name='asset_value_history')
    op.drop_table('asset_value_history')
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_index(op.f('ix_users_main_character_id'), table_name='users')
    op.drop_index(op.f('ix_users_is_active'), table_name='users')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_index('idx_user_main_char', table_name='users')
    op.drop_index('idx_user_active', table_name='users')
    op.drop_table('users')
    op.drop_index(op.f('ix_universe_types_name'), table_name='universe_types')
    op.drop_index(op.f('ix_universe_types_market_group_id'), table_name='universe_types')
    op.drop_index(op.f('ix_universe_types_id'), table_name='universe_types')
    op.drop_index(op.f('ix_universe_types_group_id'), table_name='universe_types')
    op.drop_index(op.f('ix_universe_types_category_id'), table_name='universe_types')
    op.drop_index('idx_type_published', table_name='universe_types')
    op.drop_index('idx_type_market_group', table_name='universe_types')
    op.drop_index('idx_type_group_category', table_name='universe_types')
    op.drop_table('universe_types')
    op.drop_index(op.f('ix_universe_systems_security_status'), table_name='universe_systems')
    op.drop_index(op.f('ix_universe_systems_region_id'), table_name='universe_systems')
    op.drop_index(op.f('ix_universe_systems_name'), table_name='universe_systems')
    op.drop_index(op.f('ix_universe_systems_id'), table_name='universe_systems')
    op.drop_index(op.f('ix_universe_systems_constellation_id'), table_name='universe_systems')
    op.drop_index('idx_system_security', table_name='universe_systems')
    op.drop_index('idx_system_region', table_name='universe_systems')
    op.drop_index('idx_system_constellation', table_name='universe_systems')
    op.drop_table('universe_systems')
    op.drop_index(op.f('ix_universe_regions_name'), table_name='universe_regions')
    op.drop_index(op.f('ix_universe_regions_id'), table_name='universe_regions')
    op.drop_table('universe_regions')
    op.drop_index(op.f('ix_universe_constellations_region_id'), table_name='universe_constellations')
    op.drop_index(op.f('ix_universe_constellations_name'), table_name='universe_constellations')
    op.drop_index(op.f('ix_universe_constellations_id'), table_name='universe_constellations')
    op.drop_index('idx_constellation_region', table_name='universe_constellations')
    op.drop_table('universe_constellations')
    op.drop_index(op.f('ix_market_history_id'), table_name='market_history')
    op.drop_index('idx_history_type_date', table_name='market_history')
    op.drop_index('idx_history_region_date', table_name='market_history')
    op.drop_table('market_history')
    op.drop_index(op.f('ix_corporations_ticker'), table_name='corporations')
    op.drop_index(op.f('ix_corporations_name'), table_name='corporations')
    op.drop_index(op.f('ix_corporations_id'), table_name='corporations')
    op.drop_index(op.f('ix_corporations_alliance_id'), table_name='corporations')
    op.drop_index('idx_corp_ceo', table_name='corporations')
    op.drop_index('idx_corp_alliance', table_name='corporations')
    op.drop_table('corporations')
    op.drop_index(op.f('ix_characters_name'), table_name='characters')
    op.drop_index(op.f('ix_characters_is_online'), table_name='characters')
    op.drop_index(op.f('ix_characters_id'), table_name='characters')
    op.drop_index(op.f('ix_characters_current_system_id'), table_name='characters')
    op.drop_index(op.f('ix_characters_corporation_id'), table_name='characters')
    op.drop_index(op.f('ix_characters_character_id'), table_name='characters')
    op.drop_index(op.f('ix_characters_alliance_id'), table_name='characters')
    op.drop_index('idx_character_sync', table_name='characters')
    op.drop_index('idx_character_location', table_name='characters')
    op.drop_index('idx_character_corp_alliance', table_name='characters')
    op.drop_table('characters')
    op.drop_index(op.f('ix_asset_locations_system_id'), table_name='asset_locations')
    op.drop_index(op.f('ix_asset_locations_region_id'), table_name='asset_locations')
    op.drop_index(op.f('ix_asset_locations_id'), table_name='asset_locations')
    op.drop_index('idx_location_system', table_name='asset_locations')
    op.drop_index('idx_location_region', table_name='asset_locations')
    op.drop_table('asset_locations')
    op.drop_index(op.f('ix_alliances_ticker'), table_name='alliances')
    op.drop_index(op.f('ix_alliances_name'), table_name='alliances')
    op.drop_index(op.f('ix_alliances_id'), table_name='alliances')
    op.drop_index('idx_alliance_executor', table_name='alliances')
    op.drop_index('idx_alliance_creator', table_name='alliances')
    op.drop_table('alliances')
    # ### end Alembic commands ###
