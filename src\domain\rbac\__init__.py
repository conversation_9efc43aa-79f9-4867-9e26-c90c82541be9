"""
RBAC权限管理领域模块
"""

from .aggregates import Role, Permission, UserRole
from .value_objects import (
    RoleId, PermissionId, RoleName, PermissionName,
    ResourceType, ActionType, PermissionScope
)
from .events import (
    RoleCreatedEvent, RoleUpdatedEvent, RoleDeletedEvent,
    PermissionCreatedEvent, PermissionUpdatedEvent,
    UserRoleAssignedEvent, UserRoleRevokedEvent,
    PermissionGrantedEvent, PermissionRevokedEvent
)
from .services import RBACDomainService, PermissionChecker
from .repositories import RoleRepository, PermissionRepository, UserRoleRepository
from .exceptions import (
    RBACDomainError, RoleNotFoundError, PermissionNotFoundError,
    DuplicateRoleError, DuplicatePermissionError, InsufficientPermissionError,
    CircularRoleHierarchyError, InvalidPermissionScopeError
)

__all__ = [
    # 聚合根
    'Role',
    'Permission', 
    'UserRole',
    
    # 值对象
    'RoleId',
    'PermissionId',
    'RoleName',
    'PermissionName',
    'ResourceType',
    'ActionType',
    'PermissionScope',
    
    # 领域事件
    'RoleCreatedEvent',
    'RoleUpdatedEvent',
    'RoleDeletedEvent',
    'PermissionCreatedEvent',
    'PermissionUpdatedEvent',
    'UserRoleAssignedEvent',
    'UserRoleRevokedEvent',
    'PermissionGrantedEvent',
    'PermissionRevokedEvent',
    
    # 领域服务
    'RBACDomainService',
    'PermissionChecker',
    
    # 仓储接口
    'RoleRepository',
    'PermissionRepository',
    'UserRoleRepository',
    
    # 异常
    'RBACDomainError',
    'RoleNotFoundError',
    'PermissionNotFoundError',
    'DuplicateRoleError',
    'DuplicatePermissionError',
    'InsufficientPermissionError',
    'CircularRoleHierarchyError',
    'InvalidPermissionScopeError'
]
