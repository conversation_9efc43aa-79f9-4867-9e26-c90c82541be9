"""
Pickle文件存储管理器
"""
import os
import pickle
import gzip
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Optional, Dict, List
import hashlib

from ..config import settings
from ..config.logging import get_logger

logger = get_logger(__name__)


class PickleStorage:
    """Pickle文件存储管理器"""
    
    def __init__(self, base_path: str = "data/pickle"):
        self.base_path = Path(base_path)
        self.base_path.mkdir(parents=True, exist_ok=True)
        
        # 创建子目录
        self.cache_dir = self.base_path / "cache"
        self.models_dir = self.base_path / "models"
        self.backup_dir = self.base_path / "backup"
        
        for dir_path in [self.cache_dir, self.models_dir, self.backup_dir]:
            dir_path.mkdir(exist_ok=True)
    
    def _get_file_path(self, category: str, key: str, compressed: bool = False) -> Path:
        """获取文件路径"""
        # 使用MD5哈希避免文件名过长或包含特殊字符
        safe_key = hashlib.md5(key.encode()).hexdigest()
        
        if category == "cache":
            base_dir = self.cache_dir
        elif category == "models":
            base_dir = self.models_dir
        elif category == "backup":
            base_dir = self.backup_dir
        else:
            base_dir = self.base_path / category
            base_dir.mkdir(exist_ok=True)
        
        extension = ".pkl.gz" if compressed else ".pkl"
        return base_dir / f"{safe_key}{extension}"
    
    def save(self, category: str, key: str, data: Any, 
             compressed: bool = False, ttl: Optional[int] = None) -> bool:
        """保存数据到Pickle文件"""
        try:
            file_path = self._get_file_path(category, key, compressed)
            
            # 准备元数据
            metadata = {
                "key": key,
                "created_at": datetime.utcnow(),
                "expires_at": datetime.utcnow() + timedelta(seconds=ttl) if ttl else None,
                "compressed": compressed,
                "data": data
            }
            
            # 保存文件
            if compressed:
                with gzip.open(file_path, 'wb') as f:
                    pickle.dump(metadata, f, protocol=pickle.HIGHEST_PROTOCOL)
            else:
                with open(file_path, 'wb') as f:
                    pickle.dump(metadata, f, protocol=pickle.HIGHEST_PROTOCOL)
            
            logger.debug("Pickle文件保存成功", 
                        category=category, 
                        key=key, 
                        file_path=str(file_path),
                        compressed=compressed)
            return True
            
        except Exception as e:
            logger.error("Pickle文件保存失败", 
                        category=category, 
                        key=key, 
                        error=str(e))
            return False
    
    def load(self, category: str, key: str, compressed: bool = False) -> Optional[Any]:
        """从Pickle文件加载数据"""
        try:
            file_path = self._get_file_path(category, key, compressed)
            
            if not file_path.exists():
                return None
            
            # 加载文件
            if compressed:
                with gzip.open(file_path, 'rb') as f:
                    metadata = pickle.load(f)
            else:
                with open(file_path, 'rb') as f:
                    metadata = pickle.load(f)
            
            # 检查过期时间
            if metadata.get("expires_at"):
                if datetime.utcnow() > metadata["expires_at"]:
                    # 文件已过期，删除它
                    self.delete(category, key, compressed)
                    return None
            
            logger.debug("Pickle文件加载成功", 
                        category=category, 
                        key=key, 
                        file_path=str(file_path))
            
            return metadata["data"]
            
        except Exception as e:
            logger.error("Pickle文件加载失败", 
                        category=category, 
                        key=key, 
                        error=str(e))
            return None
    
    def delete(self, category: str, key: str, compressed: bool = False) -> bool:
        """删除Pickle文件"""
        try:
            file_path = self._get_file_path(category, key, compressed)
            
            if file_path.exists():
                file_path.unlink()
                logger.debug("Pickle文件删除成功", 
                            category=category, 
                            key=key, 
                            file_path=str(file_path))
                return True
            return False
            
        except Exception as e:
            logger.error("Pickle文件删除失败", 
                        category=category, 
                        key=key, 
                        error=str(e))
            return False
    
    def exists(self, category: str, key: str, compressed: bool = False) -> bool:
        """检查文件是否存在"""
        file_path = self._get_file_path(category, key, compressed)
        return file_path.exists()
    
    def list_keys(self, category: str) -> List[str]:
        """列出分类下的所有键"""
        try:
            if category == "cache":
                base_dir = self.cache_dir
            elif category == "models":
                base_dir = self.models_dir
            elif category == "backup":
                base_dir = self.backup_dir
            else:
                base_dir = self.base_path / category
            
            if not base_dir.exists():
                return []
            
            keys = []
            for file_path in base_dir.glob("*.pkl*"):
                try:
                    # 尝试加载文件获取原始键名
                    if file_path.suffix == ".gz":
                        with gzip.open(file_path, 'rb') as f:
                            metadata = pickle.load(f)
                    else:
                        with open(file_path, 'rb') as f:
                            metadata = pickle.load(f)
                    
                    keys.append(metadata.get("key", file_path.stem))
                except:
                    # 如果无法加载，使用文件名
                    keys.append(file_path.stem)
            
            return keys
            
        except Exception as e:
            logger.error("列出Pickle文件键失败", category=category, error=str(e))
            return []
    
    def cleanup_expired(self, category: str = None) -> int:
        """清理过期文件"""
        cleaned_count = 0
        
        try:
            if category:
                dirs_to_check = [self.base_path / category]
            else:
                dirs_to_check = [self.cache_dir, self.models_dir, self.backup_dir]
            
            for dir_path in dirs_to_check:
                if not dir_path.exists():
                    continue
                
                for file_path in dir_path.glob("*.pkl*"):
                    try:
                        # 检查文件是否过期
                        if file_path.suffix == ".gz":
                            with gzip.open(file_path, 'rb') as f:
                                metadata = pickle.load(f)
                        else:
                            with open(file_path, 'rb') as f:
                                metadata = pickle.load(f)
                        
                        expires_at = metadata.get("expires_at")
                        if expires_at and datetime.utcnow() > expires_at:
                            file_path.unlink()
                            cleaned_count += 1
                            logger.debug("清理过期Pickle文件", file_path=str(file_path))
                    
                    except Exception as e:
                        logger.warning("检查Pickle文件过期失败", 
                                     file_path=str(file_path), 
                                     error=str(e))
            
            if cleaned_count > 0:
                logger.info("清理过期Pickle文件完成", cleaned_count=cleaned_count)
            
            return cleaned_count
            
        except Exception as e:
            logger.error("清理过期Pickle文件失败", error=str(e))
            return 0
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        try:
            stats = {
                "total_files": 0,
                "total_size": 0,
                "categories": {}
            }
            
            for category_dir in [self.cache_dir, self.models_dir, self.backup_dir]:
                if not category_dir.exists():
                    continue
                
                category_name = category_dir.name
                category_stats = {
                    "files": 0,
                    "size": 0,
                    "compressed_files": 0
                }
                
                for file_path in category_dir.glob("*.pkl*"):
                    category_stats["files"] += 1
                    category_stats["size"] += file_path.stat().st_size
                    
                    if file_path.suffix == ".gz":
                        category_stats["compressed_files"] += 1
                
                stats["categories"][category_name] = category_stats
                stats["total_files"] += category_stats["files"]
                stats["total_size"] += category_stats["size"]
            
            return stats
            
        except Exception as e:
            logger.error("获取Pickle存储统计失败", error=str(e))
            return {}


# 全局Pickle存储实例
pickle_storage = PickleStorage()


class ModelStorage:
    """模型存储管理器"""
    
    def __init__(self):
        self.storage = pickle_storage
    
    def save_model(self, model_name: str, model: Any, version: str = "latest") -> bool:
        """保存模型"""
        key = f"{model_name}:{version}"
        return self.storage.save("models", key, model, compressed=True)
    
    def load_model(self, model_name: str, version: str = "latest") -> Optional[Any]:
        """加载模型"""
        key = f"{model_name}:{version}"
        return self.storage.load("models", key, compressed=True)
    
    def save_analysis_result(self, character_id: int, analysis_type: str, result: Any) -> bool:
        """保存分析结果"""
        key = f"analysis:{character_id}:{analysis_type}"
        return self.storage.save("cache", key, result, compressed=True, ttl=3600)
    
    def load_analysis_result(self, character_id: int, analysis_type: str) -> Optional[Any]:
        """加载分析结果"""
        key = f"analysis:{character_id}:{analysis_type}"
        return self.storage.load("cache", key, compressed=True)


# 全局模型存储实例
model_storage = ModelStorage()
