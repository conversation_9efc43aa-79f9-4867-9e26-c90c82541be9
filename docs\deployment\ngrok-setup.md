# ngrok集成指南

## 📋 概述

EVE Online Assistant已集成ngrok隧道管理，可以自动创建公网隧道并管理EVE SSO回调URL，无需手动配置端口转发或公网IP。

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install pyngrok
```

### 2. 配置ngrok
在 `.env` 文件中配置ngrok：
```env
# ngrok配置
NGROK_ENABLED=true
NGROK_AUTH_TOKEN=""              # 可选：ngrok认证令牌
NGROK_REGION="us"               # ngrok区域
NGROK_SUBDOMAIN=""              # 可选：自定义子域名
NGROK_DOMAIN=""                 # 可选：自定义域名
NGROK_AUTO_UPDATE_CALLBACK=true # 自动更新回调URL
SERVER_PORT=8000                # 服务器端口
```

### 3. 启动应用
使用集成的启动脚本：
```bash
# 方式1：使用快速启动脚本
python start.py

# 方式2：使用详细启动脚本
python scripts/start_with_ngrok.py
```

## 🔧 配置选项

### 基础配置
```env
NGROK_ENABLED=true              # 启用ngrok隧道
SERVER_PORT=8000                # 本地服务器端口
NGROK_AUTO_UPDATE_CALLBACK=true # 自动更新EVE SSO回调URL
```

### 高级配置
```env
NGROK_AUTH_TOKEN="your-token"   # ngrok认证令牌（免费版可选）
NGROK_REGION="us"              # 服务器区域：us, eu, ap, au, sa, jp, in
NGROK_SUBDOMAIN="myapp"        # 自定义子域名（需要付费账户）
NGROK_DOMAIN="myapp.com"       # 自定义域名（需要付费账户）
```

### 区域选择
- `us` - 美国（默认）
- `eu` - 欧洲
- `ap` - 亚太
- `au` - 澳大利亚
- `sa` - 南美
- `jp` - 日本
- `in` - 印度

## 🎯 功能特性

### 自动化功能
- ✅ **自动隧道管理**：启动时自动创建ngrok隧道
- ✅ **回调URL更新**：自动更新.env文件中的EVE SSO回调URL
- ✅ **健康监控**：自动监控隧道状态，断线重连
- ✅ **优雅关闭**：Ctrl+C时自动清理隧道资源

### 管理功能
- ✅ **API管理**：通过REST API管理隧道
- ✅ **状态监控**：实时查看隧道状态和统计
- ✅ **配置管理**：动态查看和更新配置
- ✅ **错误处理**：完善的错误处理和日志记录

## 📊 API端点

### 隧道管理
```bash
# 获取ngrok状态
GET /ngrok/status

# 启动隧道
POST /ngrok/start

# 停止隧道
POST /ngrok/stop

# 重启隧道
POST /ngrok/restart

# 获取隧道详细信息
GET /ngrok/info
```

### 配置管理
```bash
# 获取ngrok配置
GET /ngrok/config

# 更新回调URL
POST /ngrok/update-callback

# 健康检查
GET /ngrok/health
```

## 💻 使用示例

### Python代码中使用
```python
from src.infrastructure.ngrok import ngrok_manager

# 启动隧道
public_url = ngrok_manager.start_tunnel(8000)
print(f"公网地址: {public_url}")

# 获取状态
status = ngrok_manager.get_status()
print(f"隧道状态: {status}")

# 停止隧道
ngrok_manager.stop_tunnel()
```

### 上下文管理器
```python
from src.infrastructure.ngrok import NgrokContextManager

# 自动管理隧道生命周期
with NgrokContextManager(port=8000) as ngrok:
    print(f"隧道地址: {ngrok.public_url}")
    # 在这里运行您的应用
    # 退出时自动清理隧道
```

### API调用示例
```bash
# 启动隧道
curl -X POST http://localhost:8000/ngrok/start

# 获取状态
curl http://localhost:8000/ngrok/status

# 更新回调URL
curl -X POST http://localhost:8000/ngrok/update-callback
```

## 🔐 EVE SSO集成

### 自动回调URL更新
当启用 `NGROK_AUTO_UPDATE_CALLBACK=true` 时，系统会：

1. 启动ngrok隧道
2. 获取公网URL
3. 自动更新.env文件中的 `EVE_SSO_CALLBACK_URL`
4. 提醒您在EVE Developer Portal中更新回调URL

### 手动更新流程
1. 启动应用后，记录显示的公网URL
2. 登录 [EVE Developer Portal](https://developers.eveonline.com/)
3. 编辑您的应用配置
4. 将回调URL更新为：`https://your-ngrok-url.ngrok.io/auth/callback`

## 🛠️ 故障排除

### 常见问题

#### 1. pyngrok未安装
```
❌ pyngrok not available
解决：pip install pyngrok
```

#### 2. ngrok未启用
```
❌ ngrok is disabled
解决：在.env中设置 NGROK_ENABLED=true
```

#### 3. 隧道启动失败
```
❌ Failed to start ngrok tunnel
可能原因：
- 端口被占用
- ngrok服务不可用
- 网络连接问题
- 认证令牌无效（如果使用）
```

#### 4. 回调URL更新失败
```
❌ Failed to update callback URL
检查：
- .env文件权限
- 文件路径是否正确
- 隧道是否正常运行
```

### 调试方法

#### 查看日志
```bash
# 启动时会显示详细日志
python start.py

# 查看日志文件
tail -f logs/app.log
```

#### 检查隧道状态
```bash
# 通过API检查
curl http://localhost:8000/ngrok/status

# 通过ngrok web界面
# 访问 http://127.0.0.1:4040
```

#### 手动测试
```python
from src.infrastructure.ngrok import ngrok_manager

# 检查可用性
print(f"ngrok可用: {ngrok_manager.is_available()}")

# 启动隧道
url = ngrok_manager.start_tunnel(8000)
print(f"隧道URL: {url}")

# 健康检查
healthy = ngrok_manager.health_check()
print(f"隧道健康: {healthy}")
```

## 🔒 安全考虑

### 免费版限制
- 随机子域名
- 隧道会话限制
- 带宽限制

### 付费版优势
- 自定义子域名
- 自定义域名
- 更高带宽
- 更多并发隧道

### 最佳实践
1. **生产环境**：使用付费版获得稳定的域名
2. **开发环境**：免费版足够使用
3. **安全性**：不要在公共网络上暴露敏感数据
4. **监控**：定期检查隧道状态和访问日志

## 📈 性能优化

### 区域选择
选择距离您最近的ngrok区域以获得最佳性能：
```env
# 亚洲用户
NGROK_REGION="ap"

# 欧洲用户  
NGROK_REGION="eu"

# 美国用户
NGROK_REGION="us"
```

### 连接监控
系统会自动监控隧道连接：
- 每30秒检查一次隧道状态
- 连接断开时自动重连
- 记录连接统计和错误日志

## 🎉 总结

通过集成ngrok，EVE Online Assistant现在可以：

1. **自动化部署**：无需手动配置网络
2. **动态回调URL**：自动管理EVE SSO回调
3. **开发友好**：快速启动和测试
4. **生产就绪**：支持自定义域名和监控
5. **易于管理**：完整的API和Web界面

这个集成大大简化了EVE Online Assistant的部署和使用，特别适合开发和测试环境。
