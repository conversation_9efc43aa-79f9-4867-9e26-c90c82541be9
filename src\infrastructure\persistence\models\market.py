"""
市场相关数据库模型
"""
from sqlalchemy import (
    Column, Integer, String, BigInteger, Float, Boolean,
    DateTime, ForeignKey, Index
)
from sqlalchemy.orm import relationship

from .base import BaseModel


class MarketOrderModel(BaseModel):
    """市场订单模型"""
    __tablename__ = "market_orders"
    
    order_id = Column(BigInteger, primary_key=True)
    character_id = Column(BigInteger, ForeignKey('characters.character_id'), nullable=True, index=True)
    
    # 订单基本信息
    type_id = Column(Integer, nullable=False, index=True)
    location_id = Column(BigInteger, nullable=False, index=True)
    region_id = Column(Integer, nullable=False, index=True)
    
    # 订单详情
    volume_total = Column(Integer, nullable=False)
    volume_remain = Column(Integer, nullable=False)
    min_volume = Column(Integer, nullable=False, default=1)
    price = Column(Float, nullable=False, index=True)
    is_buy_order = Column(Boolean, nullable=False, index=True)
    
    # 时间信息
    duration = Column(Integer, nullable=False)
    issued = Column(DateTime, nullable=False, index=True)
    
    # 订单范围
    range = Column(String(20), nullable=False)  # station, region, etc.
    
    # 数据同步
    last_sync_at = Column(DateTime, nullable=True)
    
    # 关联关系
    character = relationship("CharacterModel", back_populates="market_orders")
    
    # 索引
    __table_args__ = (
        Index('idx_market_type_region', 'type_id', 'region_id'),
        Index('idx_market_price_type', 'type_id', 'is_buy_order', 'price'),
        Index('idx_market_location', 'location_id', 'type_id'),
    )


class MarketHistoryModel(BaseModel):
    """市场历史数据模型"""
    __tablename__ = "market_history"
    
    region_id = Column(Integer, nullable=False, primary_key=True)
    type_id = Column(Integer, nullable=False, primary_key=True)
    date = Column(DateTime, nullable=False, primary_key=True)
    
    # 价格统计
    order_count = Column(Integer, nullable=False)
    volume = Column(BigInteger, nullable=False)
    highest = Column(Float, nullable=False)
    average = Column(Float, nullable=False)
    lowest = Column(Float, nullable=False)
    
    # 索引
    __table_args__ = (
        Index('idx_history_type_date', 'type_id', 'date'),
        Index('idx_history_region_date', 'region_id', 'date'),
    )
