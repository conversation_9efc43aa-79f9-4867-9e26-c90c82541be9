"""
认证领域事件
"""
from dataclasses import dataclass
from datetime import datetime
from typing import List, Optional
from uuid import UUID

from ..shared.base_entity import DomainEvent
from ..shared.value_objects import UserId, CharacterId


@dataclass(frozen=True)
class UserRegisteredEvent(DomainEvent):
    """用户注册事件"""
    user_id: UserId
    email: str
    username: str
    created_at: datetime


@dataclass(frozen=True)
class UserAuthenticatedEvent(DomainEvent):
    """用户认证成功事件"""
    user_id: UserId
    authenticated_at: datetime
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None


@dataclass(frozen=True)
class UserAuthenticationFailedEvent(DomainEvent):
    """用户认证失败事件"""
    user_id: UserId
    failed_at: datetime
    failure_reason: str
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None


@dataclass(frozen=True)
class UserAccountLockedEvent(DomainEvent):
    """用户账户锁定事件"""
    user_id: UserId
    locked_at: datetime
    locked_until: datetime
    reason: str


@dataclass(frozen=True)
class UserAccountUnlockedEvent(DomainEvent):
    """用户账户解锁事件"""
    user_id: UserId
    unlocked_at: datetime


@dataclass(frozen=True)
class CharacterBoundEvent(DomainEvent):
    """角色绑定事件"""
    user_id: UserId
    character_id: CharacterId
    scopes: List[str]
    bound_at: datetime


@dataclass(frozen=True)
class CharacterUnboundEvent(DomainEvent):
    """角色解绑事件"""
    user_id: UserId
    character_id: CharacterId
    unbound_at: datetime


@dataclass(frozen=True)
class TokenRefreshedEvent(DomainEvent):
    """令牌刷新事件"""
    user_id: UserId
    character_id: CharacterId
    refreshed_at: datetime


@dataclass(frozen=True)
class TokenRevokedEvent(DomainEvent):
    """令牌撤销事件"""
    user_id: UserId
    character_id: CharacterId
    revoked_at: datetime
    reason: str


@dataclass(frozen=True)
class SessionCreatedEvent(DomainEvent):
    """会话创建事件"""
    user_id: UserId
    session_id: UUID
    character_id: Optional[CharacterId]
    created_at: datetime
    expires_at: datetime


@dataclass(frozen=True)
class SessionExpiredEvent(DomainEvent):
    """会话过期事件"""
    user_id: UserId
    session_id: UUID
    expired_at: datetime


@dataclass(frozen=True)
class SessionInvalidatedEvent(DomainEvent):
    """会话失效事件"""
    user_id: UserId
    session_id: UUID
    invalidated_at: datetime
    reason: str


@dataclass(frozen=True)
class UserLoggedOutEvent(DomainEvent):
    """用户登出事件"""
    user_id: UserId
    logged_out_at: datetime
    all_sessions: bool = False


@dataclass(frozen=True)
class PasswordChangedEvent(DomainEvent):
    """密码修改事件"""
    user_id: UserId
    changed_at: datetime
    ip_address: Optional[str] = None


@dataclass(frozen=True)
class EmailVerifiedEvent(DomainEvent):
    """邮箱验证事件"""
    user_id: UserId
    email: str
    verified_at: datetime


@dataclass(frozen=True)
class PasswordResetRequestedEvent(DomainEvent):
    """密码重置请求事件"""
    user_id: UserId
    email: str
    requested_at: datetime
    reset_token: str
    expires_at: datetime


@dataclass(frozen=True)
class PasswordResetCompletedEvent(DomainEvent):
    """密码重置完成事件"""
    user_id: UserId
    completed_at: datetime
    reset_token: str


@dataclass(frozen=True)
class TwoFactorEnabledEvent(DomainEvent):
    """双因素认证启用事件"""
    user_id: UserId
    enabled_at: datetime
    method: str  # totp, sms, email


@dataclass(frozen=True)
class TwoFactorDisabledEvent(DomainEvent):
    """双因素认证禁用事件"""
    user_id: UserId
    disabled_at: datetime
    method: str


@dataclass(frozen=True)
class ApiKeyCreatedEvent(DomainEvent):
    """API密钥创建事件"""
    user_id: UserId
    key_id: str
    key_name: str
    scopes: List[str]
    created_at: datetime
    expires_at: Optional[datetime]


@dataclass(frozen=True)
class ApiKeyRevokedEvent(DomainEvent):
    """API密钥撤销事件"""
    user_id: UserId
    key_id: str
    revoked_at: datetime
    reason: str


@dataclass(frozen=True)
class SuspiciousActivityDetectedEvent(DomainEvent):
    """可疑活动检测事件"""
    user_id: UserId
    activity_type: str
    detected_at: datetime
    details: dict
    risk_level: str  # low, medium, high, critical


@dataclass(frozen=True)
class SecurityAlertEvent(DomainEvent):
    """安全警报事件"""
    user_id: UserId
    alert_type: str
    severity: str
    message: str
    triggered_at: datetime
    metadata: dict
