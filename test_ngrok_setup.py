#!/usr/bin/env python3
"""
测试ngrok设置功能
"""
import subprocess
import sys
import time
import requests

def test_ngrok_version():
    """测试ngrok版本"""
    print("🧪 测试ngrok版本...")
    
    try:
        result = subprocess.run(['ngrok', 'version'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ ngrok已安装: {version}")
            return True
        else:
            print("❌ ngrok未安装或无法运行")
            return False
    except (FileNotFoundError, subprocess.TimeoutExpired):
        print("❌ ngrok未找到")
        return False
    except Exception as e:
        print(f"❌ 检查ngrok时出错: {e}")
        return False

def test_env_file():
    """测试.env文件"""
    print("\n🧪 测试.env文件...")
    
    from pathlib import Path
    env_file = Path('.env')
    
    if not env_file.exists():
        print("❌ .env文件不存在")
        return False
    
    try:
        with open(env_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'EVE_SSO_CALLBACK_URL=' in content:
            print("✅ EVE_SSO_CALLBACK_URL配置存在")
            
            # 提取当前配置的URL
            for line in content.split('\n'):
                if line.startswith('EVE_SSO_CALLBACK_URL='):
                    url = line.split('=', 1)[1].strip('"')
                    print(f"   当前配置: {url}")
                    
                    if 'ngrok' in url:
                        print("✅ 检测到ngrok URL配置")
                    elif 'localhost' in url:
                        print("⚠️  检测到localhost URL配置")
                    else:
                        print("ℹ️  其他URL配置")
                    break
            
            return True
        else:
            print("❌ EVE_SSO_CALLBACK_URL配置不存在")
            return False
    except Exception as e:
        print(f"❌ 读取.env文件失败: {e}")
        return False

def test_production_script_import():
    """测试production-start.py脚本导入"""
    print("\n🧪 测试production-start.py脚本导入...")
    
    try:
        # 测试脚本语法
        result = subprocess.run([
            sys.executable, '-m', 'py_compile', 
            'scripts/deployment/production-start.py'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ 脚本语法检查通过")
        else:
            print(f"❌ 脚本语法错误: {result.stderr}")
            return False
        
        # 测试模块导入
        sys.path.insert(0, 'scripts/deployment')
        try:
            import production_start
            print("✅ 脚本模块导入成功")
            
            # 测试关键函数是否存在
            functions_to_test = [
                'check_ngrok_installed',
                'start_ngrok_tunnel',
                'update_env_callback_url',
                'setup_ngrok'
            ]
            
            for func_name in functions_to_test:
                if hasattr(production_start, func_name):
                    print(f"✅ 函数 {func_name} 存在")
                else:
                    print(f"❌ 函数 {func_name} 不存在")
                    return False
            
            return True
        except Exception as e:
            print(f"❌ 脚本模块导入失败: {e}")
            return False
    except Exception as e:
        print(f"❌ 脚本测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 production-start.py ngrok功能测试")
    print("=" * 50)
    
    tests = [
        ("ngrok版本检查", test_ngrok_version),
        ("环境文件检查", test_env_file),
        ("脚本导入测试", test_production_script_import),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"⚠️  {test_name} 测试未通过")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！production-start.py ngrok功能应该可以正常工作")
        print("\n📝 使用方法:")
        print("   python scripts/deployment/production-start.py")
        print("\n🔧 功能说明:")
        print("   - 自动检查并启动ngrok隧道")
        print("   - 自动更新.env文件中的回调URL")
        print("   - 启动FastAPI服务器")
        print("   - 统一管理所有服务的生命周期")
        return 0
    else:
        print("⚠️  部分测试未通过，请检查相关配置")
        
        if not test_ngrok_version():
            print("\n💡 ngrok安装建议:")
            print("   1. 访问 https://ngrok.com/download")
            print("   2. 下载适合您系统的版本")
            print("   3. 将ngrok添加到系统PATH")
            print("   4. 运行 'ngrok authtoken <your-token>' 进行认证")
        
        return 1

if __name__ == "__main__":
    sys.exit(main())
