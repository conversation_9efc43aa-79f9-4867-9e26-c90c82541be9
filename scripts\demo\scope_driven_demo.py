#!/usr/bin/env python3
"""
基于Scope驱动的功能演示脚本
展示SeAT架构思想在我们项目中的实现
"""
import sys
import json
from pathlib import Path
from typing import List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.application.services.scope_driven_menu_service import menu_service
from src.application.services.scope_driven_data_service import data_service, DataCategory
from src.infrastructure.config import settings


class ScopeDrivenDemo:
    """基于Scope驱动的功能演示"""
    
    def __init__(self):
        self.all_scopes = settings.eve_sso_scopes.split()
        
    def run_demo(self):
        """运行完整演示"""
        print("🌀 SeAT架构思想演示：基于ESI Scopes的功能设计")
        print("=" * 80)
        
        # 1. 展示不同权限级别的功能差异
        self.demo_permission_levels()
        
        # 2. 展示动态菜单生成
        self.demo_dynamic_menu()
        
        # 3. 展示数据访问计划
        self.demo_data_access_plan()
        
        # 4. 展示缓存策略
        self.demo_cache_strategy()
        
        # 5. 展示权限推荐
        self.demo_scope_recommendations()
        
        print("\n🎉 演示完成！")
        print("这就是SeAT项目的核心架构思想：")
        print("  - 权限驱动的功能设计")
        print("  - 动态菜单和界面生成")
        print("  - 智能的数据访问规划")
        print("  - 基于使用模式的缓存策略")
    
    def demo_permission_levels(self):
        """演示不同权限级别"""
        print("\n📊 1. 不同权限级别的功能对比")
        print("-" * 50)
        
        # 定义不同的权限级别
        permission_levels = {
            "新手用户": [
                "esi-location.read_location.v1",
                "esi-skills.read_skills.v1",
                "esi-assets.read_assets.v1"
            ],
            "标准用户": [
                "esi-location.read_location.v1",
                "esi-location.read_online.v1",
                "esi-skills.read_skills.v1",
                "esi-skills.read_skillqueue.v1",
                "esi-assets.read_assets.v1",
                "esi-wallet.read_character_wallet.v1",
                "esi-mail.read_mail.v1",
                "esi-characters.read_contacts.v1"
            ],
            "企业用户": self.all_scopes[:30],  # 前30个权限
            "完整权限": self.all_scopes
        }
        
        for level, scopes in permission_levels.items():
            menu_items = menu_service.generate_menu_for_user(scopes)
            data_plan = data_service.generate_data_access_plan(scopes)
            
            print(f"\n{level} ({len(scopes)}个权限):")
            print(f"  可用功能模块: {len(menu_items)}")
            print(f"  数据端点: {data_plan['total_endpoints']}")
            print(f"  覆盖分类: {len(data_plan['categories'])}")
            
            # 显示主要功能模块
            main_modules = [item.title for item in menu_items[:3]]
            if main_modules:
                print(f"  主要模块: {', '.join(main_modules)}")
    
    def demo_dynamic_menu(self):
        """演示动态菜单生成"""
        print("\n🎮 2. 动态菜单生成演示")
        print("-" * 50)
        
        # 使用标准用户权限
        standard_scopes = [
            "esi-location.read_location.v1",
            "esi-skills.read_skills.v1",
            "esi-assets.read_assets.v1",
            "esi-wallet.read_character_wallet.v1",
            "esi-corporations.read_corporation_membership.v1"
        ]
        
        menu_items = menu_service.generate_menu_for_user(standard_scopes)
        
        print(f"基于 {len(standard_scopes)} 个权限生成的菜单结构:")
        
        for item in menu_items:
            print(f"\n📁 {item.title} ({item.icon})")
            if item.description:
                print(f"   描述: {item.description}")
            
            if item.children:
                for child in item.children:
                    status = "✅" if not child.required_scopes or all(s in standard_scopes for s in child.required_scopes) else "❌"
                    print(f"   {status} {child.title}")
                    if child.required_scopes:
                        print(f"      需要权限: {', '.join(child.required_scopes[:2])}{'...' if len(child.required_scopes) > 2 else ''}")
    
    def demo_data_access_plan(self):
        """演示数据访问计划"""
        print("\n📊 3. 数据访问计划演示")
        print("-" * 50)
        
        # 选择几个代表性的权限
        demo_scopes = [
            "esi-location.read_location.v1",
            "esi-skills.read_skills.v1",
            "esi-assets.read_assets.v1",
            "esi-wallet.read_character_wallet.v1",
            "esi-industry.read_character_jobs.v1",
            "esi-markets.read_character_orders.v1"
        ]
        
        plan = data_service.generate_data_access_plan(demo_scopes)
        
        print(f"基于 {len(demo_scopes)} 个权限的数据访问计划:")
        print(f"总端点数: {plan['total_endpoints']}")
        
        print("\n按分类组织的端点:")
        for category, endpoints in plan['categories'].items():
            print(f"  📂 {category.upper()}: {len(endpoints)} 个端点")
            for endpoint in endpoints[:2]:  # 只显示前2个
                print(f"     - {endpoint['description']} (缓存: {endpoint['cache_level']})")
            if len(endpoints) > 2:
                print(f"     ... 还有 {len(endpoints) - 2} 个端点")
        
        print("\n缓存策略分布:")
        for cache_level, endpoints in plan['cache_strategy'].items():
            print(f"  ⏱️  {cache_level}: {len(endpoints)} 个端点")
        
        print("\n同步优先级:")
        for priority, endpoints in plan['sync_priority'].items():
            priority_names = {1: "最高", 2: "高", 3: "中", 4: "低", 5: "最低"}
            print(f"  🔥 {priority_names.get(priority, str(priority))}: {len(endpoints)} 个端点")
    
    def demo_cache_strategy(self):
        """演示缓存策略"""
        print("\n🚀 4. 缓存策略演示")
        print("-" * 50)
        
        # 展示不同类型数据的缓存策略
        cache_examples = [
            ("character_location", "角色位置", "实时数据"),
            ("character_skills", "角色技能", "常规数据"),
            ("character_wallet", "钱包余额", "频繁更新"),
            ("corporation_info", "公司信息", "日常数据"),
            ("universe_structures", "宇宙建筑", "静态数据")
        ]
        
        print("不同数据类型的缓存策略:")
        for endpoint_name, display_name, data_type in cache_examples:
            if endpoint_name in data_service.endpoints:
                endpoint = data_service.endpoints[endpoint_name]
                ttl = data_service.get_cache_ttl(endpoint_name)
                
                print(f"  📊 {display_name}:")
                print(f"     类型: {data_type}")
                print(f"     缓存级别: {endpoint.cache_level.value}")
                print(f"     TTL: {self._format_ttl(ttl)}")
                print(f"     原因: {self._get_cache_reason(endpoint.cache_level.value)}")
                print()
    
    def demo_scope_recommendations(self):
        """演示权限推荐"""
        print("\n💡 5. 智能权限推荐演示")
        print("-" * 50)
        
        # 模拟不同用户场景
        scenarios = {
            "个人玩家": [
                "esi-location.read_location.v1",
                "esi-skills.read_skills.v1"
            ],
            "工业玩家": [
                "esi-location.read_location.v1",
                "esi-skills.read_skills.v1",
                "esi-assets.read_assets.v1",
                "esi-industry.read_character_jobs.v1"
            ],
            "公司管理者": [
                "esi-location.read_location.v1",
                "esi-skills.read_skills.v1",
                "esi-assets.read_assets.v1",
                "esi-corporations.read_corporation_membership.v1"
            ]
        }
        
        for scenario, current_scopes in scenarios.items():
            print(f"\n👤 {scenario} (当前 {len(current_scopes)} 个权限):")
            
            # 分析缺失的基础权限
            basic_scopes = [
                "esi-location.read_location.v1",
                "esi-location.read_online.v1",
                "esi-skills.read_skills.v1",
                "esi-assets.read_assets.v1",
                "esi-wallet.read_character_wallet.v1"
            ]
            
            missing_basic = [s for s in basic_scopes if s not in current_scopes]
            if missing_basic:
                print(f"  🔥 推荐添加基础权限 ({len(missing_basic)}个):")
                for scope in missing_basic[:3]:
                    print(f"     - {scope}")
                if len(missing_basic) > 3:
                    print(f"     ... 还有 {len(missing_basic) - 3} 个")
            
            # 根据场景推荐相关权限
            if "工业" in scenario:
                industry_scopes = [
                    "esi-industry.read_character_mining.v1",
                    "esi-characters.read_blueprints.v1",
                    "esi-markets.read_character_orders.v1"
                ]
                missing_industry = [s for s in industry_scopes if s not in current_scopes]
                if missing_industry:
                    print(f"  ⚙️  工业相关权限推荐 ({len(missing_industry)}个):")
                    for scope in missing_industry[:2]:
                        print(f"     - {scope}")
            
            if "公司" in scenario:
                corp_scopes = [
                    "esi-corporations.read_structures.v1",
                    "esi-assets.read_corporation_assets.v1",
                    "esi-wallet.read_corporation_wallets.v1"
                ]
                missing_corp = [s for s in corp_scopes if s not in current_scopes]
                if missing_corp:
                    print(f"  🏢 公司管理权限推荐 ({len(missing_corp)}个):")
                    for scope in missing_corp[:2]:
                        print(f"     - {scope}")
    
    def _format_ttl(self, seconds: int) -> str:
        """格式化TTL显示"""
        if seconds < 60:
            return f"{seconds}秒"
        elif seconds < 3600:
            return f"{seconds // 60}分钟"
        elif seconds < 86400:
            return f"{seconds // 3600}小时"
        else:
            return f"{seconds // 86400}天"
    
    def _get_cache_reason(self, cache_level: str) -> str:
        """获取缓存策略原因"""
        reasons = {
            "realtime": "数据变化频繁，需要实时性",
            "frequent": "数据更新较快，平衡实时性和性能",
            "regular": "数据相对稳定，减少API调用",
            "daily": "数据变化缓慢，长期缓存",
            "static": "静态数据，很少变化"
        }
        return reasons.get(cache_level, "未知原因")


def main():
    """主函数"""
    demo = ScopeDrivenDemo()
    demo.run_demo()


if __name__ == "__main__":
    main()
