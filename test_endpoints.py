#!/usr/bin/env python3
import requests
import time

def test_endpoints():
    base_url = "http://localhost:8000"
    
    # 等待服务器启动
    print("等待服务器启动...")
    time.sleep(3)
    
    endpoints = [
        "/health/status",
        "/auth/status",
        "/docs"  # FastAPI自动文档
    ]
    
    for endpoint in endpoints:
        try:
            url = f"{base_url}{endpoint}"
            print(f"测试 {url}...")
            
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✅ {endpoint} - 正常")
                if endpoint != "/docs":  # 文档端点返回HTML
                    print(f"   响应: {response.json()}")
            else:
                print(f"❌ {endpoint} - HTTP {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ {endpoint} - 连接失败: {e}")
    
    # 测试POST端点
    try:
        print("测试 POST /auth/login...")
        response = requests.post(
            f"{base_url}/auth/login",
            json={"scopes": ["test"]},
            timeout=5
        )
        print(f"✅ POST /auth/login - HTTP {response.status_code}")
        print(f"   响应: {response.json()}")
    except Exception as e:
        print(f"❌ POST /auth/login - 失败: {e}")

if __name__ == "__main__":
    test_endpoints()
