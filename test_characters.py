#!/usr/bin/env python3
"""
角色管理功能测试脚本
"""
import requests
import time
import json

def test_characters():
    base_url = "http://localhost:8000"
    
    print("👤 角色管理功能测试")
    print("=" * 50)
    
    # 等待服务器启动
    print("等待服务器启动...")
    time.sleep(3)
    
    # 测试1: 获取用户角色列表
    print("\n1. 测试获取用户角色列表...")
    try:
        response = requests.get(f"{base_url}/characters/", timeout=5)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 角色列表端点正常")
            print(f"   响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        elif response.status_code == 401:
            print("⚠️  需要认证（预期行为）")
        elif response.status_code == 500:
            print("❌ 服务器内部错误")
            print(f"   错误信息: {response.text}")
            return False
        else:
            print(f"⚠️  其他状态码: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 角色列表连接失败: {e}")
        return False
    
    # 测试2: 获取特定角色信息
    print("\n2. 测试获取特定角色信息...")
    try:
        test_character_id = 123456789  # 测试角色ID
        response = requests.get(f"{base_url}/characters/{test_character_id}", timeout=5)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 角色详情端点正常")
            print(f"   响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        elif response.status_code == 401:
            print("⚠️  需要认证（预期行为）")
        elif response.status_code == 404:
            print("⚠️  角色不存在（预期行为）")
        elif response.status_code == 500:
            print("❌ 服务器内部错误")
            print(f"   错误信息: {response.text}")
            return False
        else:
            print(f"⚠️  其他状态码: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 角色详情连接失败: {e}")
        return False
    
    # 测试3: 角色搜索
    print("\n3. 测试角色搜索...")
    try:
        response = requests.get(f"{base_url}/characters/search?query=test", timeout=5)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 角色搜索端点正常")
            print(f"   响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        elif response.status_code == 401:
            print("⚠️  需要认证（预期行为）")
        elif response.status_code == 500:
            print("❌ 服务器内部错误")
            print(f"   错误信息: {response.text}")
            return False
        else:
            print(f"⚠️  其他状态码: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 角色搜索连接失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = test_characters()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 角色管理功能测试通过！")
        print("✅ 角色管理路由已恢复")
    else:
        print("❌ 角色管理功能测试失败")
        print("⚠️  需要进一步实现角色服务")
    print("=" * 50)
