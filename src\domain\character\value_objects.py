"""
角色领域值对象
"""
from dataclasses import dataclass
from datetime import datetime
from typing import Optional

from ..shared.base_entity import ValueObject
from ..shared.value_objects import SkillPoints
from ..shared.exceptions import DomainValidationError


@dataclass(frozen=True)
class Attributes(ValueObject):
    """角色属性值对象"""
    charisma: int = 20
    intelligence: int = 20
    memory: int = 20
    perception: int = 20
    willpower: int = 20
    
    def __post_init__(self):
        # 验证属性值范围
        for attr_name, attr_value in [
            ("charisma", self.charisma),
            ("intelligence", self.intelligence),
            ("memory", self.memory),
            ("perception", self.perception),
            ("willpower", self.willpower)
        ]:
            if not (1 <= attr_value <= 50):
                raise DomainValidationError(f"{attr_name}属性值必须在1-50之间")
    
    def get_total_attributes(self) -> int:
        """获取总属性点"""
        return self.charisma + self.intelligence + self.memory + self.perception + self.willpower
    
    def get_learning_attributes(self) -> int:
        """获取学习相关属性（智力+记忆）"""
        return self.intelligence + self.memory
    
    def __str__(self) -> str:
        return (f"Attributes(C:{self.charisma}, I:{self.intelligence}, "
                f"M:{self.memory}, P:{self.perception}, W:{self.willpower})")


@dataclass(frozen=True)
class Skill(ValueObject):
    """技能值对象"""
    skill_id: int
    skillpoints_in_skill: SkillPoints
    trained_skill_level: int
    active_skill_level: int
    
    def __post_init__(self):
        if self.skill_id <= 0:
            raise DomainValidationError("技能ID必须是正整数")
        if not (0 <= self.trained_skill_level <= 5):
            raise DomainValidationError("训练技能等级必须在0-5之间")
        if not (0 <= self.active_skill_level <= 5):
            raise DomainValidationError("激活技能等级必须在0-5之间")
        if self.active_skill_level > self.trained_skill_level:
            raise DomainValidationError("激活技能等级不能超过训练等级")
    
    def level_up(self) -> 'Skill':
        """升级技能"""
        if self.trained_skill_level >= 5:
            raise DomainValidationError("技能已达到最高等级")
        
        # 计算升级所需技能点（简化计算）
        next_level = self.trained_skill_level + 1
        required_sp = self._calculate_required_sp_for_level(next_level)
        new_total_sp = SkillPoints(required_sp)
        
        return Skill(
            skill_id=self.skill_id,
            skillpoints_in_skill=new_total_sp,
            trained_skill_level=next_level,
            active_skill_level=next_level
        )
    
    def _calculate_required_sp_for_level(self, level: int) -> int:
        """计算指定等级所需的总技能点"""
        # EVE Online技能点计算公式的简化版本
        base_sp = 250
        multiplier = 1  # 实际应该从技能数据获取
        
        total_sp = 0
        for i in range(1, level + 1):
            level_sp = base_sp * multiplier * (2 ** (i - 1))
            total_sp += level_sp
        
        return total_sp
    
    def get_progress_to_next_level(self) -> float:
        """获取到下一等级的进度百分比"""
        if self.trained_skill_level >= 5:
            return 100.0
        
        current_level_sp = self._calculate_required_sp_for_level(self.trained_skill_level)
        next_level_sp = self._calculate_required_sp_for_level(self.trained_skill_level + 1)
        current_sp = self.skillpoints_in_skill.value
        
        if current_sp <= current_level_sp:
            return 0.0
        
        progress = (current_sp - current_level_sp) / (next_level_sp - current_level_sp)
        return min(progress * 100, 100.0)
    
    def can_level_up(self) -> bool:
        """是否可以升级"""
        if self.trained_skill_level >= 5:
            return False
        
        required_sp = self._calculate_required_sp_for_level(self.trained_skill_level + 1)
        return self.skillpoints_in_skill.value >= required_sp
    
    def __str__(self) -> str:
        return f"Skill({self.skill_id}, Level {self.trained_skill_level}, {self.skillpoints_in_skill})"


@dataclass(frozen=True)
class SkillQueue(ValueObject):
    """技能队列项值对象"""
    skill_id: int
    finished_level: int
    training_start_sp: Optional[SkillPoints] = None
    level_end_sp: Optional[SkillPoints] = None
    level_start_sp: Optional[SkillPoints] = None
    start_date: Optional[datetime] = None
    finish_date: Optional[datetime] = None
    queue_position: int = 0
    
    def __post_init__(self):
        if self.skill_id <= 0:
            raise DomainValidationError("技能ID必须是正整数")
        if not (1 <= self.finished_level <= 5):
            raise DomainValidationError("完成等级必须在1-5之间")
        if self.queue_position < 0:
            raise DomainValidationError("队列位置不能为负数")
        
        # 验证时间逻辑
        if (self.start_date is not None and 
            self.finish_date is not None and 
            self.start_date >= self.finish_date):
            raise DomainValidationError("开始时间必须早于完成时间")
    
    def is_currently_training(self) -> bool:
        """是否正在训练"""
        if self.start_date is None or self.finish_date is None:
            return False
        
        now = datetime.utcnow()
        return self.start_date <= now <= self.finish_date
    
    def is_completed(self) -> bool:
        """是否已完成"""
        if self.finish_date is None:
            return False
        
        return datetime.utcnow() >= self.finish_date
    
    def get_remaining_time(self) -> Optional[int]:
        """获取剩余训练时间（秒）"""
        if self.finish_date is None:
            return None
        
        now = datetime.utcnow()
        if now >= self.finish_date:
            return 0
        
        return int((self.finish_date - now).total_seconds())
    
    def get_training_progress(self) -> float:
        """获取训练进度百分比"""
        if (self.start_date is None or 
            self.finish_date is None or 
            self.training_start_sp is None or 
            self.level_end_sp is None):
            return 0.0
        
        now = datetime.utcnow()
        if now <= self.start_date:
            return 0.0
        if now >= self.finish_date:
            return 100.0
        
        total_duration = (self.finish_date - self.start_date).total_seconds()
        elapsed_duration = (now - self.start_date).total_seconds()
        
        return (elapsed_duration / total_duration) * 100
    
    def __str__(self) -> str:
        return f"SkillQueue({self.skill_id}, Level {self.finished_level}, Pos {self.queue_position})"


@dataclass(frozen=True)
class Clone(ValueObject):
    """克隆体值对象"""
    clone_id: int
    name: str
    location_id: int
    clone_type: str = "clone"  # "clone" 或 "jump_clone"
    
    def __post_init__(self):
        if self.clone_id <= 0:
            raise DomainValidationError("克隆体ID必须是正整数")
        if not self.name or not self.name.strip():
            raise DomainValidationError("克隆体名称不能为空")
        if self.location_id <= 0:
            raise DomainValidationError("位置ID必须是正整数")
        if self.clone_type not in ["clone", "jump_clone"]:
            raise DomainValidationError("克隆体类型必须是clone或jump_clone")
    
    def is_jump_clone(self) -> bool:
        """是否为跳跃克隆"""
        return self.clone_type == "jump_clone"
    
    def is_active_clone(self) -> bool:
        """是否为激活克隆"""
        return self.clone_type == "clone"
    
    def __str__(self) -> str:
        return f"Clone({self.name}, Type: {self.clone_type}, Location: {self.location_id})"


@dataclass(frozen=True)
class Implant(ValueObject):
    """植入体值对象"""
    type_id: int
    slot: int
    
    def __post_init__(self):
        if self.type_id <= 0:
            raise DomainValidationError("植入体类型ID必须是正整数")
        if not (1 <= self.slot <= 10):
            raise DomainValidationError("植入体插槽必须在1-10之间")
    
    def __str__(self) -> str:
        return f"Implant(Type: {self.type_id}, Slot: {self.slot})"
