# 开发环境配置指南

## 系统要求

### 基础环境
- **操作系统**: Windows 10/11, macOS 10.15+, Ubuntu 20.04+
- **Python**: 3.11 或更高版本
- **Git**: 2.30 或更高版本
- **内存**: 至少 8GB RAM
- **存储**: 至少 10GB 可用空间

### 数据库和服务
- **PostgreSQL**: 14 或更高版本
- **Redis**: 6 或更高版本
- **Node.js**: 18+ (前端开发需要)

## 安装步骤

### 1. Python 环境配置

#### Windows
```powershell
# 下载并安装 Python 3.11+
# 从 https://www.python.org/downloads/ 下载

# 验证安装
python --version
pip --version

# 创建虚拟环境
python -m venv venv
venv\Scripts\activate
```

#### macOS
```bash
# 使用 Homebrew 安装
brew install python@3.11

# 或使用 pyenv
brew install pyenv
pyenv install 3.11.6
pyenv global 3.11.6

# 创建虚拟环境
python -m venv venv
source venv/bin/activate
```

#### Ubuntu/Debian
```bash
# 安装 Python 3.11
sudo apt update
sudo apt install python3.11 python3.11-venv python3.11-dev

# 创建虚拟环境
python3.11 -m venv venv
source venv/bin/activate
```

### 2. 数据库安装

#### PostgreSQL

**Windows:**
```powershell
# 下载并安装 PostgreSQL
# 从 https://www.postgresql.org/download/windows/ 下载

# 或使用 Chocolatey
choco install postgresql

# 创建数据库
createdb eve_assistant
```

**macOS:**
```bash
# 使用 Homebrew
brew install postgresql@14
brew services start postgresql@14

# 创建数据库
createdb eve_assistant
```

**Ubuntu:**
```bash
# 安装 PostgreSQL
sudo apt install postgresql postgresql-contrib

# 启动服务
sudo systemctl start postgresql
sudo systemctl enable postgresql

# 创建用户和数据库
sudo -u postgres createuser --interactive
sudo -u postgres createdb eve_assistant
```

#### Redis

**Windows:**
```powershell
# 使用 Chocolatey
choco install redis-64

# 或下载 Windows 版本
# https://github.com/microsoftarchive/redis/releases
```

**macOS:**
```bash
# 使用 Homebrew
brew install redis
brew services start redis
```

**Ubuntu:**
```bash
# 安装 Redis
sudo apt install redis-server

# 启动服务
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

### 3. 项目依赖安装

```bash
# 激活虚拟环境
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 升级 pip
pip install --upgrade pip

# 安装项目依赖
pip install -r requirements.txt

# 安装开发依赖
pip install -e ".[dev]"
```

### 4. 环境变量配置

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量文件
# 使用你喜欢的编辑器编辑 .env 文件
```

**必需的环境变量:**
```env
# 数据库配置
DATABASE_URL=postgresql://username:password@localhost:5432/eve_assistant

# Redis配置
REDIS_URL=redis://localhost:6379/0

# 应用配置
SECRET_KEY=your-very-secret-key-here
DEBUG=true

# ESI API配置
ESI_BASE_URL=https://esi.evetech.net
ESI_USER_AGENT=EVE-Assistant/0.1.0 (<EMAIL>)

# EVE SSO配置 (需要在 EVE Developers 创建应用)
EVE_SSO_CLIENT_ID=your-client-id
EVE_SSO_CLIENT_SECRET=your-client-secret
EVE_SSO_CALLBACK_URL=http://localhost:8000/auth/callback
```

### 5. 数据库初始化

```bash
# 安装 Alembic (如果未安装)
pip install alembic

# 初始化数据库迁移
alembic upgrade head

# 如果需要创建新的迁移
alembic revision --autogenerate -m "Initial migration"
```

## 开发工具配置

### IDE 推荐

#### PyCharm
1. 打开项目目录
2. 配置 Python 解释器为虚拟环境中的 Python
3. 安装推荐插件：
   - Database Navigator
   - .env files support

#### VS Code
1. 安装推荐扩展：
   ```json
   {
     "recommendations": [
       "ms-python.python",
       "ms-python.black-formatter",
       "ms-python.isort",
       "ms-python.mypy-type-checker",
       "ms-python.flake8",
       "bradlc.vscode-tailwindcss",
       "ms-vscode.vscode-json"
     ]
   }
   ```

2. 配置 settings.json：
   ```json
   {
     "python.defaultInterpreterPath": "./venv/bin/python",
     "python.formatting.provider": "black",
     "python.linting.enabled": true,
     "python.linting.flake8Enabled": true,
     "python.linting.mypyEnabled": true,
     "editor.formatOnSave": true,
     "python.sortImports.args": ["--profile", "black"]
   }
   ```

### Git 配置

```bash
# 配置 Git 用户信息
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# 配置 Git 编辑器
git config --global core.editor "code --wait"  # VS Code
# 或
git config --global core.editor "vim"          # Vim

# 初始化 Git 仓库 (如果还没有)
git init
git add .
git commit -m "Initial commit"
```

### 代码质量工具

```bash
# 安装 pre-commit hooks
pip install pre-commit
pre-commit install

# 手动运行所有检查
pre-commit run --all-files

# 格式化代码
black src tests
isort src tests

# 类型检查
mypy src

# 代码检查
flake8 src tests
```

## 验证安装

### 1. 运行测试
```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/unit/
pytest tests/integration/

# 生成覆盖率报告
pytest --cov=src --cov-report=html
```

### 2. 启动开发服务器
```bash
# 启动 FastAPI 开发服务器
uvicorn src.presentation.api.main:app --reload --host 0.0.0.0 --port 8000

# 访问 API 文档
# http://localhost:8000/docs
```

### 3. 检查服务状态
```bash
# 检查 PostgreSQL
psql -h localhost -U username -d eve_assistant -c "SELECT version();"

# 检查 Redis
redis-cli ping

# 检查 Python 环境
python -c "import sys; print(sys.version)"
python -c "import fastapi; print(fastapi.__version__)"
```

## 常见问题解决

### 1. Python 版本问题
```bash
# 如果系统有多个 Python 版本
which python3.11
python3.11 -m venv venv

# 或使用 pyenv
pyenv versions
pyenv local 3.11.6
```

### 2. 数据库连接问题
```bash
# 检查 PostgreSQL 服务状态
sudo systemctl status postgresql  # Linux
brew services list | grep postgresql  # macOS

# 测试连接
psql -h localhost -U username -d eve_assistant
```

### 3. 依赖安装问题
```bash
# 清理 pip 缓存
pip cache purge

# 重新安装依赖
pip install --no-cache-dir -r requirements.txt

# 如果遇到编译错误，安装构建工具
# Ubuntu/Debian
sudo apt install build-essential python3-dev

# macOS
xcode-select --install
```

### 4. 权限问题
```bash
# Linux/macOS 权限问题
sudo chown -R $USER:$USER venv/
chmod +x venv/bin/activate

# Windows 执行策略问题
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

## 下一步

环境配置完成后，你可以：

1. 查看 [API 文档](api_interface_documentation.md)
2. 阅读 [开发指南](development.md)
3. 开始开发第一个功能模块
4. 运行测试确保一切正常

如果遇到问题，请查看项目的 [FAQ](faq.md) 或提交 Issue。
