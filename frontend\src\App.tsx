import { Spin } from 'antd'
import { Suspense } from 'react'
import { Toaster } from 'react-hot-toast'
import { Navigate, Route, Routes } from 'react-router-dom'

import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { AuthLayout } from '@/components/layout/AuthLayout'
import { MainLayout } from '@/components/layout/MainLayout'
import { LoadingScreen } from '@/components/ui/LoadingScreen'
import { useAuth } from '@/hooks/useAuth'

// 懒加载页面组件
import { lazy } from 'react'

// 认证页面
const LoginPage = lazy(() => import('@/pages/auth/LoginPage'))
const RegisterPage = lazy(() => import('@/pages/auth/RegisterPage'))
const CallbackPage = lazy(() => import('@/pages/auth/CallbackPage'))
const MockEVELogin = lazy(() => import('@/pages/auth/MockEVELogin'))

// 主要页面
const DashboardPage = lazy(() => import('@/pages/dashboard/DashboardPage'))
const CharactersPage = lazy(() => import('@/pages/characters/CharactersPage'))
const CharacterDetailPage = lazy(() => import('@/pages/characters/CharacterDetailPage'))
const AssetsPage = lazy(() => import('@/pages/assets/AssetsPage'))
const MarketPage = lazy(() => import('@/pages/market/MarketPage'))
const IndustryPage = lazy(() => import('@/pages/industry/IndustryPage'))
const SettingsPage = lazy(() => import('@/pages/settings/SettingsPage'))

// 错误页面
const NotFoundPage = lazy(() => import('@/pages/error/NotFoundPage'))

function App() {
  const { isLoading, isAuthenticated } = useAuth()

  // 应用加载中
  if (isLoading) {
    return <LoadingScreen />
  }

  return (
    <>
      <Suspense fallback={
        <div className="flex items-center justify-center min-h-screen">
          <Spin size="large" tip="加载中..." />
        </div>
      }>
        <Routes>
          {/* 认证相关路由 */}
          <Route path="/auth" element={<AuthLayout />}>
            <Route path="login" element={<LoginPage />} />
            <Route path="register" element={<RegisterPage />} />
            <Route path="callback" element={<CallbackPage />} />
            <Route path="mock-eve-login" element={<MockEVELogin />} />
            <Route index element={<Navigate to="/auth/login" replace />} />
          </Route>

          {/* 主应用路由 */}
          <Route path="/" element={
            <ProtectedRoute>
              <MainLayout />
            </ProtectedRoute>
          }>
            {/* 仪表板 */}
            <Route index element={<Navigate to="/dashboard" replace />} />
            <Route path="dashboard" element={<DashboardPage />} />

            {/* 角色管理 */}
            <Route path="characters">
              <Route index element={<CharactersPage />} />
              <Route path=":characterId" element={<CharacterDetailPage />} />
            </Route>

            {/* 资产管理 */}
            <Route path="assets" element={<AssetsPage />} />

            {/* 市场交易 */}
            <Route path="market" element={<MarketPage />} />

            {/* 工业生产 */}
            <Route path="industry" element={<IndustryPage />} />

            {/* 设置 */}
            <Route path="settings" element={<SettingsPage />} />
          </Route>

          {/* 404页面 */}
          <Route path="*" element={<NotFoundPage />} />
        </Routes>
      </Suspense>

      {/* 全局通知 */}
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#363636',
            color: '#fff',
          },
          success: {
            duration: 3000,
            iconTheme: {
              primary: '#52c41a',
              secondary: '#fff',
            },
          },
          error: {
            duration: 5000,
            iconTheme: {
              primary: '#f5222d',
              secondary: '#fff',
            },
          },
        }}
      />
    </>
  )
}

export default App
