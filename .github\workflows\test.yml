name: Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.11, 3.12]

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/pyproject.toml') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e .[dev]

    - name: Lint with flake8
      run: |
        flake8 src tests --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 src tests --count --exit-zero --max-complexity=10 --max-line-length=88 --statistics

    - name: Format check with black
      run: |
        black --check src tests

    - name: Import sort check with isort
      run: |
        isort --check-only src tests

    - name: Type check with mypy
      run: |
        mypy src
      continue-on-error: true

    - name: Test module imports
      run: |
        python -c "
        print('🧪 Testing critical module imports...')
        try:
            from src.presentation.api.main import app
            print('✅ Main app import successful')
        except Exception as e:
            print(f'❌ Main app import failed: {e}')
            exit(1)
        
        try:
            from src.infrastructure.persistence.repositories import CharacterRepositoryImpl
            print('✅ Repository imports successful')
        except Exception as e:
            print(f'❌ Repository imports failed: {e}')
            exit(1)
        
        print('🎉 All critical imports successful')
        "

    - name: Run unit tests
      run: |
        pytest tests/unit -v --tb=short --cov=src --cov-report=xml

    - name: Run integration tests
      run: |
        pytest tests/integration -v --tb=short

    - name: Run module import tests
      run: |
        pytest tests/test_module_imports.py -v --tb=short

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false

  security:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.11

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install bandit safety

    - name: Run security checks with bandit
      run: |
        bandit -r src -f json -o bandit-report.json
      continue-on-error: true

    - name: Check dependencies for known vulnerabilities
      run: |
        safety check --json --output safety-report.json
      continue-on-error: true

    - name: Upload security reports
      uses: actions/upload-artifact@v3
      with:
        name: security-reports
        path: |
          bandit-report.json
          safety-report.json

  build:
    runs-on: ubuntu-latest
    needs: test
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.11

    - name: Install build dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build

    - name: Build package
      run: |
        python -m build

    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: dist
        path: dist/
