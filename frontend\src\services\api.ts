import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import toast from 'react-hot-toast'

import { ApiResponse } from '@/types'
import { getAuthTokens, removeAuthTokens, setAuthTokens } from '@/utils/auth'

// API基础配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api'
const API_TIMEOUT = 30000 // 30秒超时

// 创建axios实例
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: API_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 添加认证头
    const tokens = getAuthTokens()
    if (tokens?.accessToken) {
      config.headers.Authorization = `Bearer ${tokens.accessToken}`
    }

    // 添加请求ID用于追踪
    config.headers['X-Request-ID'] = generateRequestId()

    // 开发环境下记录请求
    if (import.meta.env.DEV) {
      console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`, {
        params: config.params,
        data: config.data,
      })
    }

    return config
  },
  (error) => {
    console.error('❌ Request Error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    // 开发环境下记录响应
    if (import.meta.env.DEV) {
      console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url}`, {
        status: response.status,
        data: response.data,
      })
    }

    return response
  },
  async (error) => {
    const originalRequest = error.config

    // 开发环境下记录错误
    if (import.meta.env.DEV) {
      console.error(`❌ API Error: ${originalRequest?.method?.toUpperCase()} ${originalRequest?.url}`, {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message,
      })
    }

    // 处理401未授权错误
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true

      try {
        // 尝试刷新令牌
        const tokens = getAuthTokens()
        if (tokens?.refreshToken) {
          const response = await refreshAccessToken(tokens.refreshToken)
          setAuthTokens(response.data)

          // 重新发送原始请求
          originalRequest.headers.Authorization = `Bearer ${response.data.accessToken}`
          return apiClient(originalRequest)
        }
      } catch (refreshError) {
        // 刷新失败，清除令牌并跳转到登录页
        removeAuthTokens()
        window.location.href = '/auth/login'
        return Promise.reject(refreshError)
      }
    }

    // 处理其他错误
    handleApiError(error)
    return Promise.reject(error)
  }
)

// 生成请求ID
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// 刷新访问令牌
async function refreshAccessToken(refreshToken: string) {
  const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {
    refresh_token: refreshToken,
  })
  return response
}

// 处理API错误
function handleApiError(error: any) {
  const status = error.response?.status
  const message = error.response?.data?.message || error.message

  switch (status) {
    case 400:
      toast.error(`请求错误: ${message}`)
      break
    case 401:
      toast.error('认证失败，请重新登录')
      break
    case 403:
      toast.error('权限不足，无法访问该资源')
      break
    case 404:
      toast.error('请求的资源不存在')
      break
    case 429:
      toast.error('请求过于频繁，请稍后再试')
      break
    case 500:
      toast.error('服务器内部错误，请稍后再试')
      break
    case 502:
    case 503:
    case 504:
      toast.error('服务暂时不可用，请稍后再试')
      break
    default:
      if (error.code === 'NETWORK_ERROR' || error.code === 'ECONNABORTED') {
        toast.error('网络连接失败，请检查网络设置')
      } else {
        toast.error(`请求失败: ${message}`)
      }
  }
}

// API方法封装
export class ApiService {
  // GET请求
  static async get<T = any>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const response = await apiClient.get(url, config)
    return response.data
  }

  // POST请求
  static async post<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const response = await apiClient.post(url, data, config)
    return response.data
  }

  // PUT请求
  static async put<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const response = await apiClient.put(url, data, config)
    return response.data
  }

  // PATCH请求
  static async patch<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const response = await apiClient.patch(url, data, config)
    return response.data
  }

  // DELETE请求
  static async delete<T = any>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const response = await apiClient.delete(url, config)
    return response.data
  }

  // 上传文件
  static async upload<T = any>(
    url: string,
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<ApiResponse<T>> {
    const formData = new FormData()
    formData.append('file', file)

    const response = await apiClient.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(progress)
        }
      },
    })

    return response.data
  }

  // 下载文件
  static async download(url: string, filename?: string): Promise<void> {
    const response = await apiClient.get(url, {
      responseType: 'blob',
    })

    // 创建下载链接
    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
  }
}

// 导出axios实例供特殊用途
export { apiClient }
export default ApiService
