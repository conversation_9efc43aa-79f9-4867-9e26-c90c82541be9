"""
模块导入测试
确保所有关键模块都能正确导入
"""
import pytest
import importlib
import sys
from pathlib import Path


@pytest.mark.unit
class TestModuleImports:
    """模块导入测试类"""
    
    def test_main_app_import(self):
        """测试主应用导入"""
        try:
            from src.presentation.api.main import app
            assert app is not None
        except ImportError as e:
            pytest.fail(f"Failed to import main app: {e}")
    
    def test_auth_router_import(self):
        """测试认证路由导入"""
        try:
            from src.presentation.api.routers.auth import router
            assert router is not None
        except ImportError as e:
            pytest.fail(f"Failed to import auth router: {e}")
    
    def test_auth_service_import(self):
        """测试认证服务导入"""
        try:
            from src.application.services.auth import AuthenticationService
            assert AuthenticationService is not None
        except ImportError as e:
            pytest.fail(f"Failed to import AuthenticationService: {e}")
    
    def test_character_repository_import(self):
        """测试角色仓储导入"""
        try:
            from src.infrastructure.persistence.repositories.character import CharacterRepositoryImpl
            assert CharacterRepositoryImpl is not None
        except ImportError as e:
            pytest.fail(f"Failed to import CharacterRepositoryImpl: {e}")
    
    def test_database_import(self):
        """测试数据库模块导入"""
        try:
            from src.infrastructure.persistence.database import Base, get_database
            assert Base is not None
            assert get_database is not None
        except ImportError as e:
            pytest.fail(f"Failed to import database modules: {e}")
    
    def test_dependencies_import(self):
        """测试依赖注入模块导入"""
        try:
            from src.presentation.api.dependencies import get_auth_service
            assert get_auth_service is not None
        except ImportError as e:
            pytest.fail(f"Failed to import dependencies: {e}")
    
    def test_all_repository_imports(self):
        """测试所有仓储模块导入"""
        try:
            from src.infrastructure.persistence.repositories import CharacterRepositoryImpl
            assert CharacterRepositoryImpl is not None
        except ImportError as e:
            pytest.fail(f"Failed to import repositories: {e}")
    
    def test_domain_entities_import(self):
        """测试领域实体导入"""
        try:
            # 根据实际存在的实体进行测试
            from src.domain.entities.user import User
            assert User is not None
        except ImportError:
            # 如果实体不存在，跳过测试
            pytest.skip("Domain entities not implemented yet")
    
    def test_models_import(self):
        """测试数据模型导入"""
        try:
            from src.presentation.api.models.auth import EVELoginRequest
            assert EVELoginRequest is not None
        except ImportError as e:
            pytest.fail(f"Failed to import models: {e}")
    
    @pytest.mark.parametrize("module_path", [
        "src.presentation.api.main",
        "src.presentation.api.routers.auth",
        "src.application.services.auth",
        "src.infrastructure.persistence.repositories.character",
        "src.infrastructure.persistence.database",
        "src.presentation.api.dependencies",
        "src.presentation.api.models.auth",
    ])
    def test_critical_modules_import(self, module_path):
        """参数化测试关键模块导入"""
        try:
            module = importlib.import_module(module_path)
            assert module is not None
        except ImportError as e:
            pytest.fail(f"Failed to import {module_path}: {e}")
    
    def test_no_circular_imports(self):
        """测试没有循环导入"""
        # 记录导入前的模块数量
        initial_modules = set(sys.modules.keys())
        
        try:
            # 导入主要模块
            from src.presentation.api.main import app
            from src.application.services.auth import AuthenticationService
            from src.infrastructure.persistence.repositories.character import CharacterRepositoryImpl
            
            # 检查是否有异常的模块加载
            final_modules = set(sys.modules.keys())
            new_modules = final_modules - initial_modules
            
            # 验证新加载的模块都是预期的
            expected_prefixes = ['src.', 'tests.']
            unexpected_modules = [
                mod for mod in new_modules 
                if not any(mod.startswith(prefix) for prefix in expected_prefixes)
                and not mod.startswith('_')  # 忽略内部模块
            ]
            
            # 如果有意外的模块，可能存在循环导入或其他问题
            if unexpected_modules:
                print(f"Unexpected modules loaded: {unexpected_modules}")
            
        except ImportError as e:
            pytest.fail(f"Circular import detected: {e}")
    
    def test_init_files_consistency(self):
        """测试__init__.py文件的一致性"""
        src_path = Path("src")
        
        for init_file in src_path.rglob("__init__.py"):
            # 检查__init__.py文件是否可以正常导入
            relative_path = init_file.relative_to(Path.cwd())
            module_path = str(relative_path.with_suffix("")).replace("/", ".").replace("\\", ".")
            
            try:
                importlib.import_module(module_path)
            except ImportError as e:
                pytest.fail(f"Failed to import {module_path}: {e}")
    
    def test_package_structure(self):
        """测试包结构完整性"""
        required_packages = [
            "src",
            "src.presentation",
            "src.presentation.api",
            "src.presentation.api.routers",
            "src.application",
            "src.application.services",
            "src.infrastructure",
            "src.infrastructure.persistence",
            "src.infrastructure.persistence.repositories",
            "src.domain",
        ]
        
        for package in required_packages:
            try:
                importlib.import_module(package)
            except ImportError as e:
                # 某些包可能还未实现，记录警告而不是失败
                print(f"Warning: Package {package} not found: {e}")
    
    def test_import_performance(self):
        """测试导入性能"""
        import time
        
        start_time = time.time()
        
        # 导入主要模块
        from src.presentation.api.main import app
        from src.application.services.auth import AuthenticationService
        
        end_time = time.time()
        import_time = end_time - start_time
        
        # 导入时间不应该超过5秒
        assert import_time < 5.0, f"Import took too long: {import_time:.2f}s"
    
    def test_module_attributes(self):
        """测试模块属性完整性"""
        from src.presentation.api.main import app
        
        # 检查FastAPI应用的基本属性
        assert hasattr(app, 'router')
        assert hasattr(app, 'middleware_stack')
        assert hasattr(app, 'routes')
        
        # 检查路由是否正确注册
        route_paths = [route.path for route in app.routes]
        assert "/health" in route_paths
        assert any("/auth" in path for path in route_paths)
