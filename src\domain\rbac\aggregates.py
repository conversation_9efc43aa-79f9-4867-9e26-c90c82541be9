"""
RBAC权限管理聚合根
"""
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set
from uuid import UUID

from ..shared.base_entity import AggregateRoot
from ..shared.value_objects import UserId
from .value_objects import (
    RoleId, PermissionId, RoleName, PermissionName, PermissionScope,
    RoleHierarchy, UserRoleAssignment, PermissionContext
)
from .events import (
    RoleCreatedEvent, RoleUpdatedEvent, RoleDeletedEvent,
    PermissionCreatedEvent, PermissionUpdatedEvent,
    UserRoleAssignedEvent, UserRoleRevokedEvent,
    PermissionGrantedEvent, PermissionRevokedEvent
)
from .exceptions import (
    DuplicatePermissionError, CircularRoleHierarchyError,
    InsufficientPermissionError, InvalidPermissionScopeError
)


@dataclass
class Permission(AggregateRoot):
    """权限聚合根"""
    permission_id: PermissionId
    name: PermissionName
    description: str
    scope: PermissionScope
    is_active: bool = True
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    
    def __post_init__(self):
        super().__post_init__()
        self.id = self.permission_id
    
    @classmethod
    def create(cls, name: PermissionName, description: str, 
               scope: PermissionScope) -> 'Permission':
        """创建新权限"""
        permission_id = PermissionId.generate()
        
        permission = cls(
            permission_id=permission_id,
            name=name,
            description=description,
            scope=scope
        )
        
        permission.add_domain_event(PermissionCreatedEvent(
            permission_id=permission_id,
            name=name.value,
            description=description,
            scope=scope,
            created_at=permission.created_at
        ))
        
        return permission
    
    def update_description(self, description: str) -> None:
        """更新权限描述"""
        old_description = self.description
        self.description = description
        self.updated_at = datetime.utcnow()
        
        self.add_domain_event(PermissionUpdatedEvent(
            permission_id=self.permission_id,
            old_description=old_description,
            new_description=description,
            updated_at=self.updated_at
        ))
    
    def deactivate(self) -> None:
        """停用权限"""
        if not self.is_active:
            return
        
        self.is_active = False
        self.updated_at = datetime.utcnow()
    
    def activate(self) -> None:
        """激活权限"""
        if self.is_active:
            return
        
        self.is_active = True
        self.updated_at = datetime.utcnow()
    
    def matches_context(self, context: PermissionContext) -> bool:
        """检查权限是否匹配上下文"""
        if not self.is_active:
            return False
        
        return self.scope.matches(
            context.resource_type,
            context.action_type,
            context.to_dict()
        )


@dataclass
class Role(AggregateRoot):
    """角色聚合根"""
    role_id: RoleId
    name: RoleName
    description: str
    permissions: Set[PermissionId] = field(default_factory=set)
    parent_roles: Set[RoleId] = field(default_factory=set)
    child_roles: Set[RoleId] = field(default_factory=set)
    is_active: bool = True
    is_system_role: bool = False
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    
    def __post_init__(self):
        super().__post_init__()
        self.id = self.role_id
    
    @classmethod
    def create(cls, name: RoleName, description: str, 
               is_system_role: bool = False) -> 'Role':
        """创建新角色"""
        role_id = RoleId.generate()
        
        role = cls(
            role_id=role_id,
            name=name,
            description=description,
            is_system_role=is_system_role
        )
        
        role.add_domain_event(RoleCreatedEvent(
            role_id=role_id,
            name=name.value,
            description=description,
            is_system_role=is_system_role,
            created_at=role.created_at
        ))
        
        return role
    
    def update_description(self, description: str) -> None:
        """更新角色描述"""
        old_description = self.description
        self.description = description
        self.updated_at = datetime.utcnow()
        
        self.add_domain_event(RoleUpdatedEvent(
            role_id=self.role_id,
            old_description=old_description,
            new_description=description,
            updated_at=self.updated_at
        ))
    
    def grant_permission(self, permission_id: PermissionId) -> None:
        """授予权限"""
        if permission_id in self.permissions:
            raise DuplicatePermissionError(f"Permission {permission_id} already granted to role {self.role_id}")
        
        self.permissions.add(permission_id)
        self.updated_at = datetime.utcnow()
        
        self.add_domain_event(PermissionGrantedEvent(
            role_id=self.role_id,
            permission_id=permission_id,
            granted_at=self.updated_at
        ))
    
    def revoke_permission(self, permission_id: PermissionId) -> None:
        """撤销权限"""
        if permission_id not in self.permissions:
            return
        
        self.permissions.remove(permission_id)
        self.updated_at = datetime.utcnow()
        
        self.add_domain_event(PermissionRevokedEvent(
            role_id=self.role_id,
            permission_id=permission_id,
            revoked_at=self.updated_at
        ))
    
    def add_parent_role(self, parent_role_id: RoleId, 
                       existing_hierarchy: Dict[RoleId, Set[RoleId]]) -> None:
        """添加父角色"""
        if parent_role_id == self.role_id:
            raise CircularRoleHierarchyError("Role cannot inherit from itself")
        
        # 检查是否会形成循环继承
        if self._would_create_cycle(parent_role_id, existing_hierarchy):
            raise CircularRoleHierarchyError(f"Adding parent role {parent_role_id} would create circular hierarchy")
        
        self.parent_roles.add(parent_role_id)
        self.updated_at = datetime.utcnow()
    
    def remove_parent_role(self, parent_role_id: RoleId) -> None:
        """移除父角色"""
        if parent_role_id in self.parent_roles:
            self.parent_roles.remove(parent_role_id)
            self.updated_at = datetime.utcnow()
    
    def add_child_role(self, child_role_id: RoleId) -> None:
        """添加子角色"""
        if child_role_id != self.role_id:
            self.child_roles.add(child_role_id)
            self.updated_at = datetime.utcnow()
    
    def remove_child_role(self, child_role_id: RoleId) -> None:
        """移除子角色"""
        if child_role_id in self.child_roles:
            self.child_roles.remove(child_role_id)
            self.updated_at = datetime.utcnow()
    
    def get_all_permissions(self, role_repository) -> Set[PermissionId]:
        """获取所有权限（包括继承的权限）"""
        all_permissions = self.permissions.copy()
        
        # 递归获取父角色的权限
        for parent_role_id in self.parent_roles:
            parent_role = role_repository.get_by_id(parent_role_id)
            if parent_role and parent_role.is_active:
                parent_permissions = parent_role.get_all_permissions(role_repository)
                all_permissions.update(parent_permissions)
        
        return all_permissions
    
    def deactivate(self) -> None:
        """停用角色"""
        if not self.is_active:
            return
        
        self.is_active = False
        self.updated_at = datetime.utcnow()
        
        self.add_domain_event(RoleDeletedEvent(
            role_id=self.role_id,
            deleted_at=self.updated_at
        ))
    
    def activate(self) -> None:
        """激活角色"""
        if self.is_active:
            return
        
        self.is_active = True
        self.updated_at = datetime.utcnow()
    
    def _would_create_cycle(self, parent_role_id: RoleId, 
                           existing_hierarchy: Dict[RoleId, Set[RoleId]]) -> bool:
        """检查是否会创建循环继承"""
        visited = set()
        
        def has_path_to_self(current_role_id: RoleId) -> bool:
            if current_role_id == self.role_id:
                return True
            
            if current_role_id in visited:
                return False
            
            visited.add(current_role_id)
            
            # 检查当前角色的所有父角色
            parent_roles = existing_hierarchy.get(current_role_id, set())
            for parent in parent_roles:
                if has_path_to_self(parent):
                    return True
            
            return False
        
        return has_path_to_self(parent_role_id)


@dataclass
class UserRole(AggregateRoot):
    """用户角色聚合根"""
    user_id: UserId
    role_assignments: Dict[RoleId, UserRoleAssignment] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    
    def __post_init__(self):
        super().__post_init__()
        self.id = self.user_id
    
    @classmethod
    def create(cls, user_id: UserId) -> 'UserRole':
        """创建用户角色"""
        return cls(user_id=user_id)
    
    def assign_role(self, role_id: RoleId, assigned_by: UserId,
                   expires_at: Optional[datetime] = None) -> None:
        """分配角色"""
        if role_id in self.role_assignments:
            # 如果角色已分配且仍有效，则不重复分配
            existing = self.role_assignments[role_id]
            if existing.is_valid():
                return
        
        expires_str = expires_at.isoformat() if expires_at else None
        
        assignment = UserRoleAssignment(
            user_id=self.user_id,
            role_id=role_id,
            assigned_by=assigned_by,
            assigned_at=datetime.utcnow().isoformat(),
            expires_at=expires_str
        )
        
        self.role_assignments[role_id] = assignment
        self.updated_at = datetime.utcnow()
        
        self.add_domain_event(UserRoleAssignedEvent(
            user_id=self.user_id,
            role_id=role_id,
            assigned_by=assigned_by,
            assigned_at=assignment.assigned_at,
            expires_at=expires_str
        ))
    
    def revoke_role(self, role_id: RoleId, revoked_by: UserId) -> None:
        """撤销角色"""
        if role_id not in self.role_assignments:
            return
        
        del self.role_assignments[role_id]
        self.updated_at = datetime.utcnow()
        
        self.add_domain_event(UserRoleRevokedEvent(
            user_id=self.user_id,
            role_id=role_id,
            revoked_by=revoked_by,
            revoked_at=self.updated_at.isoformat()
        ))
    
    def get_active_roles(self) -> Set[RoleId]:
        """获取活跃的角色"""
        active_roles = set()
        
        for role_id, assignment in self.role_assignments.items():
            if assignment.is_valid():
                active_roles.add(role_id)
        
        return active_roles
    
    def has_role(self, role_id: RoleId) -> bool:
        """检查是否拥有指定角色"""
        return role_id in self.get_active_roles()
    
    def cleanup_expired_assignments(self) -> None:
        """清理过期的角色分配"""
        expired_roles = []
        
        for role_id, assignment in self.role_assignments.items():
            if assignment.is_expired():
                expired_roles.append(role_id)
        
        for role_id in expired_roles:
            del self.role_assignments[role_id]
        
        if expired_roles:
            self.updated_at = datetime.utcnow()
    
    def get_role_assignment(self, role_id: RoleId) -> Optional[UserRoleAssignment]:
        """获取角色分配信息"""
        return self.role_assignments.get(role_id)
