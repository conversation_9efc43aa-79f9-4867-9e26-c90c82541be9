"""
JSON文件存储管理器
"""
import json
import os
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional
import shutil

from ..config import settings
from ..config.logging import get_logger

logger = get_logger(__name__)


class JSONStorage:
    """JSON文件存储管理器"""
    
    def __init__(self, base_path: str = "data/json"):
        self.base_path = Path(base_path)
        self.base_path.mkdir(parents=True, exist_ok=True)
        
        # 创建子目录
        self.config_dir = self.base_path / "config"
        self.user_settings_dir = self.base_path / "user_settings"
        self.exports_dir = self.base_path / "exports"
        self.backups_dir = self.base_path / "backups"
        
        for dir_path in [self.config_dir, self.user_settings_dir, self.exports_dir, self.backups_dir]:
            dir_path.mkdir(exist_ok=True)
    
    def save_config(self, config_name: str, data: Dict[str, Any], backup: bool = True) -> bool:
        """保存配置文件"""
        try:
            file_path = self.config_dir / f"{config_name}.json"
            
            # 备份现有文件
            if backup and file_path.exists():
                backup_path = self.backups_dir / f"{config_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                shutil.copy2(file_path, backup_path)
            
            # 添加元数据
            config_data = {
                "metadata": {
                    "name": config_name,
                    "created_at": datetime.utcnow().isoformat(),
                    "version": "1.0"
                },
                "data": data
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            logger.info("配置文件保存成功", config_name=config_name, file_path=str(file_path))
            return True
            
        except Exception as e:
            logger.error("配置文件保存失败", config_name=config_name, error=str(e))
            return False
    
    def load_config(self, config_name: str) -> Optional[Dict[str, Any]]:
        """加载配置文件"""
        try:
            file_path = self.config_dir / f"{config_name}.json"
            
            if not file_path.exists():
                return None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 返回数据部分，忽略元数据
            return config_data.get("data", config_data)
            
        except Exception as e:
            logger.error("配置文件加载失败", config_name=config_name, error=str(e))
            return None
    
    def save_user_settings(self, user_id: int, settings_data: Dict[str, Any]) -> bool:
        """保存用户设置"""
        try:
            file_path = self.user_settings_dir / f"user_{user_id}.json"
            
            user_settings = {
                "metadata": {
                    "user_id": user_id,
                    "updated_at": datetime.utcnow().isoformat(),
                    "version": "1.0"
                },
                "settings": settings_data
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(user_settings, f, indent=2, ensure_ascii=False)
            
            logger.debug("用户设置保存成功", user_id=user_id)
            return True
            
        except Exception as e:
            logger.error("用户设置保存失败", user_id=user_id, error=str(e))
            return False
    
    def load_user_settings(self, user_id: int) -> Optional[Dict[str, Any]]:
        """加载用户设置"""
        try:
            file_path = self.user_settings_dir / f"user_{user_id}.json"
            
            if not file_path.exists():
                return None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                user_data = json.load(f)
            
            return user_data.get("settings", {})
            
        except Exception as e:
            logger.error("用户设置加载失败", user_id=user_id, error=str(e))
            return None
    
    def export_data(self, export_name: str, data: Any, include_timestamp: bool = True) -> bool:
        """导出数据到JSON文件"""
        try:
            if include_timestamp:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"{export_name}_{timestamp}.json"
            else:
                filename = f"{export_name}.json"
            
            file_path = self.exports_dir / filename
            
            export_data = {
                "metadata": {
                    "export_name": export_name,
                    "exported_at": datetime.utcnow().isoformat(),
                    "version": "1.0"
                },
                "data": data
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info("数据导出成功", export_name=export_name, file_path=str(file_path))
            return True
            
        except Exception as e:
            logger.error("数据导出失败", export_name=export_name, error=str(e))
            return False
    
    def import_data(self, file_path: str) -> Optional[Dict[str, Any]]:
        """从JSON文件导入数据"""
        try:
            path = Path(file_path)
            
            if not path.exists():
                logger.error("导入文件不存在", file_path=file_path)
                return None
            
            with open(path, 'r', encoding='utf-8') as f:
                import_data = json.load(f)
            
            logger.info("数据导入成功", file_path=file_path)
            return import_data.get("data", import_data)
            
        except Exception as e:
            logger.error("数据导入失败", file_path=file_path, error=str(e))
            return None
    
    def list_configs(self) -> List[str]:
        """列出所有配置文件"""
        try:
            configs = []
            for file_path in self.config_dir.glob("*.json"):
                configs.append(file_path.stem)
            return sorted(configs)
        except Exception as e:
            logger.error("列出配置文件失败", error=str(e))
            return []
    
    def list_user_settings(self) -> List[int]:
        """列出所有用户设置"""
        try:
            user_ids = []
            for file_path in self.user_settings_dir.glob("user_*.json"):
                try:
                    user_id = int(file_path.stem.replace("user_", ""))
                    user_ids.append(user_id)
                except ValueError:
                    continue
            return sorted(user_ids)
        except Exception as e:
            logger.error("列出用户设置失败", error=str(e))
            return []
    
    def list_exports(self) -> List[Dict[str, Any]]:
        """列出所有导出文件"""
        try:
            exports = []
            for file_path in self.exports_dir.glob("*.json"):
                stat = file_path.stat()
                exports.append({
                    "name": file_path.name,
                    "path": str(file_path),
                    "size": stat.st_size,
                    "created_at": datetime.fromtimestamp(stat.st_ctime).isoformat()
                })
            return sorted(exports, key=lambda x: x["created_at"], reverse=True)
        except Exception as e:
            logger.error("列出导出文件失败", error=str(e))
            return []
    
    def cleanup_old_backups(self, keep_days: int = 30) -> int:
        """清理旧备份文件"""
        try:
            cutoff_time = datetime.now().timestamp() - (keep_days * 24 * 3600)
            cleaned_count = 0
            
            for file_path in self.backups_dir.glob("*.json"):
                if file_path.stat().st_ctime < cutoff_time:
                    file_path.unlink()
                    cleaned_count += 1
            
            if cleaned_count > 0:
                logger.info("清理旧备份文件完成", cleaned_count=cleaned_count, keep_days=keep_days)
            
            return cleaned_count
            
        except Exception as e:
            logger.error("清理旧备份文件失败", error=str(e))
            return 0


# 全局JSON存储实例
json_storage = JSONStorage()


class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self.storage = json_storage
        self._config_cache = {}
    
    def get_system_config(self, config_name: str, default: Any = None) -> Any:
        """获取系统配置"""
        if config_name in self._config_cache:
            return self._config_cache[config_name]
        
        config = self.storage.load_config(config_name)
        if config is not None:
            self._config_cache[config_name] = config
            return config
        
        return default
    
    def set_system_config(self, config_name: str, config_data: Dict[str, Any]) -> bool:
        """设置系统配置"""
        success = self.storage.save_config(config_name, config_data)
        if success:
            self._config_cache[config_name] = config_data
        return success
    
    def get_user_setting(self, user_id: int, setting_key: str, default: Any = None) -> Any:
        """获取用户设置"""
        settings = self.storage.load_user_settings(user_id)
        if settings:
            return settings.get(setting_key, default)
        return default
    
    def set_user_setting(self, user_id: int, setting_key: str, value: Any) -> bool:
        """设置用户设置"""
        settings = self.storage.load_user_settings(user_id) or {}
        settings[setting_key] = value
        return self.storage.save_user_settings(user_id, settings)
    
    def clear_config_cache(self):
        """清除配置缓存"""
        self._config_cache.clear()


# 全局配置管理器
config_manager = ConfigManager()
