import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import PictureFilledSvg from "@ant-design/icons-svg/es/asn/PictureFilled";
import AntdIcon from "../components/AntdIcon";
var PictureFilled = function PictureFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: PictureFilledSvg
  }));
};

/**![picture](data:image/svg+xml;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) */
var RefIcon = /*#__PURE__*/React.forwardRef(PictureFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'PictureFilled';
}
export default RefIcon;