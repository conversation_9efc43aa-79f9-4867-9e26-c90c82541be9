"""
数据同步服务
"""
import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from enum import Enum

from ..domain.character.repositories import CharacterRepository
from ..domain.shared.value_objects import CharacterId
from ..infrastructure.esi import ESIService
from ..infrastructure.persistence.models.auth import TokenModel
from ..infrastructure.config import settings
from ..infrastructure.config.logging import get_logger

logger = get_logger(__name__)


class SyncType(Enum):
    """同步类型"""
    REALTIME = "realtime"      # 实时数据：位置、在线状态
    FREQUENT = "frequent"      # 频繁数据：市场订单、邮件
    REGULAR = "regular"        # 常规数据：技能、资产
    DAILY = "daily"           # 每日数据：静态信息


class SyncStatus(Enum):
    """同步状态"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    SKIPPED = "skipped"


class DataSyncService:
    """数据同步服务"""
    
    def __init__(self, 
                 character_repository: CharacterRepository,
                 esi_service: ESIService,
                 token_repository):  # 令牌仓储
        self.character_repository = character_repository
        self.esi_service = esi_service
        self.token_repository = token_repository
        
        # 同步配置
        self.sync_intervals = {
            SyncType.REALTIME: settings.sync_interval_realtime,
            SyncType.FREQUENT: settings.sync_interval_frequent,
            SyncType.REGULAR: settings.sync_interval_regular,
            SyncType.DAILY: settings.sync_interval_daily
        }
        
        # 同步功能开关
        self.sync_features = {
            "character_location": settings.sync_character_location,
            "character_online": settings.sync_character_online,
            "character_skills": settings.sync_character_skills,
            "character_assets": settings.sync_character_assets,
            "character_wallet": settings.sync_character_wallet,
            "market_orders": settings.sync_market_orders,
            "industry_jobs": settings.sync_industry_jobs,
            "corporation_info": settings.sync_corporation_info
        }
        
        # 同步状态跟踪
        self.sync_status: Dict[str, Dict[str, Any]] = {}
        self.running_syncs: Dict[str, asyncio.Task] = {}
    
    async def start_sync_scheduler(self) -> None:
        """启动同步调度器"""
        logger.info("启动数据同步调度器")
        
        # 创建不同类型的同步任务
        tasks = []
        
        if self.sync_features.get("character_location") or self.sync_features.get("character_online"):
            tasks.append(self._create_sync_task(SyncType.REALTIME, self._sync_realtime_data))
        
        if self.sync_features.get("market_orders"):
            tasks.append(self._create_sync_task(SyncType.FREQUENT, self._sync_frequent_data))
        
        if (self.sync_features.get("character_skills") or 
            self.sync_features.get("character_assets") or 
            self.sync_features.get("character_wallet")):
            tasks.append(self._create_sync_task(SyncType.REGULAR, self._sync_regular_data))
        
        if self.sync_features.get("corporation_info"):
            tasks.append(self._create_sync_task(SyncType.DAILY, self._sync_daily_data))
        
        # 启动所有同步任务
        await asyncio.gather(*tasks, return_exceptions=True)
    
    async def sync_character_data(self, character_id: int, sync_types: List[SyncType] = None) -> Dict[str, Any]:
        """同步指定角色的数据"""
        if sync_types is None:
            sync_types = [SyncType.REALTIME, SyncType.FREQUENT, SyncType.REGULAR]
        
        sync_results = {}
        
        try:
            # 获取角色令牌
            token = await self._get_character_token(character_id)
            if not token:
                return {"error": "No valid token found for character"}
            
            # 获取角色实体
            char_id = CharacterId(character_id)
            character = await self.character_repository.get_by_id(char_id)
            if not character:
                return {"error": "Character not found"}
            
            # 执行不同类型的同步
            for sync_type in sync_types:
                try:
                    if sync_type == SyncType.REALTIME:
                        result = await self._sync_character_realtime(character, token)
                    elif sync_type == SyncType.FREQUENT:
                        result = await self._sync_character_frequent(character, token)
                    elif sync_type == SyncType.REGULAR:
                        result = await self._sync_character_regular(character, token)
                    elif sync_type == SyncType.DAILY:
                        result = await self._sync_character_daily(character, token)
                    else:
                        continue
                    
                    sync_results[sync_type.value] = result
                    
                except Exception as e:
                    logger.error(f"同步{sync_type.value}数据失败", 
                               character_id=character_id, error=str(e))
                    sync_results[sync_type.value] = {"status": "failed", "error": str(e)}
            
            # 保存角色
            await self.character_repository.save(character)
            
            return {
                "character_id": character_id,
                "sync_time": datetime.utcnow().isoformat(),
                "results": sync_results
            }
            
        except Exception as e:
            logger.error("同步角色数据失败", character_id=character_id, error=str(e))
            return {"error": str(e)}
    
    async def get_sync_status(self) -> Dict[str, Any]:
        """获取同步状态"""
        return {
            "sync_status": self.sync_status,
            "running_syncs": list(self.running_syncs.keys()),
            "sync_features": self.sync_features,
            "sync_intervals": {k.value: v for k, v in self.sync_intervals.items()}
        }
    
    async def _create_sync_task(self, sync_type: SyncType, sync_func) -> asyncio.Task:
        """创建同步任务"""
        async def sync_loop():
            while True:
                try:
                    task_name = f"sync_{sync_type.value}"
                    logger.debug(f"开始{sync_type.value}数据同步")
                    
                    # 更新同步状态
                    self.sync_status[task_name] = {
                        "status": SyncStatus.RUNNING.value,
                        "start_time": datetime.utcnow().isoformat(),
                        "last_error": None
                    }
                    
                    # 执行同步
                    await sync_func()
                    
                    # 更新成功状态
                    self.sync_status[task_name].update({
                        "status": SyncStatus.SUCCESS.value,
                        "end_time": datetime.utcnow().isoformat(),
                        "last_error": None
                    })
                    
                    logger.debug(f"{sync_type.value}数据同步完成")
                    
                except Exception as e:
                    # 更新失败状态
                    task_name = f"sync_{sync_type.value}"
                    self.sync_status[task_name] = {
                        "status": SyncStatus.FAILED.value,
                        "end_time": datetime.utcnow().isoformat(),
                        "last_error": str(e)
                    }
                    
                    logger.error(f"{sync_type.value}数据同步失败", error=str(e))
                
                # 等待下次同步
                await asyncio.sleep(self.sync_intervals[sync_type])
        
        task = asyncio.create_task(sync_loop())
        self.running_syncs[f"sync_{sync_type.value}"] = task
        return task
    
    async def _sync_realtime_data(self) -> None:
        """同步实时数据"""
        # 获取所有活跃角色
        characters = await self._get_active_characters()
        
        for character in characters:
            try:
                token = await self._get_character_token(character.character_id.value)
                if not token:
                    continue
                
                await self._sync_character_realtime(character, token)
                await self.character_repository.save(character)
                
            except Exception as e:
                logger.warning("同步角色实时数据失败", 
                             character_id=character.character_id.value, error=str(e))
    
    async def _sync_frequent_data(self) -> None:
        """同步频繁数据"""
        characters = await self._get_active_characters()
        
        for character in characters:
            try:
                token = await self._get_character_token(character.character_id.value)
                if not token:
                    continue
                
                await self._sync_character_frequent(character, token)
                await self.character_repository.save(character)
                
            except Exception as e:
                logger.warning("同步角色频繁数据失败", 
                             character_id=character.character_id.value, error=str(e))
    
    async def _sync_regular_data(self) -> None:
        """同步常规数据"""
        characters = await self._get_active_characters()
        
        for character in characters:
            try:
                token = await self._get_character_token(character.character_id.value)
                if not token:
                    continue
                
                await self._sync_character_regular(character, token)
                await self.character_repository.save(character)
                
            except Exception as e:
                logger.warning("同步角色常规数据失败", 
                             character_id=character.character_id.value, error=str(e))
    
    async def _sync_daily_data(self) -> None:
        """同步每日数据"""
        # 同步静态数据、公司信息等
        logger.info("开始同步每日数据")
        
        # 这里可以添加静态数据同步逻辑
        # 例如：物品类型、星系信息、公司信息等
        
        logger.info("每日数据同步完成")
    
    async def _sync_character_realtime(self, character, token: str) -> Dict[str, Any]:
        """同步角色实时数据"""
        results = {}
        character_id = character.character_id.value
        
        # 同步位置
        if self.sync_features.get("character_location"):
            try:
                esi_location = await self.esi_service.get_character_location(character_id, token)
                from ..domain.shared.value_objects import Location, SystemId
                location = Location(
                    system_id=SystemId(esi_location.solar_system_id),
                    station_id=esi_location.station_id,
                    structure_id=esi_location.structure_id
                )
                character.update_location(location)
                results["location"] = "success"
            except Exception as e:
                results["location"] = f"failed: {str(e)}"
        
        # 同步在线状态
        if self.sync_features.get("character_online"):
            try:
                esi_online = await self.esi_service.get_character_online(character_id, token)
                character.set_online_status(esi_online.online, esi_online.last_login)
                results["online_status"] = "success"
            except Exception as e:
                results["online_status"] = f"failed: {str(e)}"
        
        return results
    
    async def _sync_character_frequent(self, character, token: str) -> Dict[str, Any]:
        """同步角色频繁数据"""
        results = {}
        # 这里可以添加市场订单、邮件等同步逻辑
        return results
    
    async def _sync_character_regular(self, character, token: str) -> Dict[str, Any]:
        """同步角色常规数据"""
        results = {}
        character_id = character.character_id.value
        
        # 同步技能
        if self.sync_features.get("character_skills"):
            try:
                esi_skills = await self.esi_service.get_character_skills(character_id, token)
                from ..domain.character.value_objects import Skill
                from ..domain.shared.value_objects import SkillPoints
                
                for esi_skill in esi_skills:
                    skill = Skill(
                        skill_id=esi_skill.skill_id,
                        skillpoints_in_skill=SkillPoints(esi_skill.skillpoints_in_skill),
                        trained_skill_level=esi_skill.trained_skill_level,
                        active_skill_level=esi_skill.active_skill_level
                    )
                    character.update_skill(skill)
                
                results["skills"] = "success"
            except Exception as e:
                results["skills"] = f"failed: {str(e)}"
        
        # 同步钱包
        if self.sync_features.get("character_wallet"):
            try:
                from decimal import Decimal
                from ..domain.shared.value_objects import Money
                wallet_balance = await self.esi_service.get_character_wallet_balance(character_id, token)
                character.update_wallet_balance(Money(Decimal(str(wallet_balance))))
                results["wallet"] = "success"
            except Exception as e:
                results["wallet"] = f"failed: {str(e)}"
        
        return results
    
    async def _sync_character_daily(self, character, token: str) -> Dict[str, Any]:
        """同步角色每日数据"""
        results = {}
        # 这里可以添加属性、技能队列等同步逻辑
        return results
    
    async def _get_active_characters(self) -> List:
        """获取活跃角色列表"""
        # 这里可以根据业务逻辑定义"活跃"角色
        # 例如：最近登录过的、有有效令牌的等
        return await self.character_repository.find_online_characters()
    
    async def _get_character_token(self, character_id: int) -> Optional[str]:
        """获取角色的有效令牌"""
        try:
            # 这里需要实现令牌获取逻辑
            # 包括令牌刷新、过期检查等
            token = await self.token_repository.get_active_token_by_character(character_id)
            
            if not token:
                return None
            
            # 检查令牌是否过期
            if token.expires_at <= datetime.utcnow():
                # 尝试刷新令牌
                refreshed_token = await self._refresh_token(token)
                return refreshed_token.access_token if refreshed_token else None
            
            return token.access_token
            
        except Exception as e:
            logger.error("获取角色令牌失败", character_id=character_id, error=str(e))
            return None
    
    async def _refresh_token(self, token) -> Optional:
        """刷新令牌"""
        try:
            # 这里需要实现令牌刷新逻辑
            # 使用ESI的refresh_token端点
            logger.info("刷新令牌", character_id=token.character_id)
            
            # 实际的刷新逻辑需要调用ESI服务
            # refreshed_data = await self.esi_service.refresh_token(token.refresh_token)
            # 更新令牌信息
            
            return token  # 临时返回原令牌
            
        except Exception as e:
            logger.error("刷新令牌失败", character_id=token.character_id, error=str(e))
            return None
