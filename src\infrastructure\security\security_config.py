"""
安全中间件统一配置
"""
from typing import List, Dict, Any
from fastapi import FastAPI

from .cors_middleware import setup_cors_middleware
from .security_headers_middleware import setup_security_headers_middleware
from .rate_limiting_middleware import setup_rate_limiting_middleware
from .audit_middleware import setup_audit_middleware
from .rbac_middleware import init_rbac_middleware
from ..config import settings
from ..config.logging import get_logger

logger = get_logger(__name__)


class SecurityConfig:
    """安全配置类"""
    
    def __init__(self):
        self.enabled_middlewares = []
        self.security_policies = {}
    
    def setup_all_security_middlewares(self, app: FastAPI, 
                                     permission_checker=None) -> None:
        """设置所有安全中间件"""
        
        # 1. 审计中间件（最外层，记录所有请求）
        if settings.debug or getattr(settings, 'enable_audit', True):
            setup_audit_middleware(app)
            self.enabled_middlewares.append("audit")
            logger.info("✓ 审计中间件已启用")
        
        # 2. 安全头部中间件
        setup_security_headers_middleware(app)
        self.enabled_middlewares.append("security_headers")
        logger.info("✓ 安全头部中间件已启用")
        
        # 3. CORS中间件
        setup_cors_middleware(app)
        self.enabled_middlewares.append("cors")
        logger.info("✓ CORS中间件已启用")
        
        # 4. 速率限制中间件
        setup_rate_limiting_middleware(app)
        self.enabled_middlewares.append("rate_limiting")
        logger.info("✓ 速率限制中间件已启用")
        
        # 5. RBAC中间件（需要权限检查器）
        if permission_checker:
            init_rbac_middleware(permission_checker)
            self.enabled_middlewares.append("rbac")
            logger.info("✓ RBAC权限中间件已启用")
        else:
            logger.warning("⚠ RBAC权限中间件未启用（缺少权限检查器）")
        
        # 记录安全配置
        self._log_security_configuration()
    
    def _log_security_configuration(self) -> None:
        """记录安全配置信息"""
        config_info = {
            "enabled_middlewares": self.enabled_middlewares,
            "cors_origins": settings.get_cors_origins_list(),
            "rate_limiting": {
                "requests_per_minute": settings.rate_limit_requests_per_second * 60,
                "burst_limit": settings.rate_limit_burst
            },
            "security_headers": {
                "hsts_enabled": True,
                "csp_enabled": True,
                "x_frame_options": "DENY"
            }
        }
        
        logger.info("安全中间件配置完成", **config_info)
    
    def get_security_status(self) -> Dict[str, Any]:
        """获取安全状态"""
        return {
            "enabled_middlewares": self.enabled_middlewares,
            "total_middlewares": len(self.enabled_middlewares),
            "security_level": self._calculate_security_level(),
            "recommendations": self._get_security_recommendations()
        }
    
    def _calculate_security_level(self) -> str:
        """计算安全级别"""
        total_middlewares = 5  # 总共5个中间件
        enabled_count = len(self.enabled_middlewares)
        
        if enabled_count >= 5:
            return "high"
        elif enabled_count >= 3:
            return "medium"
        else:
            return "low"
    
    def _get_security_recommendations(self) -> List[str]:
        """获取安全建议"""
        recommendations = []
        
        if "audit" not in self.enabled_middlewares:
            recommendations.append("启用审计中间件以记录安全事件")
        
        if "rbac" not in self.enabled_middlewares:
            recommendations.append("启用RBAC权限中间件以加强访问控制")
        
        if "rate_limiting" not in self.enabled_middlewares:
            recommendations.append("启用速率限制中间件以防止滥用")
        
        if not settings.debug:
            if "*" in settings.get_cors_origins_list():
                recommendations.append("生产环境中应限制CORS允许的源")
        
        return recommendations


# 全局安全配置实例
security_config = SecurityConfig()


def setup_security(app: FastAPI, permission_checker=None) -> SecurityConfig:
    """设置应用安全"""
    security_config.setup_all_security_middlewares(app, permission_checker)
    return security_config


# 安全检查工具
class SecurityChecker:
    """安全检查工具"""
    
    @staticmethod
    def check_password_strength(password: str) -> Dict[str, Any]:
        """检查密码强度"""
        issues = []
        score = 0
        
        if len(password) >= 8:
            score += 1
        else:
            issues.append("密码长度至少8位")
        
        if any(c.isupper() for c in password):
            score += 1
        else:
            issues.append("需要包含大写字母")
        
        if any(c.islower() for c in password):
            score += 1
        else:
            issues.append("需要包含小写字母")
        
        if any(c.isdigit() for c in password):
            score += 1
        else:
            issues.append("需要包含数字")
        
        if any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
            score += 1
        else:
            issues.append("需要包含特殊字符")
        
        strength_levels = {
            0: "very_weak",
            1: "weak", 
            2: "fair",
            3: "good",
            4: "strong",
            5: "very_strong"
        }
        
        return {
            "score": score,
            "max_score": 5,
            "strength": strength_levels[score],
            "is_strong": score >= 4,
            "issues": issues
        }
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """验证邮箱格式"""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
    
    @staticmethod
    def is_safe_redirect_url(url: str, allowed_hosts: List[str]) -> bool:
        """检查重定向URL是否安全"""
        from urllib.parse import urlparse
        
        try:
            parsed = urlparse(url)
            
            # 相对URL是安全的
            if not parsed.netloc:
                return True
            
            # 检查是否在允许的主机列表中
            return parsed.netloc in allowed_hosts
            
        except Exception:
            return False
    
    @staticmethod
    def sanitize_input(input_str: str) -> str:
        """清理用户输入"""
        import html
        
        # HTML转义
        sanitized = html.escape(input_str)
        
        # 移除潜在的脚本标签
        import re
        sanitized = re.sub(r'<script.*?</script>', '', sanitized, flags=re.IGNORECASE | re.DOTALL)
        
        return sanitized
    
    @staticmethod
    def generate_csrf_token() -> str:
        """生成CSRF令牌"""
        import secrets
        return secrets.token_urlsafe(32)
    
    @staticmethod
    def verify_csrf_token(token: str, expected_token: str) -> bool:
        """验证CSRF令牌"""
        import hmac
        return hmac.compare_digest(token, expected_token)


# 安全事件类型
class SecurityEventType:
    """安全事件类型"""
    
    # 认证事件
    LOGIN_SUCCESS = "auth.login.success"
    LOGIN_FAILURE = "auth.login.failure"
    LOGOUT = "auth.logout"
    PASSWORD_CHANGE = "auth.password.change"
    
    # 授权事件
    ACCESS_GRANTED = "authz.access.granted"
    ACCESS_DENIED = "authz.access.denied"
    PERMISSION_ESCALATION = "authz.permission.escalation"
    
    # 安全违规
    RATE_LIMIT_EXCEEDED = "security.rate_limit.exceeded"
    SUSPICIOUS_ACTIVITY = "security.suspicious.activity"
    BRUTE_FORCE_ATTEMPT = "security.brute_force.attempt"
    
    # 数据访问
    DATA_ACCESS = "data.access"
    DATA_MODIFICATION = "data.modification"
    DATA_EXPORT = "data.export"
    
    # 系统事件
    SYSTEM_ERROR = "system.error"
    CONFIGURATION_CHANGE = "system.config.change"


class SecurityMetrics:
    """安全指标收集"""
    
    def __init__(self):
        self.metrics = {
            "authentication_attempts": 0,
            "authentication_failures": 0,
            "authorization_denials": 0,
            "rate_limit_violations": 0,
            "security_events": 0
        }
    
    def increment_metric(self, metric_name: str, value: int = 1):
        """增加指标值"""
        if metric_name in self.metrics:
            self.metrics[metric_name] += value
    
    def get_metrics(self) -> Dict[str, int]:
        """获取所有指标"""
        return self.metrics.copy()
    
    def get_security_score(self) -> float:
        """计算安全评分"""
        total_attempts = self.metrics["authentication_attempts"]
        if total_attempts == 0:
            return 1.0
        
        failure_rate = self.metrics["authentication_failures"] / total_attempts
        denial_rate = self.metrics["authorization_denials"] / max(1, total_attempts)
        
        # 简单的评分算法
        score = 1.0 - (failure_rate * 0.5 + denial_rate * 0.3)
        return max(0.0, min(1.0, score))


# 全局安全指标实例
security_metrics = SecurityMetrics()
