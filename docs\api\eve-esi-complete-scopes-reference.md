# EVE ESI 完整Scope权限参考

## 🎯 **EVE Online ESI 完整权限列表**

EVE Online ESI API 提供了**80多个**权限范围（Scopes），远超您当前使用的几个。以下是完整的分类列表：

## 📋 **完整Scope分类**

### **🤝 联盟相关 (Alliance)**
- `esi-alliances.read_contacts.v1` - 读取联盟联系人

### **💰 资产相关 (Assets)**
- `esi-assets.read_assets.v1` - 读取角色资产
- `esi-assets.read_corporation_assets.v1` - 读取公司资产

### **📅 日历相关 (Calendar)**
- `esi-calendar.read_calendar_events.v1` - 读取日历事件
- `esi-calendar.respond_calendar_events.v1` - 响应日历事件

### **🧑 角色相关 (Characters)**
- `esi-characters.read_agents_research.v1` - 读取代理研究信息
- `esi-characters.read_blueprints.v1` - 读取角色蓝图
- `esi-characters.read_contacts.v1` - 读取角色联系人
- `esi-characters.read_corporation_roles.v1` - 读取角色在公司中的职位
- `esi-characters.read_fatigue.v1` - 读取跳跃疲劳
- `esi-characters.read_fw_stats.v1` - 读取派系战争统计
- `esi-characters.read_loyalty.v1` - 读取忠诚点数
- `esi-characters.read_medals.v1` - 读取勋章
- `esi-characters.read_notifications.v1` - 读取通知
- `esi-characters.read_standings.v1` - 读取声望
- `esi-characters.read_titles.v1` - 读取头衔
- `esi-characters.write_contacts.v1` - 写入联系人

### **🧬 克隆体相关 (Clones)**
- `esi-clones.read_clones.v1` - 读取克隆体信息
- `esi-clones.read_implants.v1` - 读取植入体

### **📋 合同相关 (Contracts)**
- `esi-contracts.read_character_contracts.v1` - 读取角色合同
- `esi-contracts.read_corporation_contracts.v1` - 读取公司合同

### **🏢 公司相关 (Corporations)**
- `esi-corporations.read_blueprints.v1` - 读取公司蓝图
- `esi-corporations.read_contacts.v1` - 读取公司联系人
- `esi-corporations.read_container_logs.v1` - 读取容器日志
- `esi-corporations.read_corporation_membership.v1` - 读取公司成员
- `esi-corporations.read_divisions.v1` - 读取公司部门
- `esi-corporations.read_facilities.v1` - 读取公司设施
- `esi-corporations.read_fw_stats.v1` - 读取公司派系战争统计
- `esi-corporations.read_medals.v1` - 读取公司勋章
- `esi-corporations.read_standings.v1` - 读取公司声望
- `esi-corporations.read_starbases.v1` - 读取星际基地
- `esi-corporations.read_structures.v1` - 读取建筑物
- `esi-corporations.read_titles.v1` - 读取公司头衔
- `esi-corporations.track_members.v1` - 追踪成员

### **🚢 舰船配置相关 (Fittings)**
- `esi-fittings.read_fittings.v1` - 读取舰船配置
- `esi-fittings.write_fittings.v1` - 写入舰船配置

### **⚓ 舰队相关 (Fleets)**
- `esi-fleets.read_fleet.v1` - 读取舰队信息
- `esi-fleets.write_fleet.v1` - 写入舰队信息

### **🏭 工业相关 (Industry)**
- `esi-industry.read_character_jobs.v1` - 读取角色工业任务
- `esi-industry.read_character_mining.v1` - 读取角色挖矿记录
- `esi-industry.read_corporation_jobs.v1` - 读取公司工业任务
- `esi-industry.read_corporation_mining.v1` - 读取公司挖矿记录

### **💀 击杀邮件相关 (Killmails)**
- `esi-killmails.read_corporation_killmails.v1` - 读取公司击杀邮件
- `esi-killmails.read_killmails.v1` - 读取击杀邮件

### **📍 位置相关 (Location)**
- `esi-location.read_location.v1` - 读取角色当前位置
- `esi-location.read_online.v1` - 读取角色在线状态
- `esi-location.read_ship_type.v1` - 读取当前飞船类型

### **📧 邮件相关 (Mail)**
- `esi-mail.organize_mail.v1` - 整理邮件
- `esi-mail.read_mail.v1` - 读取邮件
- `esi-mail.send_mail.v1` - 发送邮件

### **💹 市场相关 (Markets)**
- `esi-markets.read_character_orders.v1` - 读取角色市场订单
- `esi-markets.read_corporation_orders.v1` - 读取公司市场订单
- `esi-markets.structure_markets.v1` - 读取建筑物市场

### **🌍 行星相关 (Planets)**
- `esi-planets.manage_planets.v1` - 管理行星
- `esi-planets.read_customs_offices.v1` - 读取海关办公室

### **🔍 搜索相关 (Search)**
- `esi-search.search_structures.v1` - 搜索建筑物

### **🎓 技能相关 (Skills)**
- `esi-skills.read_skillqueue.v1` - 读取技能队列
- `esi-skills.read_skills.v1` - 读取角色技能

### **🎮 用户界面相关 (UI)**
- `esi-ui.open_window.v1` - 打开游戏窗口
- `esi-ui.write_waypoint.v1` - 写入航点

### **🌌 宇宙相关 (Universe)**
- `esi-universe.read_structures.v1` - 读取宇宙建筑物

### **💰 钱包相关 (Wallet)**
- `esi-wallet.read_character_wallet.v1` - 读取角色钱包
- `esi-wallet.read_corporation_wallets.v1` - 读取公司钱包

## 🎯 **推荐配置方案**

### **🔰 入门配置 (5-8个scope)**
适合个人角色管理应用：
```
esi-location.read_location.v1
esi-location.read_online.v1
esi-skills.read_skills.v1
esi-wallet.read_character_wallet.v1
esi-assets.read_assets.v1
esi-clones.read_clones.v1
```

### **🔥 标准配置 (10-15个scope)**
适合功能丰富的角色管理应用：
```
esi-location.read_location.v1
esi-location.read_online.v1
esi-location.read_ship_type.v1
esi-skills.read_skills.v1
esi-skills.read_skillqueue.v1
esi-wallet.read_character_wallet.v1
esi-assets.read_assets.v1
esi-clones.read_clones.v1
esi-mail.read_mail.v1
esi-contracts.read_character_contracts.v1
esi-industry.read_character_jobs.v1
esi-markets.read_character_orders.v1
esi-killmails.read_killmails.v1
esi-fittings.read_fittings.v1
esi-calendar.read_calendar_events.v1
```

### **🚀 企业配置 (20-30个scope)**
适合公司/联盟管理应用：
```
# 个人权限 (如标准配置)
# 加上公司权限：
esi-corporations.read_corporation_membership.v1
esi-corporations.read_contacts.v1
esi-corporations.read_structures.v1
esi-corporations.read_facilities.v1
esi-corporations.read_divisions.v1
esi-corporations.track_members.v1
esi-assets.read_corporation_assets.v1
esi-wallet.read_corporation_wallets.v1
esi-industry.read_corporation_jobs.v1
esi-markets.read_corporation_orders.v1
esi-killmails.read_corporation_killmails.v1
# 联盟权限：
esi-alliances.read_contacts.v1
```

### **🌟 完整配置 (40+个scope)**
适合综合性EVE工具：
```
# 包含上述所有scope，加上：
esi-characters.read_blueprints.v1
esi-characters.read_contacts.v1
esi-characters.read_notifications.v1
esi-characters.read_standings.v1
esi-characters.write_contacts.v1
esi-mail.send_mail.v1
esi-mail.organize_mail.v1
esi-fittings.write_fittings.v1
esi-fleets.read_fleet.v1
esi-fleets.write_fleet.v1
esi-planets.manage_planets.v1
esi-ui.open_window.v1
esi-ui.write_waypoint.v1
# ... 等等
```

## ⚠️ **重要提醒**

### **权限选择原则**
1. **最小权限原则** - 只申请实际需要的权限
2. **用户体验** - 权限越多，用户授权界面越复杂
3. **安全考虑** - 不必要的权限增加安全风险
4. **性能影响** - 某些权限的数据量很大

### **无效的Scope（切勿使用）**
❌ **这些scope不存在，使用会导致错误**：
- `esi-characters.read_characters.v1` - 不存在！
- `esi-character.read_character.v1` - 不存在！

✅ **基本角色信息在OAuth过程中自动提供，无需特殊scope**

### **权限更新流程**
1. 在EVE Developer Portal中更新应用权限
2. 更新代码中的scope配置
3. 用户需要重新登录授权
4. 测试新权限的功能

## 🔗 **参考资源**

- **EVE ESI文档**: https://esi.evetech.net/ui/
- **EVE开发者门户**: https://developers.eveonline.com/
- **ESI Swagger JSON**: https://esi.evetech.net/latest/swagger.json

---

**注意**: 此列表基于EVE ESI官方swagger文档，可能会随着游戏更新而变化。建议定期检查官方文档以获取最新信息。
