"""
错误监控服务
"""
import json
import traceback
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum

from ...infrastructure.config.logging import get_logger

logger = get_logger(__name__)


class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """错误分类"""
    API = "api"
    DATABASE = "database"
    AUTHENTICATION = "authentication"
    OAUTH = "oauth"
    THIRD_PARTY_API = "third_party_api"
    CONFIG_SYNC = "config_sync"
    SYNC = "sync"
    VALIDATION = "validation"
    NETWORK = "network"
    SYSTEM = "system"
    UNKNOWN = "unknown"


@dataclass
class ErrorReport:
    """错误报告"""
    id: str
    timestamp: str
    severity: ErrorSeverity
    category: ErrorCategory
    message: str
    exception_type: str
    traceback: str
    context: Dict[str, Any]
    user_id: Optional[str] = None
    character_id: Optional[int] = None
    request_id: Optional[str] = None
    resolved: bool = False
    resolution_notes: Optional[str] = None


class ErrorMonitor:
    """错误监控服务"""
    
    def __init__(self, error_log_dir: str = "logs/errors"):
        self.error_log_dir = Path(error_log_dir)
        self.error_log_dir.mkdir(parents=True, exist_ok=True)
        
        # 错误统计
        self._error_stats = {
            "total_errors": 0,
            "errors_by_severity": {severity.value: 0 for severity in ErrorSeverity},
            "errors_by_category": {category.value: 0 for category in ErrorCategory},
            "recent_errors": [],
            "last_reset": datetime.utcnow().isoformat()
        }
    
    def report_oauth_error(
        self,
        provider: str,
        error_code: str,
        error_description: str,
        request_params: Optional[Dict[str, Any]] = None,
        response_headers: Optional[Dict[str, str]] = None,
        user_agent: Optional[str] = None,
        user_id: Optional[str] = None,
        correlation_id: Optional[str] = None
    ) -> str:
        """报告OAuth错误"""
        import uuid

        context = {
            "provider": provider,
            "error_code": error_code,
            "error_description": error_description,
            "request_params": request_params or {},
            "response_headers": response_headers or {},
            "user_agent": user_agent,
            "correlation_id": correlation_id or str(uuid.uuid4())
        }

        # 根据错误类型确定严重程度
        severity = ErrorSeverity.HIGH
        if error_code in ['invalid_scope', 'invalid_client']:
            severity = ErrorSeverity.CRITICAL  # 配置错误，需要立即修复
        elif error_code in ['access_denied', 'invalid_grant']:
            severity = ErrorSeverity.MEDIUM   # 用户相关错误

        message = f"OAuth错误 [{provider}]: {error_code} - {error_description}"

        # 创建虚拟异常来使用现有的report_error方法
        class OAuthError(Exception):
            pass

        oauth_exception = OAuthError(message)

        return self.report_error(
            exception=oauth_exception,
            severity=severity,
            category=ErrorCategory.OAUTH,
            context=context,
            user_id=user_id
        )

    def report_third_party_api_error(
        self,
        service: str,
        endpoint: str,
        status_code: int,
        response_body: Optional[str] = None,
        request_data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        user_id: Optional[str] = None
    ) -> str:
        """报告第三方API错误"""
        context = {
            "service": service,
            "endpoint": endpoint,
            "status_code": status_code,
            "response_body": response_body,
            "request_data": request_data or {},
            "headers": headers or {}
        }

        # 根据状态码确定严重程度
        if status_code >= 500:
            severity = ErrorSeverity.HIGH
        elif status_code >= 400:
            severity = ErrorSeverity.MEDIUM
        else:
            severity = ErrorSeverity.LOW

        message = f"第三方API错误 [{service}] {endpoint}: HTTP {status_code}"

        class ThirdPartyAPIError(Exception):
            pass

        api_exception = ThirdPartyAPIError(message)

        return self.report_error(
            exception=api_exception,
            severity=severity,
            category=ErrorCategory.THIRD_PARTY_API,
            context=context,
            user_id=user_id
        )

    def report_config_sync_error(
        self,
        config_type: str,
        differences: List[str],
        backend_config: Optional[Dict[str, Any]] = None,
        frontend_config: Optional[Dict[str, Any]] = None,
        suggestions: Optional[List[str]] = None
    ) -> str:
        """报告配置同步错误"""
        context = {
            "config_type": config_type,
            "differences": differences,
            "backend_config": backend_config or {},
            "frontend_config": frontend_config or {},
            "suggestions": suggestions or []
        }

        severity = ErrorSeverity.HIGH if len(differences) > 3 else ErrorSeverity.MEDIUM
        message = f"配置同步错误 [{config_type}]: 发现 {len(differences)} 个差异"

        class ConfigSyncError(Exception):
            pass

        sync_exception = ConfigSyncError(message)

        return self.report_error(
            exception=sync_exception,
            severity=severity,
            category=ErrorCategory.CONFIG_SYNC,
            context=context
        )

    def report_error(self,
                    exception: Exception,
                    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                    category: ErrorCategory = ErrorCategory.UNKNOWN,
                    context: Optional[Dict[str, Any]] = None,
                    user_id: Optional[str] = None,
                    character_id: Optional[int] = None,
                    request_id: Optional[str] = None) -> str:
        """报告错误"""
        
        error_id = f"error_{datetime.utcnow().strftime('%Y%m%d_%H%M%S_%f')}"
        
        error_report = ErrorReport(
            id=error_id,
            timestamp=datetime.utcnow().isoformat(),
            severity=severity,
            category=category,
            message=str(exception),
            exception_type=type(exception).__name__,
            traceback=traceback.format_exc(),
            context=context or {},
            user_id=user_id,
            character_id=character_id,
            request_id=request_id
        )
        
        # 保存错误报告
        self._save_error_report(error_report)
        
        # 更新统计
        self._update_error_stats(error_report)
        
        # 记录日志
        logger.error(
            f"错误报告 {error_id}",
            severity=severity.value,
            category=category.value,
            exception_type=error_report.exception_type,
            message=error_report.message,
            user_id=user_id,
            character_id=character_id,
            request_id=request_id
        )
        
        # 如果是严重错误，立即通知
        if severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
            self._send_critical_alert(error_report)
        
        return error_id
    
    def _save_error_report(self, error_report: ErrorReport) -> None:
        """保存错误报告到文件"""
        try:
            # 按日期组织错误报告
            date_str = datetime.utcnow().strftime('%Y-%m-%d')
            error_file = self.error_log_dir / f"errors_{date_str}.json"
            
            # 读取现有错误
            errors = []
            if error_file.exists():
                try:
                    with open(error_file, 'r', encoding='utf-8') as f:
                        errors = json.load(f)
                except json.JSONDecodeError:
                    errors = []
            
            # 添加新错误
            error_dict = asdict(error_report)
            error_dict['severity'] = error_report.severity.value
            error_dict['category'] = error_report.category.value
            errors.append(error_dict)
            
            # 保存回文件
            with open(error_file, 'w', encoding='utf-8') as f:
                json.dump(errors, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"保存错误报告失败: {e}")
    
    def _update_error_stats(self, error_report: ErrorReport) -> None:
        """更新错误统计"""
        self._error_stats["total_errors"] += 1
        self._error_stats["errors_by_severity"][error_report.severity.value] += 1
        self._error_stats["errors_by_category"][error_report.category.value] += 1
        
        # 保持最近错误列表（最多100个）
        self._error_stats["recent_errors"].append({
            "id": error_report.id,
            "timestamp": error_report.timestamp,
            "severity": error_report.severity.value,
            "category": error_report.category.value,
            "message": error_report.message[:100]  # 截断长消息
        })
        
        if len(self._error_stats["recent_errors"]) > 100:
            self._error_stats["recent_errors"] = self._error_stats["recent_errors"][-100:]
    
    def _send_critical_alert(self, error_report: ErrorReport) -> None:
        """发送严重错误警报"""
        # 这里可以集成邮件、Slack、钉钉等通知服务
        logger.critical(
            f"严重错误警报: {error_report.id}",
            severity=error_report.severity.value,
            category=error_report.category.value,
            message=error_report.message,
            user_id=error_report.user_id,
            character_id=error_report.character_id
        )
        
        # 可以在这里添加实际的通知逻辑
        # 例如：发送邮件、调用webhook等
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计信息"""
        return {
            **self._error_stats,
            "uptime_hours": (
                datetime.utcnow() - datetime.fromisoformat(self._error_stats["last_reset"])
            ).total_seconds() / 3600
        }
    
    def get_recent_errors(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取最近的错误"""
        return self._error_stats["recent_errors"][-limit:]
    
    def get_errors_by_date(self, date: str) -> List[Dict[str, Any]]:
        """获取指定日期的错误"""
        try:
            error_file = self.error_log_dir / f"errors_{date}.json"
            if error_file.exists():
                with open(error_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return []
        except Exception as e:
            logger.error(f"读取错误日志失败: {e}")
            return []
    
    def resolve_error(self, error_id: str, resolution_notes: str) -> bool:
        """标记错误为已解决"""
        try:
            # 查找并更新错误状态
            # 这里需要遍历错误文件来找到对应的错误
            for error_file in self.error_log_dir.glob("errors_*.json"):
                try:
                    with open(error_file, 'r', encoding='utf-8') as f:
                        errors = json.load(f)
                    
                    for error in errors:
                        if error.get('id') == error_id:
                            error['resolved'] = True
                            error['resolution_notes'] = resolution_notes
                            error['resolved_at'] = datetime.utcnow().isoformat()
                            
                            with open(error_file, 'w', encoding='utf-8') as f:
                                json.dump(errors, f, indent=2, ensure_ascii=False)
                            
                            logger.info(f"错误 {error_id} 已标记为解决")
                            return True
                            
                except json.JSONDecodeError:
                    continue
            
            logger.warning(f"未找到错误 {error_id}")
            return False
            
        except Exception as e:
            logger.error(f"解决错误失败: {e}")
            return False
    
    def cleanup_old_errors(self, days: int = 30) -> int:
        """清理旧的错误日志"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            cleaned_count = 0
            
            for error_file in self.error_log_dir.glob("errors_*.json"):
                try:
                    # 从文件名提取日期
                    date_str = error_file.stem.replace('errors_', '')
                    file_date = datetime.strptime(date_str, '%Y-%m-%d')
                    
                    if file_date < cutoff_date:
                        error_file.unlink()
                        cleaned_count += 1
                        logger.info(f"清理旧错误日志: {error_file}")
                        
                except (ValueError, OSError) as e:
                    logger.warning(f"清理错误日志失败 {error_file}: {e}")
            
            return cleaned_count
            
        except Exception as e:
            logger.error(f"清理错误日志失败: {e}")
            return 0


# 全局错误监控实例
error_monitor = ErrorMonitor()


def report_error(exception: Exception, 
                severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                category: ErrorCategory = ErrorCategory.UNKNOWN,
                **kwargs) -> str:
    """便利函数：报告错误"""
    return error_monitor.report_error(exception, severity, category, **kwargs)


def get_error_stats() -> Dict[str, Any]:
    """便利函数：获取错误统计"""
    return error_monitor.get_error_statistics()
