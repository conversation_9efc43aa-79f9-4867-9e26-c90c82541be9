#!/usr/bin/env python3
"""
测试修复后的启动功能
"""
import subprocess
import sys
import time
import os
from pathlib import Path

def test_npm_availability():
    """测试npm可用性"""
    print("🧪 测试npm可用性...")
    
    try:
        # 测试npm命令
        result = subprocess.run(
            ["npm", "--version"],
            capture_output=True,
            text=True,
            timeout=10,
            shell=True
        )
        
        if result.returncode == 0:
            print(f"   ✅ npm版本: {result.stdout.strip()}")
            return True
        else:
            print(f"   ❌ npm命令失败: {result.stderr}")
            return False
            
    except FileNotFoundError:
        print("   ❌ npm未找到")
        return False
    except Exception as e:
        print(f"   ❌ npm测试失败: {e}")
        return False

def test_frontend_startup():
    """测试前端启动"""
    print("\n🧪 测试前端启动...")
    
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("   ❌ frontend目录不存在")
        return False
    
    try:
        # 启动前端服务器
        print("   🚀 启动前端服务器...")
        
        npm_cmd = "npm"
        if os.name == 'nt':  # Windows系统
            npm_cmd = "npm.cmd"
        
        process = subprocess.Popen(
            [npm_cmd, "run", "dev"],
            cwd=frontend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            shell=True
        )
        
        # 等待启动
        print("   ⏳ 等待前端服务器启动...")
        time.sleep(10)
        
        # 检查进程状态
        if process.poll() is None:
            print("   ✅ 前端服务器进程正在运行")
            
            # 测试端口
            try:
                import urllib.request
                response = urllib.request.urlopen('http://localhost:3000', timeout=5)
                if response.getcode() == 200:
                    print("   ✅ 前端服务器响应正常")
                    success = True
                else:
                    print(f"   ⚠️  前端服务器响应异常: {response.getcode()}")
                    success = False
            except Exception as e:
                print(f"   ⚠️  前端服务器连接测试失败: {e}")
                success = False
            
            # 停止进程
            process.terminate()
            try:
                process.wait(timeout=5)
                print("   ✅ 前端服务器已停止")
            except subprocess.TimeoutExpired:
                process.kill()
                print("   ⚠️  强制终止前端服务器")
            
            return success
        else:
            # 进程已退出
            stdout, stderr = process.communicate()
            print("   ❌ 前端服务器启动失败")
            if stdout:
                print(f"      输出: {stdout[:200]}...")
            return False
            
    except Exception as e:
        print(f"   ❌ 前端启动测试失败: {e}")
        return False

def test_pydantic_warning():
    """测试Pydantic警告修复"""
    print("\n🧪 测试Pydantic配置...")
    
    try:
        # 导入ESI模型
        from src.infrastructure.esi.models import ESICharacterInfo
        print("   ✅ ESI模型导入成功")
        
        # 创建测试实例
        test_data = {
            "character_id": 123456,
            "name": "Test Character",
            "corporation_id": 789,
            "race_id": 1,
            "bloodline_id": 1,
            "gender": "Male",
            "birthday": "2023-01-01T00:00:00Z"
        }
        
        character = ESICharacterInfo(**test_data)
        print("   ✅ ESI模型实例化成功")
        print(f"      角色名称: {character.name}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Pydantic测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 测试修复后的启动功能")
    print("=" * 50)
    
    tests = [
        ("npm可用性", test_npm_availability),
        ("Pydantic配置", test_pydantic_warning),
        ("前端启动", test_frontend_startup),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"   ✅ {test_name} 测试通过")
            else:
                print(f"   ❌ {test_name} 测试失败")
        except Exception as e:
            print(f"   ❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有修复都成功！")
        print("\n📝 现在可以运行:")
        print("   python scripts/deployment/production-start.py")
        return 0
    else:
        print("⚠️  部分修复需要进一步处理")
        return 1

if __name__ == "__main__":
    sys.exit(main())
