"""
认证领域值对象
"""
import secrets
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import List, Optional
from uuid import UUID, uuid4

from ..shared.base_entity import ValueObject
from ..shared.value_objects import UserId, CharacterId


@dataclass(frozen=True)
class AccessToken(ValueObject):
    """访问令牌值对象"""
    token: str
    expires_at: datetime
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        return datetime.utcnow() >= self.expires_at
    
    def expires_in_seconds(self) -> int:
        """获取剩余有效时间（秒）"""
        delta = self.expires_at - datetime.utcnow()
        return max(0, int(delta.total_seconds()))
    
    def is_expiring_soon(self, buffer_minutes: int = 5) -> bool:
        """检查是否即将过期"""
        buffer = timedelta(minutes=buffer_minutes)
        return datetime.utcnow() >= (self.expires_at - buffer)


@dataclass(frozen=True)
class RefreshToken(ValueObject):
    """刷新令牌值对象"""
    token: str
    
    def is_valid(self) -> bool:
        """检查令牌格式是否有效"""
        return bool(self.token and len(self.token) > 10)


@dataclass(frozen=True)
class Scope(ValueObject):
    """权限范围值对象"""
    name: str
    description: str = ""
    
    def __post_init__(self):
        if not self.name:
            raise ValueError("权限范围名称不能为空")
    
    @classmethod
    def from_string(cls, scope_string: str) -> 'Scope':
        """从字符串创建权限范围"""
        return cls(name=scope_string.strip())


@dataclass(frozen=True)
class AuthorizationCode(ValueObject):
    """授权码值对象"""
    code: str
    state: str
    expires_at: datetime
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        return datetime.utcnow() >= self.expires_at
    
    def is_valid(self) -> bool:
        """检查是否有效"""
        return bool(self.code and self.state and not self.is_expired())


@dataclass(frozen=True)
class SessionToken(ValueObject):
    """会话令牌值对象"""
    token_id: UUID
    user_id: UserId
    character_id: CharacterId
    token: str
    created_at: datetime
    expires_at: datetime
    
    @classmethod
    def create(cls, user_id: UserId, character_id: CharacterId, 
               expires_in: timedelta) -> 'SessionToken':
        """创建新的会话令牌"""
        now = datetime.utcnow()
        token = secrets.token_urlsafe(32)
        
        return cls(
            token_id=uuid4(),
            user_id=user_id,
            character_id=character_id,
            token=token,
            created_at=now,
            expires_at=now + expires_in
        )
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        return datetime.utcnow() >= self.expires_at
    
    def is_valid(self) -> bool:
        """检查是否有效"""
        return bool(self.token and not self.is_expired())
    
    def expires_in_seconds(self) -> int:
        """获取剩余有效时间（秒）"""
        delta = self.expires_at - datetime.utcnow()
        return max(0, int(delta.total_seconds()))


@dataclass(frozen=True)
class CharacterBinding(ValueObject):
    """角色绑定值对象"""
    character_id: CharacterId
    access_token: AccessToken
    refresh_token: RefreshToken
    scopes: List[Scope]
    bound_at: datetime
    last_used_at: Optional[datetime] = None
    
    def is_token_valid(self) -> bool:
        """检查令牌是否有效"""
        return not self.access_token.is_expired()
    
    def needs_refresh(self, buffer_minutes: int = 5) -> bool:
        """检查是否需要刷新令牌"""
        return self.access_token.is_expiring_soon(buffer_minutes)
    
    def has_scope(self, required_scope: str) -> bool:
        """检查是否具有指定权限"""
        return any(scope.name == required_scope for scope in self.scopes)
    
    def has_scopes(self, required_scopes: List[str]) -> bool:
        """检查是否具有所有指定权限"""
        scope_names = {scope.name for scope in self.scopes}
        return all(scope in scope_names for scope in required_scopes)
    
    def refresh_token(self, new_access_token: AccessToken, 
                     new_refresh_token: Optional[RefreshToken] = None) -> 'CharacterBinding':
        """刷新令牌"""
        return CharacterBinding(
            character_id=self.character_id,
            access_token=new_access_token,
            refresh_token=new_refresh_token or self.refresh_token,
            scopes=self.scopes,
            bound_at=self.bound_at,
            last_used_at=datetime.utcnow()
        )
    
    def mark_as_used(self) -> 'CharacterBinding':
        """标记为已使用"""
        return CharacterBinding(
            character_id=self.character_id,
            access_token=self.access_token,
            refresh_token=self.refresh_token,
            scopes=self.scopes,
            bound_at=self.bound_at,
            last_used_at=datetime.utcnow()
        )


@dataclass(frozen=True)
class PKCEChallenge(ValueObject):
    """PKCE挑战值对象"""
    code_verifier: str
    code_challenge: str
    code_challenge_method: str = "S256"
    
    @classmethod
    def generate(cls) -> 'PKCEChallenge':
        """生成PKCE挑战"""
        import base64
        import hashlib
        import secrets
        
        # 生成code_verifier
        code_verifier = base64.urlsafe_b64encode(
            secrets.token_bytes(32)
        ).decode('utf-8').rstrip('=')
        
        # 生成code_challenge
        code_challenge = base64.urlsafe_b64encode(
            hashlib.sha256(code_verifier.encode('utf-8')).digest()
        ).decode('utf-8').rstrip('=')
        
        return cls(
            code_verifier=code_verifier,
            code_challenge=code_challenge,
            code_challenge_method="S256"
        )
    
    def verify(self, received_verifier: str) -> bool:
        """验证code_verifier"""
        return self.code_verifier == received_verifier


@dataclass(frozen=True)
class AuthenticationRequest(ValueObject):
    """认证请求值对象"""
    request_id: UUID
    scopes: List[Scope]
    redirect_url: Optional[str]
    pkce_challenge: Optional[PKCEChallenge]
    state: str
    created_at: datetime
    expires_at: datetime
    
    @classmethod
    def create(cls, scopes: List[Scope], redirect_url: Optional[str] = None,
               use_pkce: bool = True, expires_in_minutes: int = 10) -> 'AuthenticationRequest':
        """创建认证请求"""
        now = datetime.utcnow()
        request_id = uuid4()
        state = secrets.token_urlsafe(32)
        pkce_challenge = PKCEChallenge.generate() if use_pkce else None
        
        return cls(
            request_id=request_id,
            scopes=scopes,
            redirect_url=redirect_url,
            pkce_challenge=pkce_challenge,
            state=state,
            created_at=now,
            expires_at=now + timedelta(minutes=expires_in_minutes)
        )
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        return datetime.utcnow() >= self.expires_at
    
    def is_valid(self) -> bool:
        """检查是否有效"""
        return not self.is_expired() and bool(self.scopes)
    
    def get_scope_names(self) -> List[str]:
        """获取权限范围名称列表"""
        return [scope.name for scope in self.scopes]
