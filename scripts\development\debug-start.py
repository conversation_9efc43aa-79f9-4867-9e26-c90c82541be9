#!/usr/bin/env python3
"""
调试启动脚本
逐步检查每个组件
"""
import sys
import os
from pathlib import Path

def test_basic_imports():
    """测试基础导入"""
    print("🔍 测试基础导入...")
    
    try:
        import pydantic
        print(f"✅ pydantic {pydantic.__version__}")
    except ImportError as e:
        print(f"❌ pydantic: {e}")
        return False
    
    try:
        import fastapi
        print(f"✅ fastapi {fastapi.__version__}")
    except ImportError as e:
        print(f"❌ fastapi: {e}")
        return False
    
    try:
        import uvicorn
        print(f"✅ uvicorn")
    except ImportError as e:
        print(f"❌ uvicorn: {e}")
        return False
    
    try:
        import pyngrok
        print(f"✅ pyngrok {pyngrok.__version__}")
    except ImportError as e:
        print(f"❌ pyngrok: {e}")
        return False
    
    return True

def test_project_structure():
    """测试项目结构"""
    print("\n🔍 测试项目结构...")
    
    project_root = Path(__file__).parent
    sys.path.insert(0, str(project_root))
    
    required_paths = [
        "src",
        "src/infrastructure",
        "src/infrastructure/config",
        "src/infrastructure/ngrok",
        "src/presentation",
        "src/presentation/api",
        ".env"
    ]
    
    for path in required_paths:
        full_path = project_root / path
        if full_path.exists():
            print(f"✅ {path}")
        else:
            print(f"❌ {path} 不存在")
            return False
    
    return True

def test_config_loading():
    """测试配置加载"""
    print("\n🔍 测试配置加载...")
    
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ .env文件加载")
    except Exception as e:
        print(f"❌ .env文件加载失败: {e}")
        return False
    
    try:
        from src.infrastructure.config import settings
        print("✅ 配置模块导入")
        print(f"   ngrok启用: {settings.ngrok_enabled}")
        print(f"   服务器端口: {settings.server_port}")
        return True
    except Exception as e:
        print(f"❌ 配置模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ngrok_manager():
    """测试ngrok管理器"""
    print("\n🔍 测试ngrok管理器...")
    
    try:
        from src.infrastructure.ngrok.manager import ngrok_manager
        print("✅ ngrok管理器导入")
        print(f"   ngrok可用: {ngrok_manager.is_available()}")
        return True
    except Exception as e:
        print(f"❌ ngrok管理器导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fastapi_app():
    """测试FastAPI应用"""
    print("\n🔍 测试FastAPI应用...")
    
    try:
        from src.presentation.api.main import app
        print("✅ FastAPI应用导入")
        print(f"   应用标题: {app.title}")
        return True
    except Exception as e:
        print(f"❌ FastAPI应用导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 EVE Online Assistant 启动诊断")
    print("=" * 50)
    
    tests = [
        ("基础导入", test_basic_imports),
        ("项目结构", test_project_structure),
        ("配置加载", test_config_loading),
        ("ngrok管理器", test_ngrok_manager),
        ("FastAPI应用", test_fastapi_app),
    ]
    
    failed_tests = []
    
    for test_name, test_func in tests:
        try:
            if not test_func():
                failed_tests.append(test_name)
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            failed_tests.append(test_name)
    
    print("\n" + "=" * 50)
    print("📋 诊断结果:")
    
    if failed_tests:
        print(f"❌ 失败的测试: {', '.join(failed_tests)}")
        print("\n💡 建议:")
        print("1. 检查依赖安装: pip install -r requirements.txt")
        print("2. 检查.env文件配置")
        print("3. 检查项目文件结构")
        return 1
    else:
        print("🎉 所有测试通过！")
        print("✅ 可以尝试启动应用: python start.py")
        return 0

if __name__ == "__main__":
    sys.exit(main())
