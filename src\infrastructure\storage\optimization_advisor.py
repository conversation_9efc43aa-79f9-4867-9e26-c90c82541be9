"""
存储优化建议工具
"""
from typing import Dict, Any, List, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass

from .performance_monitor import performance_monitor
from ..config import settings
from ..config.logging import get_logger

logger = get_logger(__name__)


@dataclass
class OptimizationRecommendation:
    """优化建议"""
    category: str
    priority: str  # high, medium, low
    title: str
    description: str
    impact: str
    implementation: str
    estimated_improvement: str


class StorageOptimizationAdvisor:
    """存储优化建议工具"""
    
    def __init__(self):
        self.recommendations = []
    
    def analyze_and_recommend(self) -> List[OptimizationRecommendation]:
        """分析存储性能并提供优化建议"""
        self.recommendations.clear()
        
        # 获取性能统计
        stats = performance_monitor.get_stats()
        recent_stats = performance_monitor.get_recent_performance(30)  # 最近30分钟
        alerts = performance_monitor.get_alerts(100)
        
        # 分析各个方面
        self._analyze_cache_performance(stats, recent_stats)
        self._analyze_storage_patterns(stats)
        self._analyze_error_rates(stats)
        self._analyze_throughput(stats)
        self._analyze_alerts(alerts)
        self._analyze_configuration()
        
        # 按优先级排序
        priority_order = {"high": 0, "medium": 1, "low": 2}
        self.recommendations.sort(key=lambda x: priority_order.get(x.priority, 3))
        
        return self.recommendations
    
    def _analyze_cache_performance(self, stats: Dict[str, Any], recent_stats: Dict[str, Any]):
        """分析缓存性能"""
        redis_stats = stats.get("redis", {})
        
        # 检查Redis连接状态
        if not redis_stats.get("connected", False):
            self.recommendations.append(OptimizationRecommendation(
                category="connectivity",
                priority="high",
                title="Redis连接问题",
                description="Redis缓存服务器未连接，这将严重影响性能",
                impact="高 - 所有缓存操作将失败，性能大幅下降",
                implementation="检查Redis服务器状态，确保网络连接正常",
                estimated_improvement="恢复正常缓存性能，响应时间减少50-80%"
            ))
            return
        
        # 分析缓存命中率
        for storage_type, operations in recent_stats.items():
            if storage_type == "redis":
                get_ops = operations.get("get", {})
                set_ops = operations.get("set", {})
                
                if get_ops and set_ops:
                    get_count = get_ops.get("count", 0)
                    set_count = set_ops.get("count", 0)
                    
                    if get_count > 0:
                        # 估算缓存命中率
                        estimated_hit_rate = 1 - (set_count / get_count) if get_count > set_count else 0.5
                        
                        if estimated_hit_rate < 0.7:  # 命中率低于70%
                            self.recommendations.append(OptimizationRecommendation(
                                category="cache_efficiency",
                                priority="medium",
                                title="缓存命中率偏低",
                                description=f"Redis缓存命中率约为{estimated_hit_rate:.1%}，低于推荐的70%",
                                impact="中 - 增加数据库查询负担，响应时间增加",
                                implementation="调整缓存TTL策略，增加热数据的缓存时间",
                                estimated_improvement="命中率提升到80%以上，响应时间减少20-30%"
                            ))
    
    def _analyze_storage_patterns(self, stats: Dict[str, Any]):
        """分析存储模式"""
        # 检查存储类型使用情况
        storage_usage = {}
        total_operations = 0
        
        for storage_type, operations in stats.items():
            storage_ops = 0
            for op_type, op_stats in operations.items():
                storage_ops += op_stats.get("total_operations", 0)
            storage_usage[storage_type] = storage_ops
            total_operations += storage_ops
        
        if total_operations > 0:
            # 检查是否过度依赖某种存储
            for storage_type, ops_count in storage_usage.items():
                usage_ratio = ops_count / total_operations
                
                if storage_type == "pickle" and usage_ratio > 0.6:
                    self.recommendations.append(OptimizationRecommendation(
                        category="storage_balance",
                        priority="medium",
                        title="过度依赖Pickle存储",
                        description=f"Pickle存储占总操作的{usage_ratio:.1%}，可能影响性能",
                        impact="中 - Pickle I/O较慢，影响响应时间",
                        implementation="将热数据迁移到Redis缓存，优化数据访问模式",
                        estimated_improvement="响应时间减少30-50%"
                    ))
                
                elif storage_type == "redis" and usage_ratio > 0.8:
                    self.recommendations.append(OptimizationRecommendation(
                        category="storage_balance",
                        priority="low",
                        title="Redis使用率很高",
                        description=f"Redis占总操作的{usage_ratio:.1%}，考虑内存使用情况",
                        impact="低 - 可能需要更多内存资源",
                        implementation="监控Redis内存使用，考虑数据分层存储",
                        estimated_improvement="优化内存使用，避免内存不足"
                    ))
    
    def _analyze_error_rates(self, stats: Dict[str, Any]):
        """分析错误率"""
        for storage_type, operations in stats.items():
            for op_type, op_stats in operations.items():
                error_rate = op_stats.get("error_rate", 0)
                
                if error_rate > 0.05:  # 错误率超过5%
                    priority = "high" if error_rate > 0.1 else "medium"
                    
                    self.recommendations.append(OptimizationRecommendation(
                        category="reliability",
                        priority=priority,
                        title=f"{storage_type}存储错误率过高",
                        description=f"{storage_type}的{op_type}操作错误率为{error_rate:.1%}",
                        impact="高 - 数据操作不可靠，可能导致数据丢失",
                        implementation="检查存储服务状态，增加错误处理和重试机制",
                        estimated_improvement="错误率降低到1%以下，提高系统稳定性"
                    ))
    
    def _analyze_throughput(self, stats: Dict[str, Any]):
        """分析吞吐量"""
        for storage_type, operations in stats.items():
            for op_type, op_stats in operations.items():
                avg_throughput = op_stats.get("avg_throughput_bps", 0)
                
                # 设置不同存储类型的吞吐量阈值
                thresholds = {
                    "redis": 10240,    # 10KB/s
                    "pickle": 1024,    # 1KB/s
                    "json": 512        # 512B/s
                }
                
                threshold = thresholds.get(storage_type, 1024)
                
                if avg_throughput > 0 and avg_throughput < threshold:
                    self.recommendations.append(OptimizationRecommendation(
                        category="performance",
                        priority="medium",
                        title=f"{storage_type}吞吐量偏低",
                        description=f"{storage_type}的{op_type}操作平均吞吐量为{avg_throughput:.0f}B/s",
                        impact="中 - 大数据操作耗时较长",
                        implementation="优化数据序列化，考虑数据压缩，检查I/O性能",
                        estimated_improvement="吞吐量提升2-5倍"
                    ))
    
    def _analyze_alerts(self, alerts: List[Dict[str, Any]]):
        """分析告警"""
        if not alerts:
            return
        
        # 统计告警类型
        alert_counts = {}
        recent_alerts = []
        
        cutoff_time = datetime.utcnow() - timedelta(hours=1)
        
        for alert in alerts:
            alert_type = alert.get("type", "unknown")
            alert_counts[alert_type] = alert_counts.get(alert_type, 0) + 1
            
            try:
                alert_time = datetime.fromisoformat(alert.get("timestamp", ""))
                if alert_time > cutoff_time:
                    recent_alerts.append(alert)
            except:
                pass
        
        # 分析频繁告警
        for alert_type, count in alert_counts.items():
            if count > 10:  # 超过10次告警
                self.recommendations.append(OptimizationRecommendation(
                    category="alerts",
                    priority="high",
                    title=f"频繁{alert_type}告警",
                    description=f"最近出现{count}次{alert_type}告警",
                    impact="高 - 系统性能问题，需要立即关注",
                    implementation="根据告警类型调整配置或优化代码",
                    estimated_improvement="消除性能瓶颈，提升系统稳定性"
                ))
    
    def _analyze_configuration(self):
        """分析配置"""
        # 检查缓存TTL配置
        if settings.cache_ttl_realtime > 60:
            self.recommendations.append(OptimizationRecommendation(
                category="configuration",
                priority="low",
                title="实时数据TTL过长",
                description=f"实时数据缓存TTL为{settings.cache_ttl_realtime}秒，可能影响数据实时性",
                impact="低 - 数据可能不够实时",
                implementation="将实时数据TTL调整为30秒以内",
                estimated_improvement="提高数据实时性"
            ))
        
        if settings.cache_ttl_regular < 1800:
            self.recommendations.append(OptimizationRecommendation(
                category="configuration",
                priority="low",
                title="常规数据TTL过短",
                description=f"常规数据缓存TTL为{settings.cache_ttl_regular}秒，可能导致频繁刷新",
                impact="低 - 增加不必要的API调用",
                implementation="将常规数据TTL调整为1小时以上",
                estimated_improvement="减少API调用，提高缓存效率"
            ))
        
        # 检查存储路径配置
        if not settings.storage_enable_compression:
            self.recommendations.append(OptimizationRecommendation(
                category="configuration",
                priority="medium",
                title="未启用数据压缩",
                description="数据压缩功能未启用，可能浪费存储空间",
                impact="中 - 存储空间使用效率低",
                implementation="在配置中启用storage_enable_compression",
                estimated_improvement="存储空间节省30-50%"
            ))
    
    def generate_report(self) -> Dict[str, Any]:
        """生成优化报告"""
        recommendations = self.analyze_and_recommend()
        
        # 统计建议
        priority_counts = {"high": 0, "medium": 0, "low": 0}
        category_counts = {}
        
        for rec in recommendations:
            priority_counts[rec.priority] += 1
            category_counts[rec.category] = category_counts.get(rec.category, 0) + 1
        
        return {
            "generated_at": datetime.utcnow().isoformat(),
            "total_recommendations": len(recommendations),
            "priority_breakdown": priority_counts,
            "category_breakdown": category_counts,
            "recommendations": [
                {
                    "category": rec.category,
                    "priority": rec.priority,
                    "title": rec.title,
                    "description": rec.description,
                    "impact": rec.impact,
                    "implementation": rec.implementation,
                    "estimated_improvement": rec.estimated_improvement
                }
                for rec in recommendations
            ]
        }


# 全局优化建议工具
optimization_advisor = StorageOptimizationAdvisor()
