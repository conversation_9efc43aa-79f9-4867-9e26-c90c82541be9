#!/usr/bin/env python3
"""
最终系统测试
"""
import subprocess
import time
import urllib.request
import json
import os
from pathlib import Path

def test_system_status():
    """测试系统状态"""
    print("🔍 系统状态检查")
    print("=" * 50)
    
    results = {}
    
    # 1. 测试后端
    print("1️⃣ 测试后端服务器...")
    try:
        response = urllib.request.urlopen('http://localhost:8000/health', timeout=5)
        if response.getcode() == 200:
            print("   ✅ 后端服务器正常")
            results['backend'] = True
        else:
            print(f"   ❌ 后端异常: {response.getcode()}")
            results['backend'] = False
    except Exception as e:
        print(f"   ❌ 后端不可用: {e}")
        results['backend'] = False
    
    # 2. 测试ngrok固定域名
    print("\n2️⃣ 测试ngrok固定域名...")
    try:
        response = urllib.request.urlopen('https://diverse-monitor-model.ngrok-free.app/health', timeout=10)
        if response.getcode() == 200:
            print("   ✅ ngrok固定域名正常")
            results['ngrok'] = True
        else:
            print(f"   ❌ ngrok异常: {response.getcode()}")
            results['ngrok'] = False
    except Exception as e:
        print(f"   ❌ ngrok不可用: {e}")
        results['ngrok'] = False
    
    # 3. 测试EVE登录API
    print("\n3️⃣ 测试EVE登录API...")
    try:
        data = json.dumps({"scopes": ["esi-characters.read_characters.v1"]}).encode('utf-8')
        req = urllib.request.Request(
            'http://localhost:8000/auth/login',
            data=data,
            headers={'Content-Type': 'application/json'}
        )
        response = urllib.request.urlopen(req, timeout=10)
        
        if response.getcode() == 200:
            result = json.loads(response.read().decode())
            if result.get('success') and 'login_url' in result.get('data', {}):
                login_url = result['data']['login_url']
                if 'diverse-monitor-model.ngrok-free.app' in login_url:
                    print("   ✅ EVE登录API正常，使用固定域名")
                    results['eve_api'] = True
                else:
                    print("   ⚠️  EVE登录API正常，但域名不正确")
                    results['eve_api'] = False
            else:
                print(f"   ❌ EVE登录API响应格式错误")
                results['eve_api'] = False
        else:
            print(f"   ❌ EVE登录API异常: {response.getcode()}")
            results['eve_api'] = False
    except Exception as e:
        print(f"   ❌ EVE登录API失败: {e}")
        results['eve_api'] = False
    
    # 4. 测试前端
    print("\n4️⃣ 测试前端服务器...")
    try:
        response = urllib.request.urlopen('http://localhost:3000', timeout=5)
        if response.getcode() == 200:
            print("   ✅ 前端服务器正常")
            results['frontend'] = True
        else:
            print(f"   ❌ 前端异常: {response.getcode()}")
            results['frontend'] = False
    except Exception as e:
        print(f"   ❌ 前端不可用: {e}")
        results['frontend'] = False
    
    return results

def main():
    """主函数"""
    print("🎯 EVE Online Assistant 系统测试")
    print("=" * 60)
    
    results = test_system_status()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"   🔧 后端服务器: {'✅ 正常' if results['backend'] else '❌ 异常'}")
    print(f"   🌐 ngrok固定域名: {'✅ 正常' if results['ngrok'] else '❌ 异常'}")
    print(f"   🎮 EVE登录API: {'✅ 正常' if results['eve_api'] else '❌ 异常'}")
    print(f"   🎨 前端界面: {'✅ 正常' if results['frontend'] else '❌ 异常'}")
    
    # 核心功能检查
    core_functions = results['backend'] and results['ngrok'] and results['eve_api']
    
    if core_functions:
        print("\n🎉 核心功能全部正常！")
        print("\n✅ 固定域名配置成功:")
        print("   📋 域名: diverse-monitor-model.ngrok-free.app")
        print("   📋 回调URL: https://diverse-monitor-model.ngrok-free.app/auth/callback")
        print("\n🎯 你现在可以:")
        print("   1. 更新EVE开发者门户回调URL")
        print("   2. 使用API文档测试: https://diverse-monitor-model.ngrok-free.app/docs")
        print("   3. 每次重启都使用相同的URL，无需再更新配置")
        
        if results['frontend']:
            print("\n🎨 前端界面也正常:")
            print("   📋 访问地址: http://localhost:3000")
            print("   🎮 可以通过网页界面进行EVE登录")
        else:
            print("\n⚠️  前端界面暂时不可用:")
            print("   💡 但所有功能都可以通过API使用")
            print("   📝 API文档: https://diverse-monitor-model.ngrok-free.app/docs")
        
        return 0
    else:
        print("\n❌ 核心功能有问题:")
        if not results['backend']:
            print("   🔧 后端服务器需要修复")
        if not results['ngrok']:
            print("   🌐 ngrok隧道需要修复")
        if not results['eve_api']:
            print("   🎮 EVE登录API需要修复")
        
        return 1

if __name__ == "__main__":
    exit(main())
