"""
JWT令牌管理服务
"""
import secrets
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Union
from uuid import UUID, uuid4
from enum import Enum

import jwt
from jwt.exceptions import (
    InvalidTokenError, ExpiredSignatureError, InvalidSignatureError,
    InvalidKeyError, InvalidIssuerError, InvalidAudienceError
)

from ..config import settings
from ..config.logging import get_logger
from ...domain.shared.value_objects import UserId, CharacterId
from ...domain.shared.exceptions import (
    AuthenticationError, AuthorizationError, TokenExpiredError
)

logger = get_logger(__name__)


class TokenType(Enum):
    """令牌类型枚举"""
    ACCESS = "access"
    REFRESH = "refresh"
    SESSION = "session"
    API_KEY = "api_key"


class JWTService:
    """JWT令牌管理服务"""
    
    def __init__(self):
        self.secret_key = settings.secret_key
        self.algorithm = settings.jwt_algorithm
        self.issuer = settings.app_name
        self.audience = settings.app_name
        
        # 令牌配置
        self.access_token_expire = timedelta(minutes=settings.jwt_expire_minutes)
        self.refresh_token_expire = timedelta(days=settings.jwt_refresh_expire_days)
        self.session_token_expire = timedelta(hours=24)
        self.api_key_expire = timedelta(days=365)
        
        # 令牌黑名单（在生产环境中应使用Redis等外部存储）
        self._blacklisted_tokens: set = set()
    
    def generate_access_token(self, 
                            user_id: UserId, 
                            character_id: CharacterId,
                            scopes: List[str] = None,
                            custom_claims: Dict[str, Any] = None) -> str:
        """生成访问令牌"""
        now = datetime.utcnow()
        jti = str(uuid4())
        
        payload = {
            "jti": jti,  # JWT ID
            "iss": self.issuer,  # 发行者
            "aud": self.audience,  # 受众
            "sub": str(user_id.value),  # 主题（用户ID）
            "iat": now,  # 发行时间
            "exp": now + self.access_token_expire,  # 过期时间
            "type": TokenType.ACCESS.value,
            "user_id": user_id.value,
            "character_id": character_id.value,
            "scopes": scopes or []
        }
        
        # 添加自定义声明
        if custom_claims:
            payload.update(custom_claims)
        
        token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
        
        logger.debug(
            "生成访问令牌",
            user_id=user_id.value,
            character_id=character_id.value,
            jti=jti,
            expires_at=payload["exp"].isoformat()
        )
        
        return token
    
    def generate_refresh_token(self, 
                             user_id: UserId,
                             character_id: CharacterId,
                             access_token_jti: str) -> str:
        """生成刷新令牌"""
        now = datetime.utcnow()
        jti = str(uuid4())
        
        payload = {
            "jti": jti,
            "iss": self.issuer,
            "aud": self.audience,
            "sub": str(user_id.value),
            "iat": now,
            "exp": now + self.refresh_token_expire,
            "type": TokenType.REFRESH.value,
            "user_id": user_id.value,
            "character_id": character_id.value,
            "access_token_jti": access_token_jti  # 关联的访问令牌ID
        }
        
        token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
        
        logger.debug(
            "生成刷新令牌",
            user_id=user_id.value,
            character_id=character_id.value,
            jti=jti,
            access_token_jti=access_token_jti
        )
        
        return token
    
    def generate_session_token(self, 
                             user_id: UserId,
                             character_id: CharacterId,
                             session_data: Dict[str, Any] = None) -> str:
        """生成会话令牌"""
        now = datetime.utcnow()
        jti = str(uuid4())
        
        payload = {
            "jti": jti,
            "iss": self.issuer,
            "aud": self.audience,
            "sub": str(user_id.value),
            "iat": now,
            "exp": now + self.session_token_expire,
            "type": TokenType.SESSION.value,
            "user_id": user_id.value,
            "character_id": character_id.value,
            "session_data": session_data or {}
        }
        
        token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
        
        logger.debug(
            "生成会话令牌",
            user_id=user_id.value,
            character_id=character_id.value,
            jti=jti
        )
        
        return token
    
    def generate_api_key_token(self, 
                             user_id: UserId,
                             key_name: str,
                             scopes: List[str],
                             expires_in: Optional[timedelta] = None) -> str:
        """生成API密钥令牌"""
        now = datetime.utcnow()
        jti = str(uuid4())
        expire_time = now + (expires_in or self.api_key_expire)
        
        payload = {
            "jti": jti,
            "iss": self.issuer,
            "aud": self.audience,
            "sub": str(user_id.value),
            "iat": now,
            "exp": expire_time,
            "type": TokenType.API_KEY.value,
            "user_id": user_id.value,
            "key_name": key_name,
            "scopes": scopes
        }
        
        token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
        
        logger.info(
            "生成API密钥令牌",
            user_id=user_id.value,
            key_name=key_name,
            jti=jti,
            scopes=scopes
        )
        
        return token
    
    def verify_token(self, token: str, 
                    expected_type: Optional[TokenType] = None) -> Dict[str, Any]:
        """验证令牌"""
        try:
            # 检查黑名单
            if token in self._blacklisted_tokens:
                raise AuthenticationError("Token has been revoked")
            
            # 解码令牌
            payload = jwt.decode(
                token,
                self.secret_key,
                algorithms=[self.algorithm],
                issuer=self.issuer,
                audience=self.audience,
                options={
                    "verify_signature": True,
                    "verify_exp": True,
                    "verify_iat": True,
                    "verify_iss": True,
                    "verify_aud": True
                }
            )
            
            # 验证令牌类型
            if expected_type and payload.get("type") != expected_type.value:
                raise AuthenticationError(f"Invalid token type. Expected: {expected_type.value}")
            
            # 验证必需字段
            required_fields = ["jti", "sub", "user_id", "type"]
            for field in required_fields:
                if field not in payload:
                    raise AuthenticationError(f"Missing required field: {field}")
            
            logger.debug(
                "令牌验证成功",
                jti=payload["jti"],
                user_id=payload["user_id"],
                token_type=payload["type"]
            )
            
            return payload
            
        except ExpiredSignatureError:
            logger.warning("令牌已过期", token_prefix=token[:20])
            raise TokenExpiredError("Token has expired")
        
        except InvalidSignatureError:
            logger.error("令牌签名无效", token_prefix=token[:20])
            raise AuthenticationError("Invalid token signature")
        
        except InvalidIssuerError:
            logger.error("令牌发行者无效", token_prefix=token[:20])
            raise AuthenticationError("Invalid token issuer")
        
        except InvalidAudienceError:
            logger.error("令牌受众无效", token_prefix=token[:20])
            raise AuthenticationError("Invalid token audience")
        
        except InvalidTokenError as e:
            logger.error("令牌格式无效", error=str(e), token_prefix=token[:20])
            raise AuthenticationError(f"Invalid token: {str(e)}")
    
    def refresh_access_token(self, refresh_token: str) -> Dict[str, str]:
        """使用刷新令牌生成新的访问令牌"""
        # 验证刷新令牌
        payload = self.verify_token(refresh_token, TokenType.REFRESH)
        
        user_id = UserId(payload["user_id"])
        character_id = CharacterId(payload["character_id"])
        
        # 生成新的访问令牌
        new_access_token = self.generate_access_token(user_id, character_id)
        
        # 解析新访问令牌获取JTI
        new_payload = jwt.decode(
            new_access_token, 
            self.secret_key, 
            algorithms=[self.algorithm],
            options={"verify_exp": False}
        )
        
        # 生成新的刷新令牌
        new_refresh_token = self.generate_refresh_token(
            user_id, character_id, new_payload["jti"]
        )
        
        # 将旧的刷新令牌加入黑名单
        self.revoke_token(refresh_token)
        
        logger.info(
            "令牌刷新成功",
            user_id=user_id.value,
            character_id=character_id.value,
            old_jti=payload["jti"],
            new_jti=new_payload["jti"]
        )
        
        return {
            "access_token": new_access_token,
            "refresh_token": new_refresh_token,
            "token_type": "Bearer",
            "expires_in": int(self.access_token_expire.total_seconds())
        }
    
    def revoke_token(self, token: str) -> None:
        """撤销令牌"""
        try:
            # 解码令牌获取JTI（不验证过期时间）
            payload = jwt.decode(
                token,
                self.secret_key,
                algorithms=[self.algorithm],
                options={"verify_exp": False}
            )
            
            jti = payload.get("jti")
            if jti:
                self._blacklisted_tokens.add(token)
                logger.info("令牌已撤销", jti=jti)
            
        except Exception as e:
            logger.error("撤销令牌失败", error=str(e))
    
    def decode_token_without_verification(self, token: str) -> Dict[str, Any]:
        """解码令牌但不验证（用于调试）"""
        try:
            return jwt.decode(
                token,
                options={"verify_signature": False, "verify_exp": False}
            )
        except Exception as e:
            logger.error("解码令牌失败", error=str(e))
            return {}
    
    def get_token_info(self, token: str) -> Dict[str, Any]:
        """获取令牌信息"""
        payload = self.decode_token_without_verification(token)
        
        if not payload:
            return {}
        
        exp = payload.get("exp")
        iat = payload.get("iat")
        
        return {
            "jti": payload.get("jti"),
            "user_id": payload.get("user_id"),
            "character_id": payload.get("character_id"),
            "type": payload.get("type"),
            "scopes": payload.get("scopes", []),
            "issued_at": datetime.fromtimestamp(iat) if iat else None,
            "expires_at": datetime.fromtimestamp(exp) if exp else None,
            "is_expired": datetime.utcnow() > datetime.fromtimestamp(exp) if exp else True
        }
    
    def cleanup_blacklist(self) -> None:
        """清理过期的黑名单令牌"""
        # 在生产环境中，这应该由定期任务处理
        # 这里只是一个简单的实现
        expired_tokens = []
        
        for token in self._blacklisted_tokens:
            try:
                payload = jwt.decode(
                    token,
                    options={"verify_signature": False}
                )
                exp = payload.get("exp")
                if exp and datetime.utcnow() > datetime.fromtimestamp(exp):
                    expired_tokens.append(token)
            except:
                # 如果无法解码，也从黑名单中移除
                expired_tokens.append(token)
        
        for token in expired_tokens:
            self._blacklisted_tokens.discard(token)
        
        if expired_tokens:
            logger.debug(f"清理了 {len(expired_tokens)} 个过期的黑名单令牌")


# 全局JWT服务实例
jwt_service = JWTService()
