// 基础类型定义

export interface User {
  id: string
  email: string
  username: string
  isActive: boolean
  isVerified: boolean
  createdAt: string
  lastLoginAt?: string
  characterCount: number
  mainCharacterId?: number
}

export interface Character {
  id: number
  name: string
  corporation: string
  alliance?: string
  totalSkillPoints: number
  isOnline: boolean
  location?: string
  ship?: string
  lastLogin?: string
  portraitUrl: string
  securityStatus: number
  wallet: number
  unallocatedSkillPoints: number
}

export interface AuthTokens {
  accessToken: string
  refreshToken: string
  tokenType: string
  expiresIn: number
  expiresAt: string
}

export interface LoginCredentials {
  email: string
  password: string
  rememberMe?: boolean
}

export interface RegisterData {
  email: string
  username: string
  password: string
  confirmPassword: string
}

export interface Asset {
  id: string
  typeId: number
  name: string
  group: string
  category: string
  quantity: number
  price: number
  totalValue: number
  location: string
  locationId: number
  locationFlag: string
  isSingleton: boolean
  iconUrl: string
}

export interface MarketOrder {
  id: string
  typeId: number
  typeName: string
  isBuyOrder: boolean
  price: number
  volumeRemain: number
  volumeTotal: number
  minVolume: number
  duration: number
  issued: string
  range: string
  locationId: number
  locationName: string
  regionId: number
  regionName: string
}

export interface IndustryJob {
  id: string
  activityId: number
  activityName: string
  blueprintId: number
  blueprintName: string
  blueprintTypeId: number
  runs: number
  status: 'active' | 'paused' | 'ready' | 'delivered' | 'cancelled' | 'reverted'
  startDate: string
  endDate: string
  pausedDate?: string
  completedDate?: string
  facilityId: number
  facilityName: string
  stationId: number
  cost?: number
  licensedRuns?: number
  probability?: number
  productTypeId?: number
  successfulRuns?: number
}

export interface SkillQueue {
  queuePosition: number
  skillId: number
  skillName: string
  finishedLevel: number
  trainedSkillLevel: number
  levelStartSp: number
  levelEndSp: number
  trainingStartSp: number
  startDate: string
  finishDate: string
}

export interface Notification {
  id: string
  type: string
  title: string
  message: string
  timestamp: string
  isRead: boolean
  senderId?: number
  senderName?: string
  data?: Record<string, any>
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
  errors?: Record<string, string[]>
}

export interface PaginatedResponse<T = any> {
  data: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 表格相关类型
export interface TableColumn<T = any> {
  key: string
  title: string
  dataIndex?: keyof T
  render?: (value: any, record: T, index: number) => React.ReactNode
  sorter?: boolean | ((a: T, b: T) => number)
  filters?: Array<{ text: string; value: any }>
  width?: number | string
  align?: 'left' | 'center' | 'right'
  fixed?: 'left' | 'right'
  ellipsis?: boolean
}

export interface TableProps<T = any> {
  data: T[]
  columns: TableColumn<T>[]
  loading?: boolean
  pagination?: {
    current: number
    pageSize: number
    total: number
    showSizeChanger?: boolean
    showQuickJumper?: boolean
    onChange?: (page: number, pageSize: number) => void
  }
  rowKey?: string | ((record: T) => string)
  onRow?: (record: T) => React.HTMLAttributes<HTMLTableRowElement>
  scroll?: { x?: number | string; y?: number | string }
}

// 图表相关类型
export interface ChartData {
  name: string
  value: number
  color?: string
}

export interface TimeSeriesData {
  timestamp: string
  value: number
  label?: string
}

export interface ChartOptions {
  title?: string
  subtitle?: string
  xAxisLabel?: string
  yAxisLabel?: string
  showLegend?: boolean
  showGrid?: boolean
  colors?: string[]
  height?: number
}

// 筛选器类型
export interface FilterOption {
  label: string
  value: any
  count?: number
}

export interface AssetFilters {
  search?: string
  category?: string[]
  location?: string[]
  minValue?: number
  maxValue?: number
  sortBy?: 'name' | 'quantity' | 'value' | 'location'
  sortOrder?: 'asc' | 'desc'
}

export interface MarketFilters {
  search?: string
  region?: number[]
  orderType?: 'buy' | 'sell' | 'all'
  minPrice?: number
  maxPrice?: number
  minVolume?: number
  maxVolume?: number
}

// 主题相关类型
export type ThemeMode = 'light' | 'dark' | 'auto'

export interface ThemeConfig {
  mode: ThemeMode
  primaryColor: string
  borderRadius: number
  compactMode: boolean
}

// 设置相关类型
export interface UserSettings {
  theme: ThemeConfig
  language: string
  timezone: string
  notifications: {
    email: boolean
    push: boolean
    skillQueue: boolean
    marketOrders: boolean
    industryJobs: boolean
  }
  privacy: {
    showOnlineStatus: boolean
    allowDataSharing: boolean
  }
  display: {
    itemsPerPage: number
    showTooltips: boolean
    compactTables: boolean
  }
}

// 错误类型
export interface AppError {
  code: string
  message: string
  details?: any
  timestamp: string
}

// 路由相关类型
export interface RouteConfig {
  path: string
  title: string
  icon?: string
  component: React.ComponentType
  children?: RouteConfig[]
  requireAuth?: boolean
  permissions?: string[]
}

// 权限相关类型
export interface Permission {
  id: string
  name: string
  description: string
  resource: string
  action: string
}

export interface Role {
  id: string
  name: string
  description: string
  permissions: Permission[]
  isSystemRole: boolean
}

// 统计数据类型
export interface DashboardStats {
  totalAssetValue: number
  assetValueTrend: number
  totalSkillPoints: number
  activeOrders: number
  industryJobs: number
  characterCount: number
  lastUpdateTime: string
}

export interface AssetDistribution {
  category: string
  value: number
  percentage: number
  color: string
}

// 事件类型
export interface AppEvent {
  type: string
  payload: any
  timestamp: string
}

// 工具类型
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}
