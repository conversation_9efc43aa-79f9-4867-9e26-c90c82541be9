"""
简化版数据同步路由 - 用于基础功能恢复
"""
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query
from typing import Dict, Any, List, Optional

from ....application.sync_simple import SimpleSyncService
from ....infrastructure.config.logging import get_logger
from ..dependencies import get_sync_service, get_current_user

logger = get_logger(__name__)

router = APIRouter()


@router.get("/status")
async def get_sync_status(
    sync_service: SimpleSyncService = Depends(get_sync_service),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取同步状态"""
    try:
        status = await sync_service.get_sync_status()
        return status
        
    except Exception as e:
        logger.error("获取同步状态失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get sync status")


@router.post("/character/{character_id}")
async def sync_character(
    character_id: int,
    sync_types: List[str] = Query(default=["character", "skills", "assets"], description="同步类型"),
    background_tasks: BackgroundTasks = None,
    sync_service: SimpleSyncService = Depends(get_sync_service),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """同步指定角色数据"""
    try:
        result = await sync_service.sync_character(character_id, sync_types)
        return result
        
    except Exception as e:
        logger.error("同步角色数据失败", character_id=character_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to sync character")


@router.post("/user/all")
async def sync_all_user_characters(
    background_tasks: BackgroundTasks = None,
    sync_service: SimpleSyncService = Depends(get_sync_service),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """同步用户所有角色"""
    try:
        user_id = current_user.get("user_id", 1)
        result = await sync_service.sync_all_characters(user_id)
        return result
        
    except Exception as e:
        logger.error("同步用户所有角色失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to sync all characters")


@router.get("/history")
async def get_sync_history(
    character_id: Optional[int] = Query(None, description="角色ID过滤"),
    limit: int = Query(10, ge=1, le=100, description="返回数量限制"),
    sync_service: SimpleSyncService = Depends(get_sync_service),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取同步历史"""
    try:
        result = await sync_service.get_sync_history(character_id, limit)
        return result
        
    except Exception as e:
        logger.error("获取同步历史失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get sync history")


@router.delete("/cancel/{sync_id}")
async def cancel_sync(
    sync_id: str,
    sync_service: SimpleSyncService = Depends(get_sync_service),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """取消同步"""
    try:
        result = await sync_service.cancel_sync(sync_id)
        return result
        
    except Exception as e:
        logger.error("取消同步失败", sync_id=sync_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to cancel sync")


@router.get("/service-status")
async def sync_service_status(
    sync_service: SimpleSyncService = Depends(get_sync_service)
):
    """同步服务状态"""
    return sync_service.get_service_status()


@router.get("/types")
async def get_sync_types():
    """获取支持的同步类型"""
    from ....application.sync_simple import SyncType
    
    return {
        "sync_types": [
            {
                "type": sync_type.value,
                "name": sync_type.value.title(),
                "description": f"{sync_type.value.title()} 数据同步"
            }
            for sync_type in SyncType
        ],
        "total": len(SyncType)
    }
