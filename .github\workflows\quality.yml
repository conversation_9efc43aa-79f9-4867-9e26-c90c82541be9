name: Code Quality

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # 每周一早上8点运行
    - cron: '0 8 * * 1'

jobs:
  quality-checks:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.11

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e .[dev]

    - name: Run comprehensive tests
      run: |
        python scripts/run_tests.py --coverage

    - name: Generate test report
      run: |
        python scripts/run_tests.py --report

    - name: Upload test reports
      uses: actions/upload-artifact@v3
      with:
        name: test-reports
        path: |
          htmlcov/
          test_report_*.json
          coverage.xml

    - name: Check code complexity
      run: |
        pip install radon
        radon cc src --min B
        radon mi src --min B

    - name: Check documentation coverage
      run: |
        pip install interrogate
        interrogate src --ignore-init-method --ignore-magic --ignore-module --ignore-private --fail-under 80

    - name: Dependency vulnerability scan
      run: |
        pip install safety
        safety check --json --output safety-report.json
      continue-on-error: true

    - name: License compliance check
      run: |
        pip install pip-licenses
        pip-licenses --format=json --output-file=licenses.json

    - name: Upload quality reports
      uses: actions/upload-artifact@v3
      with:
        name: quality-reports
        path: |
          safety-report.json
          licenses.json

  module-import-validation:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.11

    - name: Install package
      run: |
        python -m pip install --upgrade pip
        pip install -e .

    - name: Validate all module imports
      run: |
        python -c "
        import sys
        import importlib
        from pathlib import Path
        
        print('🔍 Validating all module imports...')
        
        # 获取所有Python文件
        src_files = list(Path('src').rglob('*.py'))
        failed_imports = []
        
        for py_file in src_files:
            if py_file.name == '__init__.py':
                continue
                
            # 转换为模块路径
            module_path = str(py_file.with_suffix('')).replace('/', '.').replace('\\\\', '.')
            
            try:
                importlib.import_module(module_path)
                print(f'✅ {module_path}')
            except Exception as e:
                print(f'❌ {module_path}: {e}')
                failed_imports.append((module_path, str(e)))
        
        if failed_imports:
            print(f'\\n❌ {len(failed_imports)} modules failed to import:')
            for module, error in failed_imports:
                print(f'   {module}: {error}')
            sys.exit(1)
        else:
            print(f'\\n🎉 All {len(src_files)} modules imported successfully!')
        "

    - name: Check __init__.py consistency
      run: |
        python -c "
        import ast
        from pathlib import Path
        
        print('🔍 Checking __init__.py consistency...')
        
        init_files = list(Path('src').rglob('__init__.py'))
        issues = []
        
        for init_file in init_files:
            try:
                with open(init_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if not content.strip():
                    continue
                    
                # 解析AST查找导入
                tree = ast.parse(content)
                imports = []
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.ImportFrom):
                        if node.module:
                            imports.extend([alias.name for alias in node.names])
                        else:
                            imports.extend([f'.{alias.name}' for alias in node.names])
                
                # 检查导入的模块是否存在
                init_dir = init_file.parent
                for imp in imports:
                    if imp.startswith('.'):
                        module_file = init_dir / f'{imp[1:]}.py'
                        if not module_file.exists():
                            issues.append(f'{init_file}: imports non-existent module {imp}')
                
                print(f'✅ {init_file}')
                
            except Exception as e:
                issues.append(f'{init_file}: parsing error - {e}')
        
        if issues:
            print(f'\\n❌ Found {len(issues)} issues:')
            for issue in issues:
                print(f'   {issue}')
            exit(1)
        else:
            print(f'\\n🎉 All __init__.py files are consistent!')
        "

  dependency-audit:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.11

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e .[dev]

    - name: Generate dependency tree
      run: |
        pip install pipdeptree
        pipdeptree --json > dependency-tree.json
        pipdeptree --graph-output png > dependency-graph.png

    - name: Check for outdated packages
      run: |
        pip list --outdated --format=json > outdated-packages.json

    - name: Upload dependency reports
      uses: actions/upload-artifact@v3
      with:
        name: dependency-reports
        path: |
          dependency-tree.json
          dependency-graph.png
          outdated-packages.json
