"""
角色相关数据库模型
"""
from datetime import datetime
from typing import List, Optional

from sqlalchemy import (
    Column, Integer, String, Text, DateTime, Float, Boolean,
    BigInteger, ForeignKey, Index, UniqueConstraint
)
from sqlalchemy.orm import relationship

from .base import BaseModel


class CharacterModel(BaseModel):
    """角色模型"""
    __tablename__ = "characters"
    
    # EVE角色ID作为主键
    character_id = Column(BigInteger, primary_key=True, index=True)
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, default="")
    
    # 所属组织
    corporation_id = Column(BigInteger, nullable=False, index=True)
    alliance_id = Column(BigInteger, nullable=True, index=True)
    faction_id = Column(Integer, nullable=True)
    
    # 角色基本信息
    race_id = Column(Integer, nullable=False)
    bloodline_id = Column(Integer, nullable=False)
    ancestry_id = Column(Integer, nullable=True)
    gender = Column(String(10), nullable=False)
    birthday = Column(DateTime, nullable=False)
    
    # 安全等级和头衔
    security_status = Column(Float, default=0.0)
    title = Column(String(255), default="")
    
    # 位置信息
    current_system_id = Column(Integer, nullable=True, index=True)
    current_station_id = Column(BigInteger, nullable=True)
    current_structure_id = Column(BigInteger, nullable=True)
    
    # 在线状态
    is_online = Column(Boolean, default=False, index=True)
    last_login = Column(DateTime, nullable=True)
    last_logout = Column(DateTime, nullable=True)
    
    # 当前飞船
    current_ship_type_id = Column(Integer, nullable=True)
    current_ship_item_id = Column(BigInteger, nullable=True)
    current_ship_name = Column(String(255), nullable=True)
    
    # 钱包余额
    wallet_balance = Column(Float, default=0.0)
    
    # 数据同步状态
    last_sync_at = Column(DateTime, nullable=True)
    sync_error_count = Column(Integer, default=0)
    sync_error_message = Column(Text, nullable=True)
    
    # 关联关系
    attributes = relationship("AttributesModel", back_populates="character", uselist=False)
    skills = relationship("SkillModel", back_populates="character")
    skill_queue = relationship("SkillQueueModel", back_populates="character", order_by="SkillQueueModel.queue_position")
    assets = relationship("AssetModel", back_populates="character")
    market_orders = relationship("MarketOrderModel", back_populates="character")
    industry_jobs = relationship("IndustryJobModel", back_populates="character")
    tokens = relationship("TokenModel", back_populates="character")
    
    # 索引
    __table_args__ = (
        Index('idx_character_corp_alliance', 'corporation_id', 'alliance_id'),
        Index('idx_character_location', 'current_system_id', 'current_station_id'),
        Index('idx_character_sync', 'last_sync_at', 'sync_error_count'),
    )


class AttributesModel(BaseModel):
    """角色属性模型"""
    __tablename__ = "character_attributes"
    
    character_id = Column(BigInteger, ForeignKey('characters.character_id'), nullable=False, unique=True)
    
    # 五大属性
    charisma = Column(Integer, nullable=False, default=20)
    intelligence = Column(Integer, nullable=False, default=20)
    memory = Column(Integer, nullable=False, default=20)
    perception = Column(Integer, nullable=False, default=20)
    willpower = Column(Integer, nullable=False, default=20)
    
    # 重置相关
    bonus_remaps = Column(Integer, default=0)
    last_remap_date = Column(DateTime, nullable=True)
    accrued_remap_cooldown_date = Column(DateTime, nullable=True)
    
    # 关联关系
    character = relationship("CharacterModel", back_populates="attributes")


class SkillModel(BaseModel):
    """技能模型"""
    __tablename__ = "character_skills"
    
    character_id = Column(BigInteger, ForeignKey('characters.character_id'), nullable=False, index=True)
    skill_id = Column(Integer, nullable=False, index=True)
    
    # 技能点和等级
    skillpoints_in_skill = Column(BigInteger, nullable=False, default=0)
    trained_skill_level = Column(Integer, nullable=False, default=0)
    active_skill_level = Column(Integer, nullable=False, default=0)
    
    # 关联关系
    character = relationship("CharacterModel", back_populates="skills")
    
    # 唯一约束
    __table_args__ = (
        UniqueConstraint('character_id', 'skill_id', name='uq_character_skill'),
        Index('idx_skill_level', 'skill_id', 'trained_skill_level'),
    )


class SkillQueueModel(BaseModel):
    """技能队列模型"""
    __tablename__ = "character_skill_queue"
    
    character_id = Column(BigInteger, ForeignKey('characters.character_id'), nullable=False, index=True)
    skill_id = Column(Integer, nullable=False, index=True)
    
    # 队列信息
    queue_position = Column(Integer, nullable=False)
    finished_level = Column(Integer, nullable=False)
    
    # 技能点信息
    training_start_sp = Column(BigInteger, nullable=True)
    level_end_sp = Column(BigInteger, nullable=True)
    level_start_sp = Column(BigInteger, nullable=True)
    
    # 时间信息
    start_date = Column(DateTime, nullable=True)
    finish_date = Column(DateTime, nullable=True)
    
    # 关联关系
    character = relationship("CharacterModel", back_populates="skill_queue")
    
    # 索引
    __table_args__ = (
        Index('idx_skill_queue_position', 'character_id', 'queue_position'),
        Index('idx_skill_queue_time', 'start_date', 'finish_date'),
    )


class CharacterCloneModel(BaseModel):
    """角色克隆模型"""
    __tablename__ = "character_clones"
    
    character_id = Column(BigInteger, ForeignKey('characters.character_id'), nullable=False, index=True)
    clone_id = Column(BigInteger, nullable=False)
    
    # 克隆信息
    name = Column(String(255), nullable=False)
    location_id = Column(BigInteger, nullable=False)
    clone_type = Column(String(20), nullable=False, default="clone")  # clone, jump_clone
    
    # 植入体信息 (JSON格式存储)
    implants = Column(Text, nullable=True)  # JSON array of implant type_ids
    
    # 关联关系
    character = relationship("CharacterModel")
    
    # 唯一约束
    __table_args__ = (
        UniqueConstraint('character_id', 'clone_id', name='uq_character_clone'),
    )


class CharacterMailModel(BaseModel):
    """角色邮件模型"""
    __tablename__ = "character_mails"
    
    character_id = Column(BigInteger, ForeignKey('characters.character_id'), nullable=False, index=True)
    mail_id = Column(BigInteger, nullable=False, unique=True)
    
    # 邮件信息
    subject = Column(String(500), nullable=False)
    from_id = Column(BigInteger, nullable=False)
    timestamp = Column(DateTime, nullable=False, index=True)
    is_read = Column(Boolean, default=False)
    
    # 收件人信息 (JSON格式存储)
    recipients = Column(Text, nullable=True)  # JSON array
    
    # 邮件内容
    body = Column(Text, nullable=True)
    
    # 关联关系
    character = relationship("CharacterModel")
    
    # 索引
    __table_args__ = (
        Index('idx_mail_timestamp', 'character_id', 'timestamp'),
        Index('idx_mail_read_status', 'character_id', 'is_read'),
    )
