/**
 * 用户友好错误处理器
 * 将技术错误转换为用户可理解的消息
 */

export interface UserMessage {
  title: string
  message: string
  action?: string
  technical?: string
  severity: 'error' | 'warning' | 'info'
  actionButton?: {
    text: string
    onClick: () => void
  }
}

export interface OAuthError {
  error: string
  error_description: string
  state?: string
}

export interface APIError {
  status: number
  message: string
  details?: any
}

export class UserFriendlyErrorHandler {
  /**
   * 处理OAuth错误
   */
  static handleOAuthError(error: OAuthError): UserMessage {
    const errorMap: Record<string, UserMessage> = {
      'invalid_scope': {
        title: '登录配置错误',
        message: '系统配置存在问题，请联系管理员修复。',
        action: '联系技术支持',
        technical: error.error_description,
        severity: 'error',
        actionButton: {
          text: '重新尝试',
          onClick: () => window.location.reload()
        }
      },
      'invalid_client': {
        title: '应用配置错误',
        message: '应用配置不正确，请检查应用设置或联系管理员。',
        action: '检查配置或联系支持',
        technical: error.error_description,
        severity: 'error',
        actionButton: {
          text: '返回首页',
          onClick: () => window.location.href = '/'
        }
      },
      'access_denied': {
        title: '登录被拒绝',
        message: '您拒绝了登录授权，或者授权已过期。',
        action: '重新尝试登录',
        technical: error.error_description,
        severity: 'warning',
        actionButton: {
          text: '重新登录',
          onClick: () => window.location.href = '/auth/login'
        }
      },
      'invalid_grant': {
        title: '授权已过期',
        message: '登录授权已过期，请重新登录。',
        action: '重新登录',
        technical: error.error_description,
        severity: 'warning',
        actionButton: {
          text: '重新登录',
          onClick: () => window.location.href = '/auth/login'
        }
      },
      'server_error': {
        title: '服务器错误',
        message: 'EVE Online服务器暂时不可用，请稍后重试。',
        action: '稍后重试',
        technical: error.error_description,
        severity: 'error',
        actionButton: {
          text: '重试',
          onClick: () => window.location.reload()
        }
      },
      'temporarily_unavailable': {
        title: '服务暂时不可用',
        message: 'EVE Online登录服务暂时不可用，请稍后重试。',
        action: '稍后重试',
        technical: error.error_description,
        severity: 'warning',
        actionButton: {
          text: '重试',
          onClick: () => window.location.reload()
        }
      }
    }

    return errorMap[error.error] || this.getGenericOAuthError(error)
  }

  /**
   * 处理API错误
   */
  static handleAPIError(error: APIError): UserMessage {
    if (error.status >= 500) {
      return {
        title: '服务器错误',
        message: '服务器遇到了问题，我们正在努力修复。请稍后重试。',
        action: '稍后重试',
        technical: `HTTP ${error.status}: ${error.message}`,
        severity: 'error',
        actionButton: {
          text: '重试',
          onClick: () => window.location.reload()
        }
      }
    }

    if (error.status === 401) {
      return {
        title: '登录已过期',
        message: '您的登录已过期，请重新登录。',
        action: '重新登录',
        technical: `HTTP ${error.status}: ${error.message}`,
        severity: 'warning',
        actionButton: {
          text: '重新登录',
          onClick: () => window.location.href = '/auth/login'
        }
      }
    }

    if (error.status === 403) {
      return {
        title: '权限不足',
        message: '您没有权限执行此操作。',
        action: '联系管理员',
        technical: `HTTP ${error.status}: ${error.message}`,
        severity: 'warning'
      }
    }

    if (error.status === 404) {
      return {
        title: '资源不存在',
        message: '请求的资源不存在或已被删除。',
        action: '返回首页',
        technical: `HTTP ${error.status}: ${error.message}`,
        severity: 'warning',
        actionButton: {
          text: '返回首页',
          onClick: () => window.location.href = '/'
        }
      }
    }

    if (error.status === 429) {
      return {
        title: '请求过于频繁',
        message: '您的请求过于频繁，请稍后重试。',
        action: '稍后重试',
        technical: `HTTP ${error.status}: ${error.message}`,
        severity: 'warning',
        actionButton: {
          text: '稍后重试',
          onClick: () => setTimeout(() => window.location.reload(), 5000)
        }
      }
    }

    return {
      title: '请求失败',
      message: '请求失败，请检查网络连接或稍后重试。',
      action: '检查网络或重试',
      technical: `HTTP ${error.status}: ${error.message}`,
      severity: 'error',
      actionButton: {
        text: '重试',
        onClick: () => window.location.reload()
      }
    }
  }

  /**
   * 处理网络错误
   */
  static handleNetworkError(error: Error): UserMessage {
    return {
      title: '网络连接错误',
      message: '无法连接到服务器，请检查您的网络连接。',
      action: '检查网络连接',
      technical: error.message,
      severity: 'error',
      actionButton: {
        text: '重试',
        onClick: () => window.location.reload()
      }
    }
  }

  /**
   * 处理配置同步错误
   */
  static handleConfigSyncError(differences: string[]): UserMessage {
    return {
      title: '配置同步错误',
      message: '系统配置存在不一致，可能影响功能正常使用。',
      action: '联系管理员',
      technical: `配置差异: ${differences.join(', ')}`,
      severity: 'warning'
    }
  }

  /**
   * 处理第三方API错误
   */
  static handleThirdPartyAPIError(service: string, error: APIError): UserMessage {
    const serviceNames: Record<string, string> = {
      'eve_esi': 'EVE Online',
      'eve_sso': 'EVE Online登录',
      'github': 'GitHub',
      'discord': 'Discord'
    }

    const serviceName = serviceNames[service] || service

    if (error.status >= 500) {
      return {
        title: `${serviceName}服务错误`,
        message: `${serviceName}服务暂时不可用，请稍后重试。`,
        action: '稍后重试',
        technical: `${service} API: HTTP ${error.status}`,
        severity: 'error',
        actionButton: {
          text: '重试',
          onClick: () => window.location.reload()
        }
      }
    }

    return {
      title: `${serviceName}请求失败`,
      message: `与${serviceName}的通信失败，请检查网络或稍后重试。`,
      action: '检查网络或重试',
      technical: `${service} API: HTTP ${error.status}`,
      severity: 'warning',
      actionButton: {
        text: '重试',
        onClick: () => window.location.reload()
      }
    }
  }

  /**
   * 获取通用OAuth错误
   */
  private static getGenericOAuthError(error: OAuthError): UserMessage {
    return {
      title: '登录错误',
      message: '登录过程中遇到了问题，请稍后重试。',
      action: '稍后重试或联系支持',
      technical: `${error.error}: ${error.error_description}`,
      severity: 'error',
      actionButton: {
        text: '重试',
        onClick: () => window.location.reload()
      }
    }
  }

  /**
   * 显示错误消息（使用Ant Design的message组件）
   */
  static showError(userMessage: UserMessage) {
    // 这里可以集成具体的UI库，比如Ant Design
    console.error('用户错误:', userMessage)
    
    // 如果有全局的通知系统，可以在这里调用
    if (typeof window !== 'undefined' && (window as any).showNotification) {
      (window as any).showNotification({
        type: userMessage.severity,
        title: userMessage.title,
        message: userMessage.message,
        action: userMessage.actionButton
      })
    }
  }

  /**
   * 记录错误到监控系统
   */
  static logError(error: any, context?: any) {
    // 发送错误到后端监控系统
    if (typeof window !== 'undefined') {
      fetch('/api/monitoring/client-error', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          error: {
            message: error.message || String(error),
            stack: error.stack,
            name: error.name
          },
          context: context || {},
          timestamp: new Date().toISOString(),
          url: window.location.href,
          userAgent: navigator.userAgent
        })
      }).catch(err => {
        console.error('无法发送错误报告:', err)
      })
    }
  }

  /**
   * 综合错误处理入口
   */
  static handleError(error: any, context?: any): UserMessage {
    // 记录错误
    this.logError(error, context)

    // 根据错误类型选择处理方式
    if (error.error && error.error_description) {
      // OAuth错误
      return this.handleOAuthError(error as OAuthError)
    }

    if (error.status && typeof error.status === 'number') {
      // API错误
      return this.handleAPIError(error as APIError)
    }

    if (error.name === 'NetworkError' || error.message?.includes('fetch')) {
      // 网络错误
      return this.handleNetworkError(error)
    }

    // 通用错误
    return {
      title: '发生错误',
      message: '系统遇到了问题，请稍后重试。',
      action: '稍后重试',
      technical: error.message || String(error),
      severity: 'error',
      actionButton: {
        text: '重试',
        onClick: () => window.location.reload()
      }
    }
  }
}

// 导出便捷方法
export const handleError = UserFriendlyErrorHandler.handleError.bind(UserFriendlyErrorHandler)
export const showError = UserFriendlyErrorHandler.showError.bind(UserFriendlyErrorHandler)
export const logError = UserFriendlyErrorHandler.logError.bind(UserFriendlyErrorHandler)
