#!/usr/bin/env python3
"""
EVE SSO权限范围验证工具
验证.env文件中的权限配置是否与EVE Developer Portal中申请的权限一致
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def get_applied_scopes():
    """获取在EVE Developer Portal中实际申请的权限"""
    return [
        "publicData",
        "esi-calendar.respond_calendar_events.v1",
        "esi-calendar.read_calendar_events.v1",
        "esi-location.read_location.v1",
        "esi-location.read_ship_type.v1",
        "esi-mail.organize_mail.v1",
        "esi-mail.read_mail.v1",
        "esi-mail.send_mail.v1",
        "esi-skills.read_skills.v1",
        "esi-skills.read_skillqueue.v1",
        "esi-wallet.read_character_wallet.v1",
        "esi-wallet.read_corporation_wallet.v1",
        "esi-search.search_structures.v1",
        "esi-clones.read_clones.v1",
        "esi-characters.read_contacts.v1",
        "esi-universe.read_structures.v1",
        "esi-killmails.read_killmails.v1",
        "esi-corporations.read_corporation_membership.v1",
        "esi-assets.read_assets.v1",
        "esi-planets.manage_planets.v1",
        "esi-fleets.read_fleet.v1",
        "esi-fleets.write_fleet.v1",
        "esi-ui.open_window.v1",
        "esi-ui.write_waypoint.v1",
        "esi-characters.write_contacts.v1",
        "esi-fittings.read_fittings.v1",
        "esi-fittings.write_fittings.v1",
        "esi-markets.structure_markets.v1",
        "esi-corporations.read_structures.v1",
        "esi-characters.read_loyalty.v1",
        "esi-characters.read_chat_channels.v1",
        "esi-characters.read_medals.v1",
        "esi-characters.read_standings.v1",
        "esi-characters.read_agents_research.v1",
        "esi-industry.read_character_jobs.v1",
        "esi-markets.read_character_orders.v1",
        "esi-characters.read_blueprints.v1",
        "esi-characters.read_corporation_roles.v1",
        "esi-location.read_online.v1",
        "esi-contracts.read_character_contracts.v1",
        "esi-clones.read_implants.v1",
        "esi-characters.read_fatigue.v1",
        "esi-killmails.read_corporation_killmails.v1",
        "esi-corporations.track_members.v1",
        "esi-wallet.read_corporation_wallets.v1",
        "esi-characters.read_notifications.v1",
        "esi-corporations.read_divisions.v1",
        "esi-corporations.read_contacts.v1",
        "esi-assets.read_corporation_assets.v1",
        "esi-corporations.read_titles.v1",
        "esi-corporations.read_blueprints.v1",
        "esi-contracts.read_corporation_contracts.v1",
        "esi-corporations.read_standings.v1",
        "esi-corporations.read_starbases.v1",
        "esi-industry.read_corporation_jobs.v1",
        "esi-markets.read_corporation_orders.v1",
        "esi-corporations.read_container_logs.v1",
        "esi-industry.read_character_mining.v1",
        "esi-industry.read_corporation_mining.v1",
        "esi-planets.read_customs_offices.v1",
        "esi-corporations.read_facilities.v1",
        "esi-corporations.read_medals.v1",
        "esi-characters.read_titles.v1",
        "esi-alliances.read_contacts.v1",
        "esi-characters.read_fw_stats.v1",
        "esi-corporations.read_fw_stats.v1"
    ]

def get_configured_scopes():
    """获取.env文件中配置的权限"""
    try:
        from src.infrastructure.config import settings
        return settings.eve_sso_scopes.split()
    except Exception as e:
        print(f"❌ 无法读取配置文件: {e}")
        return []

def categorize_scopes(scopes):
    """按功能分类权限"""
    categories = {
        "基础权限": [],
        "角色信息": [],
        "位置状态": [],
        "技能系统": [],
        "资产财务": [],
        "通讯管理": [],
        "市场工业": [],
        "公司管理": [],
        "宇宙数据": [],
        "战斗相关": [],
        "舰队管理": [],
        "装配管理": [],
        "日历管理": [],
        "合同管理": [],
        "游戏界面": [],
        "联盟关系": [],
        "其他": []
    }
    
    for scope in scopes:
        if scope == "publicData":
            categories["基础权限"].append(scope)
        elif scope.startswith("esi-characters."):
            categories["角色信息"].append(scope)
        elif scope.startswith("esi-location."):
            categories["位置状态"].append(scope)
        elif scope.startswith("esi-skills."):
            categories["技能系统"].append(scope)
        elif scope.startswith("esi-assets.") or scope.startswith("esi-wallet."):
            categories["资产财务"].append(scope)
        elif scope.startswith("esi-mail.") or scope.startswith("esi-clones."):
            categories["通讯管理"].append(scope)
        elif scope.startswith("esi-markets.") or scope.startswith("esi-industry.") or scope.startswith("esi-planets."):
            categories["市场工业"].append(scope)
        elif scope.startswith("esi-corporations."):
            categories["公司管理"].append(scope)
        elif scope.startswith("esi-universe.") or scope.startswith("esi-search."):
            categories["宇宙数据"].append(scope)
        elif scope.startswith("esi-killmails."):
            categories["战斗相关"].append(scope)
        elif scope.startswith("esi-fleets."):
            categories["舰队管理"].append(scope)
        elif scope.startswith("esi-fittings."):
            categories["装配管理"].append(scope)
        elif scope.startswith("esi-calendar."):
            categories["日历管理"].append(scope)
        elif scope.startswith("esi-contracts."):
            categories["合同管理"].append(scope)
        elif scope.startswith("esi-ui."):
            categories["游戏界面"].append(scope)
        elif scope.startswith("esi-alliances."):
            categories["联盟关系"].append(scope)
        else:
            categories["其他"].append(scope)
    
    return categories

def validate_scopes():
    """验证权限配置"""
    print("🔍 EVE SSO权限范围验证")
    print("=" * 60)
    
    applied_scopes = set(get_applied_scopes())
    configured_scopes = set(get_configured_scopes())
    
    print(f"📋 EVE Developer Portal申请权限: {len(applied_scopes)}个")
    print(f"⚙️  .env文件配置权限: {len(configured_scopes)}个")
    print()
    
    # 检查一致性
    missing_in_config = applied_scopes - configured_scopes
    extra_in_config = configured_scopes - applied_scopes
    
    if not missing_in_config and not extra_in_config:
        print("✅ 权限配置完全一致！")
        print()
        
        # 显示分类统计
        categories = categorize_scopes(applied_scopes)
        print("📊 权限分类统计:")
        print("-" * 40)
        for category, scopes in categories.items():
            if scopes:
                print(f"  {category}: {len(scopes)}个")
        
        print(f"\n🎉 总计: {len(applied_scopes)}个权限")
        return True
    else:
        print("⚠️  权限配置不一致！")
        print()
        
        if missing_in_config:
            print(f"❌ .env文件中缺少的权限 ({len(missing_in_config)}个):")
            for scope in sorted(missing_in_config):
                print(f"   - {scope}")
            print()
        
        if extra_in_config:
            print(f"⚠️  .env文件中多余的权限 ({len(extra_in_config)}个):")
            for scope in sorted(extra_in_config):
                print(f"   + {scope}")
            print()
        
        return False

def show_scope_details():
    """显示权限详细信息"""
    applied_scopes = get_applied_scopes()
    categories = categorize_scopes(applied_scopes)
    
    print("\n📋 权限详细列表:")
    print("=" * 60)
    
    for category, scopes in categories.items():
        if scopes:
            print(f"\n🔹 {category} ({len(scopes)}个):")
            for scope in sorted(scopes):
                print(f"   ✅ {scope}")

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] in ['--details', '-d']:
        show_scope_details()
    else:
        is_valid = validate_scopes()
        
        if is_valid:
            print("\n🚀 下一步:")
            print("1. 重启应用以加载新权限配置")
            print("2. 用户需要重新登录以获得完整权限")
            print("3. 测试各项功能是否正常工作")
        else:
            print("\n🔧 修复建议:")
            print("1. 检查.env文件中的EVE_SSO_SCOPES配置")
            print("2. 确保与EVE Developer Portal中的权限一致")
            print("3. 重新运行此验证脚本确认")

if __name__ == "__main__":
    main()
