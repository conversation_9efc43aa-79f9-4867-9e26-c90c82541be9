"""
通用值对象
"""
from dataclasses import dataclass
from decimal import Decimal
from typing import Optional

from .base_entity import ValueObject
from .exceptions import DomainValidationError


@dataclass(frozen=True)
class CharacterId(ValueObject):
    """角色ID值对象"""
    value: int
    
    def __post_init__(self):
        if self.value <= 0:
            raise DomainValidationError("角色ID必须是正整数")
    
    def __str__(self) -> str:
        return str(self.value)


@dataclass(frozen=True)
class CorporationId(ValueObject):
    """公司ID值对象"""
    value: int
    
    def __post_init__(self):
        if self.value <= 0:
            raise DomainValidationError("公司ID必须是正整数")
    
    def __str__(self) -> str:
        return str(self.value)


@dataclass(frozen=True)
class AllianceId(ValueObject):
    """联盟ID值对象"""
    value: int
    
    def __post_init__(self):
        if self.value <= 0:
            raise DomainValidationError("联盟ID必须是正整数")
    
    def __str__(self) -> str:
        return str(self.value)


@dataclass(frozen=True)
class TypeId(ValueObject):
    """物品类型ID值对象"""
    value: int
    
    def __post_init__(self):
        if self.value <= 0:
            raise DomainValidationError("物品类型ID必须是正整数")
    
    def __str__(self) -> str:
        return str(self.value)


@dataclass(frozen=True)
class SystemId(ValueObject):
    """星系ID值对象"""
    value: int
    
    def __post_init__(self):
        if self.value <= 0:
            raise DomainValidationError("星系ID必须是正整数")
    
    def __str__(self) -> str:
        return str(self.value)


@dataclass(frozen=True)
class Money(ValueObject):
    """货币值对象"""
    amount: Decimal
    currency: str = "ISK"
    
    def __post_init__(self):
        if self.amount < 0:
            raise DomainValidationError("金额不能为负数")
        if not self.currency:
            raise DomainValidationError("货币类型不能为空")
    
    def add(self, other: 'Money') -> 'Money':
        """加法运算"""
        if self.currency != other.currency:
            raise DomainValidationError("不同货币类型不能相加")
        return Money(self.amount + other.amount, self.currency)
    
    def subtract(self, other: 'Money') -> 'Money':
        """减法运算"""
        if self.currency != other.currency:
            raise DomainValidationError("不同货币类型不能相减")
        result_amount = self.amount - other.amount
        if result_amount < 0:
            raise DomainValidationError("结果金额不能为负数")
        return Money(result_amount, self.currency)
    
    def multiply(self, factor: Decimal) -> 'Money':
        """乘法运算"""
        if factor < 0:
            raise DomainValidationError("乘数不能为负数")
        return Money(self.amount * factor, self.currency)
    
    def divide(self, divisor: Decimal) -> 'Money':
        """除法运算"""
        if divisor <= 0:
            raise DomainValidationError("除数必须为正数")
        return Money(self.amount / divisor, self.currency)
    
    def is_zero(self) -> bool:
        """是否为零"""
        return self.amount == 0
    
    def is_positive(self) -> bool:
        """是否为正数"""
        return self.amount > 0
    
    def __str__(self) -> str:
        return f"{self.amount:,.2f} {self.currency}"


@dataclass(frozen=True)
class Quantity(ValueObject):
    """数量值对象"""
    value: int
    
    def __post_init__(self):
        if self.value < 0:
            raise DomainValidationError("数量不能为负数")
    
    def add(self, other: 'Quantity') -> 'Quantity':
        """加法运算"""
        return Quantity(self.value + other.value)
    
    def subtract(self, other: 'Quantity') -> 'Quantity':
        """减法运算"""
        result = self.value - other.value
        if result < 0:
            raise DomainValidationError("结果数量不能为负数")
        return Quantity(result)
    
    def is_zero(self) -> bool:
        """是否为零"""
        return self.value == 0
    
    def is_positive(self) -> bool:
        """是否为正数"""
        return self.value > 0
    
    def __str__(self) -> str:
        return str(self.value)


@dataclass(frozen=True)
class Location(ValueObject):
    """位置值对象"""
    system_id: SystemId
    station_id: Optional[int] = None
    structure_id: Optional[int] = None
    
    def __post_init__(self):
        # 验证位置的有效性
        if self.station_id is not None and self.structure_id is not None:
            raise DomainValidationError("不能同时指定空间站和结构")
        
        if self.station_id is not None and self.station_id <= 0:
            raise DomainValidationError("空间站ID必须是正整数")
        
        if self.structure_id is not None and self.structure_id <= 0:
            raise DomainValidationError("结构ID必须是正整数")
    
    def is_in_space(self) -> bool:
        """是否在太空中"""
        return self.station_id is None and self.structure_id is None
    
    def is_in_station(self) -> bool:
        """是否在空间站中"""
        return self.station_id is not None
    
    def is_in_structure(self) -> bool:
        """是否在结构中"""
        return self.structure_id is not None
    
    def get_location_id(self) -> int:
        """获取位置ID"""
        if self.station_id is not None:
            return self.station_id
        elif self.structure_id is not None:
            return self.structure_id
        else:
            return self.system_id.value
    
    def __str__(self) -> str:
        if self.station_id is not None:
            return f"Station {self.station_id} in System {self.system_id}"
        elif self.structure_id is not None:
            return f"Structure {self.structure_id} in System {self.system_id}"
        else:
            return f"Space in System {self.system_id}"


@dataclass(frozen=True)
class SecurityStatus(ValueObject):
    """安全等级值对象"""
    value: float
    
    def __post_init__(self):
        if not (-10.0 <= self.value <= 10.0):
            raise DomainValidationError("安全等级必须在-10.0到10.0之间")
    
    def is_high_sec(self) -> bool:
        """是否为高安"""
        return self.value >= 0.5
    
    def is_low_sec(self) -> bool:
        """是否为低安"""
        return 0.0 < self.value < 0.5
    
    def is_null_sec(self) -> bool:
        """是否为00"""
        return self.value <= 0.0
    
    def is_negative(self) -> bool:
        """是否为负安全等级"""
        return self.value < 0.0
    
    def __str__(self) -> str:
        return f"{self.value:.1f}"


@dataclass(frozen=True)
class SkillPoints(ValueObject):
    """技能点值对象"""
    value: int
    
    def __post_init__(self):
        if self.value < 0:
            raise DomainValidationError("技能点不能为负数")
    
    def add(self, other: 'SkillPoints') -> 'SkillPoints':
        """加法运算"""
        return SkillPoints(self.value + other.value)
    
    def subtract(self, other: 'SkillPoints') -> 'SkillPoints':
        """减法运算"""
        result = self.value - other.value
        if result < 0:
            raise DomainValidationError("结果技能点不能为负数")
        return SkillPoints(result)
    
    def is_zero(self) -> bool:
        """是否为零"""
        return self.value == 0
    
    def __str__(self) -> str:
        return f"{self.value:,} SP"
