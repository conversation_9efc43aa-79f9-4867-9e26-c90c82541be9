#!/usr/bin/env python3
"""
简单的EVE SSO测试
"""
import urllib.request
import urllib.parse
import json
import os

def test_eve_sso():
    """测试EVE SSO"""
    print("🔧 简单EVE SSO测试")
    print("=" * 40)
    
    # 从环境变量读取配置
    client_id = "0643ec5d929e4a5094df698fc5780f0a"  # 直接使用已知的Client ID
    callback_url = "https://31dab6b08748.ngrok-free.app/auth/callback"
    
    print(f"Client ID: {client_id}")
    print(f"回调URL: {callback_url}")
    print()
    
    # 测试1: 生成授权URL并测试
    print("🔗 测试授权URL...")
    
    params = {
        'response_type': 'code',
        'redirect_uri': callback_url,
        'client_id': client_id,
        'scope': 'esi-characters.read_characters.v1',
        'state': 'test_state_12345'
    }
    
    auth_url = f"https://login.eveonline.com/v2/oauth/authorize?{urllib.parse.urlencode(params)}"
    
    print(f"授权URL: {auth_url}")
    print()
    
    # 测试URL访问
    print("📡 测试URL访问...")
    try:
        req = urllib.request.Request(
            auth_url,
            headers={'User-Agent': 'EVE-Assistant-Test/1.0'}
        )
        
        with urllib.request.urlopen(req, timeout=10) as response:
            status_code = response.getcode()
            content = response.read().decode('utf-8')
            
            print(f"HTTP状态码: {status_code}")
            
            if status_code == 200:
                print("✅ 授权端点可访问")
                if "login" in content.lower() or "eve online" in content.lower():
                    print("✅ 返回了登录页面")
                else:
                    print("⚠️  页面内容异常")
            elif status_code == 400:
                print("❌ 请求参数错误")
                if "client could not be found" in content.lower():
                    print("🚨 发现问题: Client ID无效")
                    print("💡 需要在EVE开发者门户中检查应用配置")
                else:
                    print(f"错误内容: {content[:200]}...")
            else:
                print(f"⚠️  意外状态码: {status_code}")
                
    except urllib.error.HTTPError as e:
        print(f"HTTP错误: {e.code}")
        try:
            error_content = e.read().decode('utf-8')
            print(f"错误内容: {error_content[:200]}...")
            
            if "client could not be found" in error_content.lower():
                print("🚨 确认问题: Client ID无效或未注册")
        except:
            print("无法读取错误内容")
            
    except Exception as e:
        print(f"测试失败: {e}")
    
    print()
    print("=" * 40)
    print("🎯 如果看到 'Client could not be found' 错误:")
    print("1. 访问 https://developers.eveonline.com/")
    print("2. 登录你的EVE账号")
    print("3. 检查应用列表中是否有对应的应用")
    print("4. 确认应用状态为 'Active'")
    print("5. 确认Client ID正确")
    print("6. 确认回调URL已正确配置")
    print()
    print("📝 当前配置的回调URL:")
    print(f"   {callback_url}")
    print("   这个URL必须在EVE开发者门户中完全匹配！")

if __name__ == "__main__":
    test_eve_sso()
