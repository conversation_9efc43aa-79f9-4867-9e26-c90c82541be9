# ngrok固定域名设置指南

## 问题描述
每次运行脚本时，ngrok都会分配一个新的随机URL，导致需要频繁更新EVE开发者门户中的回调URL配置。

## 解决方案

### 方案1：使用ngrok固定域名（推荐）

#### 步骤1：注册ngrok账号
1. 访问 https://ngrok.com/
2. 注册账号（如果还没有）
3. 登录到ngrok仪表板

#### 步骤2：获取authtoken
1. 访问 https://dashboard.ngrok.com/get-started/your-authtoken
2. 复制你的authtoken
3. 在终端中运行：
   ```bash
   ngrok config add-authtoken YOUR_AUTHTOKEN_HERE
   ```

#### 步骤3：获取固定域名
1. 访问 https://dashboard.ngrok.com/cloud-edge/domains
2. 点击 "Create Domain" 或 "New Domain"
3. 选择一个可用的域名（免费版通常提供一个固定域名）
4. 记录你的固定域名，例如：`your-app-name.ngrok-free.app`

#### 步骤4：配置项目
1. 在项目的 `.env` 文件中设置：
   ```
   NGROK_FIXED_DOMAIN="your-app-name.ngrok-free.app"
   ```

2. 运行启动脚本：
   ```bash
   python scripts/deployment/production-start.py
   ```

#### 步骤5：更新EVE开发者门户
1. 访问 https://developers.eveonline.com/
2. 找到你的应用
3. 将回调URL设置为：`https://your-app-name.ngrok-free.app/auth/callback`
4. 保存配置

### 方案2：使用ngrok配置文件

#### 步骤1：配置ngrok.yml文件
1. 编辑项目根目录的 `ngrok.yml` 文件
2. 填入你的authtoken：
   ```yaml
   authtoken: YOUR_AUTHTOKEN_HERE
   ```
3. 如果有固定域名，取消注释并填入：
   ```yaml
   hostname: your-app-name.ngrok-free.app
   ```

#### 步骤2：使用配置文件启动
```bash
ngrok start --config ngrok.yml eve-api
```

### 方案3：本地开发替代方案

如果不想使用ngrok付费版，可以考虑：

#### 选项1：使用localtunnel
```bash
npm install -g localtunnel
lt --port 8000 --subdomain your-app-name
```

#### 选项2：使用serveo
```bash
ssh -R your-app-name:80:localhost:8000 serveo.net
```

#### 选项3：使用cloudflared
```bash
cloudflared tunnel --url http://localhost:8000
```

## 费用说明

### ngrok免费版限制：
- 1个在线隧道
- 随机URL（每次重启都变化）
- 每月40,000个请求
- 基本功能

### ngrok付费版优势：
- 固定域名
- 多个并发隧道
- 自定义域名
- 更高的请求限制
- 高级功能（IP白名单、密码保护等）

## 推荐配置

对于EVE Online开发：
1. **开发阶段**：使用免费版的随机URL，手动更新EVE门户配置
2. **测试阶段**：使用付费版的固定域名，一次配置长期使用
3. **生产环境**：使用自己的域名和SSL证书

## 故障排除

### 问题1：固定域名不生效
- 检查authtoken是否正确配置
- 确认域名在ngrok仪表板中已创建
- 检查域名拼写是否正确

### 问题2：隧道启动失败
- 检查端口8000是否被占用
- 确认ngrok已正确安装
- 查看ngrok日志文件

### 问题3：EVE登录仍然失败
- 确认EVE开发者门户中的回调URL已更新
- 检查URL格式是否正确（必须包含 `/auth/callback`）
- 确认应用状态为Active

## 相关链接
- [ngrok官网](https://ngrok.com/)
- [ngrok文档](https://ngrok.com/docs)
- [EVE开发者门户](https://developers.eveonline.com/)
- [ngrok定价](https://ngrok.com/pricing)
