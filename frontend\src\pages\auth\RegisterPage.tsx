import { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { Form, Input, Button, Checkbox, Progress, Alert } from 'antd'
import { UserOutlined, MailOutlined, LockOutlined, EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons'
import { motion } from 'framer-motion'

import { RegisterData } from '@/types'
import { useRegister } from '@/hooks/useAuth'
import { GuestRoute } from '@/components/auth/ProtectedRoute'

export default function RegisterPage() {
  const [form] = Form.useForm()
  const [passwordStrength, setPasswordStrength] = useState(0)
  const [agreedToTerms, setAgreedToTerms] = useState(false)

  const registerMutation = useRegister()

  const handleRegister = async (values: RegisterData) => {
    try {
      await registerMutation.mutateAsync(values)
    } catch (error) {
      // 错误已在mutation中处理
    }
  }

  const calculatePasswordStrength = (password: string) => {
    let strength = 0
    if (password.length >= 8) strength += 20
    if (password.length >= 12) strength += 10
    if (/[a-z]/.test(password)) strength += 20
    if (/[A-Z]/.test(password)) strength += 20
    if (/[0-9]/.test(password)) strength += 15
    if (/[^A-Za-z0-9]/.test(password)) strength += 15

    setPasswordStrength(Math.min(strength, 100))
  }

  const getPasswordStrengthColor = () => {
    if (passwordStrength < 30) return '#ff4d4f'
    if (passwordStrength < 60) return '#faad14'
    if (passwordStrength < 80) return '#1890ff'
    return '#52c41a'
  }

  const getPasswordStrengthText = () => {
    if (passwordStrength < 30) return '弱'
    if (passwordStrength < 60) return '中等'
    if (passwordStrength < 80) return '强'
    return '很强'
  }

  return (
    <GuestRoute>
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-dark-900 dark:via-dark-800 dark:to-dark-900 py-12 px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-md w-full space-y-8"
        >
          {/* Logo和标题 */}
          <div className="text-center">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              className="mx-auto h-16 w-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mb-4"
            >
              <span className="text-2xl font-bold text-white">E</span>
            </motion.div>
            <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
              创建账户
            </h2>
            <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
              加入 EVE Online 管理助手
            </p>
          </div>

          {/* 注册表单 */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
            className="bg-white dark:bg-dark-800 py-8 px-6 shadow-xl rounded-xl border border-gray-200 dark:border-dark-700"
          >
            {/* 错误提示 */}
            {registerMutation.isError && (
              <Alert
                message="注册失败"
                description={registerMutation.error?.message || '请检查输入信息是否正确'}
                type="error"
                showIcon
                className="mb-4"
              />
            )}

            {/* 成功提示 */}
            {registerMutation.isSuccess && (
              <Alert
                message="注册成功"
                description="请查看邮箱中的验证链接以激活账户"
                type="success"
                showIcon
                className="mb-4"
              />
            )}

            <Form
              form={form}
              name="register"
              onFinish={handleRegister}
              autoComplete="off"
              size="large"
              layout="vertical"
            >
              <Form.Item
                name="email"
                label="邮箱地址"
                rules={[
                  { required: true, message: '请输入邮箱地址' },
                  { type: 'email', message: '请输入有效的邮箱地址' }
                ]}
              >
                <Input
                  prefix={<MailOutlined className="text-gray-400" />}
                  placeholder="<EMAIL>"
                  autoComplete="email"
                />
              </Form.Item>

              <Form.Item
                name="username"
                label="用户名"
                rules={[
                  { required: true, message: '请输入用户名' },
                  { min: 3, message: '用户名至少3位' },
                  { max: 20, message: '用户名最多20位' },
                  { pattern: /^[a-zA-Z0-9_-]+$/, message: '用户名只能包含字母、数字、下划线和连字符' }
                ]}
              >
                <Input
                  prefix={<UserOutlined className="text-gray-400" />}
                  placeholder="用户名"
                  autoComplete="username"
                />
              </Form.Item>

              <Form.Item
                name="password"
                label="密码"
                rules={[
                  { required: true, message: '请输入密码' },
                  { min: 8, message: '密码至少8位' },
                  {
                    validator: (_, value) => {
                      if (!value) return Promise.resolve()
                      if (passwordStrength < 60) {
                        return Promise.reject(new Error('密码强度不够，请使用更复杂的密码'))
                      }
                      return Promise.resolve()
                    }
                  }
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined className="text-gray-400" />}
                  placeholder="至少8位，包含字母和数字"
                  autoComplete="new-password"
                  onChange={(e) => calculatePasswordStrength(e.target.value)}
                  iconRender={(visible) =>
                    visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                  }
                />
              </Form.Item>

              {/* 密码强度指示器 */}
              {passwordStrength > 0 && (
                <div className="mb-4">
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-xs text-gray-500">密码强度</span>
                    <span className="text-xs" style={{ color: getPasswordStrengthColor() }}>
                      {getPasswordStrengthText()}
                    </span>
                  </div>
                  <Progress
                    percent={passwordStrength}
                    strokeColor={getPasswordStrengthColor()}
                    showInfo={false}
                    size="small"
                  />
                </div>
              )}

              <Form.Item
                name="confirmPassword"
                label="确认密码"
                dependencies={['password']}
                rules={[
                  { required: true, message: '请确认密码' },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('password') === value) {
                        return Promise.resolve()
                      }
                      return Promise.reject(new Error('两次输入的密码不一致'))
                    },
                  }),
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined className="text-gray-400" />}
                  placeholder="再次输入密码"
                  autoComplete="new-password"
                  iconRender={(visible) =>
                    visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                  }
                />
              </Form.Item>

              <Form.Item
                name="agreeToTerms"
                valuePropName="checked"
                rules={[
                  {
                    validator: (_, value) =>
                      value ? Promise.resolve() : Promise.reject(new Error('请同意服务条款')),
                  },
                ]}
              >
                <Checkbox onChange={(e) => setAgreedToTerms(e.target.checked)}>
                  我已阅读并同意{' '}
                  <Link
                    to="/terms"
                    target="_blank"
                    className="text-blue-600 hover:text-blue-500 dark:text-blue-400"
                  >
                    服务条款
                  </Link>
                  {' '}和{' '}
                  <Link
                    to="/privacy"
                    target="_blank"
                    className="text-blue-600 hover:text-blue-500 dark:text-blue-400"
                  >
                    隐私政策
                  </Link>
                </Checkbox>
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  size="large"
                  block
                  loading={registerMutation.isPending}
                  disabled={!agreedToTerms}
                  className="h-12"
                >
                  创建账户
                </Button>
              </Form.Item>
            </Form>

            {/* 登录链接 */}
            <div className="text-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                已有账户？{' '}
                <Link
                  to="/auth/login"
                  className="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400"
                >
                  立即登录
                </Link>
              </span>
            </div>
          </motion.div>

          {/* 安全提示 */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="text-center"
          >
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
              <div className="flex items-center justify-center mb-2">
                <span className="text-blue-600 dark:text-blue-400 text-lg">🔒</span>
              </div>
              <p className="text-xs text-blue-700 dark:text-blue-300">
                我们使用行业标准的加密技术保护您的数据安全
              </p>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </GuestRoute>
  )
}
