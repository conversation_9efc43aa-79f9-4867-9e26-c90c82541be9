#!/usr/bin/env python3
import sys
sys.path.insert(0, '.')

print("Testing step by step...")

try:
    print("1. Testing basic config...")
    from src.infrastructure.config import settings
    print("   ✅ Config OK")
    
    print("2. Testing domain services...")
    from src.domain.character.services import SkillTrainingService, CharacterAnalysisService
    print("   ✅ Domain services OK")
    
    print("3. Testing application services...")
    from src.application.character import CharacterApplicationService
    print("   ✅ Application services OK")
    
    print("4. Testing dependencies...")
    from src.presentation.api.dependencies import get_character_service
    print("   ✅ Dependencies OK")
    
    print("5. Testing main app...")
    from src.presentation.api.main import app
    print("   ✅ Main app OK")
    
    print("🎉 All tests passed!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
