"""
Redis缓存管理器
"""
import json
import pickle
import asyncio
from typing import Any, Optional, Dict, List, Union
from datetime import datetime, timedelta
import hashlib

from ..config.logging import get_logger

logger = get_logger(__name__)


class MockRedisClient:
    """模拟Redis客户端 - 用于开发环境"""
    
    def __init__(self):
        self._data: Dict[str, Any] = {}
        self._expiry: Dict[str, datetime] = {}
        self._connected = False
    
    async def connect(self):
        """连接Redis"""
        self._connected = True
        logger.info("模拟Redis客户端已连接")
    
    async def disconnect(self):
        """断开连接"""
        self._connected = False
        self._data.clear()
        self._expiry.clear()
        logger.info("模拟Redis客户端已断开")
    
    def _is_expired(self, key: str) -> bool:
        """检查键是否过期"""
        if key in self._expiry:
            return datetime.utcnow() > self._expiry[key]
        return False
    
    def _cleanup_expired(self):
        """清理过期键"""
        expired_keys = [k for k in self._expiry.keys() if self._is_expired(k)]
        for key in expired_keys:
            self._data.pop(key, None)
            self._expiry.pop(key, None)
    
    async def get(self, key: str) -> Optional[bytes]:
        """获取值"""
        self._cleanup_expired()
        if key in self._data and not self._is_expired(key):
            return self._data[key]
        return None
    
    async def set(self, key: str, value: bytes, ex: Optional[int] = None) -> bool:
        """设置值"""
        self._data[key] = value
        if ex:
            self._expiry[key] = datetime.utcnow() + timedelta(seconds=ex)
        return True
    
    async def delete(self, key: str) -> int:
        """删除键"""
        deleted = 0
        if key in self._data:
            del self._data[key]
            deleted = 1
        if key in self._expiry:
            del self._expiry[key]
        return deleted
    
    async def exists(self, key: str) -> int:
        """检查键是否存在"""
        self._cleanup_expired()
        return 1 if key in self._data and not self._is_expired(key) else 0
    
    async def keys(self, pattern: str = "*") -> List[str]:
        """获取匹配的键"""
        self._cleanup_expired()
        if pattern == "*":
            return list(self._data.keys())
        # 简单的模式匹配
        import fnmatch
        return [k for k in self._data.keys() if fnmatch.fnmatch(k, pattern)]
    
    async def flushdb(self) -> bool:
        """清空数据库"""
        self._data.clear()
        self._expiry.clear()
        return True


class RedisCache:
    """Redis缓存管理器"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379/0"):
        self.redis_url = redis_url
        self._client = None
        self._use_mock = True  # 默认使用模拟客户端
    
    async def connect(self):
        """连接Redis"""
        try:
            # 尝试导入redis
            import redis.asyncio as redis
            self._client = redis.from_url(self.redis_url)
            await self._client.ping()
            self._use_mock = False
            logger.info("Redis连接成功", url=self.redis_url)
        except ImportError:
            logger.warning("redis包未安装，使用模拟客户端")
            self._client = MockRedisClient()
            await self._client.connect()
        except Exception as e:
            logger.warning("Redis连接失败，使用模拟客户端", error=str(e))
            self._client = MockRedisClient()
            await self._client.connect()
    
    async def disconnect(self):
        """断开连接"""
        if self._client:
            if hasattr(self._client, 'close'):
                await self._client.close()
            elif hasattr(self._client, 'disconnect'):
                await self._client.disconnect()
            self._client = None
    
    def _serialize(self, data: Any) -> bytes:
        """序列化数据"""
        try:
            # 尝试JSON序列化
            return json.dumps(data, ensure_ascii=False).encode('utf-8')
        except (TypeError, ValueError):
            # 使用pickle序列化
            return pickle.dumps(data)
    
    def _deserialize(self, data: bytes) -> Any:
        """反序列化数据"""
        try:
            # 尝试JSON反序列化
            return json.loads(data.decode('utf-8'))
        except (json.JSONDecodeError, UnicodeDecodeError):
            # 使用pickle反序列化
            return pickle.loads(data)
    
    def _make_key(self, *parts) -> str:
        """生成缓存键"""
        key_parts = [str(part) for part in parts]
        return ":".join(key_parts)
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if not self._client:
            return None
        
        try:
            data = await self._client.get(key)
            if data:
                return self._deserialize(data)
        except Exception as e:
            logger.error("获取缓存失败", key=key, error=str(e))
        
        return None
    
    async def set(self, key: str, value: Any, ttl: int = 3600) -> bool:
        """设置缓存值"""
        if not self._client:
            return False
        
        try:
            data = self._serialize(value)
            await self._client.set(key, data, ex=ttl)
            return True
        except Exception as e:
            logger.error("设置缓存失败", key=key, error=str(e))
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存"""
        if not self._client:
            return False
        
        try:
            result = await self._client.delete(key)
            return result > 0
        except Exception as e:
            logger.error("删除缓存失败", key=key, error=str(e))
            return False
    
    async def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        if not self._client:
            return False
        
        try:
            result = await self._client.exists(key)
            return result > 0
        except Exception as e:
            logger.error("检查缓存存在失败", key=key, error=str(e))
            return False
    
    async def clear_pattern(self, pattern: str) -> int:
        """清除匹配模式的缓存"""
        if not self._client:
            return 0
        
        try:
            keys = await self._client.keys(pattern)
            if keys:
                deleted = 0
                for key in keys:
                    if await self._client.delete(key):
                        deleted += 1
                return deleted
        except Exception as e:
            logger.error("清除模式缓存失败", pattern=pattern, error=str(e))
        
        return 0


class CacheManager:
    """缓存管理器"""
    
    def __init__(self, redis_cache: RedisCache):
        self.redis = redis_cache
    
    # ESI数据缓存
    async def cache_esi_response(self, endpoint: str, character_id: int, data: Any, ttl: int = 300):
        """缓存ESI响应"""
        key = self.redis._make_key("esi", endpoint, character_id)
        await self.redis.set(key, data, ttl)
    
    async def get_esi_response(self, endpoint: str, character_id: int) -> Optional[Any]:
        """获取ESI响应缓存"""
        key = self.redis._make_key("esi", endpoint, character_id)
        return await self.redis.get(key)
    
    # 角色分析缓存
    async def cache_character_analysis(self, character_id: int, analysis: Any, ttl: int = 3600):
        """缓存角色分析结果"""
        key = self.redis._make_key("analysis", "character", character_id)
        await self.redis.set(key, analysis, ttl)
    
    async def get_character_analysis(self, character_id: int) -> Optional[Any]:
        """获取角色分析缓存"""
        key = self.redis._make_key("analysis", "character", character_id)
        return await self.redis.get(key)
    
    # 市场数据缓存
    async def cache_market_data(self, region_id: int, type_id: int, data: Any, ttl: int = 300):
        """缓存市场数据"""
        key = self.redis._make_key("market", region_id, type_id)
        await self.redis.set(key, data, ttl)
    
    async def get_market_data(self, region_id: int, type_id: int) -> Optional[Any]:
        """获取市场数据缓存"""
        key = self.redis._make_key("market", region_id, type_id)
        return await self.redis.get(key)
    
    # 清理操作
    async def clear_character_cache(self, character_id: int) -> int:
        """清除角色相关缓存"""
        patterns = [
            f"esi:*:{character_id}",
            f"analysis:character:{character_id}",
        ]
        
        total_cleared = 0
        for pattern in patterns:
            total_cleared += await self.redis.clear_pattern(pattern)
        
        return total_cleared
    
    async def clear_expired_cache(self) -> int:
        """清除过期缓存（模拟客户端专用）"""
        if isinstance(self.redis._client, MockRedisClient):
            self.redis._client._cleanup_expired()
            return len(self.redis._client._data)
        return 0


# 全局实例
redis_cache = RedisCache()
cache_manager = CacheManager(redis_cache)
