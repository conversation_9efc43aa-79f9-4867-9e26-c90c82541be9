#!/usr/bin/env python3
"""
全模块功能测试脚本
"""
import requests
import time
import json

def test_all_modules():
    base_url = "http://localhost:8000"
    
    print("🚀 全模块功能测试")
    print("=" * 60)
    
    # 等待服务器启动
    print("等待服务器启动...")
    time.sleep(3)
    
    test_results = {}
    
    # 测试1: 健康检查模块
    print("\n1. 🏥 测试健康检查模块...")
    try:
        response = requests.get(f"{base_url}/health/status", timeout=5)
        if response.status_code == 200:
            print("✅ 健康检查模块正常")
            test_results["health"] = "✅ 正常"
        else:
            print(f"❌ 健康检查模块异常: {response.status_code}")
            test_results["health"] = "❌ 异常"
    except Exception as e:
        print(f"❌ 健康检查模块连接失败: {e}")
        test_results["health"] = "❌ 连接失败"
    
    # 测试2: EVE SSO认证模块
    print("\n2. 🔐 测试EVE SSO认证模块...")
    try:
        response = requests.post(
            f"{base_url}/auth/eve/login",
            json={"scopes": ["esi-assets.read_assets.v1"]},
            timeout=5
        )
        if response.status_code == 200:
            print("✅ EVE SSO认证模块正常")
            test_results["auth"] = "✅ 正常"
        else:
            print(f"❌ EVE SSO认证模块异常: {response.status_code}")
            test_results["auth"] = "❌ 异常"
    except Exception as e:
        print(f"❌ EVE SSO认证模块连接失败: {e}")
        test_results["auth"] = "❌ 连接失败"
    
    # 测试3: 角色管理模块
    print("\n3. 👤 测试角色管理模块...")
    try:
        response = requests.get(f"{base_url}/characters/", timeout=5)
        if response.status_code == 200:
            print("✅ 角色管理模块正常")
            test_results["characters"] = "✅ 正常"
        else:
            print(f"❌ 角色管理模块异常: {response.status_code}")
            test_results["characters"] = "❌ 异常"
    except Exception as e:
        print(f"❌ 角色管理模块连接失败: {e}")
        test_results["characters"] = "❌ 连接失败"
    
    # 测试4: 数据同步模块
    print("\n4. 🔄 测试数据同步模块...")
    try:
        response = requests.get(f"{base_url}/sync/status", timeout=5)
        if response.status_code == 200:
            print("✅ 数据同步模块正常")
            test_results["sync"] = "✅ 正常"
        else:
            print(f"❌ 数据同步模块异常: {response.status_code}")
            test_results["sync"] = "❌ 异常"
    except Exception as e:
        print(f"❌ 数据同步模块连接失败: {e}")
        test_results["sync"] = "❌ 连接失败"
    
    # 测试5: 监控模块
    print("\n5. 📊 测试监控模块...")
    try:
        response = requests.get(f"{base_url}/monitoring/status", timeout=5)
        if response.status_code == 200:
            print("✅ 监控模块正常")
            test_results["monitoring"] = "✅ 正常"
        else:
            print(f"❌ 监控模块异常: {response.status_code}")
            test_results["monitoring"] = "❌ 异常"
    except Exception as e:
        print(f"❌ 监控模块连接失败: {e}")
        test_results["monitoring"] = "❌ 连接失败"
    
    # 测试6: 存储管理模块
    print("\n6. 💾 测试存储管理模块...")
    try:
        response = requests.get(f"{base_url}/storage/status", timeout=5)
        if response.status_code == 200:
            print("✅ 存储管理模块正常")
            test_results["storage"] = "✅ 正常"
        else:
            print(f"❌ 存储管理模块异常: {response.status_code}")
            test_results["storage"] = "❌ 异常"
    except Exception as e:
        print(f"❌ 存储管理模块连接失败: {e}")
        test_results["storage"] = "❌ 连接失败"
    
    # 测试7: API文档
    print("\n7. 📚 测试API文档...")
    try:
        response = requests.get(f"{base_url}/docs", timeout=5)
        if response.status_code == 200:
            print("✅ API文档正常")
            test_results["docs"] = "✅ 正常"
        else:
            print(f"❌ API文档异常: {response.status_code}")
            test_results["docs"] = "❌ 异常"
    except Exception as e:
        print(f"❌ API文档连接失败: {e}")
        test_results["docs"] = "❌ 连接失败"
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📋 测试结果汇总")
    print("=" * 60)
    
    for module, status in test_results.items():
        print(f"   {module.ljust(15)}: {status}")
    
    # 统计
    total_modules = len(test_results)
    success_modules = sum(1 for status in test_results.values() if "✅" in status)
    success_rate = (success_modules / total_modules) * 100 if total_modules > 0 else 0
    
    print(f"\n📊 总体统计:")
    print(f"   总模块数: {total_modules}")
    print(f"   成功模块: {success_modules}")
    print(f"   成功率: {success_rate:.1f}%")
    
    if success_rate >= 85:
        print(f"\n🎉 恭喜！全量功能恢复成功！")
        print(f"✅ {success_modules}/{total_modules} 个模块正常运行")
        return True
    else:
        print(f"\n⚠️  部分模块需要进一步修复")
        return False

if __name__ == "__main__":
    success = test_all_modules()
    
    print("\n" + "=" * 60)
    if success:
        print("🚀 MythEVE应用全量功能恢复完成！")
    else:
        print("🔧 需要继续修复部分功能")
    print("=" * 60)
