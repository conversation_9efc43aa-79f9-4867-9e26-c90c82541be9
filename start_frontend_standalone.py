#!/usr/bin/env python3
"""
独立的前端启动脚本
"""
import subprocess
import time
import os
import sys
from pathlib import Path
import urllib.request

def find_working_npm():
    """找到可用的npm命令"""
    npm_commands = ["npm", "npm.cmd", r"C:\Program Files\nodejs\npm.cmd"]
    
    for npm_cmd in npm_commands:
        try:
            result = subprocess.run(
                [npm_cmd, "--version"],
                capture_output=True,
                text=True,
                timeout=5,
                shell=True
            )
            if result.returncode == 0:
                return npm_cmd
        except:
            continue
    
    return None

def start_frontend():
    """启动前端服务器"""
    print("🎨 启动前端服务器")
    print("=" * 50)
    
    frontend_dir = Path("frontend")
    
    # 检查前端目录
    if not frontend_dir.exists():
        print("❌ frontend目录不存在")
        return False
    
    # 检查package.json
    package_json = frontend_dir / "package.json"
    if not package_json.exists():
        print("❌ package.json不存在")
        return False
    
    # 找到可用的npm命令
    working_npm = find_working_npm()
    if not working_npm:
        print("❌ 未找到可用的npm命令")
        print("💡 请确保Node.js已正确安装")
        return False
    
    print(f"✅ 使用npm命令: {working_npm}")
    
    # 检查依赖
    node_modules = frontend_dir / "node_modules"
    if not node_modules.exists():
        print("📦 安装前端依赖...")
        try:
            install_result = subprocess.run(
                [working_npm, "install"],
                cwd=frontend_dir,
                shell=True,
                timeout=120
            )
            if install_result.returncode != 0:
                print("❌ 依赖安装失败")
                return False
            print("✅ 依赖安装成功")
        except Exception as e:
            print(f"❌ 依赖安装异常: {e}")
            return False
    else:
        print("✅ 前端依赖已存在")
    
    # 启动前端服务器
    print("🚀 启动Vite开发服务器...")
    print(f"📁 工作目录: {frontend_dir.absolute()}")
    
    try:
        # 使用适合Windows的启动方式
        if os.name == 'nt':  # Windows
            cmd = f'cd /d "{frontend_dir.absolute()}" && {working_npm} run dev'
            process = subprocess.Popen(
                cmd,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1
            )
        else:
            # Unix/Linux
            process = subprocess.Popen(
                [working_npm, "run", "dev"],
                cwd=frontend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True
            )
        
        print("⏳ 等待前端服务器启动...")
        
        # 等待启动并显示输出
        startup_timeout = 30
        start_time = time.time()
        
        while time.time() - start_time < startup_timeout:
            # 检查进程状态
            if process.poll() is not None:
                # 进程已退出
                output = process.stdout.read()
                print("❌ 前端进程已退出")
                if output:
                    print(f"📋 输出: {output}")
                return False
            
            # 尝试连接前端服务器
            try:
                response = urllib.request.urlopen('http://localhost:3000', timeout=2)
                if response.getcode() == 200:
                    print("✅ 前端服务器启动成功！")
                    print("📝 访问地址: http://localhost:3000")
                    return process
            except:
                pass
            
            time.sleep(1)
        
        # 启动超时
        print("❌ 前端启动超时")
        
        # 尝试获取输出信息
        try:
            output = process.stdout.read()
            if output:
                print(f"📋 输出信息: {output}")
        except:
            pass
        
        process.terminate()
        return False
        
    except Exception as e:
        print(f"❌ 启动前端失败: {e}")
        return False

def test_frontend_access():
    """测试前端访问"""
    print("\n🧪 测试前端访问")
    print("-" * 30)
    
    try:
        response = urllib.request.urlopen('http://localhost:3000', timeout=5)
        if response.getcode() == 200:
            content = response.read().decode('utf-8')
            print("✅ 前端页面访问正常")
            
            # 检查页面内容
            if 'EVE Online' in content:
                print("✅ 页面内容包含EVE Online")
            if 'react' in content.lower():
                print("✅ 检测到React应用")
            if 'vite' in content.lower():
                print("✅ 检测到Vite开发服务器")
            
            return True
        else:
            print(f"❌ 前端访问异常: {response.getcode()}")
            return False
    except Exception as e:
        print(f"❌ 前端访问失败: {e}")
        return False

def main():
    """主函数"""
    print("🎨 前端独立启动工具")
    print("=" * 60)
    
    # 启动前端
    frontend_process = start_frontend()
    
    if frontend_process:
        # 测试访问
        if test_frontend_access():
            print("\n🎉 前端启动和测试完全成功！")
            print("\n📝 现在可以:")
            print("   1. 访问前端界面: http://localhost:3000")
            print("   2. 测试EVE登录功能")
            print("   3. 查看前端界面和后端API的集成")
            
            try:
                print("\n⏳ 前端服务器运行中...")
                print("   按 Ctrl+C 停止服务器")
                frontend_process.wait()
            except KeyboardInterrupt:
                print("\n🛑 停止前端服务器...")
                frontend_process.terminate()
                try:
                    frontend_process.wait(timeout=5)
                    print("✅ 前端服务器已停止")
                except subprocess.TimeoutExpired:
                    frontend_process.kill()
                    print("⚠️  强制终止前端服务器")
            
            return 0
        else:
            print("\n⚠️  前端启动成功但访问测试失败")
            frontend_process.terminate()
            return 1
    else:
        print("\n❌ 前端启动失败")
        print("\n💡 可能的解决方案:")
        print("   1. 检查Node.js是否正确安装")
        print("   2. 运行: cd frontend && npm install")
        print("   3. 检查端口3000是否被占用")
        print("   4. 查看上面的错误信息")
        return 1

if __name__ == "__main__":
    sys.exit(main())
