# EVE Online Assistant - API接口文档

## 概述

EVE Online Assistant 提供了一套完整的RESTful API，用于管理EVE Online游戏相关的数据和功能。API基于FastAPI框架构建，支持自动生成的OpenAPI文档。

## 基础信息

- **基础URL**: `http://localhost:8000` (开发环境)
- **API版本**: v1
- **认证方式**: JWT Bearer Token
- **数据格式**: JSON
- **字符编码**: UTF-8

## 认证

### EVE SSO 认证流程

1. **获取授权URL**
   ```http
   GET /auth/login
   ```

2. **处理回调**
   ```http
   GET /auth/callback?code={authorization_code}&state={state}
   ```

3. **使用访问令牌**
   ```http
   Authorization: Bearer {access_token}
   ```

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 错误响应
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": {}
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 分页响应
```json
{
  "success": true,
  "data": {
    "items": [],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 100,
      "total_pages": 5,
      "has_next": true,
      "has_prev": false
    }
  }
}
```

## API端点

### 1. 系统端点

#### 健康检查
```http
GET /health
```

**响应示例:**
```json
{
  "status": "healthy",
  "version": "0.1.0",
  "database": "connected",
  "debug": false
}
```

#### 应用信息
```http
GET /info
```

**响应示例:**
```json
{
  "name": "EVE Online Assistant",
  "version": "0.1.0",
  "debug": false,
  "environment": "production"
}
```

### 2. 认证端点

#### 登录
```http
GET /auth/login
```

**查询参数:**
- `redirect_uri` (可选): 登录成功后的重定向URL

**响应示例:**
```json
{
  "success": true,
  "data": {
    "login_url": "https://login.eveonline.com/v2/oauth/authorize?...",
    "state": "random_state_string"
  }
}
```

#### 回调处理
```http
GET /auth/callback
```

**查询参数:**
- `code`: 授权码
- `state`: 状态参数

**响应示例:**
```json
{
  "success": true,
  "data": {
    "access_token": "jwt_token_here",
    "refresh_token": "refresh_token_here",
    "expires_in": 1800,
    "character": {
      "character_id": 123456789,
      "character_name": "角色名称"
    }
  }
}
```

#### 刷新令牌
```http
POST /auth/refresh
```

**请求体:**
```json
{
  "refresh_token": "refresh_token_here"
}
```

#### 登出
```http
POST /auth/logout
```

**请求头:**
```http
Authorization: Bearer {access_token}
```

### 3. 角色管理端点

#### 获取角色列表
```http
GET /characters
```

**查询参数:**
- `page` (可选): 页码，默认1
- `page_size` (可选): 每页大小，默认20

**响应示例:**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "character_id": 123456789,
        "name": "角色名称",
        "corporation_id": 987654321,
        "alliance_id": 555666777,
        "security_status": -1.5,
        "birthday": "2015-03-24T11:37:00Z",
        "last_login": "2024-01-01T12:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 1,
      "total_pages": 1
    }
  }
}
```

#### 获取角色详情
```http
GET /characters/{character_id}
```

**路径参数:**
- `character_id`: 角色ID

**响应示例:**
```json
{
  "success": true,
  "data": {
    "character_id": 123456789,
    "name": "角色名称",
    "corporation_id": 987654321,
    "alliance_id": 555666777,
    "security_status": -1.5,
    "birthday": "2015-03-24T11:37:00Z",
    "attributes": {
      "charisma": 20,
      "intelligence": 20,
      "memory": 20,
      "perception": 20,
      "willpower": 20
    },
    "skills": {
      "total_sp": 50000000,
      "unallocated_sp": 100000
    },
    "location": {
      "system_id": 30000142,
      "system_name": "Jita",
      "station_id": 60003760,
      "station_name": "Jita IV - Moon 4 - Caldari Navy Assembly Plant"
    }
  }
}
```

#### 同步角色数据
```http
POST /characters/{character_id}/sync
```

**路径参数:**
- `character_id`: 角色ID

**请求头:**
```http
Authorization: Bearer {access_token}
```

### 4. 资产管理端点

#### 获取角色资产
```http
GET /characters/{character_id}/assets
```

**查询参数:**
- `location_id` (可选): 位置ID筛选
- `type_id` (可选): 物品类型ID筛选
- `page` (可选): 页码
- `page_size` (可选): 每页大小

**响应示例:**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "item_id": 1234567890,
        "type_id": 587,
        "type_name": "Rifter",
        "quantity": 1,
        "location_id": 60003760,
        "location_name": "Jita IV - Moon 4 - Caldari Navy Assembly Plant",
        "location_type": "station",
        "estimated_value": 1500000.00,
        "last_updated": "2024-01-01T12:00:00Z"
      }
    ],
    "summary": {
      "total_items": 150,
      "total_value": 500000000.00,
      "locations": 5
    }
  }
}
```

#### 获取资产价值统计
```http
GET /characters/{character_id}/assets/value
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "total_value": 500000000.00,
    "by_location": [
      {
        "location_id": 60003760,
        "location_name": "Jita IV - Moon 4 - Caldari Navy Assembly Plant",
        "value": 300000000.00,
        "percentage": 60.0
      }
    ],
    "by_category": [
      {
        "category": "Ships",
        "value": 200000000.00,
        "percentage": 40.0
      }
    ]
  }
}
```

### 5. 市场分析端点

#### 获取市场订单
```http
GET /market/orders
```

**查询参数:**
- `region_id`: 区域ID
- `type_id` (可选): 物品类型ID
- `order_type` (可选): 订单类型 (buy/sell)
- `page` (可选): 页码
- `page_size` (可选): 每页大小

**响应示例:**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "order_id": 5555666777,
        "type_id": 587,
        "type_name": "Rifter",
        "is_buy_order": false,
        "price": 1500000.00,
        "volume_remain": 10,
        "volume_total": 20,
        "location_id": 60003760,
        "issued": "2024-01-01T10:00:00Z",
        "duration": 90
      }
    ]
  }
}
```

#### 获取价格历史
```http
GET /market/history
```

**查询参数:**
- `region_id`: 区域ID
- `type_id`: 物品类型ID
- `days` (可选): 历史天数，默认30

**响应示例:**
```json
{
  "success": true,
  "data": {
    "type_id": 587,
    "region_id": 10000002,
    "history": [
      {
        "date": "2024-01-01",
        "average": 1500000.00,
        "highest": 1600000.00,
        "lowest": 1400000.00,
        "volume": 1000,
        "order_count": 50
      }
    ]
  }
}
```

### 6. 工业生产端点

#### 获取工业任务
```http
GET /characters/{character_id}/industry/jobs
```

**查询参数:**
- `status` (可选): 任务状态筛选
- `activity_id` (可选): 活动类型筛选

**响应示例:**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "job_id": 123456789,
        "activity_id": 1,
        "activity_name": "Manufacturing",
        "blueprint_id": 987654321,
        "blueprint_type_id": 11129,
        "product_type_id": 587,
        "runs": 10,
        "status": "active",
        "start_date": "2024-01-01T10:00:00Z",
        "end_date": "2024-01-02T10:00:00Z",
        "facility_id": 60003760
      }
    ]
  }
}
```

## 错误代码

| 错误代码 | HTTP状态码 | 描述 |
|---------|-----------|------|
| `INVALID_REQUEST` | 400 | 请求参数无效 |
| `UNAUTHORIZED` | 401 | 未授权访问 |
| `FORBIDDEN` | 403 | 禁止访问 |
| `NOT_FOUND` | 404 | 资源不存在 |
| `RATE_LIMITED` | 429 | 请求频率超限 |
| `INTERNAL_ERROR` | 500 | 内部服务器错误 |
| `ESI_ERROR` | 502 | ESI API错误 |
| `SERVICE_UNAVAILABLE` | 503 | 服务不可用 |

## 限流规则

- 默认限制: 每秒10个请求
- 突发限制: 20个请求
- 认证用户: 每秒20个请求
- 特殊端点可能有不同的限制

## 开发工具

### Swagger UI
访问 `http://localhost:8000/docs` 查看交互式API文档

### ReDoc
访问 `http://localhost:8000/redoc` 查看API文档

### OpenAPI规范
访问 `http://localhost:8000/openapi.json` 获取OpenAPI规范文件
