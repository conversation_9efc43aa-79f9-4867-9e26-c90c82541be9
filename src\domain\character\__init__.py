"""
角色管理域
"""
from .entities import Character
from .value_objects import Attributes, Skill, SkillQueue, Clone, Implant
from .events import (
    CharacterCreatedEvent, CharacterUpdatedEvent, CharacterLocationChangedEvent,
    CharacterOnlineStatusChangedEvent, SkillTrainingStartedEvent,
    SkillTrainingCompletedEvent, SkillTrainingPausedEvent,
    CharacterAttributesUpdatedEvent, CharacterWalletBalanceChangedEvent,
    CharacterJoinedCorporationEvent, CharacterLeftCorporationEvent,
    CharacterCloneJumpedEvent
)
from .repositories import CharacterRepository
from .services import SkillTrainingService, CharacterAnalysisService, CharacterSecurityService

__all__ = [
    # 实体
    "Character",

    # 值对象
    "Attributes",
    "Skill",
    "SkillQueue",
    "Clone",
    "Implant",

    # 事件
    "CharacterCreatedEvent",
    "CharacterUpdatedEvent",
    "CharacterLocationChangedEvent",
    "CharacterOnlineStatusChangedEvent",
    "SkillTrainingStartedEvent",
    "SkillTrainingCompletedEvent",
    "SkillTrainingPausedEvent",
    "CharacterAttributesUpdatedEvent",
    "CharacterWalletBalanceChangedEvent",
    "CharacterJoinedCorporationEvent",
    "CharacterLeftCorporationEvent",
    "CharacterCloneJumpedEvent",

    # 仓储
    "CharacterRepository",

    # 服务
    "SkillTrainingService",
    "CharacterAnalysisService",
    "CharacterSecurityService",
]
