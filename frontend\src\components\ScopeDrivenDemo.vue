<template>
  <div class="scope-driven-demo">
    <div class="container-fluid">
      <!-- 页面标题 -->
      <div class="row mb-4">
        <div class="col-12">
          <h1 class="h3 mb-0">
            <i class="fas fa-cogs me-2"></i>
            基于Scope的功能演示
          </h1>
          <p class="text-muted mt-2">
            参考SeAT架构，展示如何根据ESI权限动态生成功能菜单和数据访问计划
          </p>
        </div>
      </div>

      <!-- 功能概览卡片 -->
      <div class="row mb-4">
        <div class="col-md-3">
          <div class="card text-center">
            <div class="card-body">
              <i class="fas fa-key fa-2x text-primary mb-2"></i>
              <h5 class="card-title">{{ userScopes.length }}</h5>
              <p class="card-text text-muted">已授权权限</p>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card text-center">
            <div class="card-body">
              <i class="fas fa-list fa-2x text-success mb-2"></i>
              <h5 class="card-title">{{ menuItems.length }}</h5>
              <p class="card-text text-muted">可用功能</p>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card text-center">
            <div class="card-body">
              <i class="fas fa-database fa-2x text-info mb-2"></i>
              <h5 class="card-title">{{ dataEndpoints }}</h5>
              <p class="card-text text-muted">数据端点</p>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card text-center">
            <div class="card-body">
              <i class="fas fa-chart-pie fa-2x text-warning mb-2"></i>
              <h5 class="card-title">{{ coveragePercentage }}%</h5>
              <p class="card-text text-muted">功能覆盖率</p>
            </div>
          </div>
        </div>
      </div>

      <div class="row">
        <!-- 左侧：动态菜单 -->
        <div class="col-md-4">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="fas fa-bars me-2"></i>
                动态功能菜单
              </h5>
            </div>
            <div class="card-body p-0">
              <div class="list-group list-group-flush">
                <template v-for="item in menuItems" :key="item.id">
                  <div class="list-group-item">
                    <div class="d-flex align-items-center">
                      <i :class="item.icon + ' me-2'"></i>
                      <strong>{{ item.title }}</strong>
                      <span v-if="item.children && item.children.length" 
                            class="badge bg-secondary ms-auto">
                        {{ item.children.length }}
                      </span>
                    </div>
                    <small class="text-muted d-block mt-1">{{ item.description }}</small>
                    
                    <!-- 子菜单 -->
                    <div v-if="item.children && item.children.length" class="mt-2">
                      <div v-for="child in item.children" :key="child.id" 
                           class="d-flex align-items-center py-1 ps-3">
                        <i :class="child.icon + ' me-2 text-muted'" style="font-size: 0.8em;"></i>
                        <span class="small">{{ child.title }}</span>
                        <span v-if="child.required_scopes && child.required_scopes.length" 
                              class="badge bg-light text-dark ms-auto" style="font-size: 0.7em;">
                          需要权限
                        </span>
                      </div>
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </div>
        </div>

        <!-- 中间：数据访问计划 -->
        <div class="col-md-4">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="fas fa-project-diagram me-2"></i>
                数据访问计划
              </h5>
            </div>
            <div class="card-body">
              <div v-if="dataPlan.categories">
                <div v-for="(endpoints, category) in dataPlan.categories" 
                     :key="category" class="mb-3">
                  <h6 class="text-capitalize">
                    <i class="fas fa-folder me-1"></i>
                    {{ getCategoryDisplayName(category) }}
                    <span class="badge bg-primary ms-2">{{ endpoints.length }}</span>
                  </h6>
                  <div class="ps-3">
                    <div v-for="endpoint in endpoints.slice(0, 3)" 
                         :key="endpoint.name" class="small mb-1">
                      <i class="fas fa-circle me-1" style="font-size: 0.5em;"></i>
                      {{ endpoint.description }}
                      <span class="badge bg-light text-dark ms-1" style="font-size: 0.7em;">
                        {{ endpoint.cache_level }}
                      </span>
                    </div>
                    <div v-if="endpoints.length > 3" class="small text-muted">
                      ... 还有 {{ endpoints.length - 3 }} 个端点
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：功能可用性分析 -->
        <div class="col-md-4">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="fas fa-chart-bar me-2"></i>
                功能可用性分析
              </h5>
            </div>
            <div class="card-body">
              <!-- 总体覆盖率 -->
              <div class="mb-3">
                <div class="d-flex justify-content-between align-items-center mb-1">
                  <span class="small">总体覆盖率</span>
                  <span class="small font-weight-bold">{{ coveragePercentage }}%</span>
                </div>
                <div class="progress" style="height: 8px;">
                  <div class="progress-bar" 
                       :style="{ width: coveragePercentage + '%' }"
                       :class="getProgressBarClass(coveragePercentage)">
                  </div>
                </div>
              </div>

              <!-- 分类覆盖率 -->
              <div v-if="availability.categories">
                <h6 class="small text-muted mb-2">分类覆盖率</h6>
                <div v-for="(data, category) in availability.categories" 
                     :key="category" class="mb-2">
                  <div class="d-flex justify-content-between align-items-center mb-1">
                    <span class="small text-capitalize">{{ getCategoryDisplayName(category) }}</span>
                    <span class="small">{{ data.coverage_percentage }}%</span>
                  </div>
                  <div class="progress" style="height: 6px;">
                    <div class="progress-bar bg-info" 
                         :style="{ width: data.coverage_percentage + '%' }">
                    </div>
                  </div>
                </div>
              </div>

              <!-- 权限推荐 -->
              <div v-if="recommendations.length" class="mt-3">
                <h6 class="small text-muted mb-2">权限推荐</h6>
                <div v-for="rec in recommendations.slice(0, 2)" 
                     :key="rec.category" class="alert alert-info py-2 px-3 mb-2">
                  <div class="small">
                    <strong>{{ rec.category }}</strong>
                    <span class="badge bg-warning text-dark ms-1">{{ rec.priority }}</span>
                  </div>
                  <div class="small text-muted mt-1">{{ rec.description }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 缓存策略展示 -->
      <div class="row mt-4">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="fas fa-tachometer-alt me-2"></i>
                缓存策略分析
              </h5>
            </div>
            <div class="card-body">
              <div v-if="cacheStrategy.cache_levels" class="row">
                <div v-for="(data, level) in cacheStrategy.cache_levels" 
                     :key="level" class="col-md-2 mb-3">
                  <div class="text-center">
                    <div class="badge bg-secondary mb-2 d-block">{{ level }}</div>
                    <div class="h5 mb-1">{{ data.endpoints.length }}</div>
                    <div class="small text-muted">{{ formatTTL(data.ttl_seconds) }}</div>
                  </div>
                </div>
              </div>
              
              <div v-if="cacheStrategy.recommendations && cacheStrategy.recommendations.length" 
                   class="mt-3">
                <h6 class="small text-muted mb-2">缓存建议</h6>
                <div v-for="rec in cacheStrategy.recommendations" 
                     :key="rec.message" class="alert alert-warning py-2 px-3 mb-1">
                  <i class="fas fa-exclamation-triangle me-1"></i>
                  <span class="small">{{ rec.message }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import axios from 'axios'

export default {
  name: 'ScopeDrivenDemo',
  setup() {
    // 响应式数据
    const userScopes = ref([])
    const menuItems = ref([])
    const dataPlan = ref({})
    const availability = ref({})
    const recommendations = ref([])
    const cacheStrategy = ref({})
    const loading = ref(false)

    // 计算属性
    const dataEndpoints = computed(() => {
      return dataPlan.value.total_endpoints || 0
    })

    const coveragePercentage = computed(() => {
      return availability.value.coverage_percentage || 0
    })

    // 方法
    const loadData = async () => {
      loading.value = true
      try {
        // 并行加载所有数据
        const [menuRes, planRes, availRes, recRes, cacheRes] = await Promise.all([
          axios.get('/api/features/menu'),
          axios.get('/api/features/data-plan'),
          axios.get('/api/features/availability'),
          axios.get('/api/features/recommendations'),
          axios.get('/api/features/cache-strategy')
        ])

        menuItems.value = menuRes.data.data
        dataPlan.value = planRes.data.data
        availability.value = availRes.data.data
        recommendations.value = recRes.data.data.recommendations
        cacheStrategy.value = cacheRes.data.data

        // 提取用户权限数量（从可用性数据中）
        userScopes.value = new Array(availability.value.user_scopes || 0)

      } catch (error) {
        console.error('加载数据失败:', error)
      } finally {
        loading.value = false
      }
    }

    const getCategoryDisplayName = (category) => {
      const names = {
        character: '角色管理',
        corporation: '公司管理',
        industry: '工业管理',
        market: '市场管理',
        combat: '战斗记录',
        fleet: '舰队管理',
        universe: '宇宙数据'
      }
      return names[category] || category
    }

    const getProgressBarClass = (percentage) => {
      if (percentage >= 80) return 'bg-success'
      if (percentage >= 60) return 'bg-info'
      if (percentage >= 40) return 'bg-warning'
      return 'bg-danger'
    }

    const formatTTL = (seconds) => {
      if (seconds < 60) return `${seconds}秒`
      if (seconds < 3600) return `${Math.floor(seconds / 60)}分钟`
      if (seconds < 86400) return `${Math.floor(seconds / 3600)}小时`
      return `${Math.floor(seconds / 86400)}天`
    }

    // 生命周期
    onMounted(() => {
      loadData()
    })

    return {
      userScopes,
      menuItems,
      dataPlan,
      availability,
      recommendations,
      cacheStrategy,
      loading,
      dataEndpoints,
      coveragePercentage,
      getCategoryDisplayName,
      getProgressBarClass,
      formatTTL
    }
  }
}
</script>

<style scoped>
.scope-driven-demo {
  padding: 20px 0;
}

.card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.list-group-item {
  border-left: none;
  border-right: none;
}

.list-group-item:first-child {
  border-top: none;
}

.list-group-item:last-child {
  border-bottom: none;
}

.progress {
  background-color: #e9ecef;
}

.badge {
  font-size: 0.75em;
}

.alert {
  border: none;
}
</style>
