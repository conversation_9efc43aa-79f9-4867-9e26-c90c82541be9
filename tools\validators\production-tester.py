#!/usr/bin/env python3
"""
生产级测试脚本 - 全面验证EVE Online Assistant
"""
import sys
import subprocess
import time
import urllib.request
import json

def test_dependencies():
    """测试所有依赖"""
    print("🔍 测试依赖包...")
    
    dependencies = [
        ("pydantic", "pydantic"),
        ("pydantic_settings", "pydantic-settings"),
        ("fastapi", "fastapi"),
        ("uvicorn", "uvicorn"),
        ("structlog", "structlog"),
        ("httpx", "httpx"),
        ("sqlalchemy", "sqlalchemy"),
        ("alembic", "alembic"),
        ("dotenv", "python-dotenv"),
        ("pyngrok", "pyngrok"),
        ("jwt", "pyjwt"),
    ]
    
    failed = []
    for module, package in dependencies:
        try:
            __import__(module)
            print(f"   ✅ {package}")
        except ImportError as e:
            print(f"   ❌ {package} - {e}")
            failed.append(package)
    
    return len(failed) == 0, failed

def test_application_modules():
    """测试应用程序模块"""
    print("\n🔍 测试应用程序模块...")
    
    modules = [
        ("src.infrastructure.config", "配置模块"),
        ("src.infrastructure.esi", "ESI客户端"),
        ("src.domain.models", "领域模型"),
        ("src.infrastructure.persistence", "数据持久化"),
        ("src.application.services", "应用服务"),
        ("src.presentation.api.main", "FastAPI应用"),
    ]
    
    failed = []
    for module, name in modules:
        try:
            __import__(module)
            print(f"   ✅ {name}")
        except Exception as e:
            print(f"   ❌ {name} - {e}")
            failed.append(name)
    
    return len(failed) == 0, failed

def test_server_startup():
    """测试服务器启动"""
    print("\n🔍 测试服务器启动...")
    
    try:
        # 启动服务器进程
        process = subprocess.Popen([
            sys.executable, "-m", "uvicorn",
            "src.presentation.api.main:app",
            "--host", "127.0.0.1",
            "--port", "8000"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 等待服务器启动
        print("   ⏳ 等待服务器启动...")
        time.sleep(5)
        
        # 测试健康检查端点
        try:
            with urllib.request.urlopen('http://127.0.0.1:8000/health/', timeout=10) as response:
                data = json.loads(response.read().decode())
                print(f"   ✅ 服务器启动成功 - 状态码: {response.status}")
                print(f"   ✅ 健康检查通过")
                
                # 停止服务器
                process.terminate()
                process.wait(timeout=5)
                print("   ✅ 服务器正常停止")
                return True
                
        except Exception as e:
            print(f"   ❌ 健康检查失败: {e}")
            process.terminate()
            return False
            
    except Exception as e:
        print(f"   ❌ 服务器启动失败: {e}")
        return False

def test_api_endpoints():
    """测试API端点"""
    print("\n🔍 测试API端点...")
    
    try:
        # 启动服务器
        process = subprocess.Popen([
            sys.executable, "-m", "uvicorn",
            "src.presentation.api.main:app",
            "--host", "127.0.0.1",
            "--port", "8000"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        time.sleep(5)
        
        endpoints = [
            ("/", "根端点"),
            ("/health/", "健康检查"),
            ("/docs", "API文档"),
            ("/openapi.json", "OpenAPI规范"),
        ]
        
        failed = []
        for endpoint, name in endpoints:
            try:
                with urllib.request.urlopen(f'http://127.0.0.1:8000{endpoint}', timeout=5) as response:
                    if response.status == 200:
                        print(f"   ✅ {name} ({endpoint})")
                    else:
                        print(f"   ❌ {name} ({endpoint}) - 状态码: {response.status}")
                        failed.append(name)
            except Exception as e:
                print(f"   ❌ {name} ({endpoint}) - {e}")
                failed.append(name)
        
        process.terminate()
        process.wait(timeout=5)
        
        return len(failed) == 0, failed
        
    except Exception as e:
        print(f"   ❌ API端点测试失败: {e}")
        return False, ["API端点测试"]

def main():
    """主测试函数"""
    print("🚀 EVE Online Assistant 生产级测试")
    print("=" * 60)
    
    all_passed = True
    
    # 测试1: 依赖包
    deps_ok, failed_deps = test_dependencies()
    if not deps_ok:
        print(f"\n❌ 依赖测试失败: {', '.join(failed_deps)}")
        all_passed = False
    
    # 测试2: 应用模块
    modules_ok, failed_modules = test_application_modules()
    if not modules_ok:
        print(f"\n❌ 模块测试失败: {', '.join(failed_modules)}")
        all_passed = False
    
    # 测试3: 服务器启动
    if deps_ok and modules_ok:
        server_ok = test_server_startup()
        if not server_ok:
            print("\n❌ 服务器启动测试失败")
            all_passed = False
        
        # 测试4: API端点
        if server_ok:
            api_ok, failed_apis = test_api_endpoints()
            if not api_ok:
                print(f"\n❌ API端点测试失败: {', '.join(failed_apis)}")
                all_passed = False
    
    # 总结
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！应用已准备好生产部署")
        print("\n✅ 可以使用以下命令启动:")
        print("   python start.py")
        print("   或")
        print("   python production_start.py")
        return 0
    else:
        print("❌ 测试失败！请修复问题后重试")
        return 1

if __name__ == "__main__":
    sys.exit(main())
