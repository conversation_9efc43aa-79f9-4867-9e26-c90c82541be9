import { AuthTokens, User } from '@/types'

// 存储键名
const AUTH_TOKENS_KEY = 'eve_assistant_auth_tokens'
const USER_INFO_KEY = 'eve_assistant_user_info'

// 令牌相关操作
export function getAuthTokens(): AuthTokens | null {
  try {
    const tokens = localStorage.getItem(AUTH_TOKENS_KEY)
    if (!tokens) return null

    const parsed = JSON.parse(tokens) as AuthTokens

    // 检查令牌是否过期
    if (isTokenExpired(parsed)) {
      removeAuthTokens()
      return null
    }

    return parsed
  } catch (error) {
    console.error('Failed to get auth tokens:', error)
    removeAuthTokens()
    return null
  }
}

export function setAuthTokens(tokens: AuthTokens): void {
  try {
    localStorage.setItem(AUTH_TOKENS_KEY, JSON.stringify(tokens))
  } catch (error) {
    console.error('Failed to set auth tokens:', error)
  }
}

export function removeAuthTokens(): void {
  try {
    localStorage.removeItem(AUTH_TOKENS_KEY)
    localStorage.removeItem(USER_INFO_KEY)
  } catch (error) {
    console.error('Failed to remove auth tokens:', error)
  }
}

export function isTokenExpired(tokens: AuthTokens): boolean {
  try {
    const expiresAt = new Date(tokens.expiresAt)
    const now = new Date()

    // 提前5分钟认为过期，确保有足够时间刷新
    const bufferTime = 5 * 60 * 1000 // 5分钟
    return now.getTime() >= (expiresAt.getTime() - bufferTime)
  } catch (error) {
    console.error('Failed to check token expiration:', error)
    return true
  }
}

export function getAccessToken(): string | null {
  const tokens = getAuthTokens()
  return tokens?.accessToken || null
}

export function getRefreshToken(): string | null {
  const tokens = getAuthTokens()
  return tokens?.refreshToken || null
}

// 用户信息相关操作
export function getUserInfo(): User | null {
  try {
    const userInfo = localStorage.getItem(USER_INFO_KEY)
    return userInfo ? JSON.parse(userInfo) : null
  } catch (error) {
    console.error('Failed to get user info:', error)
    return null
  }
}

export function setUserInfo(user: User): void {
  try {
    localStorage.setItem(USER_INFO_KEY, JSON.stringify(user))
  } catch (error) {
    console.error('Failed to set user info:', error)
  }
}

export function removeUserInfo(): void {
  try {
    localStorage.removeItem(USER_INFO_KEY)
  } catch (error) {
    console.error('Failed to remove user info:', error)
  }
}

// 认证状态检查
export function isAuthenticated(): boolean {
  const tokens = getAuthTokens()
  return tokens !== null && !isTokenExpired(tokens)
}

// JWT令牌解析
export function parseJwtToken(token: string): any {
  try {
    const base64Url = token.split('.')[1]
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    )
    return JSON.parse(jsonPayload)
  } catch (error) {
    console.error('Failed to parse JWT token:', error)
    return null
  }
}

// 获取令牌信息
export function getTokenInfo(token: string): {
  userId?: string
  characterId?: number
  scopes?: string[]
  expiresAt?: Date
  issuedAt?: Date
} | null {
  try {
    const payload = parseJwtToken(token)
    if (!payload) return null

    return {
      userId: payload.user_id,
      characterId: payload.character_id,
      scopes: payload.scopes || [],
      expiresAt: payload.exp ? new Date(payload.exp * 1000) : undefined,
      issuedAt: payload.iat ? new Date(payload.iat * 1000) : undefined,
    }
  } catch (error) {
    console.error('Failed to get token info:', error)
    return null
  }
}

// 权限检查
export function hasPermission(requiredPermissions: string[]): boolean {
  const tokens = getAuthTokens()
  if (!tokens) return false

  const tokenInfo = getTokenInfo(tokens.accessToken)
  if (!tokenInfo?.scopes) return false

  return requiredPermissions.every(permission =>
    tokenInfo.scopes!.includes(permission)
  )
}

export function hasAnyPermission(permissions: string[]): boolean {
  const tokens = getAuthTokens()
  if (!tokens) return false

  const tokenInfo = getTokenInfo(tokens.accessToken)
  if (!tokenInfo?.scopes) return false

  return permissions.some(permission =>
    tokenInfo.scopes!.includes(permission)
  )
}

// 会话管理
export function createSession(tokens: AuthTokens, user: User): void {
  setAuthTokens(tokens)
  setUserInfo(user)

  // 记录登录时间
  const loginTime = new Date().toISOString()
  localStorage.setItem('eve_assistant_login_time', loginTime)
}

export function destroySession(): void {
  removeAuthTokens()
  removeUserInfo()
  localStorage.removeItem('eve_assistant_login_time')

  // 清除其他可能的缓存数据
  const keysToRemove = []
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i)
    if (key && key.startsWith('eve_assistant_')) {
      keysToRemove.push(key)
    }
  }

  keysToRemove.forEach(key => localStorage.removeItem(key))
}

export function getSessionDuration(): number {
  try {
    const loginTime = localStorage.getItem('eve_assistant_login_time')
    if (!loginTime) return 0

    const loginDate = new Date(loginTime)
    const now = new Date()
    return now.getTime() - loginDate.getTime()
  } catch (error) {
    console.error('Failed to get session duration:', error)
    return 0
  }
}

// 安全相关
export function generateState(): string {
  const array = new Uint8Array(32)
  crypto.getRandomValues(array)
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
}

export function validateState(receivedState: string, expectedState: string): boolean {
  return receivedState === expectedState
}

// 记住我功能
export function setRememberMe(remember: boolean): void {
  if (remember) {
    localStorage.setItem('eve_assistant_remember_me', 'true')
  } else {
    localStorage.removeItem('eve_assistant_remember_me')
  }
}

export function shouldRememberMe(): boolean {
  return localStorage.getItem('eve_assistant_remember_me') === 'true'
}

// 自动登录检查
export function shouldAutoLogin(): boolean {
  return isAuthenticated() && shouldRememberMe()
}

// 令牌刷新相关
export function shouldRefreshToken(): boolean {
  const tokens = getAuthTokens()
  if (!tokens) return false

  try {
    const expiresAt = new Date(tokens.expiresAt)
    const now = new Date()

    // 如果令牌在10分钟内过期，则需要刷新
    const refreshThreshold = 10 * 60 * 1000 // 10分钟
    return now.getTime() >= (expiresAt.getTime() - refreshThreshold)
  } catch (error) {
    console.error('Failed to check if should refresh token:', error)
    return false
  }
}

// 导出所有功能
export const authUtils = {
  getAuthTokens,
  setAuthTokens,
  removeAuthTokens,
  isTokenExpired,
  getAccessToken,
  getRefreshToken,
  getUserInfo,
  setUserInfo,
  removeUserInfo,
  isAuthenticated,
  parseJwtToken,
  getTokenInfo,
  hasPermission,
  hasAnyPermission,
  createSession,
  destroySession,
  getSessionDuration,
  generateState,
  validateState,
  setRememberMe,
  shouldRememberMe,
  shouldAutoLogin,
  shouldRefreshToken,
}
