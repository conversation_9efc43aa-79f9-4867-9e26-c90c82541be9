"""
角色领域仓储接口
"""
from abc import ABC, abstractmethod
from typing import List, Optional

from ..shared.value_objects import CharacterId, CorporationId, AllianceId
from .entities import Character


class CharacterRepository(ABC):
    """角色仓储接口"""
    
    @abstractmethod
    async def get_by_id(self, character_id: CharacterId) -> Optional[Character]:
        """根据ID获取角色"""
        pass
    
    @abstractmethod
    async def get_by_name(self, name: str) -> Optional[Character]:
        """根据名称获取角色"""
        pass
    
    @abstractmethod
    async def find_by_corporation(self, corporation_id: CorporationId) -> List[Character]:
        """根据公司ID查找角色"""
        pass
    
    @abstractmethod
    async def find_by_alliance(self, alliance_id: AllianceId) -> List[Character]:
        """根据联盟ID查找角色"""
        pass
    
    @abstractmethod
    async def find_online_characters(self) -> List[Character]:
        """查找在线角色"""
        pass
    
    @abstractmethod
    async def save(self, character: Character) -> None:
        """保存角色"""
        pass
    
    @abstractmethod
    async def delete(self, character_id: CharacterId) -> None:
        """删除角色"""
        pass
    
    @abstractmethod
    async def exists(self, character_id: CharacterId) -> bool:
        """检查角色是否存在"""
        pass
    
    @abstractmethod
    async def count_by_corporation(self, corporation_id: CorporationId) -> int:
        """统计公司角色数量"""
        pass
    
    @abstractmethod
    async def find_characters_with_skill(self, skill_id: int, min_level: int = 1) -> List[Character]:
        """查找拥有指定技能的角色"""
        pass
    
    @abstractmethod
    async def find_characters_in_system(self, system_id: int) -> List[Character]:
        """查找在指定星系的角色"""
        pass
    
    @abstractmethod
    async def find_characters_by_security_status_range(self, 
                                                      min_status: float, 
                                                      max_status: float) -> List[Character]:
        """根据安全等级范围查找角色"""
        pass
