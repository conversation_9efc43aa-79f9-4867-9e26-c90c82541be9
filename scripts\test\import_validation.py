#!/usr/bin/env python3
"""
导入验证脚本
用于验证所有关键模块的导入正确性
"""
import sys
import importlib
import traceback
from pathlib import Path
from typing import List, Tuple, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


class ImportValidator:
    """导入验证器"""
    
    def __init__(self):
        self.results = {
            "passed": [],
            "failed": [],
            "warnings": []
        }
    
    def validate_import(self, module_path: str, description: str = None) -> bool:
        """验证单个模块导入"""
        desc = description or module_path
        
        try:
            importlib.import_module(module_path)
            self.results["passed"].append(desc)
            print(f"✅ {desc}")
            return True
        except ImportError as e:
            self.results["failed"].append(f"{desc}: {str(e)}")
            print(f"❌ {desc}: {str(e)}")
            return False
        except Exception as e:
            self.results["failed"].append(f"{desc}: {str(e)}")
            print(f"❌ {desc}: {str(e)}")
            return False
    
    def validate_function_import(self, module_path: str, function_name: str, description: str = None) -> bool:
        """验证特定函数的导入"""
        desc = description or f"{module_path}.{function_name}"
        
        try:
            module = importlib.import_module(module_path)
            if hasattr(module, function_name):
                self.results["passed"].append(desc)
                print(f"✅ {desc}")
                return True
            else:
                error_msg = f"函数 '{function_name}' 在模块 '{module_path}' 中不存在"
                self.results["failed"].append(f"{desc}: {error_msg}")
                print(f"❌ {desc}: {error_msg}")
                return False
        except ImportError as e:
            self.results["failed"].append(f"{desc}: {str(e)}")
            print(f"❌ {desc}: {str(e)}")
            return False
    
    def inspect_module(self, module_path: str) -> Dict[str, Any]:
        """检查模块内容"""
        try:
            module = importlib.import_module(module_path)
            return {
                "exists": True,
                "attributes": [attr for attr in dir(module) if not attr.startswith('_')],
                "functions": [attr for attr in dir(module) if callable(getattr(module, attr)) and not attr.startswith('_')],
                "classes": [attr for attr in dir(module) if isinstance(getattr(module, attr), type) and not attr.startswith('_')]
            }
        except ImportError:
            return {"exists": False}
    
    def suggest_alternatives(self, module_path: str, missing_function: str) -> List[str]:
        """建议替代函数名"""
        info = self.inspect_module(module_path)
        if not info["exists"]:
            return []
        
        suggestions = []
        for func in info["functions"]:
            # 简单的相似度检查
            if missing_function.lower() in func.lower() or func.lower() in missing_function.lower():
                suggestions.append(func)
        
        return suggestions
    
    def print_summary(self):
        """打印验证结果摘要"""
        total = len(self.results["passed"]) + len(self.results["failed"])
        passed = len(self.results["passed"])
        failed = len(self.results["failed"])
        
        print("\n" + "=" * 60)
        print("📊 导入验证结果摘要")
        print("=" * 60)
        print(f"总计: {total}")
        print(f"通过: {passed} ✅")
        print(f"失败: {failed} ❌")
        print(f"成功率: {(passed/total*100):.1f}%" if total > 0 else "成功率: 0%")
        
        if self.results["failed"]:
            print(f"\n❌ 失败的导入:")
            for failure in self.results["failed"]:
                print(f"   - {failure}")
        
        if self.results["warnings"]:
            print(f"\n⚠️  警告:")
            for warning in self.results["warnings"]:
                print(f"   - {warning}")


def main():
    """主函数"""
    print("🔍 Python模块导入验证")
    print("=" * 60)
    
    validator = ImportValidator()
    
    # 核心模块验证
    print("\n📋 核心模块验证")
    print("-" * 30)
    
    core_modules = [
        ("src.infrastructure.config", "配置模块"),
        ("src.infrastructure.config.settings", "设置模块"),
        ("src.infrastructure.config.logging", "日志模块"),
        ("src.infrastructure.esi", "ESI客户端"),
        ("src.infrastructure.persistence.database", "数据库模块"),
    ]
    
    for module, desc in core_modules:
        validator.validate_import(module, desc)
    
    # 应用层验证
    print("\n📋 应用层验证")
    print("-" * 30)
    
    app_modules = [
        ("src.application.auth", "认证服务"),
        ("src.application.character", "角色服务"),
        ("src.application.sync", "同步服务"),
    ]
    
    for module, desc in app_modules:
        validator.validate_import(module, desc)
    
    # API层验证
    print("\n📋 API层验证")
    print("-" * 30)
    
    api_modules = [
        ("src.presentation.api.dependencies", "依赖注入"),
        ("src.presentation.api.routers.auth", "认证路由"),
        ("src.presentation.api.routers.characters", "角色路由"),
        ("src.presentation.api.routers.sync", "同步路由"),
        ("src.presentation.api.routers.monitoring", "监控路由"),
        ("src.presentation.api.routers.health", "健康检查路由"),
    ]
    
    for module, desc in api_modules:
        validator.validate_import(module, desc)
    
    # 关键函数验证
    print("\n📋 关键函数验证")
    print("-" * 30)
    
    function_tests = [
        ("src.presentation.api.dependencies", "get_current_user", "获取当前用户函数"),
        ("src.presentation.api.dependencies", "require_admin", "管理员权限检查函数"),
        ("src.presentation.api.dependencies", "get_database_session", "数据库会话函数"),
        ("src.infrastructure.monitoring.error_monitor", "error_monitor", "错误监控实例"),
        ("src.infrastructure.monitoring.error_monitor", "report_error", "错误报告函数"),
    ]
    
    for module, func, desc in function_tests:
        validator.validate_function_import(module, func, desc)
    
    # FastAPI应用验证
    print("\n📋 FastAPI应用验证")
    print("-" * 30)
    
    try:
        from src.presentation.api.main import app
        validator.results["passed"].append("FastAPI应用")
        print("✅ FastAPI应用导入成功")
        
        # 检查路由注册
        routes = [str(route.path) for route in app.routes]
        expected_routes = ["/health", "/auth", "/characters", "/sync", "/monitoring"]
        
        for expected in expected_routes:
            if any(expected in route for route in routes):
                validator.results["passed"].append(f"路由 {expected}")
                print(f"✅ 路由 {expected} 已注册")
            else:
                validator.results["failed"].append(f"路由 {expected} 未注册")
                print(f"❌ 路由 {expected} 未注册")
                
    except Exception as e:
        validator.results["failed"].append(f"FastAPI应用: {str(e)}")
        print(f"❌ FastAPI应用: {str(e)}")
        
        # 提供调试信息
        print(f"\n🔍 调试信息:")
        print(f"错误类型: {type(e).__name__}")
        print(f"错误详情: {str(e)}")
        
        if "cannot import name" in str(e):
            # 分析导入错误
            import re
            match = re.search(r"cannot import name '(\w+)' from '([^']+)'", str(e))
            if match:
                missing_func, module_path = match.groups()
                print(f"\n💡 建议检查:")
                print(f"   模块: {module_path}")
                print(f"   缺失函数: {missing_func}")
                
                # 检查模块内容
                module_info = validator.inspect_module(module_path)
                if module_info["exists"]:
                    print(f"   模块存在，可用函数:")
                    for func in module_info["functions"][:10]:  # 只显示前10个
                        print(f"     - {func}")
                    
                    # 建议替代函数
                    suggestions = validator.suggest_alternatives(module_path, missing_func)
                    if suggestions:
                        print(f"   建议的替代函数:")
                        for suggestion in suggestions:
                            print(f"     - {suggestion}")
    
    # 打印结果摘要
    validator.print_summary()
    
    # 返回退出码
    if validator.results["failed"]:
        print(f"\n❌ 导入验证失败，请修复上述问题后重试")
        return 1
    else:
        print(f"\n🎉 所有导入验证通过！")
        return 0


if __name__ == "__main__":
    sys.exit(main())
