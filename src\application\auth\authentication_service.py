"""
认证应用服务
"""
import hashlib
import secrets
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

from ...domain.auth.aggregates import UserAuthentication
from ...domain.auth.value_objects import Scope
from ...domain.auth.exceptions import (
    UserAlreadyExistsError, InvalidCredentialsError, AccountLockedError,
    AccountInactiveError, EmailNotVerifiedError
)
from ...domain.shared.value_objects import UserId, CharacterId
from ...infrastructure.auth.jwt_service import jwt_service
from ...infrastructure.esi.auth_enhanced import EnhancedEVESSOClient
from ...infrastructure.config.logging import get_logger

logger = get_logger(__name__)


class AuthenticationApplicationService:
    """认证应用服务"""
    
    def __init__(self,
                 user_auth_repository,  # UserAuthenticationRepository
                 sso_client: EnhancedEVESSOClient,
                 unit_of_work,  # UnitOfWork
                 event_bus):   # EventBus
        self._user_auth_repository = user_auth_repository
        self._sso_client = sso_client
        self._unit_of_work = unit_of_work
        self._event_bus = event_bus
    
    async def register_user(self, email: str, username: str, password: str) -> Dict[str, Any]:
        """用户注册"""
        async with self._unit_of_work:
            # 检查邮箱是否已存在
            existing_user = await self._user_auth_repository.find_by_email(email)
            if existing_user:
                raise UserAlreadyExistsError(email, "email")
            
            # 检查用户名是否已存在
            existing_user = await self._user_auth_repository.find_by_username(username)
            if existing_user:
                raise UserAlreadyExistsError(username, "username")
            
            # 生成用户ID和密码哈希
            user_id = UserId.generate()
            password_hash = self._hash_password(password)
            
            # 创建用户认证聚合
            user_auth = UserAuthentication.create(
                user_id=user_id,
                email=email,
                username=username,
                password_hash=password_hash
            )
            
            # 保存用户
            await self._user_auth_repository.save(user_auth)
            
            # 发布领域事件
            events = user_auth.get_domain_events()
            for event in events:
                await self._event_bus.publish(event)
            
            logger.info(
                "用户注册成功",
                user_id=user_id.value,
                email=email,
                username=username
            )
            
            return {
                "success": True,
                "user_id": user_id.value,
                "email": email,
                "username": username,
                "message": "User registered successfully. Please verify your email."
            }
    
    async def login_user(self, email: str, password: str, 
                        remember_me: bool = False) -> Dict[str, Any]:
        """用户登录"""
        async with self._unit_of_work:
            # 查找用户
            user_auth = await self._user_auth_repository.find_by_email(email)
            if not user_auth:
                raise InvalidCredentialsError("Invalid email or password")
            
            # 检查账户状态
            if user_auth.is_locked():
                raise AccountLockedError(user_auth.user_id, 
                                       user_auth.locked_until.isoformat())
            
            if not user_auth.is_active:
                raise AccountInactiveError(user_auth.user_id)
            
            # 验证密码
            password_hash = self._hash_password(password)
            if not user_auth.authenticate(password_hash):
                await self._user_auth_repository.save(user_auth)
                raise InvalidCredentialsError("Invalid email or password")
            
            # 创建会话
            session_expires = timedelta(days=30) if remember_me else timedelta(hours=24)
            session = user_auth.create_session(expires_in=session_expires)
            
            # 生成JWT令牌
            access_token = jwt_service.generate_access_token(
                user_id=user_auth.user_id,
                character_id=user_auth.main_character_id or CharacterId(0),
                scopes=[]
            )
            
            refresh_token = jwt_service.generate_refresh_token(
                user_id=user_auth.user_id,
                character_id=user_auth.main_character_id or CharacterId(0),
                access_token_jti=jwt_service.decode_token_without_verification(access_token)["jti"]
            )
            
            # 保存用户状态
            await self._user_auth_repository.save(user_auth)
            
            # 发布领域事件
            events = user_auth.get_domain_events()
            for event in events:
                await self._event_bus.publish(event)
            
            logger.info(
                "用户登录成功",
                user_id=user_auth.user_id.value,
                email=email,
                remember_me=remember_me
            )
            
            return {
                "success": True,
                "access_token": access_token,
                "refresh_token": refresh_token,
                "token_type": "Bearer",
                "expires_in": int(jwt_service.access_token_expire.total_seconds()),
                "session_token": session.token,
                "user": {
                    "user_id": user_auth.user_id.value,
                    "email": user_auth.email,
                    "username": user_auth.username,
                    "main_character_id": user_auth.main_character_id.value if user_auth.main_character_id else None,
                    "character_count": len(user_auth.character_bindings)
                }
            }
    
    async def logout_user(self, user_id: UserId, session_token: str = None,
                         all_sessions: bool = False) -> Dict[str, Any]:
        """用户登出"""
        async with self._unit_of_work:
            user_auth = await self._user_auth_repository.get_by_id(user_id)
            if not user_auth:
                return {"success": False, "message": "User not found"}
            
            if all_sessions:
                user_auth.logout_all_sessions()
            elif session_token:
                # 查找并失效特定会话
                session = user_auth.verify_session(session_token)
                if session:
                    user_auth.invalidate_session(session.token_id)
            
            await self._user_auth_repository.save(user_auth)
            
            # 发布领域事件
            events = user_auth.get_domain_events()
            for event in events:
                await self._event_bus.publish(event)
            
            logger.info(
                "用户登出",
                user_id=user_id.value,
                all_sessions=all_sessions
            )
            
            return {
                "success": True,
                "message": "Logged out successfully"
            }
    
    async def initiate_eve_login(self, user_id: UserId, 
                                scopes: List[str]) -> Dict[str, Any]:
        """发起EVE SSO登录"""
        async with self._unit_of_work:
            user_auth = await self._user_auth_repository.get_by_id(user_id)
            if not user_auth:
                raise InvalidCredentialsError("User not found")
            
            # 转换权限范围
            scope_objects = [Scope.from_string(scope) for scope in scopes]
            
            # 创建认证请求
            auth_request = user_auth.create_auth_request(scope_objects)
            
            # 发起SSO认证
            sso_result = await self._sso_client.initiate_authentication(
                scopes=scopes,
                redirect_url=None
            )
            
            # 保存用户状态
            await self._user_auth_repository.save(user_auth)
            
            logger.info(
                "EVE SSO登录发起",
                user_id=user_id.value,
                state=auth_request.state,
                scopes=scopes
            )
            
            return {
                "success": True,
                "login_url": sso_result["login_url"],
                "state": auth_request.state,
                "expires_in": 600,
                "scopes": scopes
            }
    
    async def complete_eve_login(self, user_id: UserId, authorization_code: str,
                                state: str) -> Dict[str, Any]:
        """完成EVE SSO登录"""
        async with self._unit_of_work:
            user_auth = await self._user_auth_repository.get_by_id(user_id)
            if not user_auth:
                raise InvalidCredentialsError("User not found")
            
            # 完成SSO认证
            sso_result = await self._sso_client.complete_authentication(
                authorization_code, state
            )
            
            # 处理角色绑定
            character_id = CharacterId(sso_result["character_id"])
            
            # 这里需要从SSO结果中提取令牌信息
            # 实际实现中需要根据SSO客户端的返回格式调整
            
            logger.info(
                "EVE SSO登录完成",
                user_id=user_id.value,
                character_id=character_id.value,
                character_name=sso_result.get("character_name")
            )
            
            return {
                "success": True,
                "character_id": character_id.value,
                "character_name": sso_result.get("character_name"),
                "scopes": sso_result.get("scopes", [])
            }
    
    async def refresh_token(self, refresh_token: str) -> Dict[str, Any]:
        """刷新访问令牌"""
        try:
            result = jwt_service.refresh_access_token(refresh_token)
            
            logger.info("令牌刷新成功")
            
            return {
                "success": True,
                **result
            }
            
        except Exception as e:
            logger.error("令牌刷新失败", error=str(e))
            raise InvalidCredentialsError("Invalid refresh token")
    
    async def verify_session(self, session_token: str) -> Dict[str, Any]:
        """验证会话"""
        # 这里可以添加会话验证逻辑
        # 实际实现中可能需要查询数据库
        
        try:
            # 解码会话令牌（如果使用JWT作为会话令牌）
            payload = jwt_service.verify_token(session_token)
            
            return {
                "valid": True,
                "user_id": payload["user_id"],
                "character_id": payload.get("character_id"),
                "expires_at": payload["exp"]
            }
            
        except Exception as e:
            logger.debug("会话验证失败", error=str(e))
            return {"valid": False}
    
    def _hash_password(self, password: str) -> str:
        """哈希密码"""
        # 使用更安全的密码哈希算法
        salt = secrets.token_hex(32)
        password_hash = hashlib.pbkdf2_hmac('sha256', 
                                          password.encode('utf-8'), 
                                          salt.encode('utf-8'), 
                                          100000)
        return f"{salt}:{password_hash.hex()}"
    
    def _verify_password(self, password: str, password_hash: str) -> bool:
        """验证密码"""
        try:
            salt, stored_hash = password_hash.split(':')
            password_hash_check = hashlib.pbkdf2_hmac('sha256',
                                                    password.encode('utf-8'),
                                                    salt.encode('utf-8'),
                                                    100000)
            return stored_hash == password_hash_check.hex()
        except ValueError:
            return False
