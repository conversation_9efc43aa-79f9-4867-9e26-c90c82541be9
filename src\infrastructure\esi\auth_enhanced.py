"""
增强的EVE SSO认证服务
包含高级安全特性和性能优化
"""
import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Set
from dataclasses import dataclass, field
from enum import Enum

import httpx
from jose import jwt, JWTError

from .auth import EVESSOClient
from ..config import settings
from ..config.logging import get_logger
from ...domain.shared.exceptions import (
    ESIAuthenticationError, ESIAuthorizationError, TokenExpiredError, TokenRefreshError
)

logger = get_logger(__name__)


class AuthState(Enum):
    """认证状态枚举"""
    PENDING = "pending"
    COMPLETED = "completed"
    EXPIRED = "expired"
    FAILED = "failed"


@dataclass
class AuthenticationState:
    """认证状态数据"""
    state: str
    scopes: List[str]
    code_verifier: Optional[str] = None
    redirect_url: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.utcnow)
    expires_at: datetime = field(default_factory=lambda: datetime.utcnow() + timedelta(minutes=10))
    status: AuthState = AuthState.PENDING
    attempts: int = 0
    max_attempts: int = 3
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        return datetime.utcnow() > self.expires_at
    
    def is_valid(self) -> bool:
        """检查是否有效"""
        return (self.status == AuthState.PENDING and 
                not self.is_expired() and 
                self.attempts < self.max_attempts)
    
    def increment_attempts(self) -> None:
        """增加尝试次数"""
        self.attempts += 1
        if self.attempts >= self.max_attempts:
            self.status = AuthState.FAILED


@dataclass
class TokenInfo:
    """令牌信息"""
    access_token: str
    refresh_token: str
    token_type: str
    expires_at: datetime
    character_id: int
    character_name: str
    scopes: List[str]
    
    def is_expired(self) -> bool:
        """检查访问令牌是否过期"""
        # 提前5分钟认为过期，确保有足够时间刷新
        buffer = timedelta(minutes=5)
        return datetime.utcnow() >= (self.expires_at - buffer)
    
    def expires_in_seconds(self) -> int:
        """获取剩余有效时间（秒）"""
        delta = self.expires_at - datetime.utcnow()
        return max(0, int(delta.total_seconds()))


class EnhancedEVESSOClient(EVESSOClient):
    """增强的EVE SSO客户端"""
    
    def __init__(self):
        super().__init__()
        
        # 认证状态管理
        self._auth_states: Dict[str, AuthenticationState] = {}
        self._state_cleanup_interval = 300  # 5分钟清理一次过期状态
        self._last_cleanup = datetime.utcnow()
        
        # 令牌缓存
        self._token_cache: Dict[int, TokenInfo] = {}
        
        # 安全配置
        self._max_concurrent_auths = 100
        self._rate_limit_window = timedelta(minutes=1)
        self._rate_limit_requests = 60
        self._request_timestamps: List[datetime] = []
        
        # 监控指标
        self._metrics = {
            "auth_attempts": 0,
            "auth_successes": 0,
            "auth_failures": 0,
            "token_refreshes": 0,
            "token_refresh_failures": 0
        }
    
    async def initiate_authentication(self, 
                                    scopes: List[str],
                                    redirect_url: Optional[str] = None) -> Dict[str, Any]:
        """发起认证流程"""
        await self._cleanup_expired_states()
        await self._check_rate_limit()
        
        # 检查并发认证数量
        active_auths = sum(1 for state in self._auth_states.values() 
                          if state.status == AuthState.PENDING)
        
        if active_auths >= self._max_concurrent_auths:
            raise ESIAuthenticationError("Too many concurrent authentication attempts")
        
        try:
            if self._enable_pkce:
                login_url, state, code_verifier = self.generate_login_url_with_pkce(scopes)
            else:
                login_url, state = self.generate_login_url(scopes)
                code_verifier = None
            
            # 存储认证状态
            auth_state = AuthenticationState(
                state=state,
                scopes=scopes,
                code_verifier=code_verifier,
                redirect_url=redirect_url
            )
            
            self._auth_states[state] = auth_state
            self._metrics["auth_attempts"] += 1
            
            logger.info(
                "认证流程已发起",
                state=state,
                scopes=scopes,
                pkce_enabled=self._enable_pkce,
                redirect_url=redirect_url
            )
            
            return {
                "login_url": login_url,
                "state": state,
                "expires_in": 600,  # 10分钟
                "pkce_enabled": self._enable_pkce
            }
            
        except Exception as e:
            self._metrics["auth_failures"] += 1
            logger.error("发起认证流程失败", error=str(e))
            raise
    
    async def complete_authentication(self, 
                                    authorization_code: str,
                                    state: str) -> Dict[str, Any]:
        """完成认证流程"""
        await self._cleanup_expired_states()
        
        # 验证状态
        auth_state = self._auth_states.get(state)
        if not auth_state:
            self._metrics["auth_failures"] += 1
            raise ESIAuthenticationError("Invalid authentication state")
        
        if not auth_state.is_valid():
            auth_state.status = AuthState.FAILED
            self._metrics["auth_failures"] += 1
            raise ESIAuthenticationError("Authentication state expired or invalid")
        
        try:
            # 增加尝试次数
            auth_state.increment_attempts()
            
            # 交换令牌
            token_data = await self.exchange_code_for_tokens(
                authorization_code, 
                state, 
                auth_state.code_verifier
            )
            
            # 创建令牌信息
            token_info = TokenInfo(
                access_token=token_data["access_token"],
                refresh_token=token_data["refresh_token"],
                token_type=token_data["token_type"],
                expires_at=token_data["expires_at"],
                character_id=int(token_data["character_id"]),
                character_name=token_data["character_name"],
                scopes=token_data["scopes"]
            )
            
            # 缓存令牌
            self._token_cache[token_info.character_id] = token_info
            
            # 标记认证完成
            auth_state.status = AuthState.COMPLETED
            self._metrics["auth_successes"] += 1
            
            logger.info(
                "认证流程完成",
                character_id=token_info.character_id,
                character_name=token_info.character_name,
                scopes=token_info.scopes
            )
            
            return {
                "success": True,
                "character_id": token_info.character_id,
                "character_name": token_info.character_name,
                "scopes": token_info.scopes,
                "expires_at": token_info.expires_at.isoformat(),
                "redirect_url": auth_state.redirect_url
            }
            
        except Exception as e:
            auth_state.status = AuthState.FAILED
            self._metrics["auth_failures"] += 1
            logger.error(
                "完成认证流程失败",
                state=state,
                error=str(e)
            )
            raise
        finally:
            # 清理认证状态
            if state in self._auth_states:
                del self._auth_states[state]
    
    async def refresh_token_if_needed(self, character_id: int) -> Optional[TokenInfo]:
        """如果需要则刷新令牌"""
        token_info = self._token_cache.get(character_id)
        if not token_info:
            return None
        
        if not token_info.is_expired():
            return token_info
        
        try:
            self._metrics["token_refreshes"] += 1
            
            # 刷新令牌
            refreshed_data = await self.refresh_access_token(token_info.refresh_token)
            
            # 更新令牌信息
            updated_token_info = TokenInfo(
                access_token=refreshed_data["access_token"],
                refresh_token=refreshed_data["refresh_token"],
                token_type=refreshed_data["token_type"],
                expires_at=refreshed_data["expires_at"],
                character_id=character_id,
                character_name=refreshed_data["character_name"],
                scopes=refreshed_data["scopes"]
            )
            
            # 更新缓存
            self._token_cache[character_id] = updated_token_info
            
            logger.info(
                "令牌刷新成功",
                character_id=character_id,
                character_name=updated_token_info.character_name
            )
            
            return updated_token_info
            
        except Exception as e:
            self._metrics["token_refresh_failures"] += 1
            logger.error(
                "令牌刷新失败",
                character_id=character_id,
                error=str(e)
            )
            # 从缓存中移除失效的令牌
            if character_id in self._token_cache:
                del self._token_cache[character_id]
            raise
    
    async def get_valid_token(self, character_id: int) -> Optional[str]:
        """获取有效的访问令牌"""
        token_info = await self.refresh_token_if_needed(character_id)
        return token_info.access_token if token_info else None
    
    async def _cleanup_expired_states(self) -> None:
        """清理过期的认证状态"""
        now = datetime.utcnow()
        
        if now - self._last_cleanup < timedelta(seconds=self._state_cleanup_interval):
            return
        
        expired_states = [
            state for state, auth_state in self._auth_states.items()
            if auth_state.is_expired()
        ]
        
        for state in expired_states:
            del self._auth_states[state]
        
        self._last_cleanup = now
        
        if expired_states:
            logger.debug(f"清理了 {len(expired_states)} 个过期的认证状态")
    
    async def _check_rate_limit(self) -> None:
        """检查速率限制"""
        now = datetime.utcnow()
        
        # 清理过期的请求时间戳
        cutoff = now - self._rate_limit_window
        self._request_timestamps = [
            ts for ts in self._request_timestamps if ts > cutoff
        ]
        
        # 检查是否超过速率限制
        if len(self._request_timestamps) >= self._rate_limit_requests:
            raise ESIAuthenticationError("Rate limit exceeded")
        
        # 记录当前请求
        self._request_timestamps.append(now)
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取监控指标"""
        return {
            **self._metrics,
            "active_auth_states": len(self._auth_states),
            "cached_tokens": len(self._token_cache),
            "auth_success_rate": (
                self._metrics["auth_successes"] / max(1, self._metrics["auth_attempts"])
            ),
            "token_refresh_success_rate": (
                (self._metrics["token_refreshes"] - self._metrics["token_refresh_failures"]) /
                max(1, self._metrics["token_refreshes"])
            )
        }
    
    async def cleanup(self) -> None:
        """清理资源"""
        await self._cleanup_expired_states()
        self._token_cache.clear()
        await self.close()
