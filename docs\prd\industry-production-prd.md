# 工业生产模块 - 产品需求文档 (PRD)

## 📋 模块概述

### 模块名称
工业生产模块 (Industry Production Module)

### 模块愿景
为EVE Online工业玩家提供全面的生产管理和优化工具，通过智能化的生产规划、成本分析和利润优化，帮助用户建立高效的工业帝国。

### 业务价值
- 🎯 **生产优化**: 智能生产规划和资源配置优化
- 🎯 **成本控制**: 精确的成本计算和利润分析
- 🎯 **效率提升**: 自动化生产流程和任务管理
- 🎯 **决策支持**: 基于数据的生产决策建议

## 🎯 功能需求

### 1. 工业任务管理

#### 1.1 任务概览
**功能描述**: 全面管理和监控工业生产任务

**核心功能**:
- ✅ 活跃任务实时状态展示
- ✅ 任务进度跟踪和预估完成时间
- ✅ 任务历史记录和统计
- ✅ 批量任务操作
- ✅ 任务性能分析

**数据模型**:
```python
class IndustryJob(Entity):
    job_id: JobId
    installer_id: CharacterId
    facility_id: FacilityId
    activity_id: ActivityId
    blueprint_id: BlueprintId
    blueprint_type_id: TypeId
    blueprint_location_id: LocationId
    output_location_id: LocationId
    runs: int
    cost: Money
    licensed_runs: Optional[int]
    probability: Optional[float]
    product_type_id: Optional[TypeId]
    status: JobStatus
    duration: int
    start_date: datetime
    end_date: datetime
    pause_date: Optional[datetime]
    completed_date: Optional[datetime]
```

#### 1.2 任务调度
**功能描述**: 智能任务调度和资源分配

**核心功能**:
- 🚀 生产线负载均衡
- 🚀 任务优先级管理
- 🚀 资源冲突检测
- 🚀 最优时间安排
- 🚀 自动任务排队

### 2. 蓝图管理

#### 2.1 蓝图库存
**功能描述**: 管理蓝图资产和研究状态

**核心功能**:
- ✅ 蓝图清单和分类管理
- ✅ 蓝图研究等级跟踪
- ✅ 蓝图效率和材料等级
- ✅ 蓝图价值评估
- ✅ 蓝图使用历史

**数据模型**:
```python
class Blueprint(Entity):
    item_id: ItemId
    type_id: TypeId
    location_id: LocationId
    material_efficiency: int
    time_efficiency: int
    quantity: int
    runs: int
    is_copy: bool
    max_runs: Optional[int]
    estimated_value: Money
```

#### 2.2 蓝图研究规划
**功能描述**: 优化蓝图研究投资决策

**核心功能**:
- 🚀 研究投资回报分析
- 🚀 最优研究等级建议
- 🚀 研究时间成本计算
- 🚀 研究优先级排序
- 🚀 批量研究规划

### 3. 生产规划

#### 3.1 生产计划制定
**功能描述**: 制定智能化的生产计划

**核心功能**:
- 🚀 需求驱动的生产规划
- 🚀 多层级BOM展开
- 🚀 原料需求计算
- 🚀 生产时间优化
- 🚀 产能约束考虑

**数据模型**:
```python
class ProductionPlan(Entity):
    plan_id: PlanId
    creator_id: CharacterId
    name: str
    target_products: List[ProductTarget]
    material_requirements: List[MaterialRequirement]
    estimated_cost: Money
    estimated_duration: timedelta
    profit_margin: float
    status: PlanStatus
    created_date: datetime
```

#### 3.2 供应链管理
**功能描述**: 管理复杂的供应链关系

**核心功能**:
- 🚀 供应商管理
- 🚀 采购计划优化
- 🚀 库存水平控制
- 🚀 供应风险评估
- 🚀 供应链可视化

### 4. 成本分析

#### 4.1 生产成本计算
**功能描述**: 精确计算生产成本

**核心功能**:
- ✅ 原料成本计算
- ✅ 制造费用分摊
- ✅ 时间成本评估
- ✅ 税费成本计算
- ✅ 机会成本分析

**数据模型**:
```python
class ProductionCost(ValueObject):
    product_type_id: TypeId
    material_cost: Money
    manufacturing_cost: Money
    facility_cost: Money
    tax_cost: Money
    time_cost: Money
    total_cost: Money
    cost_per_unit: Money
```

#### 4.2 盈利能力分析
**功能描述**: 分析生产项目的盈利能力

**核心功能**:
- 🚀 利润率计算
- 🚀 投资回报率分析
- 🚀 盈亏平衡点分析
- 🚀 敏感性分析
- 🚀 风险调整收益

### 5. 市场集成

#### 5.1 价格监控
**功能描述**: 监控原料和产品价格变化

**核心功能**:
- ✅ 实时价格更新
- ✅ 价格趋势分析
- ✅ 价格预警设置
- ✅ 最优采购时机
- ✅ 销售时机建议

#### 5.2 竞争分析
**功能描述**: 分析市场竞争环境

**核心功能**:
- 🚀 竞争对手识别
- 🚀 市场份额分析
- 🚀 价格竞争力评估
- 🚀 产品差异化建议
- 🚀 市场进入策略

### 6. 设施管理

#### 6.1 生产设施
**功能描述**: 管理工业生产设施

**核心功能**:
- ✅ 设施状态监控
- ✅ 设施效率分析
- ✅ 设施利用率统计
- ✅ 设施升级建议
- ✅ 设施成本分析

**数据模型**:
```python
class IndustryFacility(Entity):
    facility_id: FacilityId
    owner_id: CharacterId
    system_id: SystemId
    type_id: TypeId
    name: str
    tax_rate: float
    bonuses: List[FacilityBonus]
    available_activities: List[ActivityId]
    status: FacilityStatus
```

#### 6.2 设施优化
**功能描述**: 优化设施配置和使用

**核心功能**:
- 🚀 设施选择建议
- 🚀 生产线配置优化
- 🚀 设施投资分析
- 🚀 区位选择建议
- 🚀 设施协同效应

## 🔧 技术实现

### 领域模型设计

#### 聚合根: IndustryOperation
```python
class IndustryOperation(AggregateRoot):
    def __init__(self, operator_id: CharacterId):
        super().__init__(operator_id)
        self._jobs: Dict[JobId, IndustryJob] = {}
        self._blueprints: Dict[BlueprintId, Blueprint] = {}
        self._facilities: Dict[FacilityId, IndustryFacility] = {}
    
    def start_production_job(self, job: IndustryJob) -> None:
        """开始生产任务"""
        self._validate_job_requirements(job)
        self._jobs[job.id] = job
        self._raise_event(ProductionJobStartedEvent(self.id, job))
    
    def complete_production_job(self, job_id: JobId) -> None:
        """完成生产任务"""
        if job_id in self._jobs:
            job = self._jobs[job_id]
            job.complete()
            self._raise_event(ProductionJobCompletedEvent(self.id, job))
    
    def calculate_production_cost(self, blueprint_id: BlueprintId, runs: int) -> ProductionCost:
        """计算生产成本"""
        blueprint = self._blueprints[blueprint_id]
        return self._cost_calculator.calculate(blueprint, runs)
```

#### 领域服务
```python
class ProductionPlanningService(DomainService):
    def create_production_plan(
        self, 
        target_products: List[ProductTarget], 
        constraints: ProductionConstraints
    ) -> ProductionPlan:
        """创建生产计划"""
        
    def optimize_production_schedule(
        self, 
        jobs: List[IndustryJob], 
        facilities: List[IndustryFacility]
    ) -> ProductionSchedule:
        """优化生产调度"""

class CostCalculationService(DomainService):
    def calculate_material_cost(
        self, 
        blueprint: Blueprint, 
        runs: int, 
        material_efficiency: int
    ) -> Money:
        """计算材料成本"""
        
    def calculate_total_production_cost(
        self, 
        blueprint: Blueprint, 
        facility: IndustryFacility, 
        runs: int
    ) -> ProductionCost:
        """计算总生产成本"""
```

### 应用服务

#### IndustryApplicationService
```python
class IndustryApplicationService:
    async def get_industry_overview(
        self, 
        character_id: CharacterId
    ) -> IndustryOverviewDTO:
        """获取工业概览"""
        
    async def create_production_plan(
        self, 
        character_id: CharacterId, 
        plan_request: ProductionPlanRequestDTO
    ) -> ProductionPlanDTO:
        """创建生产计划"""
        
    async def analyze_blueprint_profitability(
        self, 
        blueprint_id: BlueprintId, 
        market_conditions: MarketConditionsDTO
    ) -> ProfitabilityAnalysisDTO:
        """分析蓝图盈利能力"""
        
    async def optimize_facility_usage(
        self, 
        character_id: CharacterId
    ) -> FacilityOptimizationDTO:
        """优化设施使用"""
```

### 数据同步策略

#### 实时数据 (每5分钟)
- 活跃工业任务状态
- 设施可用性状态

#### 频繁数据 (每30分钟)
- 蓝图研究进度
- 材料价格更新

#### 常规数据 (每2小时)
- 工业任务历史
- 设施税率更新

#### 分析数据 (每日)
- 成本效益分析
- 市场趋势分析

## 📊 用户界面设计

### 1. 工业概览页面
```
┌─────────────────────────────────────────────────────────────┐
│ 工业概览                                                     │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│ │ 活跃任务         │ │ 月产值           │ │ 平均利润率       │ │
│ │ 15 个           │ │ 2.5B ISK        │ │ 18.5%           │ │
│ │ 3个即将完成      │ │ ↑ +12% (30d)    │ │ ↑ +2.1% (30d)   │ │
│ └─────────────────┘ └─────────────────┘ └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 生产线状态                                                   │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 制造线 1  ████████████████████████████████████ 运行中    │ │
│ │ 制造线 2  ████████████████████████████████████ 运行中    │ │
│ │ 研究线 1  ████████████████████░░░░░░░░░░░░░░░░ 研究中    │ │
│ │ 研究线 2  ░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░ 空闲      │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2. 生产任务页面
```
┌─────────────────────────────────────────────────────────────┐
│ 生产任务                                                     │
├─────────────────────────────────────────────────────────────┤
│ [活跃任务] [已完成] [计划中] [新建任务]                      │
├─────────────────────────────────────────────────────────────┤
│ 产品名称           数量  进度      剩余时间    成本      利润 │
│ 🟢 Raven Navy Issue  1   ████████  2h 15m    425M ISK  25M  │
│ 🟡 Large Shield Ext. 50  ████░░░░  8h 32m    125M ISK  15M  │
│ 🔴 Tritanium         1M  ██░░░░░░  1d 4h     2.1M ISK  0.3M │
├─────────────────────────────────────────────────────────────┤
│ 任务详情: Raven Navy Issue                                   │
│ 蓝图: Raven Navy Issue Blueprint (ME: 10, TE: 20)          │
│ 设施: Jita IV - Moon 4 - Caldari Navy Assembly Plant       │
│ 材料消耗: Tritanium 15.2M, Pyerite 3.8M, Mexallon 0.9M    │
│ [暂停任务] [取消任务] [查看详情] [复制任务]                  │
└─────────────────────────────────────────────────────────────┘
```

### 3. 生产规划页面
```
┌─────────────────────────────────────────────────────────────┐
│ 生产规划器                                                   │
├─────────────────────────────────────────────────────────────┤
│ 目标产品: [选择产品▼] 数量: [100] 交付日期: [2025-01-15]    │
├─────────────────────────────────────────────────────────────┤
│ 材料需求分析                                                 │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 材料名称          需求量      库存      缺口      成本    │ │
│ │ Tritanium         1.52B      800M      720M     3.7M ISK │ │
│ │ Pyerite           380M       200M      180M     2.1M ISK │ │
│ │ Mexallon          95M        50M       45M      4.5M ISK │ │
│ │ Isogen            19M        25M       0        0 ISK    │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 生产计划                                                     │
│ 总成本: 425M ISK | 预计利润: 25M ISK | 利润率: 5.9%        │
│ 生产时间: 15天 8小时 | 所需生产线: 2条                      │
│ [优化计划] [保存计划] [开始生产] [导出BOM]                   │
└─────────────────────────────────────────────────────────────┘
```

## 🔍 测试策略

### 单元测试
- 成本计算算法测试
- 生产规划逻辑测试
- BOM展开算法测试

### 集成测试
- ESI工业数据同步测试
- 市场价格集成测试
- 任务状态更新测试

### 性能测试
- 复杂生产计划计算性能测试
- 大量任务管理性能测试
- 实时数据更新性能测试

## 📈 成功指标

### 功能指标
- 成本计算准确率 > 99.5%
- 生产计划优化效果 > 15%
- 任务状态同步延迟 < 1分钟

### 用户体验指标
- 生产效率提升 > 30%
- 利润率提升 > 20%
- 用户满意度评分 > 4.6/5.0

---

**文档版本**: v1.0  
**最后更新**: 2025-01-09  
**负责人**: 工业生产团队
