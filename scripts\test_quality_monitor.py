#!/usr/bin/env python3
"""
测试质量监控脚本
"""
import json
import sys
import subprocess
import argparse
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any


class TestQualityMonitor:
    """测试质量监控器"""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.reports_dir = self.project_root / "reports"
        self.reports_dir.mkdir(exist_ok=True)
    
    def run_command(self, cmd: str) -> subprocess.CompletedProcess:
        """执行命令"""
        return subprocess.run(
            cmd, shell=True, capture_output=True, text=True, check=False
        )
    
    def check_module_imports(self) -> Dict[str, Any]:
        """检查模块导入状态"""
        print("🔍 检查模块导入状态...")
        
        critical_modules = [
            "src.presentation.api.main",
            "src.application.services.auth", 
            "src.infrastructure.persistence.repositories.character",
            "src.infrastructure.persistence.repositories",
            "src.presentation.api.dependencies",
        ]
        
        results = {
            "timestamp": datetime.now().isoformat(),
            "total_modules": len(critical_modules),
            "successful_imports": 0,
            "failed_imports": [],
            "status": "unknown"
        }
        
        for module in critical_modules:
            try:
                __import__(module)
                results["successful_imports"] += 1
                print(f"✅ {module}")
            except Exception as e:
                results["failed_imports"].append({
                    "module": module,
                    "error": str(e)
                })
                print(f"❌ {module}: {e}")
        
        success_rate = results["successful_imports"] / results["total_modules"]
        if success_rate == 1.0:
            results["status"] = "excellent"
        elif success_rate >= 0.8:
            results["status"] = "good"
        elif success_rate >= 0.6:
            results["status"] = "warning"
        else:
            results["status"] = "critical"
        
        return results
    
    def run_test_suite(self) -> Dict[str, Any]:
        """运行测试套件"""
        print("🧪 运行测试套件...")
        
        # 运行pytest并生成JSON报告
        result = self.run_command(
            "pytest --json-report --json-report-file=test-results.json --tb=short"
        )
        
        test_results = {
            "timestamp": datetime.now().isoformat(),
            "exit_code": result.returncode,
            "status": "passed" if result.returncode == 0 else "failed"
        }
        
        # 读取详细结果
        try:
            with open("test-results.json") as f:
                detailed_results = json.load(f)
            
            summary = detailed_results.get("summary", {})
            test_results.update({
                "total_tests": summary.get("total", 0),
                "passed": summary.get("passed", 0),
                "failed": summary.get("failed", 0),
                "skipped": summary.get("skipped", 0),
                "errors": summary.get("error", 0),
                "duration": detailed_results.get("duration", 0)
            })
            
        except FileNotFoundError:
            print("⚠️ 测试结果文件未找到")
        
        return test_results
    
    def check_test_coverage(self) -> Dict[str, Any]:
        """检查测试覆盖率"""
        print("📊 检查测试覆盖率...")
        
        result = self.run_command(
            "pytest --cov=src --cov-report=json --cov-report=term-missing"
        )
        
        coverage_results = {
            "timestamp": datetime.now().isoformat(),
            "status": "unknown"
        }
        
        try:
            with open("coverage.json") as f:
                coverage_data = json.load(f)
            
            total_coverage = coverage_data["totals"]["percent_covered"]
            coverage_results.update({
                "total_coverage": total_coverage,
                "covered_lines": coverage_data["totals"]["covered_lines"],
                "missing_lines": coverage_data["totals"]["missing_lines"],
                "total_lines": coverage_data["totals"]["num_statements"],
                "files_covered": len(coverage_data["files"])
            })
            
            # 评估覆盖率状态
            if total_coverage >= 90:
                coverage_results["status"] = "excellent"
            elif total_coverage >= 80:
                coverage_results["status"] = "good"
            elif total_coverage >= 70:
                coverage_results["status"] = "warning"
            else:
                coverage_results["status"] = "critical"
                
        except FileNotFoundError:
            print("⚠️ 覆盖率数据文件未找到")
        
        return coverage_results
    
    def analyze_test_trends(self) -> Dict[str, Any]:
        """分析测试趋势"""
        print("📈 分析测试趋势...")
        
        # 读取历史数据
        history_file = self.reports_dir / "test_history.json"
        history = []
        
        if history_file.exists():
            try:
                with open(history_file) as f:
                    history = json.load(f)
            except Exception as e:
                print(f"⚠️ 读取历史数据失败: {e}")
        
        # 分析趋势
        trends = {
            "timestamp": datetime.now().isoformat(),
            "history_entries": len(history),
            "trends": {}
        }
        
        if len(history) >= 2:
            latest = history[-1]
            previous = history[-2]
            
            # 覆盖率趋势
            if "coverage" in latest and "coverage" in previous:
                coverage_change = (
                    latest["coverage"]["total_coverage"] - 
                    previous["coverage"]["total_coverage"]
                )
                trends["trends"]["coverage_change"] = coverage_change
            
            # 测试数量趋势
            if "tests" in latest and "tests" in previous:
                test_count_change = (
                    latest["tests"]["total_tests"] - 
                    previous["tests"]["total_tests"]
                )
                trends["trends"]["test_count_change"] = test_count_change
        
        return trends
    
    def generate_quality_report(self) -> Dict[str, Any]:
        """生成质量报告"""
        print("📋 生成质量报告...")
        
        # 收集所有数据
        import_results = self.check_module_imports()
        test_results = self.run_test_suite()
        coverage_results = self.check_test_coverage()
        trend_analysis = self.analyze_test_trends()
        
        # 综合评估
        overall_status = "unknown"
        status_scores = {
            "excellent": 4,
            "good": 3,
            "warning": 2,
            "critical": 1,
            "unknown": 0
        }
        
        statuses = [
            import_results.get("status", "unknown"),
            test_results.get("status", "unknown"),
            coverage_results.get("status", "unknown")
        ]
        
        avg_score = sum(status_scores.get(s, 0) for s in statuses) / len(statuses)
        
        if avg_score >= 3.5:
            overall_status = "excellent"
        elif avg_score >= 2.5:
            overall_status = "good"
        elif avg_score >= 1.5:
            overall_status = "warning"
        else:
            overall_status = "critical"
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": overall_status,
            "overall_score": avg_score,
            "imports": import_results,
            "tests": test_results,
            "coverage": coverage_results,
            "trends": trend_analysis
        }
        
        # 保存报告
        report_file = self.reports_dir / f"quality_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"📄 质量报告已保存: {report_file}")
        
        # 更新历史记录
        self.update_history(report)
        
        return report
    
    def update_history(self, report: Dict[str, Any]):
        """更新历史记录"""
        history_file = self.reports_dir / "test_history.json"
        history = []
        
        if history_file.exists():
            try:
                with open(history_file) as f:
                    history = json.load(f)
            except Exception:
                history = []
        
        # 添加新记录
        history.append(report)
        
        # 保留最近30天的记录
        cutoff_date = datetime.now() - timedelta(days=30)
        history = [
            entry for entry in history
            if datetime.fromisoformat(entry["timestamp"]) > cutoff_date
        ]
        
        # 保存历史记录
        with open(history_file, 'w') as f:
            json.dump(history, f, indent=2)
    
    def print_summary(self, report: Dict[str, Any]):
        """打印摘要"""
        print("\n" + "="*60)
        print("📊 测试质量监控摘要")
        print("="*60)
        
        status_emoji = {
            "excellent": "🟢",
            "good": "🟡", 
            "warning": "🟠",
            "critical": "🔴",
            "unknown": "⚪"
        }
        
        overall_status = report["overall_status"]
        print(f"总体状态: {status_emoji[overall_status]} {overall_status.upper()}")
        print(f"综合评分: {report['overall_score']:.2f}/4.0")
        
        print(f"\n📦 模块导入: {status_emoji[report['imports']['status']]} {report['imports']['successful_imports']}/{report['imports']['total_modules']}")
        
        if report['tests'].get('total_tests'):
            test_status = report['tests']['status']
            print(f"🧪 测试结果: {status_emoji[test_status]} {report['tests']['passed']}/{report['tests']['total_tests']}")
        
        if report['coverage'].get('total_coverage'):
            coverage_status = report['coverage']['status']
            print(f"📊 测试覆盖率: {status_emoji[coverage_status]} {report['coverage']['total_coverage']:.1f}%")
        
        print("="*60)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="测试质量监控")
    parser.add_argument("--imports-only", action="store_true", help="只检查模块导入")
    parser.add_argument("--tests-only", action="store_true", help="只运行测试")
    parser.add_argument("--coverage-only", action="store_true", help="只检查覆盖率")
    parser.add_argument("--trends-only", action="store_true", help="只分析趋势")
    
    args = parser.parse_args()
    
    monitor = TestQualityMonitor()
    
    if args.imports_only:
        result = monitor.check_module_imports()
        print(json.dumps(result, indent=2))
    elif args.tests_only:
        result = monitor.run_test_suite()
        print(json.dumps(result, indent=2))
    elif args.coverage_only:
        result = monitor.check_test_coverage()
        print(json.dumps(result, indent=2))
    elif args.trends_only:
        result = monitor.analyze_test_trends()
        print(json.dumps(result, indent=2))
    else:
        # 生成完整报告
        report = monitor.generate_quality_report()
        monitor.print_summary(report)
        
        # 根据状态设置退出码
        if report["overall_status"] in ["critical", "unknown"]:
            return 1
        elif report["overall_status"] == "warning":
            return 2
        else:
            return 0


if __name__ == "__main__":
    sys.exit(main())
