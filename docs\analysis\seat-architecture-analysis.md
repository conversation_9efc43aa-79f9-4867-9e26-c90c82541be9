# SeAT架构分析：基于ESI Scopes的功能设计

## 🌀 SeAT (Simple EVE API Tool) 架构深度分析

### 📋 项目概述
SeAT是EVE Online生态中最成熟的开源企业级管理工具，采用Laravel框架构建，为大型公司和联盟提供全面的管理功能。

## 🏗️ 核心架构设计原理

### 1. Scope驱动的模块化架构

#### 🎯 基本设计理念
```
ESI Scope → Data Service → Business Logic → Web Interface → Menu System
```

#### 📊 Scope到功能模块的映射

| Scope分类 | 对应模块 | 主要功能 | 数据表 |
|-----------|----------|----------|--------|
| **角色基础** | Character Module | 角色信息管理 | `character_infos`, `character_roles` |
| **位置追踪** | Location Module | 实时位置监控 | `character_locations`, `character_online` |
| **技能系统** | Skills Module | 技能分析 | `character_skills`, `character_skill_queue` |
| **资产管理** | Assets Module | 资产追踪 | `character_assets`, `corporation_assets` |
| **财务管理** | Wallet Module | 钱包分析 | `character_wallets`, `wallet_transactions` |
| **邮件系统** | Mail Module | 通讯管理 | `character_mails`, `mail_recipients` |
| **工业系统** | Industry Module | 生产管理 | `character_industry_jobs`, `corporation_industry_jobs` |
| **市场系统** | Market Module | 交易分析 | `character_orders`, `market_history` |
| **公司管理** | Corporation Module | 企业管理 | `corporation_infos`, `corporation_members` |
| **联盟管理** | Alliance Module | 联盟关系 | `alliance_infos`, `alliance_contacts` |

### 2. 数据库架构设计

#### 🗄️ 核心表结构设计原则

```sql
-- 1. 角色基础信息表
CREATE TABLE character_infos (
    character_id BIGINT PRIMARY KEY,
    name VARCHAR(255),
    corporation_id BIGINT,
    alliance_id BIGINT,
    -- 基于 esi-characters.* scopes
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

-- 2. Scope权限管理表
CREATE TABLE refresh_tokens (
    character_id BIGINT,
    refresh_token TEXT,
    scopes TEXT, -- 存储该token的所有scopes
    expires_on DATETIME,
    -- 动态权限管理
);

-- 3. 功能模块数据表
-- 基于 esi-location.* scopes
CREATE TABLE character_locations (
    character_id BIGINT,
    solar_system_id BIGINT,
    structure_id BIGINT,
    updated_at TIMESTAMP
);

-- 基于 esi-skills.* scopes  
CREATE TABLE character_skills (
    character_id BIGINT,
    skill_id INT,
    trained_skill_level INT,
    skillpoints_in_skill BIGINT,
    updated_at TIMESTAMP
);
```

### 3. 服务层架构

#### 🔧 Service类设计模式

```php
// SeAT的Service层设计
namespace Seat\Eveapi\Services;

class CharacterService {
    // 基于scope的数据获取
    public function getCharacterInfo($character_id) {
        // 检查是否有相应的scope权限
        if (!$this->hasScope('esi-characters.read_corporation_roles.v1')) {
            return null;
        }
        
        // 调用ESI API
        return $this->esiClient->invoke('get', '/characters/{character_id}/', [
            'character_id' => $character_id
        ]);
    }
    
    // 权限检查机制
    private function hasScope($required_scope) {
        $token = $this->getRefreshToken();
        return in_array($required_scope, explode(' ', $token->scopes));
    }
}
```

### 4. 菜单系统设计

#### 🎮 动态菜单生成

```php
// SeAT的菜单配置系统
class MenuProvider {
    public function getMenuItems() {
        $menu = [];
        
        // 基于用户权限动态生成菜单
        if ($this->hasAnyScope(['esi-characters.*'])) {
            $menu['character'] = [
                'title' => 'Character',
                'icon' => 'fa-user',
                'children' => $this->getCharacterSubMenu()
            ];
        }
        
        if ($this->hasAnyScope(['esi-corporations.*'])) {
            $menu['corporation'] = [
                'title' => 'Corporation', 
                'icon' => 'fa-building',
                'children' => $this->getCorporationSubMenu()
            ];
        }
        
        return $menu;
    }
    
    private function getCharacterSubMenu() {
        $submenu = [];
        
        if ($this->hasScope('esi-location.read_location.v1')) {
            $submenu[] = ['title' => 'Location', 'route' => 'character.location'];
        }
        
        if ($this->hasScope('esi-skills.read_skills.v1')) {
            $submenu[] = ['title' => 'Skills', 'route' => 'character.skills'];
        }
        
        if ($this->hasScope('esi-assets.read_assets.v1')) {
            $submenu[] = ['title' => 'Assets', 'route' => 'character.assets'];
        }
        
        return $submenu;
    }
}
```

### 5. 权限控制系统

#### 🔐 中间件权限验证

```php
// SeAT的权限中间件
class ScopeMiddleware {
    public function handle($request, Closure $next, ...$required_scopes) {
        $user_scopes = $this->getUserScopes();
        
        foreach ($required_scopes as $scope) {
            if (!in_array($scope, $user_scopes)) {
                return response()->json(['error' => 'Insufficient permissions'], 403);
            }
        }
        
        return $next($request);
    }
}

// 路由中的使用
Route::get('/character/skills', 'SkillsController@index')
     ->middleware('scope:esi-skills.read_skills.v1');
```

## 🎯 具体功能模块实现

### 1. 角色管理模块

#### 📊 基于Scope的功能分级

```php
class CharacterController {
    // 基础信息 - 无需特殊scope
    public function basicInfo($character_id) {
        return view('character.basic', [
            'character' => Character::find($character_id)
        ]);
    }
    
    // 详细信息 - 需要特定scope
    public function detailedInfo($character_id) {
        $data = [];
        
        if ($this->hasScope('esi-characters.read_corporation_roles.v1')) {
            $data['roles'] = $this->getRoles($character_id);
        }
        
        if ($this->hasScope('esi-characters.read_standings.v1')) {
            $data['standings'] = $this->getStandings($character_id);
        }
        
        return view('character.detailed', $data);
    }
}
```

### 2. 公司管理模块

#### 🏢 企业级功能设计

```php
class CorporationController {
    public function dashboard($corporation_id) {
        $widgets = [];
        
        // 基于scope动态加载组件
        if ($this->hasScope('esi-corporations.read_corporation_membership.v1')) {
            $widgets[] = $this->getMembershipWidget($corporation_id);
        }
        
        if ($this->hasScope('esi-wallet.read_corporation_wallets.v1')) {
            $widgets[] = $this->getWalletWidget($corporation_id);
        }
        
        if ($this->hasScope('esi-assets.read_corporation_assets.v1')) {
            $widgets[] = $this->getAssetsWidget($corporation_id);
        }
        
        return view('corporation.dashboard', compact('widgets'));
    }
}
```

### 3. 数据同步系统

#### 🔄 基于Scope的任务调度

```php
class DataSyncScheduler {
    public function scheduleJobs() {
        $characters = $this->getActiveCharacters();
        
        foreach ($characters as $character) {
            $scopes = $this->getCharacterScopes($character->id);
            
            // 基于scope调度不同的同步任务
            if (in_array('esi-location.read_location.v1', $scopes)) {
                dispatch(new SyncCharacterLocation($character->id));
            }
            
            if (in_array('esi-skills.read_skills.v1', $scopes)) {
                dispatch(new SyncCharacterSkills($character->id));
            }
            
            if (in_array('esi-assets.read_assets.v1', $scopes)) {
                dispatch(new SyncCharacterAssets($character->id));
            }
        }
    }
}
```

## 🎨 前端界面设计

### 1. 响应式组件系统

#### 🖥️ 基于权限的UI组件

```blade
{{-- SeAT的Blade模板设计 --}}
@extends('web::layouts.grids.12')

@section('title', 'Character Overview')

@section('content')
<div class="row">
    {{-- 基础信息卡片 - 始终显示 --}}
    <div class="col-md-4">
        @include('web::character.partials.basic-info')
    </div>
    
    {{-- 位置信息 - 需要location scope --}}
    @can('scope', 'esi-location.read_location.v1')
    <div class="col-md-4">
        @include('web::character.partials.location')
    </div>
    @endcan
    
    {{-- 技能信息 - 需要skills scope --}}
    @can('scope', 'esi-skills.read_skills.v1')
    <div class="col-md-4">
        @include('web::character.partials.skills-summary')
    </div>
    @endcan
</div>

{{-- 详细数据表格 --}}
<div class="row">
    @can('scope', 'esi-assets.read_assets.v1')
    <div class="col-md-12">
        @include('web::character.partials.assets-table')
    </div>
    @endcan
</div>
@endsection
```

### 2. 数据表格系统

#### 📊 DataTables集成

```javascript
// SeAT的前端数据表格
$(document).ready(function() {
    // 基于权限动态配置表格列
    var columns = [
        { data: 'name', title: 'Name' },
        { data: 'type', title: 'Type' }
    ];
    
    // 如果有位置权限，添加位置列
    if (hasScope('esi-location.read_location.v1')) {
        columns.push({ data: 'location', title: 'Location' });
    }
    
    // 如果有资产权限，添加数量列
    if (hasScope('esi-assets.read_assets.v1')) {
        columns.push({ data: 'quantity', title: 'Quantity' });
    }
    
    $('#assets-table').DataTable({
        ajax: '/character/assets/data',
        columns: columns,
        processing: true,
        serverSide: true
    });
});
```

## 🔧 配置管理系统

### 1. Scope配置管理

#### ⚙️ 动态Scope配置

```php
// SeAT的配置管理
class ScopeConfiguration {
    // 默认scope配置
    protected $defaultScopes = [
        'character' => [
            'esi-location.read_location.v1',
            'esi-location.read_online.v1',
            'esi-skills.read_skills.v1',
            'esi-assets.read_assets.v1',
            'esi-wallet.read_character_wallet.v1'
        ],
        'corporation' => [
            'esi-corporations.read_corporation_membership.v1',
            'esi-corporations.read_structures.v1',
            'esi-assets.read_corporation_assets.v1',
            'esi-wallet.read_corporation_wallets.v1'
        ]
    ];
    
    // 获取推荐scope配置
    public function getRecommendedScopes($type = 'character') {
        return $this->defaultScopes[$type] ?? [];
    }
    
    // 验证scope有效性
    public function validateScopes($scopes) {
        $validScopes = $this->getValidScopes();
        return array_intersect($scopes, $validScopes);
    }
}
```

## 📈 性能优化策略

### 1. 缓存系统

#### 🚀 基于Scope的缓存策略

```php
class CacheManager {
    // 不同scope数据的缓存时间
    protected $cacheTTL = [
        'esi-location.read_location.v1' => 60,      // 1分钟
        'esi-skills.read_skills.v1' => 3600,       // 1小时
        'esi-assets.read_assets.v1' => 1800,       // 30分钟
        'esi-wallet.read_character_wallet.v1' => 300, // 5分钟
    ];
    
    public function getCacheKey($scope, $character_id) {
        return "seat::{$scope}::{$character_id}";
    }
    
    public function getCacheTTL($scope) {
        return $this->cacheTTL[$scope] ?? 3600;
    }
}
```

## 🎯 最佳实践总结

### 1. 架构设计原则
- **权限驱动**: 所有功能基于ESI scope权限设计
- **模块化**: 每个scope对应独立的功能模块
- **动态加载**: 基于用户权限动态生成界面
- **缓存优化**: 不同数据类型采用不同缓存策略

### 2. 开发建议
- **渐进式权限**: 从基础权限开始，逐步扩展
- **用户体验**: 清晰的权限说明和授权流程
- **错误处理**: 优雅处理权限不足的情况
- **性能监控**: 监控API调用频率和响应时间

这就是SeAT项目基于ESI scopes设计功能菜单和数据结构的完整架构分析。
