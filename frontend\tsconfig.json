{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": [
      "ES2020",
      "DOM",
      "DOM.Iterable"
    ],
    "module": "ESNext",
    "skipLibCheck": true,
    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    /* Path mapping */
    "baseUrl": ".",
    "paths": {
      "@/*": [
        "./src/*"
      ],
      "@components/*": [
        "./src/components/*"
      ],
      "@pages/*": [
        "./src/pages/*"
      ],
      "@services/*": [
        "./src/services/*"
      ],
      "@hooks/*": [
        "./src/hooks/*"
      ],
      "@stores/*": [
        "./src/stores/*"
      ],
      "@utils/*": [
        "./src/utils/*"
      ],
      "@types/*": [
        "./src/types/*"
      ],
      "@constants/*": [
        "./src/constants/*"
      ],
      "@assets/*": [
        "./src/assets/*"
      ]
    },
    /* Additional options */
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true
  },
  "include": [
    "src"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "build"
  ],
  "references": [
    {
      "path": "./tsconfig.node.json"
    }
  ]
}