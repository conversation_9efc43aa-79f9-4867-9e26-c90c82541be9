"""
Alembic 环境配置
"""
import asyncio
from logging.config import fileConfig
from typing import Any

from alembic import context
from sqlalchemy import engine_from_config, pool
from sqlalchemy.engine import Connection
from sqlalchemy.ext.asyncio import AsyncEngine

# 导入模型以确保它们被注册
from src.infrastructure.persistence.database import Base
from src.infrastructure.config import settings

# 导入所有模型以确保它们被注册到Base.metadata
from src.infrastructure.persistence.models import (
    CharacterModel, SkillModel, SkillQueueModel, AttributesModel,
    AssetModel, MarketOrderModel, MarketHistoryModel, IndustryJobModel,
    CorporationModel, AllianceModel, UniverseTypeModel, UniverseSystemModel,
    UserModel, TokenModel
)

# 这是 Alembic Config 对象，它提供了对正在使用的 .ini 文件中的值的访问。
config = context.config

# 解释用于 Python 日志记录的配置文件。
# 这行设置了从配置文件中配置记录器。
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# 为 'autogenerate' 支持添加您的模型的 MetaData 对象
target_metadata = Base.metadata

# 其他从 myapp 导入的值
# target_metadata = mymodel.Base.metadata


def get_url():
    """获取数据库URL"""
    return settings.database_url


def run_migrations_offline() -> None:
    """在 'offline' 模式下运行迁移。

    这将配置上下文，只使用 URL 而不是 Engine，
    尽管这里也需要一个 Engine，但我们不创建连接。
    通过跳过 Engine 创建，我们甚至不需要 DBAPI 可用。

    调用 context.execute() 将发出给定的字符串到脚本输出。
    """
    url = get_url()
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def do_run_migrations(connection: Connection) -> None:
    """运行迁移"""
    context.configure(connection=connection, target_metadata=target_metadata)

    with context.begin_transaction():
        context.run_migrations()


async def run_async_migrations() -> None:
    """在异步模式下运行迁移"""
    configuration = config.get_section(config.config_ini_section)
    configuration["sqlalchemy.url"] = get_url()

    connectable = AsyncEngine(
        engine_from_config(
            configuration,
            prefix="sqlalchemy.",
            poolclass=pool.NullPool,
        )
    )

    async with connectable.connect() as connection:
        await connection.run_sync(do_run_migrations)

    await connectable.dispose()


def run_migrations_online() -> None:
    """在 'online' 模式下运行迁移。

    在这种情况下，我们需要创建一个 Engine 并将连接与上下文关联。
    """
    # 检查是否是异步数据库
    database_url = get_url()
    
    if "asyncpg" in database_url or "aiomysql" in database_url or "aiosqlite" in database_url:
        # 异步数据库
        asyncio.run(run_async_migrations())
    else:
        # 同步数据库
        configuration = config.get_section(config.config_ini_section)
        configuration["sqlalchemy.url"] = database_url
        
        connectable = engine_from_config(
            configuration,
            prefix="sqlalchemy.",
            poolclass=pool.NullPool,
        )

        with connectable.connect() as connection:
            do_run_migrations(connection)


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
