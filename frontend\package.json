{"name": "eve-assistant-frontend", "version": "1.0.0", "description": "EVE Online管理助手前端应用", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "antd": "^5.12.8", "tailwindcss": "^3.3.6", "@ant-design/icons": "^5.2.6", "@tanstack/react-query": "^5.8.4", "@tanstack/react-query-devtools": "^5.8.4", "zustand": "^4.4.7", "axios": "^1.6.2", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "framer-motion": "^10.16.5", "react-helmet-async": "^1.3.0", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "date-fns": "^2.30.0", "clsx": "^2.0.0", "react-hot-toast": "^2.4.1", "react-window": "^1.8.8", "react-window-infinite-loader": "^1.0.9"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/react-window": "^1.8.8", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "typescript": "^5.2.2", "vite": "^5.0.0", "vitest": "^0.34.6", "@vitest/ui": "^0.34.6", "@vitest/coverage-v8": "^0.34.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "@storybook/addon-essentials": "^7.5.3", "@storybook/addon-interactions": "^7.5.3", "@storybook/addon-links": "^7.5.3", "@storybook/blocks": "^7.5.3", "@storybook/react": "^7.5.3", "@storybook/react-vite": "^7.5.3", "@storybook/testing-library": "^0.2.2", "storybook": "^7.5.3", "husky": "^8.0.3", "lint-staged": "^15.1.0", "prettier": "^3.1.0"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.10.5"}