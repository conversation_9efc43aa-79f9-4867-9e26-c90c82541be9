"""
角色领域单元测试
"""
import pytest
from datetime import datetime, timedelta
from decimal import Decimal

from src.domain.shared.value_objects import CharacterId, CorporationId, Money, SecurityStatus
from src.domain.shared.exceptions import DomainValidationError
from src.domain.character.entities import Character
from src.domain.character.value_objects import Attributes, Skill, SkillQueue
from src.domain.character.events import CharacterCreatedEvent


class TestCharacterId:
    """测试角色ID值对象"""
    
    def test_valid_character_id(self):
        """测试有效的角色ID"""
        char_id = CharacterId(123456789)
        assert char_id.value == 123456789
        assert str(char_id) == "123456789"
    
    def test_invalid_character_id(self):
        """测试无效的角色ID"""
        with pytest.raises(DomainValidationError):
            CharacterId(0)
        
        with pytest.raises(DomainValidationError):
            CharacterId(-1)


class TestMoney:
    """测试货币值对象"""
    
    def test_valid_money(self):
        """测试有效的货币"""
        money = Money(Decimal("1000.50"))
        assert money.amount == Decimal("1000.50")
        assert money.currency == "ISK"
        assert str(money) == "1,000.50 ISK"
    
    def test_money_operations(self):
        """测试货币运算"""
        money1 = Money(Decimal("1000"))
        money2 = Money(Decimal("500"))
        
        # 加法
        result = money1.add(money2)
        assert result.amount == Decimal("1500")
        
        # 减法
        result = money1.subtract(money2)
        assert result.amount == Decimal("500")
        
        # 乘法
        result = money1.multiply(Decimal("2"))
        assert result.amount == Decimal("2000")
    
    def test_invalid_money(self):
        """测试无效的货币"""
        with pytest.raises(DomainValidationError):
            Money(Decimal("-100"))


class TestAttributes:
    """测试角色属性值对象"""
    
    def test_valid_attributes(self):
        """测试有效的属性"""
        attrs = Attributes(
            charisma=25,
            intelligence=30,
            memory=28,
            perception=22,
            willpower=26
        )
        assert attrs.charisma == 25
        assert attrs.get_total_attributes() == 131
        assert attrs.get_learning_attributes() == 58
    
    def test_invalid_attributes(self):
        """测试无效的属性"""
        with pytest.raises(DomainValidationError):
            Attributes(charisma=0)  # 太低
        
        with pytest.raises(DomainValidationError):
            Attributes(intelligence=51)  # 太高


class TestSkill:
    """测试技能值对象"""
    
    def test_valid_skill(self):
        """测试有效的技能"""
        from src.domain.shared.value_objects import SkillPoints
        
        skill = Skill(
            skill_id=3300,  # Gunnery
            skillpoints_in_skill=SkillPoints(256000),
            trained_skill_level=4,
            active_skill_level=4
        )
        assert skill.skill_id == 3300
        assert skill.trained_skill_level == 4
        assert skill.active_skill_level == 4
    
    def test_invalid_skill(self):
        """测试无效的技能"""
        from src.domain.shared.value_objects import SkillPoints
        
        with pytest.raises(DomainValidationError):
            Skill(
                skill_id=0,  # 无效ID
                skillpoints_in_skill=SkillPoints(1000),
                trained_skill_level=1,
                active_skill_level=1
            )
        
        with pytest.raises(DomainValidationError):
            Skill(
                skill_id=3300,
                skillpoints_in_skill=SkillPoints(1000),
                trained_skill_level=6,  # 超过最大等级
                active_skill_level=1
            )


class TestCharacter:
    """测试角色聚合根"""
    
    def test_create_character(self):
        """测试创建角色"""
        char_id = CharacterId(123456789)
        corp_id = CorporationId(987654321)
        birthday = datetime(2015, 3, 24, 11, 37, 0)
        
        character = Character(
            character_id=char_id,
            name="Test Character",
            corporation_id=corp_id,
            birthday=birthday,
            race_id=1,
            bloodline_id=3,
            gender="male"
        )
        
        assert character.character_id == char_id
        assert character.name == "Test Character"
        assert character.corporation_id == corp_id
        assert character.birthday == birthday
        assert character.race_id == 1
        assert character.bloodline_id == 3
        assert character.gender == "male"
        
        # 检查是否生成了创建事件
        events = character.domain_events
        assert len(events) == 1
        assert isinstance(events[0], CharacterCreatedEvent)
    
    def test_invalid_character_creation(self):
        """测试无效的角色创建"""
        char_id = CharacterId(123456789)
        corp_id = CorporationId(987654321)
        birthday = datetime(2015, 3, 24, 11, 37, 0)
        
        # 空名称
        with pytest.raises(DomainValidationError):
            Character(
                character_id=char_id,
                name="",
                corporation_id=corp_id,
                birthday=birthday,
                race_id=1,
                bloodline_id=3,
                gender="male"
            )
        
        # 无效性别
        with pytest.raises(DomainValidationError):
            Character(
                character_id=char_id,
                name="Test Character",
                corporation_id=corp_id,
                birthday=birthday,
                race_id=1,
                bloodline_id=3,
                gender="unknown"
            )
        
        # 未来生日
        future_birthday = datetime.utcnow() + timedelta(days=1)
        with pytest.raises(DomainValidationError):
            Character(
                character_id=char_id,
                name="Test Character",
                corporation_id=corp_id,
                birthday=future_birthday,
                race_id=1,
                bloodline_id=3,
                gender="male"
            )
    
    def test_update_character_info(self):
        """测试更新角色信息"""
        char_id = CharacterId(123456789)
        corp_id = CorporationId(987654321)
        new_corp_id = CorporationId(111222333)
        birthday = datetime(2015, 3, 24, 11, 37, 0)
        
        character = Character(
            character_id=char_id,
            name="Test Character",
            corporation_id=corp_id,
            birthday=birthday,
            race_id=1,
            bloodline_id=3,
            gender="male"
        )
        
        # 清除创建事件
        character.clear_domain_events()
        
        # 更新公司
        character.update_basic_info(corporation_id=new_corp_id)
        
        assert character.corporation_id == new_corp_id
        
        # 检查是否生成了更新事件
        events = character.domain_events
        assert len(events) == 1
        from src.domain.character.events import CharacterUpdatedEvent
        assert isinstance(events[0], CharacterUpdatedEvent)
    
    def test_character_wallet_operations(self):
        """测试角色钱包操作"""
        char_id = CharacterId(123456789)
        corp_id = CorporationId(987654321)
        birthday = datetime(2015, 3, 24, 11, 37, 0)
        
        character = Character(
            character_id=char_id,
            name="Test Character",
            corporation_id=corp_id,
            birthday=birthday,
            race_id=1,
            bloodline_id=3,
            gender="male"
        )
        
        # 初始余额应该为0
        assert character.wallet_balance.is_zero()
        
        # 更新余额
        new_balance = Money(Decimal("1000000"))
        character.update_wallet_balance(new_balance)
        
        assert character.wallet_balance.amount == Decimal("1000000")
    
    def test_character_skill_operations(self):
        """测试角色技能操作"""
        from src.domain.shared.value_objects import SkillPoints
        
        char_id = CharacterId(123456789)
        corp_id = CorporationId(987654321)
        birthday = datetime(2015, 3, 24, 11, 37, 0)
        
        character = Character(
            character_id=char_id,
            name="Test Character",
            corporation_id=corp_id,
            birthday=birthday,
            race_id=1,
            bloodline_id=3,
            gender="male"
        )
        
        # 添加技能
        skill = Skill(
            skill_id=3300,
            skillpoints_in_skill=SkillPoints(256000),
            trained_skill_level=4,
            active_skill_level=4
        )
        character.update_skill(skill)
        
        # 检查技能
        assert character.has_skill(3300, 4)
        assert not character.has_skill(3300, 5)
        
        retrieved_skill = character.get_skill_by_id(3300)
        assert retrieved_skill is not None
        assert retrieved_skill.trained_skill_level == 4
