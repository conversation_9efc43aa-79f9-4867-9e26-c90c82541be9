"""
审计中间件
"""
import time
import json
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from datetime import datetime
from uuid import uuid4

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from ..config.logging import get_logger

logger = get_logger(__name__)


@dataclass
class AuditEvent:
    """审计事件"""
    event_id: str
    timestamp: datetime
    user_id: Optional[str]
    session_id: Optional[str]
    ip_address: str
    user_agent: str
    method: str
    path: str
    query_params: Dict[str, Any]
    request_headers: Dict[str, str]
    response_status: int
    response_headers: Dict[str, str]
    processing_time: float
    request_size: int
    response_size: int
    event_type: str
    resource_type: Optional[str] = None
    resource_id: Optional[str] = None
    action_type: Optional[str] = None
    success: bool = True
    error_message: Optional[str] = None
    additional_data: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data


class AuditMiddleware(BaseHTTPMiddleware):
    """审计中间件"""
    
    def __init__(self, app,
                 enable_request_logging: bool = True,
                 enable_response_logging: bool = True,
                 log_request_body: bool = False,
                 log_response_body: bool = False,
                 sensitive_headers: Optional[List[str]] = None,
                 excluded_paths: Optional[List[str]] = None,
                 max_body_size: int = 1024 * 1024):  # 1MB
        super().__init__(app)
        
        self.enable_request_logging = enable_request_logging
        self.enable_response_logging = enable_response_logging
        self.log_request_body = log_request_body
        self.log_response_body = log_response_body
        self.max_body_size = max_body_size
        
        # 敏感头部（不记录）
        self.sensitive_headers = set(header.lower() for header in (sensitive_headers or [
            "authorization", "cookie", "x-api-key", "x-auth-token",
            "x-csrf-token", "x-session-token"
        ]))
        
        # 排除的路径
        self.excluded_paths = set(excluded_paths or [
            "/health", "/metrics", "/favicon.ico", "/robots.txt",
            "/api/health", "/api/metrics"
        ])
        
        # 审计事件存储（生产环境应使用数据库或消息队列）
        self.audit_events: List[AuditEvent] = []
        self.max_events_in_memory = 10000
    
    async def dispatch(self, request: Request, call_next):
        # 检查是否需要审计
        if request.url.path in self.excluded_paths:
            return await call_next(request)
        
        start_time = time.time()
        event_id = str(uuid4())
        
        # 收集请求信息
        request_info = await self._collect_request_info(request, event_id)
        
        # 处理请求
        try:
            response = await call_next(request)
            success = True
            error_message = None
        except Exception as e:
            # 创建错误响应
            response = Response(
                content=f"Internal Server Error: {str(e)}",
                status_code=500
            )
            success = False
            error_message = str(e)
        
        # 计算处理时间
        processing_time = time.time() - start_time
        
        # 收集响应信息
        response_info = self._collect_response_info(response)
        
        # 创建审计事件
        audit_event = AuditEvent(
            event_id=event_id,
            timestamp=datetime.utcnow(),
            user_id=request_info.get("user_id"),
            session_id=request_info.get("session_id"),
            ip_address=request_info["ip_address"],
            user_agent=request_info["user_agent"],
            method=request.method,
            path=request.url.path,
            query_params=dict(request.query_params),
            request_headers=request_info["headers"],
            response_status=response.status_code,
            response_headers=response_info["headers"],
            processing_time=processing_time,
            request_size=request_info["size"],
            response_size=response_info["size"],
            event_type=self._determine_event_type(request),
            resource_type=self._extract_resource_type(request),
            resource_id=self._extract_resource_id(request),
            action_type=self._determine_action_type(request),
            success=success,
            error_message=error_message,
            additional_data=request_info.get("additional_data")
        )
        
        # 记录审计事件
        await self._record_audit_event(audit_event)
        
        # 添加审计头部
        response.headers["X-Audit-Event-ID"] = event_id
        response.headers["X-Processing-Time"] = f"{processing_time:.3f}s"
        
        return response
    
    async def _collect_request_info(self, request: Request, event_id: str) -> Dict[str, Any]:
        """收集请求信息"""
        # 获取客户端IP
        ip_address = self._get_client_ip(request)
        
        # 获取用户代理
        user_agent = request.headers.get("user-agent", "")
        
        # 过滤敏感头部
        headers = {}
        for key, value in request.headers.items():
            if key.lower() not in self.sensitive_headers:
                headers[key] = value
            else:
                headers[key] = "[REDACTED]"
        
        # 获取用户信息
        user_id = getattr(request.state, "user_id", None)
        session_id = getattr(request.state, "session_id", None)
        
        # 计算请求大小
        request_size = 0
        if hasattr(request, "body"):
            try:
                body = await request.body()
                request_size = len(body)
            except:
                pass
        
        # 收集额外数据
        additional_data = {}
        if self.log_request_body and request_size > 0 and request_size <= self.max_body_size:
            try:
                body = await request.body()
                if body:
                    # 尝试解析JSON
                    try:
                        additional_data["request_body"] = json.loads(body.decode())
                    except:
                        additional_data["request_body"] = body.decode()[:1000]  # 限制长度
            except:
                pass
        
        return {
            "ip_address": ip_address,
            "user_agent": user_agent,
            "headers": headers,
            "user_id": str(user_id) if user_id else None,
            "session_id": session_id,
            "size": request_size,
            "additional_data": additional_data if additional_data else None
        }
    
    def _collect_response_info(self, response: Response) -> Dict[str, Any]:
        """收集响应信息"""
        # 过滤敏感头部
        headers = {}
        for key, value in response.headers.items():
            if key.lower() not in self.sensitive_headers:
                headers[key] = value
            else:
                headers[key] = "[REDACTED]"
        
        # 计算响应大小
        response_size = 0
        if hasattr(response, "body"):
            try:
                response_size = len(response.body)
            except:
                pass
        
        return {
            "headers": headers,
            "size": response_size
        }
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP"""
        # 检查代理头部
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # 使用客户端IP
        return request.client.host if request.client else "unknown"
    
    def _determine_event_type(self, request: Request) -> str:
        """确定事件类型"""
        path = request.url.path.lower()
        method = request.method.upper()
        
        if "/auth/" in path:
            if "login" in path:
                return "authentication.login"
            elif "logout" in path:
                return "authentication.logout"
            elif "register" in path:
                return "authentication.register"
            else:
                return "authentication.other"
        
        if "/api/" in path:
            if method == "GET":
                return "api.read"
            elif method == "POST":
                return "api.create"
            elif method == "PUT" or method == "PATCH":
                return "api.update"
            elif method == "DELETE":
                return "api.delete"
            else:
                return "api.other"
        
        return "web.request"
    
    def _extract_resource_type(self, request: Request) -> Optional[str]:
        """提取资源类型"""
        path_parts = request.url.path.strip("/").split("/")
        
        if len(path_parts) >= 2 and path_parts[0] == "api":
            return path_parts[1]
        
        return None
    
    def _extract_resource_id(self, request: Request) -> Optional[str]:
        """提取资源ID"""
        # 从路径参数中提取ID
        if hasattr(request, "path_params"):
            for key, value in request.path_params.items():
                if key.endswith("_id") or key == "id":
                    return str(value)
        
        return None
    
    def _determine_action_type(self, request: Request) -> Optional[str]:
        """确定操作类型"""
        method = request.method.upper()
        
        action_mapping = {
            "GET": "read",
            "POST": "create",
            "PUT": "update",
            "PATCH": "update",
            "DELETE": "delete"
        }
        
        return action_mapping.get(method)
    
    async def _record_audit_event(self, event: AuditEvent) -> None:
        """记录审计事件"""
        # 记录到日志
        logger.info(
            "审计事件",
            event_id=event.event_id,
            user_id=event.user_id,
            method=event.method,
            path=event.path,
            status=event.response_status,
            processing_time=event.processing_time,
            success=event.success,
            error=event.error_message
        )
        
        # 存储到内存（生产环境应使用持久化存储）
        self.audit_events.append(event)
        
        # 限制内存中的事件数量
        if len(self.audit_events) > self.max_events_in_memory:
            self.audit_events = self.audit_events[-self.max_events_in_memory:]
        
        # 这里可以添加其他存储逻辑：
        # - 发送到消息队列
        # - 存储到数据库
        # - 发送到日志聚合系统
    
    def get_audit_events(self, limit: int = 100, 
                        user_id: Optional[str] = None,
                        event_type: Optional[str] = None,
                        start_time: Optional[datetime] = None,
                        end_time: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """获取审计事件"""
        events = self.audit_events
        
        # 过滤条件
        if user_id:
            events = [e for e in events if e.user_id == user_id]
        
        if event_type:
            events = [e for e in events if e.event_type == event_type]
        
        if start_time:
            events = [e for e in events if e.timestamp >= start_time]
        
        if end_time:
            events = [e for e in events if e.timestamp <= end_time]
        
        # 按时间倒序排列
        events.sort(key=lambda x: x.timestamp, reverse=True)
        
        # 限制数量
        events = events[:limit]
        
        return [event.to_dict() for event in events]
    
    def get_audit_statistics(self) -> Dict[str, Any]:
        """获取审计统计"""
        total_events = len(self.audit_events)
        
        if total_events == 0:
            return {"total_events": 0}
        
        # 统计事件类型
        event_types = {}
        success_count = 0
        error_count = 0
        
        for event in self.audit_events:
            event_types[event.event_type] = event_types.get(event.event_type, 0) + 1
            if event.success:
                success_count += 1
            else:
                error_count += 1
        
        return {
            "total_events": total_events,
            "success_count": success_count,
            "error_count": error_count,
            "success_rate": success_count / total_events,
            "event_types": event_types
        }


def setup_audit_middleware(app):
    """设置审计中间件"""
    app.add_middleware(AuditMiddleware)
    
    logger.info("审计中间件已配置")
