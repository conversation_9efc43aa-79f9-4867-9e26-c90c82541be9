#!/usr/bin/env python3
"""
EVE SSO权限范围管理工具
用于快速切换和管理EVE SSO权限配置
"""
import os
import sys
from pathlib import Path

# 预定义的权限配置
SCOPE_CONFIGS = {
    "minimal": {
        "name": "最小配置 (基础功能)",
        "description": "只包含最基本的功能，适合简单应用",
        "scopes": [
            "esi-characters.read_characters.v1",      # 角色基本信息
            "esi-location.read_location.v1",          # 当前位置
            "esi-skills.read_skills.v1",              # 技能信息
            "esi-assets.read_assets.v1",              # 资产信息
        ]
    },
    
    "recommended": {
        "name": "推荐配置 (常用功能)",
        "description": "包含大部分常用功能，平衡功能性和复杂度",
        "scopes": [
            "esi-characters.read_characters.v1",
            "esi-characters.read_corporation_roles.v1",
            "esi-location.read_location.v1",
            "esi-location.read_online.v1",
            "esi-skills.read_skills.v1",
            "esi-skills.read_skillqueue.v1",
            "esi-assets.read_assets.v1",
            "esi-wallet.read_character_wallet.v1",
            "esi-markets.read_character_orders.v1",
            "esi-industry.read_character_jobs.v1",
        ]
    },
    
    "full": {
        "name": "完整配置 (所有功能)",
        "description": "包含所有可用权限，提供完整功能支持",
        "scopes": [
            "esi-characters.read_characters.v1",
            "esi-characters.read_corporation_roles.v1",
            "esi-location.read_location.v1",
            "esi-location.read_online.v1",
            "esi-skills.read_skills.v1",
            "esi-skills.read_skillqueue.v1",
            "esi-assets.read_assets.v1",
            "esi-wallet.read_character_wallet.v1",
            "esi-mail.read_mail.v1",
            "esi-clones.read_clones.v1",
            "esi-markets.read_character_orders.v1",
            "esi-industry.read_character_jobs.v1",
            "esi-corporations.read_corporation_membership.v1",
            "esi-alliances.read_contacts.v1",
            "esi-universe.read_structures.v1",
            "esi-search.search_structures.v1",
            "esi-contracts.read_character_contracts.v1",
            "esi-killmails.read_killmails.v1",
            "esi-fittings.read_fittings.v1",
            "esi-calendar.read_calendar_events.v1",
        ]
    }
}

def get_project_root():
    """获取项目根目录"""
    current = Path(__file__).parent
    while current.parent != current:
        if (current / '.env').exists():
            return current
        current = current.parent
    return Path.cwd()

def read_env_file():
    """读取.env文件内容"""
    env_path = get_project_root() / '.env'
    if not env_path.exists():
        print(f"❌ 未找到.env文件: {env_path}")
        return None
    
    with open(env_path, 'r', encoding='utf-8') as f:
        return f.read()

def write_env_file(content):
    """写入.env文件"""
    env_path = get_project_root() / '.env'
    with open(env_path, 'w', encoding='utf-8') as f:
        f.write(content)

def format_scopes(scopes):
    """格式化权限范围为多行字符串"""
    if not scopes:
        return '""'
    
    formatted = '"\\\n'
    for i, scope in enumerate(scopes):
        if i == len(scopes) - 1:
            formatted += f'{scope}"'
        else:
            formatted += f'{scope} \\\n'
    return formatted

def update_scopes_in_env(content, new_scopes):
    """更新.env文件中的权限配置"""
    lines = content.split('\n')
    new_lines = []
    in_scopes_section = False
    
    for line in lines:
        if line.startswith('EVE_SSO_SCOPES='):
            # 开始权限配置部分
            in_scopes_section = True
            new_lines.append(f'EVE_SSO_SCOPES={format_scopes(new_scopes)}')
            continue
        elif in_scopes_section and line.strip().endswith('"'):
            # 权限配置结束
            in_scopes_section = False
            continue
        elif in_scopes_section:
            # 跳过权限配置中间的行
            continue
        else:
            new_lines.append(line)
    
    return '\n'.join(new_lines)

def show_current_config():
    """显示当前配置"""
    print("🔍 当前EVE SSO权限配置:")
    print("=" * 60)
    
    try:
        sys.path.insert(0, str(get_project_root()))
        from src.infrastructure.config import settings
        
        current_scopes = settings.eve_sso_scopes.split()
        print(f"权限数量: {len(current_scopes)}")
        print("\n当前权限列表:")
        
        for i, scope in enumerate(current_scopes, 1):
            print(f"  {i:2d}. {scope}")
            
    except Exception as e:
        print(f"❌ 无法读取当前配置: {e}")

def show_available_configs():
    """显示可用的配置选项"""
    print("\n📋 可用的权限配置:")
    print("=" * 60)
    
    for key, config in SCOPE_CONFIGS.items():
        print(f"\n🎯 {key.upper()}: {config['name']}")
        print(f"   描述: {config['description']}")
        print(f"   权限数量: {len(config['scopes'])}")

def apply_config(config_name):
    """应用指定的配置"""
    if config_name not in SCOPE_CONFIGS:
        print(f"❌ 未知的配置: {config_name}")
        print(f"可用配置: {', '.join(SCOPE_CONFIGS.keys())}")
        return False
    
    config = SCOPE_CONFIGS[config_name]
    print(f"\n🔧 应用配置: {config['name']}")
    print(f"权限数量: {len(config['scopes'])}")
    
    # 读取当前.env文件
    content = read_env_file()
    if content is None:
        return False
    
    # 更新权限配置
    new_content = update_scopes_in_env(content, config['scopes'])
    
    # 写入文件
    write_env_file(new_content)
    
    print("✅ 配置已更新!")
    print("\n⚠️  重要提醒:")
    print("1. 需要在EVE Developer Portal中更新应用权限")
    print("2. 用户需要重新登录以获得新权限")
    print("3. 重启应用以加载新配置")
    
    return True

def main():
    """主函数"""
    print("🚀 EVE SSO权限范围管理工具")
    print("=" * 60)
    
    if len(sys.argv) < 2:
        show_current_config()
        show_available_configs()
        print("\n使用方法:")
        print("  python eve-sso-scope-manager.py <配置名称>")
        print("  python eve-sso-scope-manager.py minimal")
        print("  python eve-sso-scope-manager.py recommended")
        print("  python eve-sso-scope-manager.py full")
        return
    
    config_name = sys.argv[1].lower()
    
    if config_name in ['show', 'current', 'status']:
        show_current_config()
    elif config_name in ['list', 'available']:
        show_available_configs()
    else:
        if apply_config(config_name):
            print(f"\n🎉 成功切换到 {config_name.upper()} 配置!")
        else:
            print(f"\n❌ 配置切换失败!")

if __name__ == "__main__":
    main()
