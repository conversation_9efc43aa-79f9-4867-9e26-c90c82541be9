{"name": "@storybook/addon-outline", "version": "7.6.20", "description": "Outline all elements with CSS to help with layout placement and alignment", "keywords": ["storybook-addons", "essentials", "outline", "css", "layout", "debug", "storybook-addon", "style"], "homepage": "https://github.com/storybookjs/storybook/tree/next/code/addons/outline", "bugs": {"url": "https://github.com/storybookjs/storybook/issues"}, "repository": {"type": "git", "url": "https://github.com/storybookjs/storybook.git", "directory": "code/addons/outline"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "license": "MIT", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "require": "./dist/index.js", "import": "./dist/index.mjs"}, "./manager": "./dist/manager.js", "./preview": "./dist/preview.js", "./register": "./dist/manager.js", "./package.json": "./package.json"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist/**/*", "README.md", "*.js", "*.d.ts", "!src/**/*"], "scripts": {"check": "node --loader ../../../scripts/node_modules/esbuild-register/loader.js -r ../../../scripts/node_modules/esbuild-register/register.js ../../../scripts/prepare/check.ts", "prep": "node --loader ../../../scripts/node_modules/esbuild-register/loader.js -r ../../../scripts/node_modules/esbuild-register/register.js ../../../scripts/prepare/addon-bundle.ts"}, "dependencies": {"@storybook/global": "^5.0.0", "ts-dedent": "^2.0.0"}, "devDependencies": {"@storybook/client-logger": "7.6.20", "@storybook/components": "7.6.20", "@storybook/core-events": "7.6.20", "@storybook/manager-api": "7.6.20", "@storybook/preview-api": "7.6.20", "@storybook/types": "7.6.20", "react": "^16.8.0", "react-dom": "^16.8.0", "typescript": "~4.9.3"}, "publishConfig": {"access": "public"}, "bundler": {"exportEntries": ["./src/index.ts"], "managerEntries": ["./src/manager.tsx"], "previewEntries": ["./src/preview.tsx"]}, "gitHead": "e6a7fd8a655c69780bc20b9749c2699e44beae17", "storybook": {"displayName": "Outline", "unsupportedFrameworks": ["react-native"], "icon": "https://user-images.githubusercontent.com/263385/101991674-48355c80-3c7c-11eb-9686-f684e755fcdd.png"}}