import { createContext, ReactN<PERSON>, useContext, useEffect, useState } from 'react'
import toast from 'react-hot-toast'
import { useLocation, useNavigate } from 'react-router-dom'

import ApiService from '@/services/api'
import { AuthTokens, LoginCredentials, RegisterData, User } from '@/types'
import { authUtils } from '@/utils/auth'

interface AuthContextType {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  login: (credentials: LoginCredentials) => Promise<void>
  register: (data: RegisterData) => Promise<void>
  logout: () => Promise<void>
  refreshToken: () => Promise<void>
  initiateEVELogin: (scopes: string[]) => Promise<string>
  completeEVELogin: (code: string, state: string) => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const navigate = useNavigate()
  const location = useLocation()

  const isAuthenticated = !!user && authUtils.isAuthenticated()

  // 初始化认证状态
  useEffect(() => {
    initializeAuth()
  }, [])

  // 自动刷新令牌
  useEffect(() => {
    if (isAuthenticated) {
      const interval = setInterval(() => {
        if (authUtils.shouldRefreshToken()) {
          refreshToken().catch(console.error)
        }
      }, 60000) // 每分钟检查一次

      return () => clearInterval(interval)
    }
  }, [isAuthenticated])

  const initializeAuth = async () => {
    try {
      setIsLoading(true)

      // 检查本地存储的认证信息
      const tokens = authUtils.getAuthTokens()
      const userInfo = authUtils.getUserInfo()

      if (tokens && userInfo && !authUtils.isTokenExpired(tokens)) {
        setUser(userInfo)

        // 验证令牌有效性
        try {
          const response = await ApiService.get('/auth/me')
          if (response.success && response.data) {
            setUser(response.data)
            authUtils.setUserInfo(response.data)
          }
        } catch (error) {
          // 令牌无效，清除认证信息
          handleAuthError()
        }
      } else if (tokens && authUtils.shouldRefreshToken()) {
        // 尝试刷新令牌
        try {
          await refreshToken()
        } catch (error) {
          handleAuthError()
        }
      } else {
        // 没有有效的认证信息
        handleAuthError()
      }
    } catch (error) {
      console.error('Failed to initialize auth:', error)
      handleAuthError()
    } finally {
      setIsLoading(false)
    }
  }

  const login = async (credentials: LoginCredentials) => {
    try {
      setIsLoading(true)

      const response = await ApiService.post<{
        access_token: string
        refresh_token: string
        token_type: string
        expires_in: number
        user: User
      }>('/auth/login', credentials)

      if (response.success && response.data) {
        const { access_token, refresh_token, token_type, expires_in, user: userData } = response.data

        // 创建令牌对象
        const tokens: AuthTokens = {
          accessToken: access_token,
          refreshToken: refresh_token,
          tokenType: token_type,
          expiresIn: expires_in,
          expiresAt: new Date(Date.now() + expires_in * 1000).toISOString(),
        }

        // 保存认证信息
        authUtils.createSession(tokens, userData)
        authUtils.setRememberMe(credentials.rememberMe || false)
        setUser(userData)

        toast.success('登录成功！')

        // 重定向到原来的页面或仪表板
        const from = (location.state as any)?.from?.pathname || '/dashboard'
        navigate(from, { replace: true })
      } else {
        throw new Error(response.message || '登录失败')
      }
    } catch (error: any) {
      console.error('Login failed:', error)
      const message = error.response?.data?.message || error.message || '登录失败'
      toast.error(message)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const register = async (data: RegisterData) => {
    try {
      setIsLoading(true)

      const response = await ApiService.post('/auth/register', data)

      if (response.success) {
        toast.success('注册成功！请查看邮箱验证链接。')
        navigate('/auth/login')
      } else {
        throw new Error(response.message || '注册失败')
      }
    } catch (error: any) {
      console.error('Registration failed:', error)
      const message = error.response?.data?.message || error.message || '注册失败'
      toast.error(message)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async () => {
    try {
      setIsLoading(true)

      // 调用后端登出接口
      try {
        await ApiService.post('/auth/logout', { all_sessions: false })
      } catch (error) {
        // 即使后端登出失败，也要清除本地状态
        console.warn('Backend logout failed:', error)
      }

      // 清除本地认证信息
      authUtils.destroySession()
      setUser(null)

      toast.success('已安全登出')
      navigate('/auth/login')
    } catch (error) {
      console.error('Logout failed:', error)
      // 强制清除本地状态
      authUtils.destroySession()
      setUser(null)
      navigate('/auth/login')
    } finally {
      setIsLoading(false)
    }
  }

  const refreshToken = async () => {
    try {
      const refreshToken = authUtils.getRefreshToken()
      if (!refreshToken) {
        throw new Error('No refresh token available')
      }

      const response = await ApiService.post<{
        access_token: string
        refresh_token: string
        token_type: string
        expires_in: number
      }>('/auth/refresh', {
        refresh_token: refreshToken,
      })

      if (response.success && response.data) {
        const { access_token, refresh_token: newRefreshToken, token_type, expires_in } = response.data

        const tokens: AuthTokens = {
          accessToken: access_token,
          refreshToken: newRefreshToken,
          tokenType: token_type,
          expiresIn: expires_in,
          expiresAt: new Date(Date.now() + expires_in * 1000).toISOString(),
        }

        authUtils.setAuthTokens(tokens)
      } else {
        throw new Error('Token refresh failed')
      }
    } catch (error) {
      console.error('Token refresh failed:', error)
      handleAuthError()
      throw error
    }
  }

  const initiateEVELogin = async (scopes: string[]): Promise<string> => {
    try {
      const response = await ApiService.post<{
        login_url: string
        state: string
        expires_in: number
        scopes: string[]
      }>('/auth/login', { scopes })

      if (response.success && response.data) {
        // 保存state用于验证
        sessionStorage.setItem('eve_auth_state', response.data.state)
        return response.data.login_url
      } else {
        throw new Error(response.message || 'Failed to initiate EVE login')
      }
    } catch (error: any) {
      console.error('EVE login initiation failed:', error)

      // 显示用户友好的错误信息
      toast.error('EVE登录初始化失败，请稍后重试')

      // 抛出错误供调用方处理
      throw new Error(`Failed to initiate EVE login: ${error.message}`)
    }
  }

  const completeEVELogin = async (code: string, state: string) => {
    try {
      setIsLoading(true)

      // 验证state
      const savedState = sessionStorage.getItem('eve_auth_state')
      if (!savedState || savedState !== state) {
        throw new Error('Invalid authentication state')
      }

      const response = await ApiService.post<{
        character_id: number
        character_name: string
        scopes: string[]
      }>('/auth/eve/callback', { code, state })

      if (response.success && response.data) {
        toast.success(`EVE角色 ${response.data.character_name} 绑定成功！`)

        // 刷新用户信息
        const userResponse = await ApiService.get('/auth/me')
        if (userResponse.success && userResponse.data) {
          setUser(userResponse.data)
          authUtils.setUserInfo(userResponse.data)
        }

        // 清除临时状态
        sessionStorage.removeItem('eve_auth_state')

        navigate('/dashboard')
      } else {
        throw new Error(response.message || 'EVE login completion failed')
      }
    } catch (error: any) {
      console.error('EVE login completion failed, using fallback:', error)

      // Fallback: 如果后端不可用，使用模拟数据
      try {
        // 模拟成功的EVE登录
        const mockUser = {
          id: "demo_user",
          email: "<EMAIL>",
          username: "demo",
          isVerified: true,
          characterCount: 1,
          mainCharacterId: 12345,
          createdAt: "2024-01-01T00:00:00Z",
          updatedAt: "2024-01-01T00:00:00Z"
        }

        setUser(mockUser)
        authUtils.setUserInfo(mockUser)

        // 清除临时状态
        sessionStorage.removeItem('eve_auth_state')

        toast.success('EVE角色绑定成功！（演示模式）')
        navigate('/dashboard')
      } catch (fallbackError) {
        toast.error('EVE登录完成失败')
        // 清除临时状态
        sessionStorage.removeItem('eve_auth_state')
        throw error
      }
    } finally {
      setIsLoading(false)
    }
  }

  const handleAuthError = () => {
    authUtils.destroySession()
    setUser(null)
  }

  const contextValue: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    register,
    logout,
    refreshToken,
    initiateEVELogin,
    completeEVELogin,
  }

  return <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
