#!/usr/bin/env python3
"""
真实EVE SSO集成测试脚本
"""
import requests
import time
import json
import webbrowser
from urllib.parse import urlparse, parse_qs

def test_real_eve_sso():
    base_url = "http://localhost:8000"
    
    print("🔐 真实EVE SSO集成测试")
    print("=" * 60)
    
    # 等待服务器启动
    print("等待服务器启动...")
    time.sleep(3)
    
    # 测试1: 检查认证状态
    print("\n1. 检查认证服务状态...")
    try:
        response = requests.get(f"{base_url}/auth/status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ 认证服务正常运行")
            print(f"   服务类型: {data.get('service_type')}")
            print(f"   SSO客户端: {data.get('sso_client')}")
            print(f"   活动状态: {data.get('active_states', 0)}")
            
            features = data.get('features', [])
            if features:
                print("   支持功能:")
                for feature in features:
                    print(f"     - {feature}")
        else:
            print(f"❌ 认证服务异常: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 认证服务连接失败: {e}")
        return False
    
    # 测试2: 发起EVE SSO登录
    print("\n2. 发起EVE SSO登录...")
    try:
        test_scopes = [
            "esi-assets.read_assets.v1",
            "esi-skills.read_skills.v1",
            "esi-wallet.read_character_wallet.v1"
        ]
        
        response = requests.post(
            f"{base_url}/auth/eve/login",
            json={"scopes": test_scopes},
            timeout=10
        )
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ EVE SSO登录发起成功")
            
            # 检查响应格式
            if data.get("success"):
                # 从data字段中获取信息
                data_content = data.get("data", {})
                login_url = data_content.get("login_url")
                state = data_content.get("state")
                scopes = data_content.get("scopes", [])

                if login_url:
                    print(f"   登录URL长度: {len(login_url)} 字符")
                    print(f"   状态码: {state}")
                    print(f"   权限范围: {len(scopes)} 个")
                    print(f"   过期时间: {data_content.get('expires_in')} 秒")
                else:
                    print(f"   ❌ 登录URL为空")
                    print(f"   响应数据: {data}")
                    return False
                
                # 保存登录信息
                login_info = {
                    "timestamp": time.time(),
                    "login_url": login_url,
                    "state": state,
                    "scopes": scopes,
                    "expires_in": data_content.get("expires_in")
                }
                
                with open("real_eve_login.json", "w") as f:
                    json.dump(login_info, f, indent=2)
                
                print(f"   📄 登录信息已保存到: real_eve_login.json")
                
                # 询问是否打开浏览器
                print(f"\n🌐 是否要在浏览器中打开EVE SSO登录页面？")
                print(f"   登录URL: {login_url[:80]}...")
                
                user_input = input("   输入 'y' 打开浏览器，其他键跳过: ").strip().lower()
                if user_input == 'y':
                    try:
                        webbrowser.open(login_url)
                        print("   ✅ 浏览器已打开EVE SSO登录页面")
                        
                        print(f"\n📋 登录步骤:")
                        print(f"   1. 在浏览器中完成EVE SSO登录")
                        print(f"   2. 授权应用访问您的角色数据")
                        print(f"   3. 系统会自动跳转到回调URL")
                        print(f"   4. 查看服务器日志了解回调处理结果")
                        
                    except Exception as e:
                        print(f"   ⚠️  无法打开浏览器: {e}")
                        print(f"   请手动复制URL到浏览器中打开")
                
                return True
                
            else:
                print(f"❌ EVE SSO登录发起失败")
                print(f"   错误信息: {data.get('message')}")
                return False
        else:
            print(f"❌ EVE SSO登录发起失败: HTTP {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ EVE SSO登录发起连接失败: {e}")
        return False
    
    # 测试3: 检查回调端点
    print("\n3. 检查EVE SSO回调端点...")
    try:
        response = requests.get(f"{base_url}/auth/callback", timeout=5)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code in [400, 422]:  # 没有参数是正常的
            print("✅ 回调端点存在且可访问")
            print("   (400/422状态码是正常的，因为没有提供回调参数)")
        elif response.status_code == 200:
            print("✅ 回调端点正常响应")
        else:
            print(f"⚠️  回调端点状态异常: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ 回调端点连接失败: {e}")

def check_callback_result():
    """检查回调处理结果"""
    print("\n" + "=" * 60)
    print("🔍 检查EVE SSO回调处理结果")
    print("=" * 60)
    
    # 检查服务器日志或状态
    base_url = "http://localhost:8000"
    
    try:
        response = requests.get(f"{base_url}/auth/status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            active_states = data.get('active_states', 0)
            print(f"当前活动认证状态: {active_states}")
            
            if active_states == 0:
                print("✅ 可能已完成认证流程（无活动状态）")
            else:
                print("⏳ 认证流程进行中...")
        
    except Exception as e:
        print(f"无法检查认证状态: {e}")

if __name__ == "__main__":
    success = test_real_eve_sso()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 真实EVE SSO集成测试通过！")
        print("✅ EVE SSO登录URL生成成功")
        print("✅ 可以进行真实的EVE登录流程")
        
        print(f"\n📋 后续步骤:")
        print(f"1. 在浏览器中完成EVE SSO登录")
        print(f"2. 检查服务器日志查看回调处理")
        print(f"3. 验证token获取和角色信息")
        
        # 提供检查回调结果的选项
        print(f"\n输入 'check' 检查回调处理结果:")
        user_input = input().strip().lower()
        if user_input == 'check':
            check_callback_result()
            
    else:
        print("❌ 真实EVE SSO集成测试失败")
        print("⚠️  需要检查配置和服务状态")
    
    print("=" * 60)
