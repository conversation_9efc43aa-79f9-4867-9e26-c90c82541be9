"""
领域事件基类和通用事件
"""
from abc import ABC
from datetime import datetime
from typing import Any, Dict, Optional
from uuid import UUID, uuid4


class DomainEvent(ABC):
    """领域事件基类"""
    
    def __init__(self, 
                 aggregate_id: Any,
                 event_data: Optional[Dict[str, Any]] = None,
                 event_id: Optional[UUID] = None,
                 occurred_at: Optional[datetime] = None):
        self.event_id = event_id or uuid4()
        self.aggregate_id = aggregate_id
        self.event_data = event_data or {}
        self.occurred_at = occurred_at or datetime.utcnow()
        self.event_type = self.__class__.__name__
    
    def __eq__(self, other: object) -> bool:
        """事件相等性比较"""
        if not isinstance(other, DomainEvent):
            return False
        return self.event_id == other.event_id
    
    def __hash__(self) -> int:
        """事件哈希值"""
        return hash(self.event_id)
    
    def __repr__(self) -> str:
        """事件字符串表示"""
        return (f"{self.__class__.__name__}("
                f"event_id={self.event_id}, "
                f"aggregate_id={self.aggregate_id}, "
                f"occurred_at={self.occurred_at})")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "event_id": str(self.event_id),
            "event_type": self.event_type,
            "aggregate_id": str(self.aggregate_id),
            "event_data": self.event_data,
            "occurred_at": self.occurred_at.isoformat(),
        }


class DomainEventHandler(ABC):
    """领域事件处理器基类"""
    
    def can_handle(self, event: DomainEvent) -> bool:
        """检查是否可以处理该事件"""
        raise NotImplementedError
    
    def handle(self, event: DomainEvent) -> None:
        """处理事件"""
        raise NotImplementedError


class EventBus(ABC):
    """事件总线接口"""
    
    def publish(self, event: DomainEvent) -> None:
        """发布事件"""
        raise NotImplementedError
    
    def subscribe(self, handler: DomainEventHandler) -> None:
        """订阅事件处理器"""
        raise NotImplementedError
    
    def unsubscribe(self, handler: DomainEventHandler) -> None:
        """取消订阅事件处理器"""
        raise NotImplementedError
