"""
简化日志配置 - 兼容所有Python环境
"""
import logging
import sys
from typing import Any

def configure_logging() -> None:
    """配置应用日志"""
    logging.basicConfig(
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        stream=sys.stdout,
        level=logging.INFO,
    )

def get_logger(name: str):
    """获取日志记录器"""
    return logging.getLogger(name)

# 配置日志
configure_logging()

# 创建默认日志记录器
logger = get_logger(__name__)
