import { ArrowDownOutlined, ArrowUpOutlined, QuestionCircleOutlined } from '@ant-design/icons'
import { Card, Tooltip } from 'antd'
import clsx from 'clsx'
import { motion } from 'framer-motion'
import React from 'react'

interface StatCardProps {
  title: string
  value: number | string
  format?: 'number' | 'currency' | 'percentage'
  precision?: number
  trend?: number
  icon?: React.ReactNode
  loading?: boolean
  className?: string
  tooltip?: string
  color?: 'default' | 'primary' | 'success' | 'warning' | 'error'
  size?: 'small' | 'default' | 'large'
}

export function StatCard({
  title,
  value,
  format = 'number',
  precision = 0,
  trend,
  icon,
  loading = false,
  className = '',
  tooltip,
  color = 'default',
  size = 'default',
}: StatCardProps) {
  const formatValue = (val: number | string) => {
    if (typeof val === 'string') return val

    switch (format) {
      case 'currency':
        return new Intl.NumberFormat('zh-CN', {
          style: 'decimal',
          minimumFractionDigits: precision,
          maximumFractionDigits: precision,
        }).format(val)
      case 'percentage':
        return `${val.toFixed(precision)}%`
      default:
        return new Intl.NumberFormat('zh-CN', {
          minimumFractionDigits: precision,
          maximumFractionDigits: precision,
        }).format(val)
    }
  }

  const getValueColor = () => {
    switch (color) {
      case 'primary':
        return '#1890ff'
      case 'success':
        return '#52c41a'
      case 'warning':
        return '#faad14'
      case 'error':
        return '#f5222d'
      default:
        return undefined
    }
  }

  const getTrendColor = () => {
    if (trend === undefined) return undefined
    return trend >= 0 ? '#52c41a' : '#f5222d'
  }

  const getTrendIcon = () => {
    if (trend === undefined) return null
    return trend >= 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />
  }

  const cardSize = size === 'small' ? 'small' : 'default'

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={className}
    >
      <Card
        loading={loading}
        size={cardSize}
        className={clsx(
          'hover:shadow-md transition-shadow duration-200',
          {
            'border-primary-200 bg-primary-50/50 dark:border-primary-800 dark:bg-primary-900/20': color === 'primary',
            'border-green-200 bg-green-50/50 dark:border-green-800 dark:bg-green-900/20': color === 'success',
            'border-yellow-200 bg-yellow-50/50 dark:border-yellow-800 dark:bg-yellow-900/20': color === 'warning',
            'border-red-200 bg-red-50/50 dark:border-red-800 dark:bg-red-900/20': color === 'error',
          }
        )}
      >
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <span className={clsx(
                'text-sm font-medium',
                size === 'small' ? 'text-xs' : 'text-sm',
                'text-gray-600 dark:text-gray-400'
              )}>
                {title}
              </span>
              {tooltip && (
                <Tooltip title={tooltip}>
                  <QuestionCircleOutlined className="text-gray-400 text-xs" />
                </Tooltip>
              )}
            </div>

            <div className="flex items-baseline space-x-2">
              <span
                className={clsx(
                  'font-bold',
                  size === 'small' ? 'text-lg' : size === 'large' ? 'text-3xl' : 'text-2xl'
                )}
                style={{ color: getValueColor() }}
              >
                {formatValue(value)}
                {format === 'currency' && (
                  <span className="text-sm font-normal ml-1 text-gray-500">
                    ISK
                  </span>
                )}
              </span>

              {trend !== undefined && (
                <span
                  className="text-sm flex items-center"
                  style={{ color: getTrendColor() }}
                >
                  {getTrendIcon()}
                  <span className="ml-1">{Math.abs(trend).toFixed(1)}%</span>
                </span>
              )}
            </div>
          </div>

          {icon && (
            <div className={clsx(
              'flex items-center justify-center rounded-full',
              size === 'small' ? 'w-10 h-10' : 'w-12 h-12',
              'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
            )}>
              <span className={size === 'small' ? 'text-lg' : 'text-xl'}>
                {icon}
              </span>
            </div>
          )}
        </div>
      </Card>
    </motion.div>
  )
}

// 预设的统计卡片组件
export function AssetValueCard({ value, trend, loading }: {
  value: number
  trend?: number
  loading?: boolean
}) {
  return (
    <StatCard
      title="总资产价值"
      value={value}
      format="currency"
      trend={trend}
      loading={loading}
      color="success"
      icon="💰"
    />
  )
}

export function SkillPointsCard({ value, loading }: {
  value: number
  loading?: boolean
}) {
  return (
    <StatCard
      title="技能点"
      value={value}
      format="number"
      loading={loading}
      color="primary"
      icon="🏆"
      tooltip="角色总技能点数"
    />
  )
}

export function ActiveOrdersCard({ value, loading }: {
  value: number
  loading?: boolean
}) {
  return (
    <StatCard
      title="活跃订单"
      value={value}
      format="number"
      loading={loading}
      color="warning"
      icon="📊"
    />
  )
}

export function IndustryJobsCard({ value, loading }: {
  value: number
  loading?: boolean
}) {
  return (
    <StatCard
      title="工业任务"
      value={value}
      format="number"
      loading={loading}
      color="primary"
      icon="🔧"
    />
  )
}
