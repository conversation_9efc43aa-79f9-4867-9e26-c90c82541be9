# 资产管理模块 - 产品需求文档 (PRD)

## 📋 模块概述

### 模块名称
资产管理模块 (Asset Management Module)

### 模块愿景
为EVE Online玩家和公司提供全面的资产跟踪、库存管理和资产分析服务，帮助用户优化资产配置、降低管理成本、提升资产利用效率。

### 业务价值
- 🎯 **资产可视化**: 提供直观的资产分布和价值展示
- 🎯 **库存优化**: 智能库存管理和补货建议
- 🎯 **价值跟踪**: 实时资产价值评估和趋势分析
- 🎯 **风险管理**: 资产安全评估和风险预警

## 🎯 功能需求

### 1. 资产概览与统计

#### 1.1 资产总览
**功能描述**: 提供用户所有资产的全局视图和统计信息

**核心功能**:
- ✅ 总资产价值实时计算
- ✅ 资产分类统计（舰船、装备、原料、蓝图等）
- ✅ 资产地理分布图
- ✅ 资产价值趋势图表
- ✅ 高价值资产排行榜

**数据模型**:
```python
class AssetSummary(ValueObject):
    total_value: Money
    total_items: int
    categories: Dict[AssetCategory, AssetCategoryStats]
    locations: Dict[LocationId, LocationAssetStats]
    last_updated: datetime
```

#### 1.2 资产分类管理
**功能描述**: 按不同维度对资产进行分类和管理

**核心功能**:
- ✅ 按物品类型分类（舰船、模块、弹药、原料等）
- ✅ 按价值区间分类（高价值、中价值、低价值）
- ✅ 按用途分类（PvP装备、PvE装备、工业用品等）
- ✅ 自定义分类标签
- ✅ 分类筛选和搜索

### 2. 资产详细管理

#### 2.1 资产清单
**功能描述**: 详细的资产清单管理和查看

**核心功能**:
- ✅ 资产详细列表展示
- ✅ 多维度排序和筛选
- ✅ 资产搜索和快速定位
- ✅ 批量操作支持
- ✅ 资产导出功能

**数据模型**:
```python
class Asset(Entity):
    item_id: ItemId
    type_id: TypeId
    location_id: LocationId
    location_flag: LocationFlag
    location_type: LocationType
    quantity: Quantity
    is_singleton: bool
    is_blueprint_copy: Optional[bool]
    blueprint_runs: Optional[int]
    estimated_value: Money
```

#### 2.2 位置管理
**功能描述**: 管理资产在不同位置的分布

**核心功能**:
- ✅ 空间站资产管理
- ✅ 舰船货舱资产管理
- ✅ 个人机库资产管理
- ✅ 公司机库资产管理
- ✅ 位置间资产转移跟踪

### 3. 舰船管理

#### 3.1 舰船清单
**功能描述**: 专门的舰船资产管理

**核心功能**:
- ✅ 舰船列表和详细信息
- ✅ 舰船配置和装备展示
- ✅ 舰船价值评估
- ✅ 舰船保险状态跟踪
- ✅ 舰船使用历史记录

**数据模型**:
```python
class Ship(Entity):
    ship_id: ShipId
    type_id: TypeId
    name: str
    location: Location
    fitted_items: List[FittedItem]
    cargo_items: List[Asset]
    estimated_value: Money
    insurance_info: Optional[InsuranceInfo]
```

#### 3.2 舰船配置管理
**功能描述**: 管理舰船的装配和配置

**核心功能**:
- ✅ 装配方案保存和加载
- ✅ 装配效果计算
- ✅ 装配成本分析
- ✅ 装配优化建议
- ✅ 装配分享功能

### 4. 库存管理

#### 4.1 库存监控
**功能描述**: 智能库存水平监控和管理

**核心功能**:
- 🚀 库存水平实时监控
- 🚀 低库存自动预警
- 🚀 库存补货建议
- 🚀 库存周转率分析
- 🚀 滞销物品识别

**数据模型**:
```python
class InventoryItem(Entity):
    type_id: TypeId
    total_quantity: Quantity
    available_quantity: Quantity
    reserved_quantity: Quantity
    locations: Dict[LocationId, Quantity]
    reorder_point: Optional[Quantity]
    target_stock: Optional[Quantity]
```

#### 4.2 库存优化
**功能描述**: 基于使用模式的库存优化建议

**核心功能**:
- 🚀 基于历史数据的需求预测
- 🚀 最优库存水平计算
- 🚀 库存成本分析
- 🚀 库存整合建议
- 🚀 过期物品处理建议

### 5. 价值评估与分析

#### 5.1 实时价值评估
**功能描述**: 基于市场数据的实时资产价值评估

**核心功能**:
- ✅ 多种价格源支持（Jita、Amarr、区域平均等）
- ✅ 实时价格更新
- ✅ 历史价值趋势分析
- ✅ 价值变化预警
- ✅ 自定义估价规则

**数据模型**:
```python
class AssetValuation(ValueObject):
    asset_id: AssetId
    current_value: Money
    price_source: PriceSource
    unit_price: Money
    quantity: Quantity
    last_updated: datetime
    price_history: List[PricePoint]
```

#### 5.2 投资组合分析
**功能描述**: 从投资角度分析资产组合

**核心功能**:
- 🚀 资产配置分析
- 🚀 风险评估
- 🚀 收益率计算
- 🚀 多元化建议
- 🚀 投资机会识别

### 6. 资产安全管理

#### 6.1 安全评估
**功能描述**: 评估资产的安全风险

**核心功能**:
- ✅ 位置安全等级评估
- ✅ 高价值资产风险预警
- ✅ 资产分散度分析
- ✅ 保险覆盖率统计
- ✅ 安全建议生成

#### 6.2 损失跟踪
**功能描述**: 跟踪和分析资产损失

**核心功能**:
- ✅ 损失事件记录
- ✅ 损失价值统计
- ✅ 损失原因分析
- ✅ 损失趋势分析
- ✅ 保险赔付跟踪

## 🔧 技术实现

### 领域模型设计

#### 聚合根: AssetPortfolio
```python
class AssetPortfolio(AggregateRoot):
    def __init__(self, owner_id: CharacterId):
        super().__init__(owner_id)
        self._assets: Dict[AssetId, Asset] = {}
        self._locations: Dict[LocationId, Location] = {}
    
    def add_asset(self, asset: Asset) -> None:
        """添加资产"""
        self._assets[asset.id] = asset
        self._raise_event(AssetAddedEvent(self.id, asset))
    
    def update_asset_value(self, asset_id: AssetId, new_value: Money) -> None:
        """更新资产价值"""
        if asset_id in self._assets:
            self._assets[asset_id].update_value(new_value)
            self._raise_event(AssetValueUpdatedEvent(self.id, asset_id, new_value))
    
    def calculate_total_value(self) -> Money:
        """计算总资产价值"""
        return sum(asset.estimated_value for asset in self._assets.values())
```

#### 领域服务
```python
class AssetValuationService(DomainService):
    def calculate_asset_value(
        self, 
        asset: Asset, 
        price_source: PriceSource
    ) -> Money:
        """计算资产价值"""
        
    def analyze_portfolio_risk(
        self, 
        portfolio: AssetPortfolio
    ) -> RiskAnalysis:
        """分析投资组合风险"""

class InventoryOptimizationService(DomainService):
    def calculate_reorder_point(
        self, 
        item: InventoryItem, 
        usage_history: List[UsageRecord]
    ) -> Quantity:
        """计算补货点"""
        
    def suggest_inventory_optimization(
        self, 
        inventory: List[InventoryItem]
    ) -> List[OptimizationSuggestion]:
        """库存优化建议"""
```

### 应用服务

#### AssetApplicationService
```python
class AssetApplicationService:
    async def get_asset_overview(
        self, 
        character_id: CharacterId
    ) -> AssetOverviewDTO:
        """获取资产概览"""
        
    async def search_assets(
        self, 
        character_id: CharacterId, 
        search_criteria: AssetSearchCriteria
    ) -> List[AssetDTO]:
        """搜索资产"""
        
    async def update_asset_values(
        self, 
        character_id: CharacterId
    ) -> None:
        """更新资产价值"""
        
    async def generate_inventory_report(
        self, 
        character_id: CharacterId
    ) -> InventoryReportDTO:
        """生成库存报告"""
```

### 数据同步策略

#### 实时数据 (每10分钟)
- 高价值资产位置变化
- 舰船状态更新

#### 频繁数据 (每1小时)
- 资产列表更新
- 库存数量变化

#### 常规数据 (每4小时)
- 资产价值重新计算
- 市场价格更新

#### 批量数据 (每日)
- 完整资产扫描
- 历史数据归档

## 📊 用户界面设计

### 1. 资产概览页面
```
┌─────────────────────────────────────────────────────────────┐
│ 资产概览                                                     │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│ │ 总资产价值       │ │ 资产数量         │ │ 位置数量         │ │
│ │ 125.6B ISK      │ │ 15,234 件       │ │ 45 个位置       │ │
│ │ ↑ +2.3% (24h)   │ │                 │ │                 │ │
│ └─────────────────┘ └─────────────────┘ └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 资产分布                                                     │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 舰船 ████████████████████████████████████████ 45.2B    │ │
│ │ 模块 ████████████████████████ 28.7B                    │ │
│ │ 原料 ████████████████ 18.9B                            │ │
│ │ 蓝图 ██████████ 12.3B                                  │ │
│ │ 其他 ████ 5.1B                                         │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2. 资产清单页面
```
┌─────────────────────────────────────────────────────────────┐
│ 资产清单                                                     │
├─────────────────────────────────────────────────────────────┤
│ [搜索框] [筛选▼] [位置▼] [分类▼] [排序: 价值▼] [导出]        │
├─────────────────────────────────────────────────────────────┤
│ ☑ 物品名称                    数量    单价      总价值   位置 │
│ ☑ Raven Navy Issue            1      450M ISK  450M ISK Jita│
│ ☑ Large Shield Extender II    50     2.5M ISK  125M ISK Jita│
│ ☑ Tritanium                   1.2M   5.2 ISK   6.2M ISK Jita│
│ ☑ Raven Blueprint             1      25M ISK   25M ISK Jita │
│ ☐ ...                                                        │
├─────────────────────────────────────────────────────────────┤
│ 已选择 4 项 | 总价值: 606.2M ISK                            │
│ [批量移动] [批量出售] [添加到购物清单] [创建合同]            │
└─────────────────────────────────────────────────────────────┘
```

### 3. 舰船管理页面
```
┌─────────────────────────────────────────────────────────────┐
│ 舰船管理                                                     │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────────────────────────────────┐ │
│ │   舰船图标   │ │ Raven Navy Issue "战神"                 │ │
│ │             │ │ 位置: Jita IV - Moon 4                  │ │
│ │             │ │ 状态: 停靠中                            │ │
│ │             │ │ 保险: 已投保 (90天)                     │ │
│ └─────────────┘ │ 估值: 450M ISK                          │ │
│                 └─────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 装配信息                                                     │
│ ┌─ 高槽 ────────────────────────────────────────────────────┐ │
│ │ [1] Cruise Missile Launcher II                           │ │
│ │ [2] Cruise Missile Launcher II                           │ │
│ │ [3] 空                                                   │ │
│ └─────────────────────────────────────────────────────────┘ │
│ [保存装配] [加载装配] [模拟战斗] [计算DPS] [分享装配]        │
└─────────────────────────────────────────────────────────────┘
```

## 🔍 测试策略

### 单元测试
- 资产价值计算测试
- 库存优化算法测试
- 风险评估逻辑测试

### 集成测试
- 市场价格API集成测试
- 资产数据同步测试
- 价值更新流程测试

### 性能测试
- 大量资产加载性能测试
- 价值计算性能测试
- 搜索和筛选性能测试

## 📈 成功指标

### 功能指标
- 资产数据同步准确率 > 99.8%
- 价值计算准确率 > 99.5%
- 页面加载时间 < 3秒

### 用户体验指标
- 资产查找效率提升 > 70%
- 库存管理效率提升 > 60%
- 用户满意度评分 > 4.6/5.0

---

**文档版本**: v1.0  
**最后更新**: 2025-01-09  
**负责人**: 资产管理团队
