#!/usr/bin/env python3
"""
自动启动EVE Online Assistant with ngrok
自动管理ngrok隧道并启动应用服务器
"""
import sys
import os
import time
import signal
import asyncio
import threading
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.infrastructure.config import settings
    from src.infrastructure.ngrok.manager import ngrok_manager
    from src.infrastructure.config.logging import get_logger
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("请确保您在项目根目录运行此脚本")
    sys.exit(1)

logger = get_logger(__name__)


class EVEAssistantLauncher:
    """EVE Assistant启动器"""
    
    def __init__(self):
        self.ngrok_started = False
        self.server_process = None
        self.shutdown_event = threading.Event()
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info(f"收到信号 {signum}，开始清理...")
        self.shutdown()
    
    def check_dependencies(self) -> bool:
        """检查依赖"""
        print("🔍 检查依赖...")
        
        # 检查pyngrok
        if not ngrok_manager.is_available():
            print("❌ pyngrok未安装")
            print("请运行: pip install pyngrok")
            return False
        
        print("✅ pyngrok已安装")
        
        # 检查配置
        if not settings.ngrok_enabled:
            print("⚠️  ngrok未启用，在.env中设置 NGROK_ENABLED=true")
            return False
        
        print("✅ ngrok已启用")
        return True
    
    def setup_ngrok(self) -> bool:
        """设置并启动ngrok"""
        print("🚀 启动ngrok隧道...")
        
        try:
            # 启动隧道
            public_url = ngrok_manager.start_tunnel(settings.server_port)
            
            if public_url:
                print(f"✅ ngrok隧道已启动: {public_url}")
                print(f"   本地端口: {settings.server_port}")
                
                # 显示回调URL信息
                callback_url = f"{public_url}/auth/callback"
                print(f"   EVE SSO回调URL: {callback_url}")
                
                if settings.ngrok_auto_update_callback:
                    print("   ✅ 回调URL已自动更新到.env文件")
                    print("   ⚠️  请在EVE Developer Portal中更新回调URL!")
                
                self.ngrok_started = True
                return True
            else:
                print("❌ ngrok隧道启动失败")
                return False
                
        except Exception as e:
            print(f"❌ ngrok设置失败: {e}")
            return False
    
    def start_server(self):
        """启动FastAPI服务器"""
        print("🌐 启动FastAPI服务器...")
        
        try:
            import uvicorn
            from src.presentation.api.main import app
            
            # 在单独线程中启动服务器
            def run_server():
                uvicorn.run(
                    app,
                    host="127.0.0.1",
                    port=settings.server_port,
                    log_level="info",
                    access_log=True
                )
            
            server_thread = threading.Thread(target=run_server, daemon=True)
            server_thread.start()
            
            print(f"✅ FastAPI服务器已启动")
            print(f"   本地地址: http://127.0.0.1:{settings.server_port}")
            
            if self.ngrok_started:
                print(f"   公网地址: {ngrok_manager.public_url}")
            
            return server_thread
            
        except Exception as e:
            print(f"❌ 服务器启动失败: {e}")
            return None
    
    def monitor_services(self):
        """监控服务状态"""
        print("📊 开始监控服务...")
        
        while not self.shutdown_event.is_set():
            try:
                # 检查ngrok状态
                if self.ngrok_started and not ngrok_manager.health_check():
                    logger.warning("ngrok隧道连接丢失，尝试重启...")
                    ngrok_manager.restart_tunnel(settings.server_port)
                
                # 等待30秒后再次检查
                self.shutdown_event.wait(30)
                
            except Exception as e:
                logger.error("服务监控异常", error=str(e))
                self.shutdown_event.wait(10)
    
    def display_info(self):
        """显示服务信息"""
        print("\n" + "="*60)
        print("🎉 EVE Online Assistant 已启动!")
        print("="*60)
        
        print(f"📍 本地服务器: http://127.0.0.1:{settings.server_port}")
        
        if self.ngrok_started:
            print(f"🌐 公网地址: {ngrok_manager.public_url}")
            print(f"🔗 API文档: {ngrok_manager.public_url}/docs")
            print(f"🔐 EVE SSO登录: {ngrok_manager.public_url}/auth/login")
        
        print("\n📋 可用端点:")
        print("   /health/          - 健康检查")
        print("   /docs             - API文档")
        print("   /auth/login       - EVE SSO登录")
        print("   /characters/      - 角色管理")
        print("   /storage/stats    - 存储统计")
        
        print("\n⚠️  重要提醒:")
        if self.ngrok_started:
            callback_url = f"{ngrok_manager.public_url}/auth/callback"
            print(f"   请在EVE Developer Portal中设置回调URL为:")
            print(f"   {callback_url}")
        
        print("\n💡 使用说明:")
        print("   - 按 Ctrl+C 停止服务")
        print("   - 服务会自动监控ngrok连接状态")
        print("   - 日志文件保存在 logs/ 目录")
        
        print("="*60)
    
    def shutdown(self):
        """关闭服务"""
        print("\n🛑 正在关闭服务...")
        
        # 设置关闭事件
        self.shutdown_event.set()
        
        # 停止ngrok
        if self.ngrok_started:
            print("   停止ngrok隧道...")
            ngrok_manager.cleanup()
        
        print("✅ 服务已关闭")
        sys.exit(0)
    
    def run(self):
        """运行启动器"""
        print("🚀 EVE Online Assistant 启动器")
        print("="*50)
        
        # 检查依赖
        if not self.check_dependencies():
            sys.exit(1)
        
        # 设置ngrok
        if not self.setup_ngrok():
            sys.exit(1)
        
        # 启动服务器
        server_thread = self.start_server()
        if not server_thread:
            self.shutdown()
            sys.exit(1)
        
        # 等待服务器启动
        time.sleep(3)
        
        # 显示信息
        self.display_info()
        
        # 开始监控
        try:
            self.monitor_services()
        except KeyboardInterrupt:
            self.shutdown()


def main():
    """主函数"""
    launcher = EVEAssistantLauncher()
    launcher.run()


if __name__ == "__main__":
    main()
