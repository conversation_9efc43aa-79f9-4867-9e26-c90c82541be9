#!/usr/bin/env python3
"""
简单的HTTP服务器用于测试EVE登录
"""
import json
import urllib.parse
from http.server import HTTPServer, BaseHTTPRequestHandler

class EVELoginHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/health':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            response = {"status": "ok", "message": "Simple HTTP Server is running"}
            self.wfile.write(json.dumps(response).encode())
        else:
            self.send_response(404)
            self.end_headers()
    
    def do_POST(self):
        if self.path == '/auth/login':
            # 读取请求体
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            
            try:
                request_data = json.loads(post_data.decode('utf-8'))
                scopes = request_data.get('scopes', ['esi-characters.read_characters.v1'])
                
                print(f"收到EVE登录请求，scopes: {scopes}")
                
                # 返回模拟的EVE登录响应
                response = {
                    "success": True,
                    "data": {
                        "login_url": "https://login.eveonline.com/v2/oauth/authorize?response_type=code&redirect_uri=http://localhost:3000/auth/callback&client_id=demo&scope=esi-characters.read_characters.v1&state=demo_state_123",
                        "state": "demo_state_123",
                        "expires_in": 300,
                        "scopes": scopes
                    }
                }
                
                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                self.wfile.write(json.dumps(response).encode())
                
            except Exception as e:
                print(f"Error processing request: {e}")
                self.send_response(500)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                error_response = {"success": False, "message": str(e)}
                self.wfile.write(json.dumps(error_response).encode())
        else:
            self.send_response(404)
            self.end_headers()
    
    def do_OPTIONS(self):
        # 处理CORS预检请求
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()

if __name__ == "__main__":
    print("Starting simple HTTP server on port 8000...")
    try:
        server = HTTPServer(('127.0.0.1', 8000), EVELoginHandler)
        print("Server started at http://127.0.0.1:8000")
        print("You can test it with: curl http://localhost:8000/health")
        server.serve_forever()
    except Exception as e:
        print(f"Server startup failed: {e}")
        import traceback
        traceback.print_exc()
    except KeyboardInterrupt:
        print("\nServer stopped.")
        server.server_close()
