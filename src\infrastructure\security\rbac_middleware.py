"""
RBAC权限检查中间件
"""
from typing import Dict, List, Optional, Callable, Any
from functools import wraps

from fastapi import HTTPException, status, Depends, Request
from fastapi.security import HTT<PERSON><PERSON>earer

from ...domain.rbac.services import <PERSON><PERSON><PERSON><PERSON><PERSON>
from ...domain.rbac.value_objects import Permission<PERSON>ontext
from ...domain.shared.value_objects import UserId, CharacterId
from ...infrastructure.auth.middleware import AuthenticationContext, get_authenticated_user
from ...infrastructure.config.logging import get_logger

logger = get_logger(__name__)


class RBACMiddleware:
    """RBAC权限检查中间件"""
    
    def __init__(self, permission_checker: PermissionChecker):
        self.permission_checker = permission_checker
    
    def require_permission(self, resource_type: str, action_type: str,
                          resource_id_param: Optional[str] = None,
                          character_id_param: Optional[str] = None):
        """权限检查装饰器"""
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            async def wrapper(*args, **kwargs):
                # 获取认证上下文
                auth_context = None
                request = None
                
                for arg in args:
                    if isinstance(arg, Request):
                        request = arg
                        break
                
                for key, value in kwargs.items():
                    if isinstance(value, AuthenticationContext):
                        auth_context = value
                        break
                    elif isinstance(value, Request):
                        request = value
                
                if not auth_context:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="Authentication required"
                    )
                
                # 构建权限上下文
                resource_id = None
                character_id = None
                
                if resource_id_param and request:
                    resource_id = request.path_params.get(resource_id_param)
                
                if character_id_param and request:
                    character_id = request.path_params.get(character_id_param)
                elif auth_context.character_id:
                    character_id = auth_context.character_id.value
                
                permission_context = PermissionContext(
                    user_id=auth_context.user_id,
                    resource_type=resource_type,
                    action_type=action_type,
                    resource_id=resource_id,
                    character_id=character_id
                )
                
                # 检查权限
                try:
                    self.permission_checker.require_permission(
                        auth_context.user_id, 
                        permission_context
                    )
                except Exception as e:
                    logger.warning(
                        "权限检查失败",
                        user_id=str(auth_context.user_id),
                        resource_type=resource_type,
                        action_type=action_type,
                        resource_id=resource_id,
                        character_id=character_id,
                        error=str(e)
                    )
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail=f"Insufficient permissions for {resource_type}:{action_type}"
                    )
                
                return await func(*args, **kwargs)
            return wrapper
        return decorator
    
    def check_resource_access(self, resource_type: str, action_type: str = "read"):
        """资源访问权限检查"""
        def dependency(
            request: Request,
            auth: AuthenticationContext = Depends(get_authenticated_user)
        ) -> AuthenticationContext:
            
            # 从路径参数中获取资源信息
            resource_id = None
            character_id = None
            
            # 尝试从路径参数获取资源ID
            path_params = request.path_params
            for param_name, param_value in path_params.items():
                if param_name.endswith('_id') or param_name == 'id':
                    resource_id = str(param_value)
                    break
            
            # 获取角色ID
            if 'character_id' in path_params:
                character_id = int(path_params['character_id'])
            elif auth.character_id:
                character_id = auth.character_id.value
            
            # 构建权限上下文
            permission_context = PermissionContext(
                user_id=auth.user_id,
                resource_type=resource_type,
                action_type=action_type,
                resource_id=resource_id,
                character_id=character_id
            )
            
            # 检查权限
            if not self.permission_checker.check_permission(auth.user_id, permission_context):
                logger.warning(
                    "资源访问权限不足",
                    user_id=str(auth.user_id),
                    resource_type=resource_type,
                    action_type=action_type,
                    resource_id=resource_id,
                    character_id=character_id
                )
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Access denied to {resource_type}"
                )
            
            return auth
        
        return Depends(dependency)
    
    def filter_by_permissions(self, resources: List[Dict[str, Any]], 
                             resource_type: str, action_type: str,
                             auth: AuthenticationContext) -> List[Dict[str, Any]]:
        """根据权限过滤资源列表"""
        return self.permission_checker.filter_by_permissions(
            auth.user_id, resources, resource_type, action_type
        )


# 全局RBAC中间件实例（需要在应用启动时初始化）
rbac_middleware: Optional[RBACMiddleware] = None


def init_rbac_middleware(permission_checker: PermissionChecker):
    """初始化RBAC中间件"""
    global rbac_middleware
    rbac_middleware = RBACMiddleware(permission_checker)
    logger.info("RBAC中间件已初始化")


def get_rbac_middleware() -> RBACMiddleware:
    """获取RBAC中间件实例"""
    if rbac_middleware is None:
        raise RuntimeError("RBAC middleware not initialized")
    return rbac_middleware


# 便捷的权限检查装饰器
def require_permission(resource_type: str, action_type: str,
                      resource_id_param: Optional[str] = None,
                      character_id_param: Optional[str] = None):
    """权限检查装饰器"""
    def decorator(func: Callable) -> Callable:
        middleware = get_rbac_middleware()
        return middleware.require_permission(
            resource_type, action_type, resource_id_param, character_id_param
        )(func)
    return decorator


# 便捷的依赖项
def RequireResourceAccess(resource_type: str, action_type: str = "read"):
    """资源访问权限检查依赖项"""
    middleware = get_rbac_middleware()
    return middleware.check_resource_access(resource_type, action_type)


# 预定义的权限检查依赖项
RequireUserRead = lambda: RequireResourceAccess("user", "read")
RequireUserManage = lambda: RequireResourceAccess("user", "manage")
RequireCharacterRead = lambda: RequireResourceAccess("character", "read")
RequireCharacterManage = lambda: RequireResourceAccess("character", "manage")
RequireAssetRead = lambda: RequireResourceAccess("asset", "read")
RequireAssetExport = lambda: RequireResourceAccess("asset", "export")
RequireMarketRead = lambda: RequireResourceAccess("market", "read")
RequireMarketManage = lambda: RequireResourceAccess("market", "manage")
RequireIndustryRead = lambda: RequireResourceAccess("industry", "read")
RequireIndustryManage = lambda: RequireResourceAccess("industry", "manage")
RequireSystemAdmin = lambda: RequireResourceAccess("system", "admin")


class PermissionDependency:
    """权限依赖项类"""
    
    def __init__(self, resource_type: str, action_type: str):
        self.resource_type = resource_type
        self.action_type = action_type
    
    def __call__(self, 
                 request: Request,
                 auth: AuthenticationContext = Depends(get_authenticated_user)) -> AuthenticationContext:
        middleware = get_rbac_middleware()
        
        # 构建权限上下文
        permission_context = PermissionContext(
            user_id=auth.user_id,
            resource_type=self.resource_type,
            action_type=self.action_type,
            character_id=auth.character_id.value if auth.character_id else None
        )
        
        # 检查权限
        if not middleware.permission_checker.check_permission(auth.user_id, permission_context):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient permissions for {self.resource_type}:{self.action_type}"
            )
        
        return auth


# 角色级别的权限检查
class RoleBasedAccess:
    """基于角色的访问控制"""
    
    @staticmethod
    def require_role(role_name: str):
        """要求特定角色"""
        def dependency(auth: AuthenticationContext = Depends(get_authenticated_user)) -> AuthenticationContext:
            # 这里需要检查用户是否具有指定角色
            # 实际实现需要查询用户角色信息
            # 暂时简化实现
            return auth
        
        return Depends(dependency)
    
    @staticmethod
    def require_any_role(role_names: List[str]):
        """要求任意一个角色"""
        def dependency(auth: AuthenticationContext = Depends(get_authenticated_user)) -> AuthenticationContext:
            # 检查用户是否具有任意一个指定角色
            return auth
        
        return Depends(dependency)
    
    @staticmethod
    def require_all_roles(role_names: List[str]):
        """要求所有角色"""
        def dependency(auth: AuthenticationContext = Depends(get_authenticated_user)) -> AuthenticationContext:
            # 检查用户是否具有所有指定角色
            return auth
        
        return Depends(dependency)


# 预定义的角色检查
RequireAdmin = RoleBasedAccess.require_role("admin")
RequireModerator = RoleBasedAccess.require_role("moderator")
RequirePremiumUser = RoleBasedAccess.require_role("premium_user")
RequireMarketAnalyst = RoleBasedAccess.require_role("market_analyst")
RequireIndustryManager = RoleBasedAccess.require_role("industry_manager")
RequireCorporationManager = RoleBasedAccess.require_role("corporation_manager")
