-- EVE Online Assistant - 数据库初始化脚本

-- 创建数据库 (如果不存在)
-- CREATE DATABASE eve_assistant;

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 创建索引函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建基础表结构注释
COMMENT ON DATABASE eve_assistant IS 'EVE Online 游戏管理助手数据库';
