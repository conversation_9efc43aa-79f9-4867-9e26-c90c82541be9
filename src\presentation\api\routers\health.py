"""
健康检查路由
"""
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from typing import Dict, Any

from ....infrastructure.config import settings
from ....infrastructure.config.logging import get_logger
from ....infrastructure.persistence.database import get_db
from ..dependencies import get_database_session

logger = get_logger(__name__)

router = APIRouter()


@router.get("/", response_model=Dict[str, Any])
async def health_check(db: Session = Depends(get_database_session)):
    """基础健康检查"""
    try:
        # 测试数据库连接
        from sqlalchemy import text
        db.execute(text("SELECT 1"))
        db_status = "healthy"
    except Exception as e:
        logger.error("数据库健康检查失败", error=str(e))
        db_status = "unhealthy"
    
    return {
        "status": "healthy" if db_status == "healthy" else "unhealthy",
        "version": settings.app_version,
        "environment": "development" if settings.debug else "production",
        "components": {
            "database": db_status,
            "api": "healthy"
        }
    }


@router.get("/detailed", response_model=Dict[str, Any])
async def detailed_health_check(db: Session = Depends(get_database_session)):
    """详细健康检查"""
    health_status = {
        "status": "healthy",
        "timestamp": "2024-01-01T00:00:00Z",  # 实际应该使用当前时间
        "version": settings.app_version,
        "environment": "development" if settings.debug else "production",
        "components": {}
    }
    
    # 数据库检查
    try:
        from sqlalchemy import text
        db.execute(text("SELECT 1"))
        health_status["components"]["database"] = {
            "status": "healthy",
            "details": "Connection successful"
        }
    except Exception as e:
        health_status["status"] = "unhealthy"
        health_status["components"]["database"] = {
            "status": "unhealthy",
            "details": str(e)
        }
    
    # ESI API检查
    try:
        # 这里可以添加ESI API连接检查
        health_status["components"]["esi_api"] = {
            "status": "healthy",
            "details": "ESI client configured"
        }
    except Exception as e:
        health_status["components"]["esi_api"] = {
            "status": "unhealthy",
            "details": str(e)
        }
    
    # 配置检查
    config_issues = []
    if not settings.eve_sso_client_id or settings.eve_sso_client_id == "your-client-id-here":
        config_issues.append("EVE SSO Client ID not configured")
    
    if not settings.eve_sso_client_secret or settings.eve_sso_client_secret == "your-client-secret-here":
        config_issues.append("EVE SSO Client Secret not configured")
    
    if config_issues:
        health_status["components"]["configuration"] = {
            "status": "warning",
            "details": config_issues
        }
    else:
        health_status["components"]["configuration"] = {
            "status": "healthy",
            "details": "All required configuration present"
        }
    
    return health_status


@router.get("/ready", response_model=Dict[str, Any])
async def readiness_check(db: Session = Depends(get_database_session)):
    """就绪检查 - 用于Kubernetes等容器编排"""
    try:
        # 检查数据库连接
        from sqlalchemy import text
        db.execute(text("SELECT 1"))
        
        # 检查必要的配置
        if (not settings.eve_sso_client_id or 
            settings.eve_sso_client_id == "your-client-id-here"):
            return {
                "status": "not_ready",
                "reason": "EVE SSO not configured"
            }
        
        return {
            "status": "ready",
            "message": "Service is ready to accept requests"
        }
        
    except Exception as e:
        logger.error("就绪检查失败", error=str(e))
        return {
            "status": "not_ready",
            "reason": str(e)
        }


@router.get("/live", response_model=Dict[str, Any])
async def liveness_check():
    """存活检查 - 用于Kubernetes等容器编排"""
    return {
        "status": "alive",
        "message": "Service is running"
    }
