"""
宇宙静态数据相关模型
"""
from sqlalchemy import (
    Column, Integer, String, Float, Boolean,
    Text, DateTime, Index
)

from .base import BaseModel


class UniverseTypeModel(BaseModel):
    """物品类型模型"""
    __tablename__ = "universe_types"
    
    type_id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, default="")
    published = Column(Boolean, nullable=False, default=True)
    
    # 分类信息
    group_id = Column(Integer, nullable=False, index=True)
    category_id = Column(Integer, nullable=False, index=True)
    market_group_id = Column(Integer, nullable=True, index=True)
    
    # 物理属性
    mass = Column(Float, nullable=True)
    volume = Column(Float, nullable=True)
    capacity = Column(Float, nullable=True)
    portion_size = Column(Integer, nullable=True)
    radius = Column(Float, nullable=True)
    
    # 图形信息
    graphic_id = Column(Integer, nullable=True)
    icon_id = Column(Integer, nullable=True)
    sound_id = Column(Integer, nullable=True)
    
    # 数据同步
    last_sync_at = Column(DateTime, nullable=True)
    
    # 索引
    __table_args__ = (
        Index('idx_type_group_category', 'group_id', 'category_id'),
        Index('idx_type_market_group', 'market_group_id'),
        Index('idx_type_published', 'published', 'category_id'),
    )


class UniverseSystemModel(BaseModel):
    """星系模型"""
    __tablename__ = "universe_systems"
    
    system_id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False, index=True)
    constellation_id = Column(Integer, nullable=False, index=True)
    region_id = Column(Integer, nullable=False, index=True)
    
    # 安全等级
    security_status = Column(Float, nullable=False, index=True)
    security_class = Column(String(10), nullable=True)
    
    # 天体信息
    star_id = Column(Integer, nullable=True)
    
    # 数据同步
    last_sync_at = Column(DateTime, nullable=True)
    
    # 索引
    __table_args__ = (
        Index('idx_system_constellation', 'constellation_id'),
        Index('idx_system_region', 'region_id'),
        Index('idx_system_security', 'security_status'),
    )


class UniverseRegionModel(BaseModel):
    """星域模型"""
    __tablename__ = "universe_regions"
    
    region_id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, default="")
    
    # 数据同步
    last_sync_at = Column(DateTime, nullable=True)


class UniverseConstellationModel(BaseModel):
    """星座模型"""
    __tablename__ = "universe_constellations"
    
    constellation_id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False, index=True)
    region_id = Column(Integer, nullable=False, index=True)
    
    # 数据同步
    last_sync_at = Column(DateTime, nullable=True)
    
    # 索引
    __table_args__ = (
        Index('idx_constellation_region', 'region_id'),
    )
