#!/usr/bin/env python3
"""
测试production-start.py脚本
"""
import subprocess
import sys
import time
import requests

def test_ngrok_check():
    """测试ngrok检查功能"""
    print("🧪 测试ngrok检查功能...")
    
    try:
        result = subprocess.run(['ngrok', 'version'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print(f"✅ ngrok已安装: {result.stdout.strip()}")
            return True
        else:
            print("❌ ngrok未安装或无法运行")
            return False
    except (FileNotFoundError, subprocess.TimeoutExpired):
        print("❌ ngrok未找到")
        return False
    except Exception as e:
        print(f"❌ 检查ngrok时出错: {e}")
        return False

def test_python_imports():
    """测试Python模块导入"""
    print("\n🧪 测试Python模块导入...")
    
    modules_to_test = [
        ("json", "JSON模块"),
        ("requests", "Requests模块"),
        ("threading", "Threading模块"),
        ("pathlib", "Pathlib模块"),
    ]
    
    all_passed = True
    for module, name in modules_to_test:
        try:
            __import__(module)
            print(f"✅ {name}")
        except ImportError as e:
            print(f"❌ {name} - {e}")
            all_passed = False
    
    return all_passed

def test_production_script_syntax():
    """测试production-start.py脚本语法"""
    print("\n🧪 测试production-start.py脚本语法...")
    
    try:
        result = subprocess.run([
            sys.executable, '-m', 'py_compile', 
            'scripts/deployment/production-start.py'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ 脚本语法检查通过")
            return True
        else:
            print(f"❌ 脚本语法错误: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 语法检查失败: {e}")
        return False

def test_env_file_exists():
    """测试.env文件是否存在"""
    print("\n🧪 测试.env文件...")
    
    from pathlib import Path
    env_file = Path('.env')
    
    if env_file.exists():
        print("✅ .env文件存在")
        
        # 检查EVE_SSO_CALLBACK_URL配置
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if 'EVE_SSO_CALLBACK_URL=' in content:
                print("✅ EVE_SSO_CALLBACK_URL配置存在")
                
                # 提取当前配置的URL
                for line in content.split('\n'):
                    if line.startswith('EVE_SSO_CALLBACK_URL='):
                        url = line.split('=', 1)[1].strip('"')
                        print(f"   当前配置: {url}")
                        
                        if 'ngrok' in url:
                            print("✅ 检测到ngrok URL配置")
                        elif 'localhost' in url:
                            print("⚠️  检测到localhost URL配置")
                        else:
                            print("ℹ️  其他URL配置")
                        break
                
                return True
            else:
                print("❌ EVE_SSO_CALLBACK_URL配置不存在")
                return False
        except Exception as e:
            print(f"❌ 读取.env文件失败: {e}")
            return False
    else:
        print("❌ .env文件不存在")
        return False

def main():
    """主函数"""
    print("🚀 production-start.py 功能测试")
    print("=" * 50)
    
    tests = [
        ("ngrok检查", test_ngrok_check),
        ("Python模块导入", test_python_imports),
        ("脚本语法检查", test_production_script_syntax),
        ("环境文件检查", test_env_file_exists),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"⚠️  {test_name} 测试未通过")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！production-start.py 应该可以正常工作")
        print("\n📝 使用方法:")
        print("   python scripts/deployment/production-start.py")
        return 0
    else:
        print("⚠️  部分测试未通过，请检查相关配置")
        return 1

if __name__ == "__main__":
    sys.exit(main())
