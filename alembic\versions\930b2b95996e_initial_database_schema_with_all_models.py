"""Initial database schema with all models

Revision ID: 930b2b95996e
Revises: 
Create Date: 2025-08-06 22:44:28.723784

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '930b2b95996e'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """升级数据库"""
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    """降级数据库"""
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
