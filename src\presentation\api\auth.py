"""
认证API端点
"""
from typing import Dict, Any, List
from datetime import datetime

from fastapi import APIRouter, HTTPException, status, Depends, Request, Response
from pydantic import BaseModel, EmailStr, Field

from ...application.auth.authentication_service import AuthenticationApplicationService
from ...infrastructure.auth.middleware import (
    get_authenticated_user, get_current_user, AuthenticationContext
)
from ...domain.shared.value_objects import UserId
from ...domain.auth.exceptions import (
    UserAlreadyExistsError, InvalidCredentialsError, AccountLockedError,
    AccountInactiveError, EmailNotVerifiedError
)
from ...infrastructure.config.logging import get_logger

logger = get_logger(__name__)

router = APIRouter(prefix="/auth", tags=["authentication"])


# 请求模型
class UserRegistrationRequest(BaseModel):
    """用户注册请求"""
    email: EmailStr = Field(..., description="用户邮箱")
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    password: str = Field(..., min_length=8, max_length=128, description="密码")
    confirm_password: str = Field(..., description="确认密码")
    
    def validate_passwords_match(self):
        if self.password != self.confirm_password:
            raise ValueError("Passwords do not match")


class UserLoginRequest(BaseModel):
    """用户登录请求"""
    email: EmailStr = Field(..., description="用户邮箱")
    password: str = Field(..., description="密码")
    remember_me: bool = Field(default=False, description="记住我")


class EVELoginRequest(BaseModel):
    """EVE SSO登录请求"""
    scopes: List[str] = Field(..., description="请求的权限范围")


class EVECallbackRequest(BaseModel):
    """EVE SSO回调请求"""
    code: str = Field(..., description="授权码")
    state: str = Field(..., description="状态参数")


class RefreshTokenRequest(BaseModel):
    """刷新令牌请求"""
    refresh_token: str = Field(..., description="刷新令牌")


class LogoutRequest(BaseModel):
    """登出请求"""
    all_sessions: bool = Field(default=False, description="登出所有会话")


# 响应模型
class AuthResponse(BaseModel):
    """认证响应"""
    success: bool
    message: str = ""
    access_token: str = ""
    refresh_token: str = ""
    token_type: str = "Bearer"
    expires_in: int = 0
    user: Dict[str, Any] = {}


class EVELoginResponse(BaseModel):
    """EVE登录响应"""
    success: bool
    login_url: str
    state: str
    expires_in: int
    scopes: List[str]


class UserInfoResponse(BaseModel):
    """用户信息响应"""
    user_id: str
    email: str
    username: str
    is_active: bool
    is_verified: bool
    created_at: datetime
    last_login_at: datetime = None
    character_count: int
    main_character_id: int = None


# API端点
@router.post("/register", response_model=AuthResponse)
async def register_user(
    request: UserRegistrationRequest,
    auth_service: AuthenticationApplicationService = Depends()
):
    """用户注册"""
    try:
        # 验证密码匹配
        request.validate_passwords_match()
        
        result = await auth_service.register_user(
            email=request.email,
            username=request.username,
            password=request.password
        )
        
        return AuthResponse(
            success=True,
            message="Registration successful. Please check your email for verification."
        )
        
    except UserAlreadyExistsError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=f"User with {e.identifier_type} '{e.identifier}' already exists"
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error("用户注册失败", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )


@router.post("/login", response_model=AuthResponse)
async def login_user(
    request: UserLoginRequest,
    auth_service: AuthenticationApplicationService = Depends()
):
    """用户登录"""
    try:
        result = await auth_service.login_user(
            email=request.email,
            password=request.password,
            remember_me=request.remember_me
        )
        
        return AuthResponse(
            success=True,
            message="Login successful",
            access_token=result["access_token"],
            refresh_token=result["refresh_token"],
            token_type=result["token_type"],
            expires_in=result["expires_in"],
            user=result["user"]
        )
        
    except InvalidCredentialsError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid email or password"
        )
    except AccountLockedError as e:
        raise HTTPException(
            status_code=status.HTTP_423_LOCKED,
            detail=f"Account is locked until {e.locked_until}"
        )
    except AccountInactiveError:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Account is inactive"
        )
    except Exception as e:
        logger.error("用户登录失败", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )


@router.post("/logout")
async def logout_user(
    request: LogoutRequest,
    auth: AuthenticationContext = Depends(get_authenticated_user),
    auth_service: AuthenticationApplicationService = Depends()
):
    """用户登出"""
    try:
        result = await auth_service.logout_user(
            user_id=auth.user_id,
            all_sessions=request.all_sessions
        )
        
        return {"success": True, "message": "Logged out successfully"}
        
    except Exception as e:
        logger.error("用户登出失败", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout failed"
        )


@router.post("/refresh", response_model=AuthResponse)
async def refresh_token(
    request: RefreshTokenRequest,
    auth_service: AuthenticationApplicationService = Depends()
):
    """刷新访问令牌"""
    try:
        result = await auth_service.refresh_token(request.refresh_token)
        
        return AuthResponse(
            success=True,
            message="Token refreshed successfully",
            access_token=result["access_token"],
            refresh_token=result["refresh_token"],
            token_type=result["token_type"],
            expires_in=result["expires_in"]
        )
        
    except InvalidCredentialsError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )
    except Exception as e:
        logger.error("令牌刷新失败", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed"
        )


@router.post("/eve/login", response_model=EVELoginResponse)
async def initiate_eve_login(
    request: EVELoginRequest,
    auth: AuthenticationContext = Depends(get_authenticated_user),
    auth_service: AuthenticationApplicationService = Depends()
):
    """发起EVE SSO登录"""
    try:
        result = await auth_service.initiate_eve_login(
            user_id=auth.user_id,
            scopes=request.scopes
        )
        
        return EVELoginResponse(
            success=True,
            login_url=result["login_url"],
            state=result["state"],
            expires_in=result["expires_in"],
            scopes=result["scopes"]
        )
        
    except Exception as e:
        logger.error("EVE SSO登录发起失败", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="EVE login initiation failed"
        )


@router.post("/eve/callback")
async def eve_login_callback(
    request: EVECallbackRequest,
    auth: AuthenticationContext = Depends(get_authenticated_user),
    auth_service: AuthenticationApplicationService = Depends()
):
    """处理EVE SSO回调"""
    try:
        result = await auth_service.complete_eve_login(
            user_id=auth.user_id,
            authorization_code=request.code,
            state=request.state
        )
        
        return {
            "success": True,
            "message": "EVE character bound successfully",
            "character_id": result["character_id"],
            "character_name": result["character_name"],
            "scopes": result["scopes"]
        }
        
    except Exception as e:
        logger.error("EVE SSO回调处理失败", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="EVE login callback failed"
        )


@router.get("/me", response_model=UserInfoResponse)
async def get_current_user_info(
    auth: AuthenticationContext = Depends(get_authenticated_user)
):
    """获取当前用户信息"""
    # 这里需要从数据库获取完整的用户信息
    # 暂时返回基本信息
    return UserInfoResponse(
        user_id=str(auth.user_id.value),
        email="",  # 需要从数据库获取
        username="",  # 需要从数据库获取
        is_active=True,
        is_verified=True,
        created_at=datetime.utcnow(),
        character_count=0,
        main_character_id=auth.character_id.value if auth.character_id else None
    )


@router.get("/verify-session")
async def verify_session(
    auth: AuthenticationContext = Depends(get_current_user)
):
    """验证会话状态"""
    if auth:
        return {
            "valid": True,
            "user_id": auth.user_id.value,
            "character_id": auth.character_id.value if auth.character_id else None,
            "scopes": auth.scopes
        }
    else:
        return {"valid": False}


@router.get("/health")
async def auth_health_check():
    """认证服务健康检查"""
    return {
        "status": "healthy",
        "service": "authentication",
        "timestamp": datetime.utcnow().isoformat()
    }
