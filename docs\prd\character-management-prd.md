# 角色管理模块 - 产品需求文档 (PRD)

## 📋 模块概述

### 模块名称
角色管理模块 (Character Management Module)

### 模块愿景
为EVE Online玩家提供全面的角色信息管理、技能规划和角色发展跟踪服务，帮助玩家优化角色发展路径和提升游戏体验。

### 业务价值
- 🎯 **角色优化**: 帮助玩家制定最优的技能训练计划
- 🎯 **数据洞察**: 提供角色发展的深度分析和建议
- 🎯 **多角色管理**: 支持管理多个EVE角色的统一视图
- 🎯 **历史跟踪**: 记录角色发展历史和里程碑

## 🎯 功能需求

### 1. 角色基础信息管理

#### 1.1 角色档案
**功能描述**: 管理角色的基本信息和档案数据

**核心功能**:
- ✅ 角色基本信息展示（姓名、种族、血统、性别）
- ✅ 角色头像和肖像管理
- ✅ 角色创建时间和年龄显示
- ✅ 角色所属公司和联盟信息
- ✅ 角色当前位置和状态

**数据模型**:
```python
class Character(AggregateRoot):
    character_id: CharacterId
    name: str
    race: Race
    bloodline: Bloodline
    gender: Gender
    birthday: datetime
    corporation_id: CorporationId
    alliance_id: Optional[AllianceId]
    current_location: Location
    online_status: OnlineStatus
```

#### 1.2 角色状态跟踪
**功能描述**: 实时跟踪角色的在线状态和位置信息

**核心功能**:
- 🔄 实时在线状态监控
- 🔄 位置变化历史记录
- 🔄 登录/登出时间统计
- 🔄 活跃度分析报告

### 2. 技能系统管理

#### 2.1 技能概览
**功能描述**: 全面展示角色的技能状况和能力

**核心功能**:
- ✅ 技能点总数和分布统计
- ✅ 技能等级可视化展示
- ✅ 技能分类和筛选功能
- ✅ 技能搜索和快速定位
- ✅ 技能效果和加成说明

**数据模型**:
```python
class Skill(ValueObject):
    skill_id: SkillId
    skill_name: str
    skill_level: int  # 0-5
    skill_points: SkillPoints
    skill_group: SkillGroup
    trained_skill_level: int
    active_skill_level: int
```

#### 2.2 技能训练队列
**功能描述**: 管理和优化技能训练队列

**核心功能**:
- ✅ 当前训练队列展示
- ✅ 训练进度实时更新
- ✅ 训练完成时间预估
- ✅ 队列优化建议
- ✅ 训练历史记录

**数据模型**:
```python
class SkillQueue(ValueObject):
    queue_position: int
    skill_id: SkillId
    finished_level: int
    training_start_sp: SkillPoints
    level_end_sp: SkillPoints
    start_date: datetime
    finish_date: datetime
```

#### 2.3 技能规划器
**功能描述**: 智能技能训练规划和路径优化

**核心功能**:
- 🚀 目标导向的技能规划
- 🚀 多种规划模板（PvP、PvE、工业、贸易）
- 🚀 训练时间优化算法
- 🚀 前置技能自动计算
- 🚀 植入体效果计算
- 🚀 训练成本分析

### 3. 角色属性管理

#### 3.1 基础属性
**功能描述**: 管理角色的基础属性和加成

**核心功能**:
- ✅ 五大属性展示（感知、记忆、意志、智力、魅力）
- ✅ 属性加成来源分析
- ✅ 属性重分配历史
- ✅ 属性对技能训练的影响计算

**数据模型**:
```python
class Attributes(ValueObject):
    perception: int
    memory: int
    willpower: int
    intelligence: int
    charisma: int
    bonus_remaps: int
    last_remap_date: Optional[datetime]
    accrued_remap_cooldown_date: Optional[datetime]
```

#### 3.2 植入体管理
**功能描述**: 管理角色的植入体和加成效果

**核心功能**:
- ✅ 当前植入体展示
- ✅ 植入体效果计算
- ✅ 植入体组合优化建议
- ✅ 植入体价值评估

### 4. 克隆体管理

#### 4.1 克隆体状态
**功能描述**: 管理角色的克隆体信息

**核心功能**:
- ✅ 当前克隆体位置
- ✅ 克隆体植入体配置
- ✅ 跳跃克隆体列表
- ✅ 克隆体跳跃历史

**数据模型**:
```python
class Clone(ValueObject):
    clone_type: CloneType  # current, jump
    location_id: LocationId
    location_name: str
    implants: List[Implant]
    jump_clone_id: Optional[int]
```

### 5. 钱包管理

#### 5.1 钱包概览
**功能描述**: 管理角色的ISK资产

**核心功能**:
- ✅ 当前ISK余额
- ✅ 钱包交易历史
- ✅ 收支分类统计
- ✅ 财务趋势分析

## 🔧 技术实现

### 领域模型设计

#### 聚合根: Character
```python
class Character(AggregateRoot):
    def __init__(self, character_id: CharacterId, name: str, ...):
        super().__init__(character_id)
        self._validate_character_data(name, ...)
        # 初始化角色数据
    
    def update_skills(self, skills: List[Skill]) -> None:
        """更新技能数据"""
        self._skills = skills
        self._raise_event(CharacterSkillsUpdatedEvent(self.id, skills))
    
    def plan_skill_training(self, target_skills: List[SkillTarget]) -> TrainingPlan:
        """制定技能训练计划"""
        return self._skill_planner.create_plan(self._skills, target_skills)
```

#### 领域服务
```python
class SkillTrainingService(DomainService):
    def calculate_training_time(
        self, 
        character: Character, 
        skill_queue: List[SkillQueue]
    ) -> TrainingTimeCalculation:
        """计算技能训练时间"""
        
    def optimize_training_order(
        self, 
        skills: List[SkillTarget], 
        attributes: Attributes
    ) -> List[SkillQueue]:
        """优化技能训练顺序"""
```

### 应用服务

#### CharacterApplicationService
```python
class CharacterApplicationService:
    async def get_character_overview(
        self, 
        character_id: CharacterId
    ) -> CharacterOverviewDTO:
        """获取角色概览"""
        
    async def update_character_data(
        self, 
        character_id: CharacterId
    ) -> None:
        """更新角色数据"""
        
    async def create_skill_plan(
        self, 
        character_id: CharacterId, 
        plan_request: SkillPlanRequestDTO
    ) -> SkillPlanDTO:
        """创建技能规划"""
```

### 数据同步策略

#### 实时数据 (每5分钟)
- 在线状态
- 当前位置
- 技能训练队列

#### 频繁数据 (每30分钟)
- 技能点变化
- 钱包余额
- 植入体状态

#### 常规数据 (每2小时)
- 技能列表
- 角色属性
- 克隆体信息

#### 静态数据 (每日)
- 角色基本信息
- 公司联盟信息

## 📊 用户界面设计

### 1. 角色概览页面
```
┌─────────────────────────────────────────────────────────────┐
│ 角色概览 - [角色名称]                                        │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────┐  ┌─────────────────────────────────────────┐ │
│ │   角色头像   │  │ 基本信息                                 │ │
│ │             │  │ 种族: Caldari  血统: Civire             │ │
│ │             │  │ 公司: [公司名]  联盟: [联盟名]           │ │
│ │             │  │ 位置: Jita IV - Moon 4                  │ │
│ └─────────────┘  │ 状态: 在线 (2小时前)                    │ │
│                  └─────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 快速统计                                                     │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│ │ 总技能点     │ │ 可飞船只     │ │ ISK余额      │ │ 训练中   │ │
│ │ 45,234,567  │ │ 156艘       │ │ 2.3B ISK    │ │ 2天3小时 │ │
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2. 技能管理页面
```
┌─────────────────────────────────────────────────────────────┐
│ 技能管理                                                     │
├─────────────────────────────────────────────────────────────┤
│ [搜索框] [筛选] [分组: 技能组▼] [排序: 技能点▼]              │
├─────────────────────────────────────────────────────────────┤
│ ┌─ 战斗技能 ────────────────────────────────────────────────┐ │
│ │ ✓ Gunnery                    V    ████████████ 5,120,000 │ │
│ │ ✓ Small Hybrid Turret        V    ████████████ 2,560,000 │ │
│ │ ○ Medium Hybrid Turret       III  ████░░░░░░░░   512,000 │ │
│ └─────────────────────────────────────────────────────────┘ │
│ ┌─ 工程技能 ────────────────────────────────────────────────┐ │
│ │ ✓ Engineering                V    ████████████ 5,120,000 │ │
│ │ ✓ Shield Management          IV   ████████░░░░ 1,280,000 │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 3. 技能规划页面
```
┌─────────────────────────────────────────────────────────────┐
│ 技能规划器                                                   │
├─────────────────────────────────────────────────────────────┤
│ 规划目标: [选择模板▼] [自定义目标]                           │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 目标: 驾驶战列舰                                         │ │
│ │ 预计时间: 45天 12小时                                    │ │
│ │ 所需技能点: 2,450,000 SP                                │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 训练计划:                                                    │
│ 1. Spaceship Command V        (当前III → V)    15天 4小时   │
│ 2. Battleship I               (未学习 → I)      2天 8小时   │
│ 3. Gunnery V                  (当前IV → V)     28天 0小时   │
│ ────────────────────────────────────────────────────────── │
│ [优化顺序] [添加到队列] [保存计划] [分享计划]                │
└─────────────────────────────────────────────────────────────┘
```

## 🔍 测试策略

### 单元测试
- 领域模型测试覆盖率 > 95%
- 业务规则验证测试
- 技能计算算法测试

### 集成测试
- ESI API集成测试
- 数据同步流程测试
- 缓存机制测试

### 端到端测试
- 用户操作流程测试
- 性能基准测试
- 数据一致性测试

## 📈 成功指标

### 功能指标
- 角色数据同步准确率 > 99.5%
- 技能规划计算准确率 > 99.9%
- 页面加载时间 < 2秒

### 用户体验指标
- 用户满意度评分 > 4.5/5.0
- 功能使用率 > 80%
- 用户留存率 > 90%

---

**文档版本**: v1.0  
**最后更新**: 2025-01-09  
**负责人**: 角色管理团队
