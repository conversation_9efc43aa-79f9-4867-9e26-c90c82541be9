#!/usr/bin/env python3
"""
EVE SSO权限配置验证工具
验证.env文件中的权限配置是否正确
"""
import re
from pathlib import Path


def verify_scopes_config():
    """验证权限配置"""
    project_root = Path(__file__).parent.parent
    env_file = project_root / ".env"
    
    if not env_file.exists():
        print("❌ .env文件不存在")
        return False
    
    with open(env_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 提取EVE_SSO_SCOPES配置
    match = re.search(r'EVE_SSO_SCOPES="([^"]*)"', content, re.DOTALL)
    if not match:
        print("❌ 未找到EVE_SSO_SCOPES配置")
        return False
    
    scopes_content = match.group(1)
    
    # 解析权限列表
    scopes = []
    for line in scopes_content.split('\n'):
        line = line.strip()
        if line and not line.startswith('#'):
            # 提取权限名称（去掉注释）
            scope_name = line.split('#')[0].strip()
            if scope_name and not scope_name.startswith('#'):
                scopes.append(scope_name)
    
    print("🎉 EVE SSO权限配置验证结果")
    print("=" * 50)
    print(f"📊 总权限数量: {len(scopes)}")
    
    # 按分类统计
    categories = {
        "基础权限": ["publicData"],
        "位置状态": [s for s in scopes if "location" in s],
        "技能系统": [s for s in scopes if "skills" in s],
        "克隆体": [s for s in scopes if "clones" in s],
        "资产财务": [s for s in scopes if "assets" in s or "wallet" in s],
        "角色信息": [s for s in scopes if "characters" in s],
        "公司管理": [s for s in scopes if "corporations" in s],
        "联盟管理": [s for s in scopes if "alliances" in s],
        "通讯系统": [s for s in scopes if "mail" in s or "calendar" in s],
        "合同系统": [s for s in scopes if "contracts" in s],
        "工业系统": [s for s in scopes if "industry" in s],
        "市场系统": [s for s in scopes if "markets" in s],
        "战斗记录": [s for s in scopes if "killmails" in s],
        "舰队管理": [s for s in scopes if "fleets" in s or "fittings" in s],
        "行星管理": [s for s in scopes if "planets" in s],
        "宇宙数据": [s for s in scopes if "search" in s or "universe" in s],
        "用户界面": [s for s in scopes if "ui" in s]
    }
    
    print("\n📋 分类统计:")
    for category, category_scopes in categories.items():
        if category_scopes:
            print(f"   {category}: {len(category_scopes)}个")
    
    # 检查必需权限
    print("\n🔍 关键权限检查:")
    if "publicData" in scopes:
        print("   ✅ publicData - 基础权限已配置")
    else:
        print("   ❌ publicData - 缺少基础权限")
    
    # 检查常用权限
    common_scopes = [
        "esi-location.read_location.v1",
        "esi-skills.read_skills.v1",
        "esi-assets.read_assets.v1",
        "esi-wallet.read_character_wallet.v1"
    ]
    
    print("\n🎯 常用权限检查:")
    for scope in common_scopes:
        if scope in scopes:
            print(f"   ✅ {scope}")
        else:
            print(f"   ❌ {scope}")
    
    print(f"\n✅ 配置验证完成！共发现 {len(scopes)} 个有效权限")
    return True


if __name__ == "__main__":
    verify_scopes_config()
