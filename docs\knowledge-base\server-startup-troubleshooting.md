# 服务器启动问题排查知识库

## 问题分类

### 1. FastAPI/Uvicorn启动问题

#### 1.1 文件监控导致的崩溃

**问题描述**：
- 服务器启动后立即退出
- 错误信息：`FileNotFoundError: 'fsevents'` 或类似文件监控错误
- 在Windows系统上尝试使用macOS特有的文件监控

**解决方案**：
```bash
# 限制监控目录范围
uvicorn src.presentation.api.main:app --reload --reload-dir src

# 排除特定目录
uvicorn src.presentation.api.main:app --reload --reload-exclude frontend/node_modules
```

**根本原因**：
- uvicorn默认监控整个项目目录
- frontend/node_modules包含大量文件和平台特定的包
- 文件监控系统无法处理某些特殊文件

#### 1.2 依赖注入链断裂

**问题描述**：
- API请求返回500错误
- 错误发生在依赖解析阶段
- 通常是某个服务类无法实例化

**诊断方法**：
1. 检查错误堆栈中的具体模块
2. 验证该模块的所有导入
3. 检查依赖注入配置

**解决步骤**：
1. 修复缺失的模块文件
2. 更新__init__.py中的导入
3. 验证服务类的构造函数

### 2. 端口和网络问题

#### 2.1 端口被占用

**诊断命令**：
```bash
# Windows
netstat -ano | findstr :8000

# Linux/Mac
lsof -i :8000
```

**解决方案**：
1. 终止占用端口的进程
2. 使用不同的端口
3. 配置端口复用

#### 2.2 防火墙阻止

**检查方法**：
- 尝试从本地访问：`curl http://localhost:8000/health`
- 检查防火墙设置
- 验证host绑定配置

### 3. 前端服务器问题

#### 3.1 Node.js/npm问题

**常见问题**：
- 依赖安装不完整
- Node.js版本不兼容
- npm缓存问题

**解决方案**：
```bash
# 清理并重新安装
rm -rf node_modules package-lock.json
npm install

# 清理npm缓存
npm cache clean --force

# 使用yarn作为替代
yarn install
```

#### 3.2 Vite配置问题

**检查配置**：
```javascript
// vite.config.ts
export default defineConfig({
  server: {
    port: 3000,
    host: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  }
})
```

## 启动流程标准化

### 1. 后端启动检查清单

- [ ] Python环境激活
- [ ] 依赖包安装完成
- [ ] 项目包安装（pip install -e .）
- [ ] 数据库连接正常
- [ ] 环境变量配置
- [ ] 端口可用性检查

### 2. 前端启动检查清单

- [ ] Node.js版本兼容
- [ ] 依赖安装完成
- [ ] 环境变量配置
- [ ] 代理配置正确
- [ ] 端口可用性检查

### 3. 集成启动脚本

```python
#!/usr/bin/env python3
"""统一启动脚本"""
import subprocess
import sys
import time
import requests

def check_backend():
    """检查后端服务"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def check_frontend():
    """检查前端服务"""
    try:
        response = requests.get("http://localhost:3000", timeout=5)
        return response.status_code == 200
    except:
        return False

def start_backend():
    """启动后端服务"""
    print("🚀 启动后端服务...")
    return subprocess.Popen([
        sys.executable, "-m", "uvicorn",
        "src.presentation.api.main:app",
        "--reload", "--reload-dir", "src",
        "--host", "127.0.0.1", "--port", "8000"
    ])

def start_frontend():
    """启动前端服务"""
    print("🚀 启动前端服务...")
    return subprocess.Popen(
        ["npm", "run", "dev"],
        cwd="frontend"
    )

def main():
    """主启动流程"""
    # 启动后端
    backend_proc = start_backend()
    time.sleep(5)
    
    if not check_backend():
        print("❌ 后端启动失败")
        return 1
    
    # 启动前端
    frontend_proc = start_frontend()
    time.sleep(10)
    
    if not check_frontend():
        print("❌ 前端启动失败")
        return 1
    
    print("✅ 所有服务启动成功")
    print("   后端: http://localhost:8000")
    print("   前端: http://localhost:3000")
    
    try:
        backend_proc.wait()
    except KeyboardInterrupt:
        print("\n🛑 停止所有服务...")
        backend_proc.terminate()
        frontend_proc.terminate()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
```

## 监控和健康检查

### 1. 健康检查端点

```python
@app.get("/health")
async def health_check():
    """健康检查"""
    checks = {
        "database": check_database(),
        "redis": check_redis(),
        "external_apis": check_external_apis(),
    }
    
    all_healthy = all(checks.values())
    status_code = 200 if all_healthy else 503
    
    return JSONResponse(
        status_code=status_code,
        content={
            "status": "healthy" if all_healthy else "unhealthy",
            "checks": checks,
            "timestamp": datetime.utcnow().isoformat()
        }
    )
```

### 2. 启动时验证

```python
async def startup_event():
    """应用启动时的验证"""
    logger.info("🚀 启动 EVE Online Assistant API")
    
    # 验证关键模块导入
    try:
        from .routers import auth, characters
        logger.info("✅ 路由模块导入成功")
    except ImportError as e:
        logger.error(f"❌ 路由模块导入失败: {e}")
        raise
    
    # 验证数据库连接
    try:
        await database.connect()
        logger.info("✅ 数据库连接成功")
    except Exception as e:
        logger.error(f"❌ 数据库连接失败: {e}")
        raise
    
    # 验证外部API
    try:
        await verify_external_apis()
        logger.info("✅ 外部API验证成功")
    except Exception as e:
        logger.warning(f"⚠️ 外部API验证失败: {e}")
```

## 故障恢复

### 1. 自动重启机制

```bash
#!/bin/bash
# 服务监控脚本
while true; do
    if ! curl -f http://localhost:8000/health > /dev/null 2>&1; then
        echo "服务异常，重启中..."
        pkill -f uvicorn
        sleep 5
        python scripts/start.py &
    fi
    sleep 30
done
```

### 2. 优雅关闭

```python
async def shutdown_event():
    """应用关闭时的清理"""
    logger.info("🛑 关闭 EVE Online Assistant API")
    
    # 关闭数据库连接
    await database.disconnect()
    
    # 清理缓存
    await cache.clear()
    
    # 保存状态
    await save_application_state()
```

## 性能优化

### 1. 启动时间优化

- 延迟加载非关键模块
- 使用连接池预热
- 缓存配置信息
- 并行初始化独立组件

### 2. 资源使用优化

- 限制工作进程数量
- 配置合适的超时时间
- 使用异步I/O
- 监控内存使用

---

*最后更新：2025-08-10*
*相关问题：EVE登录功能服务器启动问题修复*
