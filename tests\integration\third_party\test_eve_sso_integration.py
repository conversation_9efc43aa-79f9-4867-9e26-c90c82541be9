"""
EVE SSO集成测试套件
测试EVE SSO登录流程的完整性
"""
import pytest
import requests
import json
from unittest.mock import Mock, patch
from typing import Dict, Any
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.infrastructure.esi import EVESSOClient
from src.application.auth import AuthenticationService
from src.infrastructure.config import settings


class TestEVESSOIntegration:
    """EVE SSO集成测试"""
    
    @pytest.fixture
    def sso_client(self):
        """创建SSO客户端实例"""
        return EVESSOClient()
    
    @pytest.fixture
    def auth_service(self, sso_client):
        """创建认证服务实例"""
        return AuthenticationService(sso_client)
    
    def test_scope_validity(self, sso_client):
        """测试所有配置的scope都是有效的"""
        # 获取配置的scopes
        configured_scopes = settings.eve_sso_scopes.split()
        
        # 已知的有效scopes
        valid_scopes = {
            'esi-location.read_location.v1',
            'esi-location.read_online.v1',
            'esi-location.read_ship_type.v1',
            'esi-skills.read_skills.v1',
            'esi-skills.read_skillqueue.v1',
            'esi-assets.read_assets.v1',
            'esi-wallet.read_character_wallet.v1',
            'esi-clones.read_clones.v1',
            'esi-mail.read_mail.v1',
            'esi-contracts.read_character_contracts.v1',
            'esi-industry.read_character_jobs.v1',
            'esi-markets.read_character_orders.v1'
        }
        
        # 已知的无效scopes
        invalid_scopes = {
            'esi-characters.read_characters.v1',
            'esi-character.read_character.v1'
        }
        
        # 检查配置的scopes
        for scope in configured_scopes:
            assert scope in valid_scopes, f"无效的scope: {scope}"
            assert scope not in invalid_scopes, f"使用了已知无效的scope: {scope}"
    
    def test_login_url_generation(self, auth_service):
        """测试登录URL生成"""
        # 测试基本登录URL生成
        login_url, state = auth_service.initiate_login()
        
        assert login_url is not None
        assert state is not None
        assert 'login.eveonline.com' in login_url
        assert 'client_id' in login_url
        assert 'redirect_uri' in login_url
        assert 'response_type=code' in login_url
        assert 'state=' in login_url
        
        # 确保URL中不包含无效的scope
        assert 'esi-characters.read_characters.v1' not in login_url
    
    def test_login_url_with_custom_scopes(self, auth_service):
        """测试使用自定义scopes生成登录URL"""
        custom_scopes = [
            'esi-location.read_location.v1',
            'esi-skills.read_skills.v1'
        ]
        
        login_url, state = auth_service.initiate_login(scopes=custom_scopes)
        
        assert login_url is not None
        assert state is not None
        
        # 检查URL中包含正确的scopes
        for scope in custom_scopes:
            assert scope in login_url
    
    @patch('requests.post')
    def test_token_exchange_success(self, mock_post, auth_service):
        """测试成功的令牌交换"""
        # 模拟成功的令牌响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'access_token': 'test_access_token',
            'token_type': 'Bearer',
            'expires_in': 1200,
            'refresh_token': 'test_refresh_token'
        }
        mock_post.return_value = mock_response
        
        # 测试令牌交换
        result = auth_service.exchange_code_for_token('test_code', 'test_state')
        
        assert result is not None
        assert result.get('access_token') == 'test_access_token'
        assert result.get('refresh_token') == 'test_refresh_token'
        
        # 验证请求参数
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        assert 'grant_type=authorization_code' in call_args[1]['data']
        assert 'code=test_code' in call_args[1]['data']
    
    @patch('requests.post')
    def test_token_exchange_invalid_scope_error(self, mock_post, auth_service):
        """测试无效scope的令牌交换错误"""
        # 模拟无效scope错误响应
        mock_response = Mock()
        mock_response.status_code = 400
        mock_response.json.return_value = {
            'error': 'invalid_scope',
            'error_description': "The requested 'esi-characters.read_characters.v1' scope is not valid."
        }
        mock_post.return_value = mock_response
        
        # 测试令牌交换应该抛出异常
        with pytest.raises(Exception) as exc_info:
            auth_service.exchange_code_for_token('test_code', 'test_state')
        
        assert 'invalid_scope' in str(exc_info.value)
    
    @patch('requests.get')
    def test_character_info_retrieval(self, mock_get, auth_service):
        """测试角色信息获取"""
        # 模拟JWT验证响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'CharacterID': 123456789,
            'CharacterName': 'Test Character',
            'CharacterOwnerHash': 'test_hash',
            'ExpiresOn': '2024-12-31T23:59:59'
        }
        mock_get.return_value = mock_response
        
        # 测试角色信息获取
        character_info = auth_service.get_character_info('test_access_token')
        
        assert character_info is not None
        assert character_info.get('CharacterID') == 123456789
        assert character_info.get('CharacterName') == 'Test Character'
    
    def test_oauth_endpoints_accessibility(self):
        """测试OAuth端点的可访问性"""
        endpoints = {
            'authorization': 'https://login.eveonline.com/v2/oauth/authorize',
            'token': 'https://login.eveonline.com/v2/oauth/token',
            'verify': 'https://login.eveonline.com/oauth/verify'
        }
        
        for name, url in endpoints.items():
            try:
                response = requests.head(url, timeout=5)
                # 对于OAuth端点，4xx响应是正常的（需要参数）
                assert response.status_code < 500, f"端点 {name} 返回服务器错误: {response.status_code}"
            except requests.RequestException as e:
                pytest.fail(f"无法访问端点 {name} ({url}): {str(e)}")
    
    def test_esi_base_endpoint_accessibility(self):
        """测试ESI基础端点的可访问性"""
        esi_base = 'https://esi.evetech.net'
        
        try:
            response = requests.get(f"{esi_base}/ping", timeout=5)
            assert response.status_code == 200
        except requests.RequestException as e:
            pytest.fail(f"无法访问ESI基础端点: {str(e)}")
    
    def test_configuration_completeness(self):
        """测试配置的完整性"""
        required_settings = [
            'eve_client_id',
            'eve_client_secret',
            'eve_callback_url',
            'eve_sso_scopes'
        ]
        
        for setting in required_settings:
            value = getattr(settings, setting, None)
            assert value is not None, f"缺少必需的配置: {setting}"
            assert value != '', f"配置 {setting} 不能为空"
    
    def test_callback_url_format(self):
        """测试回调URL格式的正确性"""
        callback_url = settings.eve_callback_url
        
        assert callback_url.startswith('http'), "回调URL必须以http开头"
        assert '/auth/callback' in callback_url, "回调URL应包含/auth/callback路径"
    
    @patch('requests.post')
    def test_token_refresh(self, mock_post, auth_service):
        """测试令牌刷新机制"""
        # 模拟成功的令牌刷新响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'access_token': 'new_access_token',
            'token_type': 'Bearer',
            'expires_in': 1200,
            'refresh_token': 'new_refresh_token'
        }
        mock_post.return_value = mock_response
        
        # 测试令牌刷新
        result = auth_service.refresh_token('old_refresh_token')
        
        assert result is not None
        assert result.get('access_token') == 'new_access_token'
        assert result.get('refresh_token') == 'new_refresh_token'
        
        # 验证请求参数
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        assert 'grant_type=refresh_token' in call_args[1]['data']
        assert 'refresh_token=old_refresh_token' in call_args[1]['data']
    
    def test_error_handling_completeness(self, auth_service):
        """测试错误处理的完整性"""
        # 测试各种错误场景的处理
        error_scenarios = [
            ('invalid_scope', 'invalid_scope'),
            ('invalid_client', 'invalid_client'),
            ('access_denied', 'access_denied'),
            ('server_error', 'server_error')
        ]
        
        for error_code, expected_handling in error_scenarios:
            # 这里可以测试错误处理逻辑
            # 具体实现取决于错误处理的设计
            pass
    
    def test_scope_categories_coverage(self):
        """测试scope分类的覆盖性"""
        configured_scopes = settings.eve_sso_scopes.split()
        
        # 按功能分类检查scope覆盖
        scope_categories = {
            'location': ['esi-location.read_location.v1', 'esi-location.read_online.v1'],
            'skills': ['esi-skills.read_skills.v1'],
            'assets': ['esi-assets.read_assets.v1'],
            'wallet': ['esi-wallet.read_character_wallet.v1']
        }
        
        for category, scopes in scope_categories.items():
            category_covered = any(scope in configured_scopes for scope in scopes)
            if not category_covered:
                # 这是警告而不是错误，因为不是所有功能都必需
                print(f"警告: {category} 类别的scope未配置")


class TestConfigSyncIntegration:
    """配置同步集成测试"""
    
    def test_frontend_backend_scope_consistency(self):
        """测试前后端scope配置一致性"""
        # 获取后端配置的scopes
        backend_scopes = set(settings.eve_sso_scopes.split())
        
        # 从前端代码中提取scopes（这里需要实际的文件读取逻辑）
        frontend_scopes = self._extract_frontend_scopes()
        
        # 检查一致性
        backend_only = backend_scopes - frontend_scopes
        frontend_only = frontend_scopes - backend_scopes
        
        if backend_only:
            print(f"警告: 后端独有的scopes: {backend_only}")
        
        if frontend_only:
            print(f"警告: 前端独有的scopes: {frontend_only}")
        
        # 检查关键scopes的一致性
        critical_scopes = {
            'esi-location.read_location.v1',
            'esi-skills.read_skills.v1'
        }
        
        for scope in critical_scopes:
            if scope in backend_scopes:
                assert scope in frontend_scopes, f"关键scope {scope} 在前端缺失"
    
    def _extract_frontend_scopes(self) -> set:
        """从前端代码中提取scopes（简化版本）"""
        # 这里应该实际读取前端文件并提取scopes
        # 为了测试目的，返回一个示例集合
        return {
            'esi-location.read_location.v1',
            'esi-skills.read_skills.v1',
            'esi-assets.read_assets.v1',
            'esi-wallet.read_character_wallet.v1'
        }


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
