#!/usr/bin/env python3
"""
调试EVE登录问题
"""
import os
import asyncio
import httpx
from dotenv import load_dotenv

async def test_eve_client_validation():
    """测试EVE客户端验证"""
    load_dotenv()
    
    client_id = os.getenv('EVE_SSO_CLIENT_ID')
    client_secret = os.getenv('EVE_SSO_CLIENT_SECRET')
    callback_url = os.getenv('EVE_SSO_CALLBACK_URL')
    
    print("🔧 EVE SSO客户端验证测试")
    print("=" * 50)
    print(f"Client ID: {client_id}")
    print(f"回调URL: {callback_url}")
    print()
    
    # 测试1: 检查EVE SSO发现端点
    print("🔍 测试1: EVE SSO发现端点")
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                "https://login.eveonline.com/.well-known/oauth_authorization_server",
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                print("   ✅ EVE SSO发现端点正常")
                print(f"   📋 授权端点: {data.get('authorization_endpoint')}")
                print(f"   📋 令牌端点: {data.get('token_endpoint')}")
            else:
                print(f"   ❌ EVE SSO发现端点异常: {response.status_code}")
                
    except Exception as e:
        print(f"   ❌ EVE SSO发现端点测试失败: {e}")
    
    print()
    
    # 测试2: 生成授权URL
    print("🔗 测试2: 生成授权URL")
    try:
        from urllib.parse import urlencode
        
        params = {
            'response_type': 'code',
            'redirect_uri': callback_url,
            'client_id': client_id,
            'scope': 'esi-characters.read_characters.v1',
            'state': 'test_state_12345'
        }
        
        auth_url = f"https://login.eveonline.com/v2/oauth/authorize?{urlencode(params)}"
        
        print("   ✅ 授权URL生成成功")
        print(f"   📋 完整URL: {auth_url}")
        
        # 测试URL是否可访问（应该返回登录页面）
        async with httpx.AsyncClient() as client:
            response = await client.get(auth_url, timeout=10, follow_redirects=False)
            
            print(f"   📋 HTTP状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ 授权端点可访问（返回登录页面）")
            elif response.status_code in [302, 303, 307, 308]:
                print("   ✅ 授权端点重定向（正常行为）")
                print(f"   📋 重定向到: {response.headers.get('location', 'Unknown')}")
            elif response.status_code == 400:
                print("   ❌ 请求参数错误")
                print(f"   📋 响应内容: {response.text[:200]}...")
                
                # 检查是否包含"Client could not be found"错误
                if "client could not be found" in response.text.lower():
                    print("   🚨 发现问题: Client ID无效或未在EVE门户中注册")
                    print("   💡 解决方案:")
                    print("      1. 检查EVE开发者门户: https://developers.eveonline.com/")
                    print("      2. 确认应用状态为活跃")
                    print("      3. 确认Client ID正确")
                    print("      4. 确认回调URL已注册")
            else:
                print(f"   ⚠️  意外状态码: {response.status_code}")
                print(f"   📋 响应内容: {response.text[:200]}...")
                
    except Exception as e:
        print(f"   ❌ 授权URL测试失败: {e}")
    
    print()
    
    # 测试3: 客户端凭据验证
    print("🔐 测试3: 客户端凭据验证")
    try:
        token_url = "https://login.eveonline.com/v2/oauth/token"
        
        # 使用无效的授权码来测试客户端凭据
        data = {
            'grant_type': 'authorization_code',
            'code': 'invalid_test_code',
            'redirect_uri': callback_url,
            'client_id': client_id,
            'client_secret': client_secret
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                token_url,
                data=data,
                headers={'Content-Type': 'application/x-www-form-urlencoded'},
                timeout=10
            )
            
            print(f"   📋 HTTP状态码: {response.status_code}")
            
            if response.status_code == 400:
                error_data = response.json()
                error_code = error_data.get('error', 'unknown')
                error_desc = error_data.get('error_description', 'No description')
                
                print(f"   📋 错误代码: {error_code}")
                print(f"   📋 错误描述: {error_desc}")
                
                if error_code == 'invalid_client':
                    print("   ❌ 客户端凭据无效")
                    print("   💡 请检查Client ID和Client Secret")
                elif error_code == 'invalid_request' and 'client could not be found' in error_desc.lower():
                    print("   ❌ 客户端未找到")
                    print("   💡 请检查EVE开发者门户中的应用配置")
                elif error_code == 'invalid_grant':
                    print("   ✅ 客户端凭据正确（授权码无效是预期的）")
                else:
                    print(f"   ⚠️  其他错误: {error_code}")
            else:
                print(f"   ⚠️  意外响应: {response.text}")
                
    except Exception as e:
        print(f"   ❌ 客户端凭据测试失败: {e}")
    
    print()
    print("=" * 50)
    print("🎯 总结:")
    print("如果看到 'Client could not be found' 错误，请:")
    print("1. 访问 https://developers.eveonline.com/")
    print("2. 检查你的应用配置")
    print("3. 确认应用状态为 'Active'")
    print("4. 确认回调URL完全匹配")
    print("5. 如果需要，创建新的应用")

async def main():
    """主函数"""
    await test_eve_client_validation()

if __name__ == "__main__":
    asyncio.run(main())
