#!/usr/bin/env python3
"""
导入问题诊断脚本
"""
import sys
import traceback
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_import(module_name, description):
    """测试单个模块导入"""
    print(f"🔍 测试 {description}...")
    try:
        __import__(module_name)
        print(f"   ✅ {module_name}")
        return True
    except Exception as e:
        print(f"   ❌ {module_name}: {e}")
        print(f"   📋 详细错误:")
        traceback.print_exc()
        print("-" * 50)
        return False

def main():
    """主函数"""
    print("🚀 开始导入诊断...")
    
    # 测试基础模块
    modules_to_test = [
        ("src.infrastructure.config", "基础配置"),
        ("src.infrastructure.config.settings", "设置模块"),
        ("src.infrastructure.config.logging", "日志模块"),
        ("src.infrastructure.monitoring.error_monitor", "错误监控"),
        ("src.domain.shared.base_entity", "领域基类"),
        ("src.domain.shared.exceptions", "领域异常"),
        ("src.domain.shared.value_objects", "值对象"),
        ("src.domain.character.entities", "角色实体"),
        ("src.domain.character.services", "角色服务"),
        ("src.application.authorization.scope_service", "权限服务"),
        ("src.application.menu.menu_builder", "菜单构建器"),
        ("src.presentation.api.dependencies", "依赖注入"),
        ("src.presentation.api.routers.scope_driven_features", "新路由"),
        ("src.presentation.api.main", "主应用"),
    ]
    
    failed_modules = []
    
    for module_name, description in modules_to_test:
        if not test_import(module_name, description):
            failed_modules.append(module_name)
    
    print("\n" + "=" * 60)
    if failed_modules:
        print(f"❌ 失败的模块数量: {len(failed_modules)}")
        print("失败的模块:")
        for module in failed_modules:
            print(f"   - {module}")
    else:
        print("🎉 所有模块导入成功！")
    
    return len(failed_modules) == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
