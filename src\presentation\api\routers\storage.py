"""
存储管理路由
"""
from fastapi import APIRouter, Depends, HTTPException
from typing import Dict, Any, List, Optional

from ....infrastructure.storage import storage_manager, eve_storage
from ....infrastructure.storage.performance_monitor import performance_monitor
from ....infrastructure.storage.optimization_advisor import optimization_advisor
from ....infrastructure.config.logging import get_logger
from ..dependencies import require_admin, get_current_user

logger = get_logger(__name__)

router = APIRouter()


@router.get("/stats")
async def get_storage_stats(
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取存储统计信息"""
    try:
        stats = await storage_manager.get_storage_stats()
        return {
            "success": True,
            "data": stats
        }
    except Exception as e:
        logger.error("获取存储统计失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get storage stats")


@router.get("/performance")
async def get_performance_stats(
    storage_type: Optional[str] = None,
    operation_type: Optional[str] = None,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取性能统计"""
    try:
        stats = performance_monitor.get_stats(storage_type, operation_type)
        recent_stats = performance_monitor.get_recent_performance(30)
        
        return {
            "success": True,
            "data": {
                "overall_stats": stats,
                "recent_performance": recent_stats,
                "monitoring_enabled": performance_monitor.monitoring_enabled
            }
        }
    except Exception as e:
        logger.error("获取性能统计失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get performance stats")


@router.get("/alerts")
async def get_storage_alerts(
    limit: int = 50,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取存储告警"""
    try:
        alerts = performance_monitor.get_alerts(limit)
        return {
            "success": True,
            "data": {
                "alerts": alerts,
                "total_count": len(alerts)
            }
        }
    except Exception as e:
        logger.error("获取存储告警失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get storage alerts")


@router.delete("/alerts")
async def clear_storage_alerts(
    current_user: Dict[str, Any] = Depends(require_admin)
):
    """清除存储告警"""
    try:
        performance_monitor.clear_alerts()
        return {
            "success": True,
            "message": "Storage alerts cleared successfully"
        }
    except Exception as e:
        logger.error("清除存储告警失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to clear storage alerts")


@router.get("/optimization-report")
async def get_optimization_report(
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取存储优化报告"""
    try:
        report = optimization_advisor.generate_report()
        return {
            "success": True,
            "data": report
        }
    except Exception as e:
        logger.error("生成优化报告失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to generate optimization report")


@router.post("/optimize")
async def optimize_storage(
    current_user: Dict[str, Any] = Depends(require_admin)
):
    """执行存储优化"""
    try:
        await eve_storage.optimize_storage()
        return {
            "success": True,
            "message": "Storage optimization completed successfully"
        }
    except Exception as e:
        logger.error("存储优化失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to optimize storage")


@router.post("/cleanup")
async def cleanup_storage(
    current_user: Dict[str, Any] = Depends(require_admin)
):
    """清理存储数据"""
    try:
        results = await storage_manager.cleanup_expired_data()
        return {
            "success": True,
            "message": "Storage cleanup completed successfully",
            "data": results
        }
    except Exception as e:
        logger.error("存储清理失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to cleanup storage")


@router.get("/cache/{character_id}")
async def get_character_cache_status(
    character_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取角色缓存状态"""
    try:
        cache_data = await eve_storage.batch_get_character_data(
            character_id, 
            ["location", "online", "skills", "assets", "market_orders", "analysis"]
        )
        
        cache_status = {}
        for data_type, data in cache_data.items():
            cache_status[data_type] = {
                "cached": data is not None,
                "data_size": len(str(data)) if data else 0
            }
        
        return {
            "success": True,
            "data": {
                "character_id": character_id,
                "cache_status": cache_status
            }
        }
    except Exception as e:
        logger.error("获取角色缓存状态失败", character_id=character_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get character cache status")


@router.delete("/cache/{character_id}")
async def clear_character_cache(
    character_id: int,
    data_types: Optional[List[str]] = None,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """清除角色缓存"""
    try:
        await eve_storage.invalidate_character_cache(character_id, data_types)
        
        return {
            "success": True,
            "message": f"Character {character_id} cache cleared successfully",
            "data": {
                "character_id": character_id,
                "cleared_types": data_types or "all"
            }
        }
    except Exception as e:
        logger.error("清除角色缓存失败", character_id=character_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to clear character cache")


@router.post("/monitoring/enable")
async def enable_monitoring(
    current_user: Dict[str, Any] = Depends(require_admin)
):
    """启用性能监控"""
    try:
        performance_monitor.enable_monitoring()
        return {
            "success": True,
            "message": "Performance monitoring enabled"
        }
    except Exception as e:
        logger.error("启用性能监控失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to enable monitoring")


@router.post("/monitoring/disable")
async def disable_monitoring(
    current_user: Dict[str, Any] = Depends(require_admin)
):
    """禁用性能监控"""
    try:
        performance_monitor.disable_monitoring()
        return {
            "success": True,
            "message": "Performance monitoring disabled"
        }
    except Exception as e:
        logger.error("禁用性能监控失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to disable monitoring")


@router.put("/monitoring/thresholds")
async def update_monitoring_thresholds(
    thresholds: Dict[str, float],
    current_user: Dict[str, Any] = Depends(require_admin)
):
    """更新监控阈值"""
    try:
        performance_monitor.set_thresholds(thresholds)
        return {
            "success": True,
            "message": "Monitoring thresholds updated successfully",
            "data": {
                "updated_thresholds": thresholds
            }
        }
    except Exception as e:
        logger.error("更新监控阈值失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to update monitoring thresholds")


@router.get("/config")
async def get_storage_config(
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取存储配置"""
    try:
        from ....infrastructure.config import settings
        
        config = {
            "storage_base_path": settings.storage_base_path,
            "storage_enable_compression": settings.storage_enable_compression,
            "storage_auto_cleanup": settings.storage_auto_cleanup,
            "storage_backup_enabled": settings.storage_backup_enabled,
            "cache_ttl": {
                "realtime": settings.cache_ttl_realtime,
                "frequent": settings.cache_ttl_frequent,
                "regular": settings.cache_ttl_regular,
                "daily": settings.cache_ttl_daily,
                "computed": settings.cache_ttl_computed
            },
            "cache_optimization": {
                "enabled": settings.cache_optimization_enabled,
                "interval": settings.cache_optimization_interval,
                "max_memory_usage": settings.cache_max_memory_usage,
                "eviction_policy": settings.cache_eviction_policy
            }
        }
        
        return {
            "success": True,
            "data": config
        }
    except Exception as e:
        logger.error("获取存储配置失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get storage config")
