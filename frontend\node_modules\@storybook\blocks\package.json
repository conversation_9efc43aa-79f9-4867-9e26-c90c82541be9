{"name": "@storybook/blocks", "version": "7.6.20", "description": "Storybook Doc Blocks", "keywords": ["storybook"], "homepage": "https://github.com/storybookjs/storybook/tree/next/code/ui/blocks", "bugs": {"url": "https://github.com/storybookjs/storybook/issues"}, "repository": {"type": "git", "url": "https://github.com/storybookjs/storybook.git", "directory": "code/ui/blocks"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "license": "MIT", "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "require": "./dist/index.js", "import": "./dist/index.mjs"}, "./package.json": "./package.json"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist/**/*", "README.md", "*.js", "*.d.ts", "!src/**/*"], "scripts": {"check": "node --loader ../../../scripts/node_modules/esbuild-register/loader.js -r ../../../scripts/node_modules/esbuild-register/register.js ../../../scripts/prepare/check.ts", "prep": "node --loader ../../../scripts/node_modules/esbuild-register/loader.js -r ../../../scripts/node_modules/esbuild-register/register.js ../../../scripts/prepare/bundle.ts"}, "dependencies": {"@storybook/channels": "7.6.20", "@storybook/client-logger": "7.6.20", "@storybook/components": "7.6.20", "@storybook/core-events": "7.6.20", "@storybook/csf": "^0.1.2", "@storybook/docs-tools": "7.6.20", "@storybook/global": "^5.0.0", "@storybook/manager-api": "7.6.20", "@storybook/preview-api": "7.6.20", "@storybook/theming": "7.6.20", "@storybook/types": "7.6.20", "@types/lodash": "^4.14.167", "color-convert": "^2.0.1", "dequal": "^2.0.2", "lodash": "^4.17.21", "markdown-to-jsx": "^7.1.8", "memoizerific": "^1.11.3", "polished": "^4.2.2", "react-colorful": "^5.1.2", "telejson": "^7.2.0", "tocbot": "^4.20.1", "ts-dedent": "^2.0.0", "util-deprecate": "^1.0.2"}, "devDependencies": {"@storybook/addon-actions": "7.6.20", "@types/color-convert": "^2.0.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0"}, "publishConfig": {"access": "public"}, "bundler": {"entries": ["./src/index.ts"]}, "gitHead": "e6a7fd8a655c69780bc20b9749c2699e44beae17"}