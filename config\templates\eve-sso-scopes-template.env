# EVE SSO权限范围配置模板
# 按功能分类，方便选择性启用/禁用
# 使用方法：复制需要的权限到主配置文件

# ============================================================================
# 🧑 角色基础信息 (Character Information) - 核心必需
# ============================================================================
# 注意：基本角色信息(ID、姓名等)在OAuth过程中自动提供，无需特殊scope
# esi-characters.read_corporation_roles.v1   # 读取角色公司职位

# ============================================================================
# 📍 位置和状态 (Location & Status) - 实时数据
# ============================================================================
# esi-location.read_location.v1              # 读取角色当前位置
# esi-location.read_online.v1                # 读取角色在线状态

# ============================================================================
# 🎓 技能系统 (Skills) - 角色发展
# ============================================================================
# esi-skills.read_skills.v1                  # 读取角色技能 (推荐)
# esi-skills.read_skillqueue.v1              # 读取技能训练队列 (推荐)

# ============================================================================
# 💰 资产和财务 (Assets & Wallet) - 财务管理
# ============================================================================
# esi-assets.read_assets.v1                  # 读取角色资产 (核心功能)
# esi-wallet.read_character_wallet.v1        # 读取角色钱包 (财务分析)

# ============================================================================
# 📧 通讯和克隆 (Communication & Clones) - 角色管理
# ============================================================================
# esi-mail.read_mail.v1                      # 读取角色邮件
# esi-clones.read_clones.v1                  # 读取克隆体信息

# ============================================================================
# 💼 市场交易 (Market Trading) - 商业活动
# ============================================================================
# esi-markets.read_character_orders.v1       # 读取市场订单 (交易分析)

# ============================================================================
# 🏭 工业生产 (Industry) - 生产管理
# ============================================================================
# esi-industry.read_character_jobs.v1        # 读取工业作业 (生产监控)

# ============================================================================
# 🏢 组织关系 (Organizations) - 社交网络
# ============================================================================
# esi-corporations.read_corporation_membership.v1  # 读取公司成员信息
# esi-alliances.read_contacts.v1             # 读取联盟联系人

# ============================================================================
# 🌌 宇宙数据 (Universe) - 空间信息
# ============================================================================
# esi-universe.read_structures.v1            # 读取宇宙结构信息
# esi-search.search_structures.v1            # 搜索结构

# ============================================================================
# 📜 合同和其他 (Contracts & Others) - 扩展功能
# ============================================================================
# esi-contracts.read_character_contracts.v1  # 读取角色合同
# esi-killmails.read_killmails.v1           # 读取击杀邮件
# esi-fittings.read_fittings.v1             # 读取装配方案
# esi-calendar.read_calendar_events.v1       # 读取日历事件

# ============================================================================
# 🚀 扩展权限 (Extended Permissions) - 可选功能
# ============================================================================
# esi-bookmarks.read_character_bookmarks.v1  # 读取书签 (导航辅助)
# esi-planets.manage_planets.v1              # 行星管理 (PI生产)
# esi-fleets.read_fleet.v1                   # 舰队信息 (团队活动)
# esi-loyalty.read_loyalty_points.v1         # 忠诚点数 (LP管理)

# ============================================================================
# 📊 推荐配置组合
# ============================================================================

# 🎯 最小配置 (基础功能)
SCOPES_MINIMAL="\
esi-location.read_location.v1 \
esi-skills.read_skills.v1 \
esi-assets.read_assets.v1"

# 🔥 推荐配置 (常用功能)
SCOPES_RECOMMENDED="\
esi-characters.read_corporation_roles.v1 \
esi-location.read_location.v1 \
esi-location.read_online.v1 \
esi-skills.read_skills.v1 \
esi-skills.read_skillqueue.v1 \
esi-assets.read_assets.v1 \
esi-wallet.read_character_wallet.v1 \
esi-markets.read_character_orders.v1 \
esi-industry.read_character_jobs.v1"

# 🚀 完整配置 (所有功能)
SCOPES_FULL="\
esi-characters.read_corporation_roles.v1 \
esi-location.read_location.v1 \
esi-location.read_online.v1 \
esi-skills.read_skills.v1 \
esi-skills.read_skillqueue.v1 \
esi-assets.read_assets.v1 \
esi-wallet.read_character_wallet.v1 \
esi-mail.read_mail.v1 \
esi-clones.read_clones.v1 \
esi-markets.read_character_orders.v1 \
esi-industry.read_character_jobs.v1 \
esi-corporations.read_corporation_membership.v1 \
esi-alliances.read_contacts.v1 \
esi-universe.read_structures.v1 \
esi-search.search_structures.v1 \
esi-contracts.read_character_contracts.v1 \
esi-killmails.read_killmails.v1 \
esi-fittings.read_fittings.v1 \
esi-calendar.read_calendar_events.v1"

# ============================================================================
# 使用说明
# ============================================================================
# 1. 选择合适的配置级别 (MINIMAL/RECOMMENDED/FULL)
# 2. 复制对应的权限到主配置文件的 EVE_SSO_SCOPES
# 3. 或者根据功能分类选择性添加权限
# 4. 在EVE Developer Portal中更新应用权限
# 5. 用户需要重新登录以获得新权限
