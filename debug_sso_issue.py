#!/usr/bin/env python3
"""
调试EVE SSO问题
"""
import requests
import json

def debug_sso_issue():
    base_url = "http://localhost:8000"
    
    print("🔍 调试EVE SSO问题")
    print("=" * 40)
    
    # 测试认证状态
    print("\n1. 测试认证状态...")
    try:
        response = requests.get(f"{base_url}/auth/status", timeout=5)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"错误响应: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")
    
    # 测试EVE登录
    print("\n2. 测试EVE登录...")
    try:
        response = requests.post(
            f"{base_url}/auth/eve/login",
            json={"scopes": ["esi-assets.read_assets.v1"]},
            timeout=10
        )
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"JSON响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
            except:
                print(f"非JSON响应: {response.text}")
        else:
            print(f"错误响应: {response.text}")
            
    except Exception as e:
        print(f"请求失败: {e}")
    
    # 测试健康检查
    print("\n3. 测试健康检查...")
    try:
        response = requests.get(f"{base_url}/health/status", timeout=5)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"健康状态: {data.get('status')}")
        else:
            print(f"健康检查失败: {response.text}")
    except Exception as e:
        print(f"健康检查请求失败: {e}")

if __name__ == "__main__":
    debug_sso_issue()
