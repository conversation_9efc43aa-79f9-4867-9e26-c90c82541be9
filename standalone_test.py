#!/usr/bin/env python3
"""
完全独立的测试 - 不导入任何项目代码
"""
print("Starting standalone test...")

try:
    print("1. Testing basic Python...")
    import os
    print("   ✅ os module OK")
    
    print("2. Testing FastAPI...")
    from fastapi import FastAPI
    print("   ✅ FastAPI import OK")
    
    print("3. Creating app...")
    app = FastAPI()
    print("   ✅ FastAPI app created")
    
    print("4. Testing uvicorn...")
    import uvicorn
    print("   ✅ uvicorn import OK")
    
    print("🎉 All basic tests passed!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()

print("Test completed.")
