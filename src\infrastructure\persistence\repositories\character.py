"""
角色仓储实现
"""
from datetime import datetime
from typing import List, Optional
from decimal import Decimal

from sqlalchemy.orm import Session, selectinload
from sqlalchemy import and_, or_

from ....domain.character.entities import Character
from ....domain.character.repositories import CharacterRepository
from ....domain.character.value_objects import Attributes, Skill, SkillQueue
from ....domain.shared.value_objects import (
    CharacterId, CorporationId, AllianceId, Money, SecurityStatus, 
    Location, SystemId, SkillPoints
)
from ....domain.shared.exceptions import CharacterNotFoundError
from ..models.character import CharacterModel, AttributesModel, SkillModel, SkillQueueModel
from ...config.logging import get_logger

logger = get_logger(__name__)


class CharacterRepositoryImpl(CharacterRepository):
    """角色仓储实现"""
    
    def __init__(self, session: Session):
        self.session = session
    
    async def get_by_id(self, character_id: CharacterId) -> Optional[Character]:
        """根据ID获取角色"""
        try:
            # 查询角色及其关联数据
            character_model = (
                self.session.query(CharacterModel)
                .options(
                    selectinload(CharacterModel.attributes),
                    selectinload(CharacterModel.skills),
                    selectinload(CharacterModel.skill_queue)
                )
                .filter(CharacterModel.character_id == character_id.value)
                .first()
            )
            
            if not character_model:
                return None
            
            return self._model_to_entity(character_model)
            
        except Exception as e:
            logger.error("获取角色失败", character_id=character_id.value, error=str(e))
            raise
    
    async def get_by_name(self, name: str) -> Optional[Character]:
        """根据名称获取角色"""
        try:
            character_model = (
                self.session.query(CharacterModel)
                .options(
                    selectinload(CharacterModel.attributes),
                    selectinload(CharacterModel.skills),
                    selectinload(CharacterModel.skill_queue)
                )
                .filter(CharacterModel.name == name)
                .first()
            )
            
            if not character_model:
                return None
            
            return self._model_to_entity(character_model)
            
        except Exception as e:
            logger.error("根据名称获取角色失败", name=name, error=str(e))
            raise
    
    async def find_by_corporation(self, corporation_id: CorporationId) -> List[Character]:
        """根据公司ID查找角色"""
        try:
            character_models = (
                self.session.query(CharacterModel)
                .options(
                    selectinload(CharacterModel.attributes),
                    selectinload(CharacterModel.skills),
                    selectinload(CharacterModel.skill_queue)
                )
                .filter(CharacterModel.corporation_id == corporation_id.value)
                .all()
            )
            
            return [self._model_to_entity(model) for model in character_models]
            
        except Exception as e:
            logger.error("根据公司查找角色失败", corporation_id=corporation_id.value, error=str(e))
            raise
    
    async def find_by_alliance(self, alliance_id: AllianceId) -> List[Character]:
        """根据联盟ID查找角色"""
        try:
            character_models = (
                self.session.query(CharacterModel)
                .options(
                    selectinload(CharacterModel.attributes),
                    selectinload(CharacterModel.skills),
                    selectinload(CharacterModel.skill_queue)
                )
                .filter(CharacterModel.alliance_id == alliance_id.value)
                .all()
            )
            
            return [self._model_to_entity(model) for model in character_models]
            
        except Exception as e:
            logger.error("根据联盟查找角色失败", alliance_id=alliance_id.value, error=str(e))
            raise
    
    async def find_online_characters(self) -> List[Character]:
        """查找在线角色"""
        try:
            character_models = (
                self.session.query(CharacterModel)
                .options(
                    selectinload(CharacterModel.attributes),
                    selectinload(CharacterModel.skills),
                    selectinload(CharacterModel.skill_queue)
                )
                .filter(CharacterModel.is_online == True)
                .all()
            )
            
            return [self._model_to_entity(model) for model in character_models]
            
        except Exception as e:
            logger.error("查找在线角色失败", error=str(e))
            raise
    
    async def save(self, character: Character) -> None:
        """保存角色"""
        try:
            # 查找现有角色
            character_model = (
                self.session.query(CharacterModel)
                .filter(CharacterModel.character_id == character.character_id.value)
                .first()
            )
            
            if character_model:
                # 更新现有角色
                self._update_model_from_entity(character_model, character)
            else:
                # 创建新角色
                character_model = self._entity_to_model(character)
                self.session.add(character_model)
            
            # 保存属性
            await self._save_attributes(character)
            
            # 保存技能
            await self._save_skills(character)
            
            # 保存技能队列
            await self._save_skill_queue(character)
            
            self.session.commit()
            
            logger.info("角色保存成功", character_id=character.character_id.value)
            
        except Exception as e:
            self.session.rollback()
            logger.error("保存角色失败", character_id=character.character_id.value, error=str(e))
            raise
    
    async def delete(self, character_id: CharacterId) -> None:
        """删除角色"""
        try:
            character_model = (
                self.session.query(CharacterModel)
                .filter(CharacterModel.character_id == character_id.value)
                .first()
            )
            
            if not character_model:
                raise CharacterNotFoundError(character_id.value)
            
            self.session.delete(character_model)
            self.session.commit()
            
            logger.info("角色删除成功", character_id=character_id.value)
            
        except Exception as e:
            self.session.rollback()
            logger.error("删除角色失败", character_id=character_id.value, error=str(e))
            raise
    
    async def exists(self, character_id: CharacterId) -> bool:
        """检查角色是否存在"""
        try:
            count = (
                self.session.query(CharacterModel)
                .filter(CharacterModel.character_id == character_id.value)
                .count()
            )
            return count > 0
            
        except Exception as e:
            logger.error("检查角色存在性失败", character_id=character_id.value, error=str(e))
            raise
    
    async def count_by_corporation(self, corporation_id: CorporationId) -> int:
        """统计公司角色数量"""
        try:
            return (
                self.session.query(CharacterModel)
                .filter(CharacterModel.corporation_id == corporation_id.value)
                .count()
            )
            
        except Exception as e:
            logger.error("统计公司角色数量失败", corporation_id=corporation_id.value, error=str(e))
            raise
    
    async def find_characters_with_skill(self, skill_id: int, min_level: int = 1) -> List[Character]:
        """查找拥有指定技能的角色"""
        try:
            character_models = (
                self.session.query(CharacterModel)
                .join(SkillModel)
                .options(
                    selectinload(CharacterModel.attributes),
                    selectinload(CharacterModel.skills),
                    selectinload(CharacterModel.skill_queue)
                )
                .filter(
                    and_(
                        SkillModel.skill_id == skill_id,
                        SkillModel.trained_skill_level >= min_level
                    )
                )
                .all()
            )
            
            return [self._model_to_entity(model) for model in character_models]
            
        except Exception as e:
            logger.error("查找拥有技能的角色失败", skill_id=skill_id, min_level=min_level, error=str(e))
            raise
    
    async def find_characters_in_system(self, system_id: int) -> List[Character]:
        """查找在指定星系的角色"""
        try:
            character_models = (
                self.session.query(CharacterModel)
                .options(
                    selectinload(CharacterModel.attributes),
                    selectinload(CharacterModel.skills),
                    selectinload(CharacterModel.skill_queue)
                )
                .filter(CharacterModel.current_system_id == system_id)
                .all()
            )
            
            return [self._model_to_entity(model) for model in character_models]
            
        except Exception as e:
            logger.error("查找星系内角色失败", system_id=system_id, error=str(e))
            raise
    
    async def find_characters_by_security_status_range(self, 
                                                      min_status: float, 
                                                      max_status: float) -> List[Character]:
        """根据安全等级范围查找角色"""
        try:
            character_models = (
                self.session.query(CharacterModel)
                .options(
                    selectinload(CharacterModel.attributes),
                    selectinload(CharacterModel.skills),
                    selectinload(CharacterModel.skill_queue)
                )
                .filter(
                    and_(
                        CharacterModel.security_status >= min_status,
                        CharacterModel.security_status <= max_status
                    )
                )
                .all()
            )
            
            return [self._model_to_entity(model) for model in character_models]
            
        except Exception as e:
            logger.error("根据安全等级查找角色失败", min_status=min_status, max_status=max_status, error=str(e))
            raise
    
    def _model_to_entity(self, model: CharacterModel) -> Character:
        """将数据库模型转换为领域实体"""
        # 创建角色实体
        character = Character(
            character_id=CharacterId(model.character_id),
            name=model.name,
            corporation_id=CorporationId(model.corporation_id),
            birthday=model.birthday,
            race_id=model.race_id,
            bloodline_id=model.bloodline_id,
            gender=model.gender,
            alliance_id=AllianceId(model.alliance_id) if model.alliance_id else None,
            faction_id=model.faction_id,
            security_status=SecurityStatus(model.security_status),
            description=model.description,
            title=model.title
        )
        
        # 设置位置
        if model.current_system_id:
            location = Location(
                system_id=SystemId(model.current_system_id),
                station_id=model.current_station_id,
                structure_id=model.current_structure_id
            )
            character.update_location(location)
        
        # 设置钱包余额
        character.update_wallet_balance(Money(Decimal(str(model.wallet_balance))))
        
        # 设置在线状态
        character.set_online_status(model.is_online, model.last_login)
        
        # 设置属性
        if model.attributes:
            attributes = Attributes(
                charisma=model.attributes.charisma,
                intelligence=model.attributes.intelligence,
                memory=model.attributes.memory,
                perception=model.attributes.perception,
                willpower=model.attributes.willpower
            )
            character.update_attributes(attributes)
        
        # 设置技能
        for skill_model in model.skills:
            skill = Skill(
                skill_id=skill_model.skill_id,
                skillpoints_in_skill=SkillPoints(skill_model.skillpoints_in_skill),
                trained_skill_level=skill_model.trained_skill_level,
                active_skill_level=skill_model.active_skill_level
            )
            character.update_skill(skill)
        
        # 设置技能队列
        for queue_model in model.skill_queue:
            skill_queue = SkillQueue(
                skill_id=queue_model.skill_id,
                finished_level=queue_model.finished_level,
                queue_position=queue_model.queue_position,
                training_start_sp=SkillPoints(queue_model.training_start_sp) if queue_model.training_start_sp else None,
                level_end_sp=SkillPoints(queue_model.level_end_sp) if queue_model.level_end_sp else None,
                level_start_sp=SkillPoints(queue_model.level_start_sp) if queue_model.level_start_sp else None,
                start_date=queue_model.start_date,
                finish_date=queue_model.finish_date
            )
            character.add_skill_to_queue(skill_queue)
        
        # 清除领域事件（从数据库加载的实体不应该有事件）
        character.clear_domain_events()
        
        return character
    
    def _entity_to_model(self, character: Character) -> CharacterModel:
        """将领域实体转换为数据库模型"""
        return CharacterModel(
            character_id=character.character_id.value,
            name=character.name,
            description=character.description,
            corporation_id=character.corporation_id.value,
            alliance_id=character.alliance_id.value if character.alliance_id else None,
            faction_id=character.faction_id,
            race_id=character.race_id,
            bloodline_id=character.bloodline_id,
            gender=character.gender,
            birthday=character.birthday,
            security_status=character.security_status.value,
            title=character.title,
            current_system_id=character.current_location.system_id.value if character.current_location else None,
            current_station_id=character.current_location.station_id if character.current_location else None,
            current_structure_id=character.current_location.structure_id if character.current_location else None,
            is_online=character.is_online,
            last_login=character.last_login,
            wallet_balance=float(character.wallet_balance.amount),
            last_sync_at=datetime.utcnow()
        )
    
    def _update_model_from_entity(self, model: CharacterModel, character: Character) -> None:
        """从领域实体更新数据库模型"""
        model.name = character.name
        model.description = character.description
        model.corporation_id = character.corporation_id.value
        model.alliance_id = character.alliance_id.value if character.alliance_id else None
        model.faction_id = character.faction_id
        model.security_status = character.security_status.value
        model.title = character.title
        
        if character.current_location:
            model.current_system_id = character.current_location.system_id.value
            model.current_station_id = character.current_location.station_id
            model.current_structure_id = character.current_location.structure_id
        
        model.is_online = character.is_online
        model.last_login = character.last_login
        model.wallet_balance = float(character.wallet_balance.amount)
        model.last_sync_at = datetime.utcnow()
        model.updated_at = datetime.utcnow()
    
    async def _save_attributes(self, character: Character) -> None:
        """保存角色属性"""
        attributes_model = (
            self.session.query(AttributesModel)
            .filter(AttributesModel.character_id == character.character_id.value)
            .first()
        )
        
        if attributes_model:
            # 更新现有属性
            attributes_model.charisma = character.attributes.charisma
            attributes_model.intelligence = character.attributes.intelligence
            attributes_model.memory = character.attributes.memory
            attributes_model.perception = character.attributes.perception
            attributes_model.willpower = character.attributes.willpower
            attributes_model.updated_at = datetime.utcnow()
        else:
            # 创建新属性
            attributes_model = AttributesModel(
                character_id=character.character_id.value,
                charisma=character.attributes.charisma,
                intelligence=character.attributes.intelligence,
                memory=character.attributes.memory,
                perception=character.attributes.perception,
                willpower=character.attributes.willpower
            )
            self.session.add(attributes_model)
    
    async def _save_skills(self, character: Character) -> None:
        """保存角色技能"""
        # 删除现有技能
        self.session.query(SkillModel).filter(
            SkillModel.character_id == character.character_id.value
        ).delete()
        
        # 添加新技能
        for skill in character.skills.values():
            skill_model = SkillModel(
                character_id=character.character_id.value,
                skill_id=skill.skill_id,
                skillpoints_in_skill=skill.skillpoints_in_skill.value,
                trained_skill_level=skill.trained_skill_level,
                active_skill_level=skill.active_skill_level
            )
            self.session.add(skill_model)
    
    async def _save_skill_queue(self, character: Character) -> None:
        """保存技能队列"""
        # 删除现有队列
        self.session.query(SkillQueueModel).filter(
            SkillQueueModel.character_id == character.character_id.value
        ).delete()
        
        # 添加新队列
        for queue_item in character.skill_queue:
            queue_model = SkillQueueModel(
                character_id=character.character_id.value,
                skill_id=queue_item.skill_id,
                queue_position=queue_item.queue_position,
                finished_level=queue_item.finished_level,
                training_start_sp=queue_item.training_start_sp.value if queue_item.training_start_sp else None,
                level_end_sp=queue_item.level_end_sp.value if queue_item.level_end_sp else None,
                level_start_sp=queue_item.level_start_sp.value if queue_item.level_start_sp else None,
                start_date=queue_item.start_date,
                finish_date=queue_item.finish_date
            )
            self.session.add(queue_model)
