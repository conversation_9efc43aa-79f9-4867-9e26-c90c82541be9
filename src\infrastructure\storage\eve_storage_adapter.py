"""
EVE Online 专用存储适配器
针对EVE数据特点优化的存储接口
"""
from datetime import datetime, timedelta
from typing import Any, Optional, Dict, List
import asyncio

from .smart_cache import smart_cache, DataCategory
from .redis_cache import redis_cache
from .pickle_storage import pickle_storage
from .json_storage import json_storage
from ..config import settings
from ..config.logging import get_logger

logger = get_logger(__name__)


class EVEStorageAdapter:
    """EVE Online 存储适配器"""
    
    def __init__(self):
        self.smart_cache = smart_cache
        self.redis = redis_cache
        self.pickle = pickle_storage
        self.json = json_storage
    
    # ==================== ESI数据缓存 ====================
    
    async def cache_character_location(self, character_id: int, location_data: Dict[str, Any]) -> bool:
        """缓存角色位置（实时数据）"""
        return await self.smart_cache.set(
            "location", location_data, DataCategory.REALTIME, character_id, ttl=30
        )
    
    async def get_character_location(self, character_id: int) -> Optional[Dict[str, Any]]:
        """获取角色位置缓存"""
        return await self.smart_cache.get("location", DataCategory.REALTIME, character_id)
    
    async def cache_character_online_status(self, character_id: int, online_data: Dict[str, Any]) -> bool:
        """缓存角色在线状态（实时数据）"""
        return await self.smart_cache.set(
            "online", online_data, DataCategory.REALTIME, character_id, ttl=30
        )
    
    async def get_character_online_status(self, character_id: int) -> Optional[Dict[str, Any]]:
        """获取角色在线状态缓存"""
        return await self.smart_cache.get("online", DataCategory.REALTIME, character_id)
    
    async def cache_market_orders(self, character_id: int, orders_data: List[Dict[str, Any]]) -> bool:
        """缓存市场订单（频繁数据）"""
        return await self.smart_cache.set(
            "market_orders", orders_data, DataCategory.FREQUENT, character_id, ttl=300
        )
    
    async def get_market_orders(self, character_id: int) -> Optional[List[Dict[str, Any]]]:
        """获取市场订单缓存"""
        return await self.smart_cache.get("market_orders", DataCategory.FREQUENT, character_id)
    
    async def cache_character_skills(self, character_id: int, skills_data: Dict[str, Any]) -> bool:
        """缓存角色技能（常规数据）"""
        return await self.smart_cache.set(
            "skills", skills_data, DataCategory.REGULAR, character_id, ttl=3600
        )
    
    async def get_character_skills(self, character_id: int) -> Optional[Dict[str, Any]]:
        """获取角色技能缓存"""
        return await self.smart_cache.get("skills", DataCategory.REGULAR, character_id)
    
    async def cache_character_assets(self, character_id: int, assets_data: List[Dict[str, Any]]) -> bool:
        """缓存角色资产（常规数据）"""
        return await self.smart_cache.set(
            "assets", assets_data, DataCategory.REGULAR, character_id, ttl=3600
        )
    
    async def get_character_assets(self, character_id: int) -> Optional[List[Dict[str, Any]]]:
        """获取角色资产缓存"""
        return await self.smart_cache.get("assets", DataCategory.REGULAR, character_id)
    
    async def cache_character_info(self, character_id: int, info_data: Dict[str, Any]) -> bool:
        """缓存角色基本信息（每日数据）"""
        return await self.smart_cache.set(
            "info", info_data, DataCategory.DAILY, character_id, ttl=86400
        )
    
    async def get_character_info(self, character_id: int) -> Optional[Dict[str, Any]]:
        """获取角色基本信息缓存"""
        return await self.smart_cache.get("info", DataCategory.DAILY, character_id)
    
    # ==================== 计算结果缓存 ====================
    
    async def cache_character_analysis(self, character_id: int, analysis_data: Dict[str, Any]) -> bool:
        """缓存角色分析结果"""
        return await self.smart_cache.set(
            "analysis", analysis_data, DataCategory.COMPUTED, character_id, ttl=3600
        )
    
    async def get_character_analysis(self, character_id: int) -> Optional[Dict[str, Any]]:
        """获取角色分析结果"""
        return await self.smart_cache.get("analysis", DataCategory.COMPUTED, character_id)
    
    async def cache_skill_training_plan(self, character_id: int, plan_data: Dict[str, Any]) -> bool:
        """缓存技能训练计划"""
        return await self.smart_cache.set(
            "training_plan", plan_data, DataCategory.COMPUTED, character_id, ttl=1800
        )
    
    async def get_skill_training_plan(self, character_id: int) -> Optional[Dict[str, Any]]:
        """获取技能训练计划"""
        return await self.smart_cache.get("training_plan", DataCategory.COMPUTED, character_id)
    
    async def cache_market_analysis(self, region_id: int, type_id: int, analysis_data: Dict[str, Any]) -> bool:
        """缓存市场分析结果"""
        key = f"market_analysis_{region_id}_{type_id}"
        return await self.smart_cache.set(
            key, analysis_data, DataCategory.COMPUTED, ttl=900
        )
    
    async def get_market_analysis(self, region_id: int, type_id: int) -> Optional[Dict[str, Any]]:
        """获取市场分析结果"""
        key = f"market_analysis_{region_id}_{type_id}"
        return await self.smart_cache.get(key, DataCategory.COMPUTED)
    
    # ==================== 用户配置管理 ====================
    
    def save_user_dashboard_config(self, user_id: int, dashboard_config: Dict[str, Any]) -> bool:
        """保存用户仪表板配置"""
        return self.json.save_user_settings(user_id, {
            "dashboard": dashboard_config,
            "updated_at": datetime.utcnow().isoformat()
        })
    
    def get_user_dashboard_config(self, user_id: int) -> Optional[Dict[str, Any]]:
        """获取用户仪表板配置"""
        settings_data = self.json.load_user_settings(user_id)
        if settings_data:
            return settings_data.get("dashboard")
        return None
    
    def save_user_alert_rules(self, user_id: int, alert_rules: List[Dict[str, Any]]) -> bool:
        """保存用户告警规则"""
        return self.json.save_user_settings(user_id, {
            "alert_rules": alert_rules,
            "updated_at": datetime.utcnow().isoformat()
        })
    
    def get_user_alert_rules(self, user_id: int) -> List[Dict[str, Any]]:
        """获取用户告警规则"""
        settings_data = self.json.load_user_settings(user_id)
        if settings_data:
            return settings_data.get("alert_rules", [])
        return []
    
    # ==================== 批量操作 ====================
    
    async def batch_cache_character_data(self, character_id: int, data_bundle: Dict[str, Any]) -> Dict[str, bool]:
        """批量缓存角色数据"""
        results = {}
        
        # 并发执行多个缓存操作
        tasks = []
        
        if "location" in data_bundle:
            tasks.append(("location", self.cache_character_location(character_id, data_bundle["location"])))
        
        if "online" in data_bundle:
            tasks.append(("online", self.cache_character_online_status(character_id, data_bundle["online"])))
        
        if "skills" in data_bundle:
            tasks.append(("skills", self.cache_character_skills(character_id, data_bundle["skills"])))
        
        if "assets" in data_bundle:
            tasks.append(("assets", self.cache_character_assets(character_id, data_bundle["assets"])))
        
        if "market_orders" in data_bundle:
            tasks.append(("market_orders", self.cache_market_orders(character_id, data_bundle["market_orders"])))
        
        # 等待所有任务完成
        if tasks:
            task_results = await asyncio.gather(*[task[1] for task in tasks], return_exceptions=True)
            
            for i, (key, _) in enumerate(tasks):
                result = task_results[i]
                if isinstance(result, Exception):
                    logger.error(f"批量缓存{key}失败", character_id=character_id, error=str(result))
                    results[key] = False
                else:
                    results[key] = result
        
        return results
    
    async def batch_get_character_data(self, character_id: int, 
                                     data_types: List[str]) -> Dict[str, Any]:
        """批量获取角色数据"""
        results = {}
        tasks = []
        
        for data_type in data_types:
            if data_type == "location":
                tasks.append(("location", self.get_character_location(character_id)))
            elif data_type == "online":
                tasks.append(("online", self.get_character_online_status(character_id)))
            elif data_type == "skills":
                tasks.append(("skills", self.get_character_skills(character_id)))
            elif data_type == "assets":
                tasks.append(("assets", self.get_character_assets(character_id)))
            elif data_type == "market_orders":
                tasks.append(("market_orders", self.get_market_orders(character_id)))
            elif data_type == "analysis":
                tasks.append(("analysis", self.get_character_analysis(character_id)))
        
        if tasks:
            task_results = await asyncio.gather(*[task[1] for task in tasks], return_exceptions=True)
            
            for i, (key, _) in enumerate(tasks):
                result = task_results[i]
                if isinstance(result, Exception):
                    logger.error(f"批量获取{key}失败", character_id=character_id, error=str(result))
                    results[key] = None
                else:
                    results[key] = result
        
        return results
    
    # ==================== 缓存管理 ====================
    
    async def invalidate_character_cache(self, character_id: int, data_types: Optional[List[str]] = None):
        """清除角色缓存"""
        if data_types is None:
            # 清除所有角色相关缓存
            await self.smart_cache.invalidate_character_cache(character_id)
        else:
            # 清除指定类型的缓存
            for data_type in data_types:
                if data_type == "location":
                    await self.smart_cache.delete("location", DataCategory.REALTIME, character_id)
                elif data_type == "online":
                    await self.smart_cache.delete("online", DataCategory.REALTIME, character_id)
                elif data_type == "skills":
                    await self.smart_cache.delete("skills", DataCategory.REGULAR, character_id)
                elif data_type == "assets":
                    await self.smart_cache.delete("assets", DataCategory.REGULAR, character_id)
                elif data_type == "market_orders":
                    await self.smart_cache.delete("market_orders", DataCategory.FREQUENT, character_id)
                elif data_type == "analysis":
                    await self.smart_cache.delete("analysis", DataCategory.COMPUTED, character_id)
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        stats = {
            "redis": {
                "connected": self.redis._client is not None,
                "status": "healthy" if self.redis._client else "disconnected"
            },
            "pickle": self.pickle.get_storage_stats(),
            "json": {
                "configs": len(self.json.list_configs()),
                "user_settings": len(self.json.list_user_settings()),
                "exports": len(self.json.list_exports())
            },
            "access_patterns": len(self.smart_cache.access_stats)
        }
        return stats
    
    async def optimize_storage(self):
        """优化存储性能"""
        await self.smart_cache.optimize_cache()
    
    # ==================== 数据导出 ====================
    
    def export_character_data(self, character_id: int, include_cache: bool = False) -> bool:
        """导出角色数据"""
        try:
            export_data = {
                "character_id": character_id,
                "export_time": datetime.utcnow().isoformat(),
                "include_cache": include_cache
            }
            
            if include_cache:
                # 导出缓存数据（同步方式，仅用于导出）
                cache_data = {}
                
                # 这里可以添加缓存数据的同步获取逻辑
                # 注意：这是简化版本，实际应该使用异步方式
                
                export_data["cache_data"] = cache_data
            
            return self.json.export_data(f"character_{character_id}_full", export_data)
            
        except Exception as e:
            logger.error("导出角色数据失败", character_id=character_id, error=str(e))
            return False


# 全局EVE存储适配器
eve_storage = EVEStorageAdapter()
