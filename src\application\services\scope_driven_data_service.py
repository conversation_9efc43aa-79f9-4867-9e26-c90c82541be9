"""
基于Scope驱动的数据服务
参考SeAT架构，根据权限动态管理数据结构和API调用
"""
from typing import Dict, List, Optional, Any, Type
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import asyncio

from ...infrastructure.config import settings
from ...infrastructure.monitoring.error_monitor import error_monitor


class DataCategory(Enum):
    """数据分类"""
    CHARACTER = "character"
    CORPORATION = "corporation"
    ALLIANCE = "alliance"
    MARKET = "market"
    INDUSTRY = "industry"
    COMBAT = "combat"
    UNIVERSE = "universe"


class CacheLevel(Enum):
    """缓存级别"""
    REALTIME = "realtime"      # 实时数据，30秒缓存
    FREQUENT = "frequent"      # 频繁更新，5分钟缓存
    REGULAR = "regular"        # 常规数据，1小时缓存
    DAILY = "daily"           # 日常数据，24小时缓存
    STATIC = "static"         # 静态数据，7天缓存


@dataclass
class DataEndpoint:
    """数据端点定义"""
    scope: str
    category: DataCategory
    endpoint: str
    cache_level: CacheLevel
    description: str
    required_params: List[str] = field(default_factory=list)
    optional_params: List[str] = field(default_factory=list)
    response_model: Optional[Type] = None
    dependencies: List[str] = field(default_factory=list)  # 依赖的其他scope


class ScopeDrivenDataService:
    """基于Scope的数据服务"""
    
    def __init__(self):
        self.endpoints = self._initialize_endpoints()
        self.cache_ttl = self._get_cache_ttl_config()
    
    def _initialize_endpoints(self) -> Dict[str, DataEndpoint]:
        """初始化数据端点定义"""
        return {
            # 角色基础信息
            "character_info": DataEndpoint(
                scope="publicData",
                category=DataCategory.CHARACTER,
                endpoint="/characters/{character_id}/",
                cache_level=CacheLevel.DAILY,
                description="角色基础信息",
                required_params=["character_id"]
            ),
            
            # 角色位置信息
            "character_location": DataEndpoint(
                scope="esi-location.read_location.v1",
                category=DataCategory.CHARACTER,
                endpoint="/characters/{character_id}/location/",
                cache_level=CacheLevel.REALTIME,
                description="角色当前位置",
                required_params=["character_id"]
            ),
            
            "character_online": DataEndpoint(
                scope="esi-location.read_online.v1",
                category=DataCategory.CHARACTER,
                endpoint="/characters/{character_id}/online/",
                cache_level=CacheLevel.REALTIME,
                description="角色在线状态",
                required_params=["character_id"]
            ),
            
            "character_ship": DataEndpoint(
                scope="esi-location.read_ship_type.v1",
                category=DataCategory.CHARACTER,
                endpoint="/characters/{character_id}/ship/",
                cache_level=CacheLevel.FREQUENT,
                description="角色当前飞船",
                required_params=["character_id"]
            ),
            
            # 技能相关
            "character_skills": DataEndpoint(
                scope="esi-skills.read_skills.v1",
                category=DataCategory.CHARACTER,
                endpoint="/characters/{character_id}/skills/",
                cache_level=CacheLevel.REGULAR,
                description="角色技能信息",
                required_params=["character_id"]
            ),
            
            "character_skillqueue": DataEndpoint(
                scope="esi-skills.read_skillqueue.v1",
                category=DataCategory.CHARACTER,
                endpoint="/characters/{character_id}/skillqueue/",
                cache_level=CacheLevel.FREQUENT,
                description="技能训练队列",
                required_params=["character_id"]
            ),
            
            # 资产相关
            "character_assets": DataEndpoint(
                scope="esi-assets.read_assets.v1",
                category=DataCategory.CHARACTER,
                endpoint="/characters/{character_id}/assets/",
                cache_level=CacheLevel.REGULAR,
                description="角色资产",
                required_params=["character_id"],
                optional_params=["page"]
            ),
            
            "corporation_assets": DataEndpoint(
                scope="esi-assets.read_corporation_assets.v1",
                category=DataCategory.CORPORATION,
                endpoint="/corporations/{corporation_id}/assets/",
                cache_level=CacheLevel.REGULAR,
                description="公司资产",
                required_params=["corporation_id"],
                optional_params=["page"]
            ),
            
            # 钱包相关
            "character_wallet": DataEndpoint(
                scope="esi-wallet.read_character_wallet.v1",
                category=DataCategory.CHARACTER,
                endpoint="/characters/{character_id}/wallet/",
                cache_level=CacheLevel.FREQUENT,
                description="角色钱包余额",
                required_params=["character_id"]
            ),
            
            "character_wallet_journal": DataEndpoint(
                scope="esi-wallet.read_character_wallet.v1",
                category=DataCategory.CHARACTER,
                endpoint="/characters/{character_id}/wallet/journal/",
                cache_level=CacheLevel.FREQUENT,
                description="角色钱包交易记录",
                required_params=["character_id"],
                optional_params=["page"]
            ),
            
            "corporation_wallets": DataEndpoint(
                scope="esi-wallet.read_corporation_wallets.v1",
                category=DataCategory.CORPORATION,
                endpoint="/corporations/{corporation_id}/wallets/",
                cache_level=CacheLevel.FREQUENT,
                description="公司钱包",
                required_params=["corporation_id"]
            ),
            
            # 邮件相关
            "character_mail": DataEndpoint(
                scope="esi-mail.read_mail.v1",
                category=DataCategory.CHARACTER,
                endpoint="/characters/{character_id}/mail/",
                cache_level=CacheLevel.FREQUENT,
                description="角色邮件",
                required_params=["character_id"],
                optional_params=["labels", "last_mail_id"]
            ),
            
            # 联系人相关
            "character_contacts": DataEndpoint(
                scope="esi-characters.read_contacts.v1",
                category=DataCategory.CHARACTER,
                endpoint="/characters/{character_id}/contacts/",
                cache_level=CacheLevel.REGULAR,
                description="角色联系人",
                required_params=["character_id"],
                optional_params=["page"]
            ),
            
            # 公司相关
            "corporation_info": DataEndpoint(
                scope="publicData",
                category=DataCategory.CORPORATION,
                endpoint="/corporations/{corporation_id}/",
                cache_level=CacheLevel.DAILY,
                description="公司基础信息",
                required_params=["corporation_id"]
            ),
            
            "corporation_members": DataEndpoint(
                scope="esi-corporations.read_corporation_membership.v1",
                category=DataCategory.CORPORATION,
                endpoint="/corporations/{corporation_id}/members/",
                cache_level=CacheLevel.REGULAR,
                description="公司成员",
                required_params=["corporation_id"]
            ),
            
            "corporation_structures": DataEndpoint(
                scope="esi-corporations.read_structures.v1",
                category=DataCategory.CORPORATION,
                endpoint="/corporations/{corporation_id}/structures/",
                cache_level=CacheLevel.REGULAR,
                description="公司建筑",
                required_params=["corporation_id"],
                optional_params=["page"]
            ),
            
            # 工业相关
            "character_industry_jobs": DataEndpoint(
                scope="esi-industry.read_character_jobs.v1",
                category=DataCategory.INDUSTRY,
                endpoint="/characters/{character_id}/industry/jobs/",
                cache_level=CacheLevel.FREQUENT,
                description="角色工业任务",
                required_params=["character_id"],
                optional_params=["include_completed"]
            ),
            
            "character_mining": DataEndpoint(
                scope="esi-industry.read_character_mining.v1",
                category=DataCategory.INDUSTRY,
                endpoint="/characters/{character_id}/mining/",
                cache_level=CacheLevel.REGULAR,
                description="角色挖矿记录",
                required_params=["character_id"],
                optional_params=["page"]
            ),
            
            "corporation_industry_jobs": DataEndpoint(
                scope="esi-industry.read_corporation_jobs.v1",
                category=DataCategory.INDUSTRY,
                endpoint="/corporations/{corporation_id}/industry/jobs/",
                cache_level=CacheLevel.FREQUENT,
                description="公司工业任务",
                required_params=["corporation_id"],
                optional_params=["include_completed", "page"]
            ),
            
            # 市场相关
            "character_orders": DataEndpoint(
                scope="esi-markets.read_character_orders.v1",
                category=DataCategory.MARKET,
                endpoint="/characters/{character_id}/orders/",
                cache_level=CacheLevel.FREQUENT,
                description="角色市场订单",
                required_params=["character_id"]
            ),
            
            "corporation_orders": DataEndpoint(
                scope="esi-markets.read_corporation_orders.v1",
                category=DataCategory.MARKET,
                endpoint="/corporations/{corporation_id}/orders/",
                cache_level=CacheLevel.FREQUENT,
                description="公司市场订单",
                required_params=["corporation_id"],
                optional_params=["page"]
            ),
            
            # 战斗相关
            "character_killmails": DataEndpoint(
                scope="esi-killmails.read_killmails.v1",
                category=DataCategory.COMBAT,
                endpoint="/characters/{character_id}/killmails/recent/",
                cache_level=CacheLevel.FREQUENT,
                description="角色击杀邮件",
                required_params=["character_id"],
                optional_params=["page"]
            ),
            
            "corporation_killmails": DataEndpoint(
                scope="esi-killmails.read_corporation_killmails.v1",
                category=DataCategory.COMBAT,
                endpoint="/corporations/{corporation_id}/killmails/recent/",
                cache_level=CacheLevel.FREQUENT,
                description="公司击杀邮件",
                required_params=["corporation_id"],
                optional_params=["page"]
            ),
            
            # 舰队相关
            "character_fleets": DataEndpoint(
                scope="esi-fleets.read_fleet.v1",
                category=DataCategory.CHARACTER,
                endpoint="/characters/{character_id}/fleet/",
                cache_level=CacheLevel.REALTIME,
                description="角色舰队信息",
                required_params=["character_id"]
            ),
            
            "character_fittings": DataEndpoint(
                scope="esi-fittings.read_fittings.v1",
                category=DataCategory.CHARACTER,
                endpoint="/characters/{character_id}/fittings/",
                cache_level=CacheLevel.REGULAR,
                description="角色装配方案",
                required_params=["character_id"]
            ),
            
            # 宇宙相关
            "universe_structures": DataEndpoint(
                scope="esi-universe.read_structures.v1",
                category=DataCategory.UNIVERSE,
                endpoint="/universe/structures/{structure_id}/",
                cache_level=CacheLevel.DAILY,
                description="宇宙建筑信息",
                required_params=["structure_id"]
            )
        }
    
    def _get_cache_ttl_config(self) -> Dict[CacheLevel, int]:
        """获取缓存TTL配置"""
        return {
            CacheLevel.REALTIME: getattr(settings, 'cache_ttl_realtime', 30),
            CacheLevel.FREQUENT: getattr(settings, 'cache_ttl_frequent', 300),
            CacheLevel.REGULAR: getattr(settings, 'cache_ttl_regular', 3600),
            CacheLevel.DAILY: getattr(settings, 'cache_ttl_daily', 86400),
            CacheLevel.STATIC: getattr(settings, 'cache_ttl_static', 604800)
        }
    
    def get_available_endpoints_for_scopes(self, scopes: List[str]) -> Dict[str, DataEndpoint]:
        """根据权限获取可用的数据端点"""
        available_endpoints = {}
        
        for endpoint_name, endpoint in self.endpoints.items():
            if endpoint.scope == "publicData" or endpoint.scope in scopes:
                available_endpoints[endpoint_name] = endpoint
        
        return available_endpoints
    
    def get_endpoints_by_category(self, category: DataCategory, scopes: List[str]) -> Dict[str, DataEndpoint]:
        """根据分类和权限获取数据端点"""
        available_endpoints = self.get_available_endpoints_for_scopes(scopes)
        
        return {
            name: endpoint for name, endpoint in available_endpoints.items()
            if endpoint.category == category
        }
    
    def get_cache_ttl(self, endpoint_name: str) -> int:
        """获取端点的缓存TTL"""
        endpoint = self.endpoints.get(endpoint_name)
        if not endpoint:
            return self.cache_ttl[CacheLevel.REGULAR]
        
        return self.cache_ttl[endpoint.cache_level]
    
    def validate_endpoint_params(self, endpoint_name: str, params: Dict[str, Any]) -> tuple[bool, List[str]]:
        """验证端点参数"""
        endpoint = self.endpoints.get(endpoint_name)
        if not endpoint:
            return False, [f"未知的端点: {endpoint_name}"]
        
        errors = []
        
        # 检查必需参数
        for required_param in endpoint.required_params:
            if required_param not in params:
                errors.append(f"缺少必需参数: {required_param}")
        
        return len(errors) == 0, errors
    
    def get_endpoint_dependencies(self, endpoint_name: str) -> List[str]:
        """获取端点依赖"""
        endpoint = self.endpoints.get(endpoint_name)
        if not endpoint:
            return []
        
        return endpoint.dependencies
    
    def generate_data_access_plan(self, scopes: List[str], categories: List[DataCategory] = None) -> Dict[str, Any]:
        """生成数据访问计划"""
        available_endpoints = self.get_available_endpoints_for_scopes(scopes)
        
        if categories:
            available_endpoints = {
                name: endpoint for name, endpoint in available_endpoints.items()
                if endpoint.category in categories
            }
        
        # 按分类组织
        plan = {
            "total_endpoints": len(available_endpoints),
            "categories": {},
            "cache_strategy": {},
            "sync_priority": {}
        }
        
        for endpoint_name, endpoint in available_endpoints.items():
            category_name = endpoint.category.value
            
            if category_name not in plan["categories"]:
                plan["categories"][category_name] = []
            
            plan["categories"][category_name].append({
                "name": endpoint_name,
                "description": endpoint.description,
                "endpoint": endpoint.endpoint,
                "cache_level": endpoint.cache_level.value,
                "ttl": self.get_cache_ttl(endpoint_name)
            })
            
            # 缓存策略
            cache_level = endpoint.cache_level.value
            if cache_level not in plan["cache_strategy"]:
                plan["cache_strategy"][cache_level] = []
            plan["cache_strategy"][cache_level].append(endpoint_name)
            
            # 同步优先级（基于缓存级别）
            priority_map = {
                CacheLevel.REALTIME: 1,
                CacheLevel.FREQUENT: 2,
                CacheLevel.REGULAR: 3,
                CacheLevel.DAILY: 4,
                CacheLevel.STATIC: 5
            }
            priority = priority_map[endpoint.cache_level]
            if priority not in plan["sync_priority"]:
                plan["sync_priority"][priority] = []
            plan["sync_priority"][priority].append(endpoint_name)
        
        return plan


# 全局数据服务实例
data_service = ScopeDrivenDataService()
