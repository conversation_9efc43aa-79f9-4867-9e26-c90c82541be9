# 调试知识库

## 模块导入错误排查指南

### 问题类型：ImportError - 无法导入指定名称

#### 典型错误信息
```
cannot import name 'require_admin_permission' from 'src.presentation.api.dependencies'
```

#### 排查步骤
1. **确认目标模块存在**
   ```bash
   # 检查文件是否存在
   ls -la src/presentation/api/dependencies.py
   ```

2. **检查函数/类是否在目标模块中定义**
   ```bash
   # 搜索函数定义
   grep -n "def require_admin_permission" src/presentation/api/dependencies.py
   grep -n "require_admin_permission" src/presentation/api/dependencies.py
   ```

3. **查看模块的实际导出内容**
   ```python
   # 在Python中检查模块内容
   import src.presentation.api.dependencies as deps
   print(dir(deps))
   ```

4. **检查相似名称的函数**
   ```bash
   # 搜索相似的函数名
   grep -n "require_admin" src/presentation/api/dependencies.py
   ```

#### 解决方案
- **方案1**: 修正导入的函数名
- **方案2**: 在目标模块中添加缺失的函数
- **方案3**: 使用别名导入 `from module import actual_name as expected_name`

#### 预防措施
1. **开发前检查现有API结构**
2. **使用IDE的自动补全功能**
3. **添加导入验证测试**
4. **建立API文档并及时更新**

---

## FastAPI应用启动失败排查

### 问题类型：应用无法启动

#### 常见原因
1. **模块导入错误** - 最常见
2. **依赖注入配置错误**
3. **数据库连接问题**
4. **环境变量缺失**
5. **端口占用**

#### 排查工具
```python
# 测试应用导入
python -c "from src.presentation.api.main import app; print('✅ 导入成功')"

# 测试特定模块
python -c "from src.presentation.api.routers import monitoring; print('✅ 路由导入成功')"
```

#### 调试技巧
1. **分层测试**: 从底层模块开始逐层测试
2. **隔离测试**: 单独测试有问题的模块
3. **日志分析**: 查看详细的错误堆栈
4. **依赖图分析**: 理解模块间的依赖关系

---

## 测试策略改进

### 测试金字塔在Python项目中的应用

#### 单元测试 (70%)
- 测试单个函数/方法
- 使用mock隔离依赖
- 快速执行，高覆盖率

#### 集成测试 (20%)
- 测试模块间协作
- **重点**: 导入测试、依赖注入测试
- 数据库集成测试

#### 端到端测试 (10%)
- 完整应用启动测试
- API端点可访问性测试
- 用户场景测试

### 关键测试检查点
1. **模块导入完整性**
2. **依赖注入正确性**
3. **API路由注册**
4. **数据库连接**
5. **环境配置**

---

## Python项目依赖管理最佳实践

### 依赖检查工具
- **mypy**: 静态类型检查
- **flake8**: 代码质量检查
- **isort**: 导入排序检查
- **black**: 代码格式化

### 预提交检查
```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/psf/black
    rev: 22.3.0
    hooks:
      - id: black
  - repo: https://github.com/pycqa/flake8
    rev: 4.0.1
    hooks:
      - id: flake8
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v0.950
    hooks:
      - id: mypy
```

### 导入规范
1. **标准库导入** (第一组)
2. **第三方库导入** (第二组)
3. **本地应用导入** (第三组)
4. **相对导入** (第四组)

---

## 错误监控和日志分析

### 结构化日志记录
```python
import structlog

logger = structlog.get_logger(__name__)

# 记录导入错误
logger.error(
    "模块导入失败",
    module="src.presentation.api.dependencies",
    function="require_admin_permission",
    error_type="ImportError",
    suggestion="检查函数名是否正确"
)
```

### 错误分类
- **CRITICAL**: 应用无法启动
- **HIGH**: 核心功能不可用
- **MEDIUM**: 部分功能异常
- **LOW**: 性能或体验问题

### 监控指标
- 导入错误频率
- 应用启动成功率
- API响应时间
- 错误恢复时间

---

## 开发流程改进建议

### 功能开发检查清单
- [ ] 检查现有API结构
- [ ] 验证依赖函数存在
- [ ] 运行导入测试
- [ ] 执行集成测试
- [ ] 更新API文档
- [ ] 提交前完整测试

### 代码审查要点
1. **导入语句正确性**
2. **依赖注入配置**
3. **错误处理完整性**
4. **测试覆盖率**
5. **文档同步更新**

### 持续集成流程
1. **静态分析** → 2. **单元测试** → 3. **集成测试** → 4. **部署测试**

---

## EVE SSO集成问题排查

### 问题类型：EVE SSO invalid_scope错误

#### 典型错误信息
```json
{"error":"invalid_scope","error_description":"The requested 'esi-characters.read_characters.v1' scope is not valid."}
```

#### 排查步骤
1. **检查EVE ESI官方文档**
   - 访问 https://esi.evetech.net/ui/ 查看有效scope列表
   - 确认所使用的scope确实存在

2. **检查前后端scope配置一致性**
   ```bash
   # 搜索前端scope配置
   grep -r "esi-characters.read_characters.v1" frontend/src/

   # 搜索后端scope配置
   grep -r "esi-characters.read_characters.v1" src/
   ```

3. **验证EVE SSO基本原理**
   - 基本角色信息在OAuth过程中自动提供
   - 无需特殊scope获取角色ID、姓名、公司信息
   - 只有特定功能需要对应scope

#### 解决方案
- **移除无效scope**: 删除不存在的scope引用
- **更新文档**: 同步修正所有相关文档和模板
- **前后端同步**: 确保前后端使用相同的有效scope列表

#### 有效的EVE ESI Scopes示例
```javascript
// 有效的scope配置
const validScopes = [
  'esi-location.read_location.v1',      // 角色位置
  'esi-skills.read_skills.v1',          // 技能信息
  'esi-wallet.read_character_wallet.v1', // 钱包信息
  'esi-assets.read_assets.v1',          // 角色资产
  'esi-clones.read_clones.v1'           // 克隆体信息
]
```

#### 预防措施
1. **官方文档优先**: 始终以EVE ESI官方文档为准
2. **前后端配置同步**: 建立统一的scope配置管理
3. **集成测试**: 包含完整的EVE SSO登录流程测试
4. **文档版本控制**: 及时更新所有相关文档和示例

---

## 常见问题FAQ

### Q: 如何快速定位导入错误？
A: 使用分层测试法，从最底层的模块开始逐层验证导入。

### Q: 如何避免循环导入？
A: 使用延迟导入、重构模块结构、使用依赖注入。

### Q: 如何处理大型项目的依赖管理？
A: 建立清晰的模块层次、使用接口抽象、定期重构。

### Q: 测试环境和生产环境导入行为不一致怎么办？
A: 确保环境一致性、使用容器化部署、建立staging环境。

### Q: EVE SSO集成时如何避免scope错误？
A: 始终参考EVE ESI官方文档，理解基本角色信息无需特殊scope的原理。
