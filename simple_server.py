"""
简单的FastAPI服务器用于测试
"""
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

app = FastAPI(title="EVE Assistant API Demo")

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "EVE Assistant API is running!"}

@app.get("/health")
async def health():
    return {"status": "ok"}

@app.post("/auth/login")
async def login():
    return {
        "success": True,
        "data": {
            "access_token": "demo_token",
            "refresh_token": "demo_refresh",
            "token_type": "bearer",
            "expires_in": 3600,
            "user": {
                "id": "demo_user",
                "email": "<EMAIL>",
                "username": "demo",
                "isVerified": True,
                "characterCount": 2
            }
        }
    }

if __name__ == "__main__":
    print("🚀 启动简单演示服务器...")
    uvicorn.run(app, host="0.0.0.0", port=8000)
