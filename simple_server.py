"""
简单的FastAPI服务器用于测试
"""
from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

app = FastAPI(title="EVE Assistant API Demo")

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "EVE Assistant API is running!"}

@app.get("/health")
async def health():
    return {"status": "ok"}

@app.post("/auth/login")
async def login():
    return {
        "success": True,
        "data": {
            "access_token": "demo_token",
            "refresh_token": "demo_refresh",
            "token_type": "bearer",
            "expires_in": 3600,
            "user": {
                "id": "demo_user",
                "email": "<EMAIL>",
                "username": "demo",
                "isVerified": True,
                "characterCount": 2
            }
        }
    }

@app.post("/auth/eve/login")
async def eve_login():
    """EVE SSO登录接口"""
    return {
        "success": True,
        "data": {
            "login_url": "https://login.eveonline.com/v2/oauth/authorize?response_type=code&redirect_uri=http://localhost:3000/auth/callback&client_id=demo&scope=esi-characters.read_characters.v1&state=demo_state_123",
            "state": "demo_state_123",
            "expires_in": 300,
            "scopes": ["esi-characters.read_characters.v1"]
        }
    }

@app.post("/auth/eve/callback")
async def eve_callback():
    """EVE SSO回调接口"""
    return {
        "success": True,
        "data": {
            "character_id": 12345,
            "character_name": "Demo Character",
            "scopes": ["esi-characters.read_characters.v1"]
        }
    }

@app.get("/auth/me")
async def get_me():
    """获取当前用户信息"""
    return {
        "success": True,
        "data": {
            "id": "demo_user",
            "email": "<EMAIL>",
            "username": "demo",
            "isVerified": True,
            "characterCount": 2,
            "mainCharacterId": 12345,
            "createdAt": "2024-01-01T00:00:00Z",
            "updatedAt": "2024-01-01T00:00:00Z"
        }
    }

if __name__ == "__main__":
    print("🚀 启动简单演示服务器...")
    uvicorn.run(app, host="0.0.0.0", port=8000)
