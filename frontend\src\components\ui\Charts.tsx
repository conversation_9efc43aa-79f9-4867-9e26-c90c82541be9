import { useRef, useEffect } from 'react'
import * as echarts from 'echarts'
import { Card, Empty, Spin } from 'antd'
import { motion } from 'framer-motion'
import clsx from 'clsx'

import { ChartData, TimeSeriesData, ChartOptions } from '@/types'
import { useTheme } from '@/providers/ThemeProvider'

interface BaseChartProps {
  loading?: boolean
  className?: string
  height?: number
  options?: ChartOptions
}

// 基础图表组件
function BaseChart({
  loading = false,
  className = '',
  height = 300,
  children,
}: BaseChartProps & { children: React.ReactNode }) {
  if (loading) {
    return (
      <div className={clsx('flex items-center justify-center', className)} style={{ height }}>
        <Spin size="large" />
      </div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className={clsx('chart-container', className)}
      style={{ height }}
    >
      {children}
    </motion.div>
  )
}

// 饼图组件
interface PieChartProps extends BaseChartProps {
  data: ChartData[]
  title?: string
}

export function PieChart({ data, title, loading, className, height = 300, options }: PieChartProps) {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<echarts.ECharts>()
  const { isDark } = useTheme()

  useEffect(() => {
    if (!chartRef.current || loading) return

    // 初始化图表
    chartInstance.current = echarts.init(chartRef.current, isDark ? 'dark' : 'light')

    const chartOptions = {
      title: {
        text: title || options?.title,
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold',
        },
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        show: options?.showLegend !== false,
      },
      series: [
        {
          name: title || '数据分布',
          type: 'pie',
          radius: '50%',
          data: data.map(item => ({
            value: item.value,
            name: item.name,
            itemStyle: {
              color: item.color,
            },
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
        },
      ],
    }

    chartInstance.current.setOption(chartOptions)

    // 响应式处理
    const handleResize = () => {
      chartInstance.current?.resize()
    }
    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      chartInstance.current?.dispose()
    }
  }, [data, title, options, isDark, loading])

  if (!data || data.length === 0) {
    return (
      <div className={clsx('flex items-center justify-center', className)} style={{ height }}>
        <Empty description="暂无数据" />
      </div>
    )
  }

  return (
    <BaseChart loading={loading} className={className} height={height}>
      <div ref={chartRef} style={{ width: '100%', height: '100%' }} />
    </BaseChart>
  )
}

// 柱状图组件
interface BarChartProps extends BaseChartProps {
  data: ChartData[]
  title?: string
  horizontal?: boolean
}

export function BarChart({
  data,
  title,
  horizontal = false,
  loading,
  className,
  height = 300,
  options
}: BarChartProps) {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<echarts.ECharts>()
  const { isDark } = useTheme()

  useEffect(() => {
    if (!chartRef.current || loading) return

    chartInstance.current = echarts.init(chartRef.current, isDark ? 'dark' : 'light')

    const chartOptions = {
      title: {
        text: title || options?.title,
        left: 'center',
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: horizontal ? 'value' : 'category',
        data: horizontal ? undefined : data.map(item => item.name),
        axisLabel: {
          rotate: horizontal ? 0 : 45,
        },
      },
      yAxis: {
        type: horizontal ? 'category' : 'value',
        data: horizontal ? data.map(item => item.name) : undefined,
      },
      series: [
        {
          name: title || '数值',
          type: 'bar',
          data: data.map(item => ({
            value: item.value,
            itemStyle: {
              color: item.color || (options?.colors?.[0] || '#1890ff'),
            },
          })),
          emphasis: {
            focus: 'series',
          },
        },
      ],
    }

    chartInstance.current.setOption(chartOptions)

    const handleResize = () => {
      chartInstance.current?.resize()
    }
    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      chartInstance.current?.dispose()
    }
  }, [data, title, horizontal, options, isDark, loading])

  if (!data || data.length === 0) {
    return (
      <div className={clsx('flex items-center justify-center', className)} style={{ height }}>
        <Empty description="暂无数据" />
      </div>
    )
  }

  return (
    <BaseChart loading={loading} className={className} height={height}>
      <div ref={chartRef} style={{ width: '100%', height: '100%' }} />
    </BaseChart>
  )
}

// 折线图组件
interface LineChartProps extends BaseChartProps {
  data: TimeSeriesData[]
  title?: string
  smooth?: boolean
}

export function LineChart({
  data,
  title,
  smooth = true,
  loading,
  className,
  height = 300,
  options
}: LineChartProps) {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<echarts.ECharts>()
  const { isDark } = useTheme()

  useEffect(() => {
    if (!chartRef.current || loading) return

    chartInstance.current = echarts.init(chartRef.current, isDark ? 'dark' : 'light')

    const chartOptions = {
      title: {
        text: title || options?.title,
        left: 'center',
      },
      tooltip: {
        trigger: 'axis',
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: data.map(item => new Date(item.timestamp).toLocaleDateString()),
      },
      yAxis: {
        type: 'value',
      },
      series: [
        {
          name: title || '数值',
          type: 'line',
          smooth,
          data: data.map(item => item.value),
          areaStyle: {
            opacity: 0.3,
          },
          lineStyle: {
            color: options?.colors?.[0] || '#1890ff',
          },
          itemStyle: {
            color: options?.colors?.[0] || '#1890ff',
          },
        },
      ],
    }

    chartInstance.current.setOption(chartOptions)

    const handleResize = () => {
      chartInstance.current?.resize()
    }
    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      chartInstance.current?.dispose()
    }
  }, [data, title, smooth, options, isDark, loading])

  if (!data || data.length === 0) {
    return (
      <div className={clsx('flex items-center justify-center', className)} style={{ height }}>
        <Empty description="暂无数据" />
      </div>
    )
  }

  return (
    <BaseChart loading={loading} className={className} height={height}>
      <div ref={chartRef} style={{ width: '100%', height: '100%' }} />
    </BaseChart>
  )
}

// 图表卡片包装器
interface ChartCardProps {
  title: string
  extra?: React.ReactNode
  children: React.ReactNode
  className?: string
}

export function ChartCard({ title, extra, children, className }: ChartCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={className}
    >
      <Card title={title} extra={extra} className="h-full">
        {children}
      </Card>
    </motion.div>
  )
}

// 预设的图表组件
export function AssetDistributionChart({
  data,
  loading
}: {
  data?: ChartData[]
  loading?: boolean
}) {
  const mockData: ChartData[] = [
    { name: '舰船', value: 45, color: '#1890ff' },
    { name: '装备', value: 30, color: '#52c41a' },
    { name: '弹药', value: 15, color: '#faad14' },
    { name: '其他', value: 10, color: '#f5222d' },
  ]

  return (
    <ChartCard title="资产分布">
      <PieChart
        data={data || mockData}
        loading={loading}
        height={300}
      />
    </ChartCard>
  )
}

export function WealthTrendChart({
  data,
  loading
}: {
  data?: TimeSeriesData[]
  loading?: boolean
}) {
  const mockData: TimeSeriesData[] = Array.from({ length: 30 }, (_, i) => ({
    timestamp: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString(),
    value: Math.random() * 1000000000 + 5000000000,
  }))

  return (
    <ChartCard title="财富趋势">
      <LineChart
        data={data || mockData}
        loading={loading}
        height={300}
        smooth
      />
    </ChartCard>
  )
}
