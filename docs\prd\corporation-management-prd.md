# 公司管理模块 - 产品需求文档 (PRD)

## 📋 模块概述

### 模块名称
公司管理模块 (Corporation Management Module)

### 模块愿景
为EVE Online公司管理者提供全面的公司治理和运营管理工具，通过数字化的管理手段提升公司运营效率，加强成员协作，优化资源配置。

### 业务价值
- 🎯 **高效治理**: 简化公司管理流程，提升决策效率
- 🎯 **成员协作**: 增强成员间的沟通和协作能力
- 🎯 **资源优化**: 优化公司资源配置和使用效率
- 🎯 **数据驱动**: 基于数据的公司运营决策支持

## 🎯 功能需求

### 1. 公司基础信息管理

#### 1.1 公司档案
**功能描述**: 管理公司的基本信息和档案数据

**核心功能**:
- ✅ 公司基本信息展示（名称、标志、描述）
- ✅ 公司历史和里程碑记录
- ✅ 公司统计数据概览
- ✅ 联盟关系管理
- ✅ 公司声誉和评级

**数据模型**:
```python
class Corporation(AggregateRoot):
    corporation_id: CorporationId
    name: str
    ticker: str
    description: str
    url: Optional[str]
    alliance_id: Optional[AllianceId]
    ceo_id: CharacterId
    creator_id: CharacterId
    date_founded: datetime
    member_count: int
    shares: int
    tax_rate: float
    faction_id: Optional[FactionId]
    home_station_id: Optional[StationId]
```

### 2. 成员管理

#### 2.1 成员档案
**功能描述**: 全面管理公司成员信息

**核心功能**:
- ✅ 成员基本信息管理
- ✅ 成员技能和能力评估
- ✅ 成员活跃度跟踪
- ✅ 成员贡献度统计
- ✅ 成员历史记录

**数据模型**:
```python
class CorporationMember(Entity):
    character_id: CharacterId
    corporation_id: CorporationId
    name: str
    title: str
    start_date: datetime
    logoff_date: Optional[datetime]
    logon_date: Optional[datetime]
    location_id: Optional[LocationId]
    ship_type_id: Optional[TypeId]
    roles: List[CorporationRole]
    base_id: Optional[LocationId]
    grantable_roles: List[CorporationRole]
```

#### 2.2 招聘管理
**功能描述**: 管理公司招聘流程和申请

**核心功能**:
- 🚀 招聘需求发布
- 🚀 申请审核流程
- 🚀 候选人评估
- 🚀 面试安排管理
- 🚀 招聘数据分析

### 3. 权限和角色管理

#### 3.1 角色定义
**功能描述**: 定义和管理公司内的各种角色

**核心功能**:
- ✅ 标准角色模板
- ✅ 自定义角色创建
- ✅ 角色权限配置
- ✅ 角色继承关系
- ✅ 角色使用统计

**数据模型**:
```python
class CorporationRole(ValueObject):
    role_id: RoleId
    role_name: str
    role_type: RoleType  # director, personnel_manager, accountant, etc.
    permissions: List[Permission]
    is_grantable: bool
    description: str
```

### 4. 财务管理

#### 4.1 公司钱包
**功能描述**: 管理公司的财务资产

**核心功能**:
- ✅ 钱包余额实时监控
- ✅ 收支明细记录
- ✅ 财务报表生成
- ✅ 预算管理
- ✅ 财务审计

**数据模型**:
```python
class CorporationWallet(Entity):
    division: int
    balance: Money
    transactions: List[WalletTransaction]
    journal_entries: List[WalletJournal]
    access_roles: List[CorporationRole]
```

### 5. 资产管理

#### 5.1 公司资产
**功能描述**: 管理公司的各类资产

**核心功能**:
- ✅ 资产清单管理
- ✅ 资产分配和使用
- ✅ 资产价值评估
- ✅ 资产安全监控
- ✅ 资产使用权限

### 6. 活动和任务管理

#### 6.1 公司活动
**功能描述**: 组织和管理公司集体活动

**核心功能**:
- 🚀 活动计划和发布
- 🚀 成员参与管理
- 🚀 活动资源协调
- 🚀 活动效果评估
- 🚀 活动历史记录

## 🔧 技术实现

### 领域模型设计

#### 聚合根: Corporation
```python
class Corporation(AggregateRoot):
    def __init__(self, corporation_id: CorporationId, name: str, ...):
        super().__init__(corporation_id)
        self._members: Dict[CharacterId, CorporationMember] = {}
        self._roles: Dict[RoleId, CorporationRole] = {}
        self._wallets: Dict[int, CorporationWallet] = {}
    
    def add_member(self, member: CorporationMember) -> None:
        """添加成员"""
        self._validate_member_eligibility(member)
        self._members[member.character_id] = member
        self._raise_event(MemberJoinedEvent(self.id, member))
    
    def assign_role(self, character_id: CharacterId, role: CorporationRole) -> None:
        """分配角色"""
        if character_id in self._members:
            self._members[character_id].add_role(role)
            self._raise_event(RoleAssignedEvent(self.id, character_id, role))
```

### 应用服务

#### CorporationApplicationService
```python
class CorporationApplicationService:
    async def get_corporation_overview(
        self, 
        corporation_id: CorporationId
    ) -> CorporationOverviewDTO:
        """获取公司概览"""
        
    async def manage_member_roles(
        self, 
        corporation_id: CorporationId, 
        role_changes: List[RoleChangeDTO]
    ) -> None:
        """管理成员角色"""
        
    async def generate_financial_report(
        self, 
        corporation_id: CorporationId, 
        period: TimePeriod
    ) -> FinancialReportDTO:
        """生成财务报告"""
```

## 📊 用户界面设计

### 1. 公司概览页面
```
┌─────────────────────────────────────────────────────────────┐
│ 公司概览 - [公司名称]                                        │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────┐  ┌─────────────────────────────────────────┐ │
│ │   公司标志   │  │ 基本信息                                 │ │
│ │             │  │ 成立时间: 2020-03-15                    │ │
│ │             │  │ CEO: [角色名]  成员数: 156              │ │
│ │             │  │ 联盟: [联盟名]  税率: 10%               │ │
│ └─────────────┘  │ 总部: Jita IV - Moon 4                  │ │
│                  └─────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 快速统计                                                     │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│ │ 公司资产     │ │ 月收入       │ │ 活跃成员     │ │ 在线率   │ │
│ │ 45.6B ISK   │ │ 2.3B ISK    │ │ 89/156      │ │ 67%     │ │
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2. 成员管理页面
```
┌─────────────────────────────────────────────────────────────┐
│ 成员管理                                                     │
├─────────────────────────────────────────────────────────────┤
│ [搜索框] [筛选▼] [角色▼] [状态▼] [排序: 加入时间▼]          │
├─────────────────────────────────────────────────────────────┤
│ ☑ 成员名称        角色          加入时间    最后在线    状态 │
│ ☑ Character A     Director      2020-03-15  2小时前    在线 │
│ ☑ Character B     Member        2020-04-20  1天前     离线 │
│ ☑ Character C     Accountant    2020-05-10  30分钟前   在线 │
│ ☐ ...                                                        │
├─────────────────────────────────────────────────────────────┤
│ 已选择 3 名成员                                              │
│ [批量分配角色] [发送邮件] [踢出公司] [导出列表]              │
└─────────────────────────────────────────────────────────────┘
```

## 📈 成功指标

### 功能指标
- 成员数据同步准确率 > 99.8%
- 权限变更响应时间 < 30秒
- 财务数据准确率 > 99.9%

### 用户体验指标
- 管理效率提升 > 50%
- 成员满意度 > 4.5/5.0
- 功能使用率 > 85%

---

**文档版本**: v1.0  
**最后更新**: 2025-01-09  
**负责人**: 公司管理团队
