"""
基于Scope驱动的菜单服务
参考SeAT架构设计，根据用户权限动态生成功能菜单
"""
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

from ...infrastructure.config import settings


class MenuItemType(Enum):
    """菜单项类型"""
    CATEGORY = "category"
    PAGE = "page"
    ACTION = "action"
    SEPARATOR = "separator"


@dataclass
class MenuItem:
    """菜单项数据结构"""
    id: str
    title: str
    type: MenuItemType
    icon: Optional[str] = None
    route: Optional[str] = None
    required_scopes: List[str] = None
    children: List['MenuItem'] = None
    badge: Optional[str] = None
    description: Optional[str] = None
    order: int = 0
    
    def __post_init__(self):
        if self.required_scopes is None:
            self.required_scopes = []
        if self.children is None:
            self.children = []


class ScopeDrivenMenuService:
    """基于Scope的菜单服务"""
    
    def __init__(self):
        self.menu_definitions = self._initialize_menu_definitions()
    
    def _initialize_menu_definitions(self) -> Dict[str, MenuItem]:
        """初始化菜单定义"""
        return {
            # 角色管理模块
            "character": MenuItem(
                id="character",
                title="角色管理",
                type=MenuItemType.CATEGORY,
                icon="fa-user",
                order=1,
                children=[
                    MenuItem(
                        id="character.overview",
                        title="角色概览",
                        type=MenuItemType.PAGE,
                        route="/character/overview",
                        icon="fa-dashboard",
                        description="查看角色基本信息和状态",
                        order=1
                    ),
                    MenuItem(
                        id="character.location",
                        title="位置追踪",
                        type=MenuItemType.PAGE,
                        route="/character/location",
                        icon="fa-map-marker",
                        required_scopes=["esi-location.read_location.v1"],
                        description="实时追踪角色位置",
                        order=2
                    ),
                    MenuItem(
                        id="character.skills",
                        title="技能管理",
                        type=MenuItemType.PAGE,
                        route="/character/skills",
                        icon="fa-graduation-cap",
                        required_scopes=["esi-skills.read_skills.v1"],
                        description="查看技能信息和训练队列",
                        order=3
                    ),
                    MenuItem(
                        id="character.assets",
                        title="资产管理",
                        type=MenuItemType.PAGE,
                        route="/character/assets",
                        icon="fa-cubes",
                        required_scopes=["esi-assets.read_assets.v1"],
                        description="管理角色资产",
                        order=4
                    ),
                    MenuItem(
                        id="character.wallet",
                        title="钱包管理",
                        type=MenuItemType.PAGE,
                        route="/character/wallet",
                        icon="fa-credit-card",
                        required_scopes=["esi-wallet.read_character_wallet.v1"],
                        description="查看钱包余额和交易记录",
                        order=5
                    ),
                    MenuItem(
                        id="character.mail",
                        title="邮件系统",
                        type=MenuItemType.PAGE,
                        route="/character/mail",
                        icon="fa-envelope",
                        required_scopes=["esi-mail.read_mail.v1"],
                        description="管理游戏内邮件",
                        order=6
                    ),
                    MenuItem(
                        id="character.contacts",
                        title="联系人",
                        type=MenuItemType.PAGE,
                        route="/character/contacts",
                        icon="fa-address-book",
                        required_scopes=["esi-characters.read_contacts.v1"],
                        description="管理角色联系人",
                        order=7
                    )
                ]
            ),
            
            # 公司管理模块
            "corporation": MenuItem(
                id="corporation",
                title="公司管理",
                type=MenuItemType.CATEGORY,
                icon="fa-building",
                order=2,
                children=[
                    MenuItem(
                        id="corporation.overview",
                        title="公司概览",
                        type=MenuItemType.PAGE,
                        route="/corporation/overview",
                        icon="fa-dashboard",
                        required_scopes=["esi-corporations.read_corporation_membership.v1"],
                        description="查看公司基本信息",
                        order=1
                    ),
                    MenuItem(
                        id="corporation.members",
                        title="成员管理",
                        type=MenuItemType.PAGE,
                        route="/corporation/members",
                        icon="fa-users",
                        required_scopes=["esi-corporations.read_corporation_membership.v1"],
                        description="管理公司成员",
                        order=2
                    ),
                    MenuItem(
                        id="corporation.assets",
                        title="公司资产",
                        type=MenuItemType.PAGE,
                        route="/corporation/assets",
                        icon="fa-warehouse",
                        required_scopes=["esi-assets.read_corporation_assets.v1"],
                        description="管理公司资产",
                        order=3
                    ),
                    MenuItem(
                        id="corporation.wallet",
                        title="公司财务",
                        type=MenuItemType.PAGE,
                        route="/corporation/wallet",
                        icon="fa-money-bill",
                        required_scopes=["esi-wallet.read_corporation_wallets.v1"],
                        description="查看公司财务状况",
                        order=4
                    ),
                    MenuItem(
                        id="corporation.structures",
                        title="建筑管理",
                        type=MenuItemType.PAGE,
                        route="/corporation/structures",
                        icon="fa-building-o",
                        required_scopes=["esi-corporations.read_structures.v1"],
                        description="管理公司建筑",
                        order=5
                    )
                ]
            ),
            
            # 工业管理模块
            "industry": MenuItem(
                id="industry",
                title="工业管理",
                type=MenuItemType.CATEGORY,
                icon="fa-industry",
                order=3,
                children=[
                    MenuItem(
                        id="industry.jobs",
                        title="工业任务",
                        type=MenuItemType.PAGE,
                        route="/industry/jobs",
                        icon="fa-tasks",
                        required_scopes=["esi-industry.read_character_jobs.v1"],
                        description="查看工业任务状态",
                        order=1
                    ),
                    MenuItem(
                        id="industry.mining",
                        title="挖矿记录",
                        type=MenuItemType.PAGE,
                        route="/industry/mining",
                        icon="fa-diamond",
                        required_scopes=["esi-industry.read_character_mining.v1"],
                        description="查看挖矿记录",
                        order=2
                    ),
                    MenuItem(
                        id="industry.blueprints",
                        title="蓝图管理",
                        type=MenuItemType.PAGE,
                        route="/industry/blueprints",
                        icon="fa-file-text",
                        required_scopes=["esi-characters.read_blueprints.v1"],
                        description="管理蓝图",
                        order=3
                    )
                ]
            ),
            
            # 市场管理模块
            "market": MenuItem(
                id="market",
                title="市场管理",
                type=MenuItemType.CATEGORY,
                icon="fa-line-chart",
                order=4,
                children=[
                    MenuItem(
                        id="market.orders",
                        title="市场订单",
                        type=MenuItemType.PAGE,
                        route="/market/orders",
                        icon="fa-shopping-cart",
                        required_scopes=["esi-markets.read_character_orders.v1"],
                        description="管理市场订单",
                        order=1
                    ),
                    MenuItem(
                        id="market.history",
                        title="交易历史",
                        type=MenuItemType.PAGE,
                        route="/market/history",
                        icon="fa-history",
                        required_scopes=["esi-wallet.read_character_wallet.v1"],
                        description="查看交易历史",
                        order=2
                    )
                ]
            ),
            
            # 战斗模块
            "combat": MenuItem(
                id="combat",
                title="战斗记录",
                type=MenuItemType.CATEGORY,
                icon="fa-crosshairs",
                order=5,
                children=[
                    MenuItem(
                        id="combat.killmails",
                        title="击杀邮件",
                        type=MenuItemType.PAGE,
                        route="/combat/killmails",
                        icon="fa-skull",
                        required_scopes=["esi-killmails.read_killmails.v1"],
                        description="查看击杀记录",
                        order=1
                    )
                ]
            ),
            
            # 舰队管理模块
            "fleet": MenuItem(
                id="fleet",
                title="舰队管理",
                type=MenuItemType.CATEGORY,
                icon="fa-rocket",
                order=6,
                children=[
                    MenuItem(
                        id="fleet.management",
                        title="舰队信息",
                        type=MenuItemType.PAGE,
                        route="/fleet/management",
                        icon="fa-users",
                        required_scopes=["esi-fleets.read_fleet.v1"],
                        description="管理舰队",
                        order=1
                    ),
                    MenuItem(
                        id="fleet.fittings",
                        title="装配方案",
                        type=MenuItemType.PAGE,
                        route="/fleet/fittings",
                        icon="fa-wrench",
                        required_scopes=["esi-fittings.read_fittings.v1"],
                        description="管理装配方案",
                        order=2
                    )
                ]
            ),
            
            # 系统管理模块
            "system": MenuItem(
                id="system",
                title="系统管理",
                type=MenuItemType.CATEGORY,
                icon="fa-cogs",
                order=10,
                children=[
                    MenuItem(
                        id="system.settings",
                        title="系统设置",
                        type=MenuItemType.PAGE,
                        route="/system/settings",
                        icon="fa-gear",
                        description="系统配置管理",
                        order=1
                    ),
                    MenuItem(
                        id="system.scopes",
                        title="权限管理",
                        type=MenuItemType.PAGE,
                        route="/system/scopes",
                        icon="fa-key",
                        description="管理ESI权限",
                        order=2
                    )
                ]
            )
        }
    
    def generate_menu_for_user(self, user_scopes: List[str]) -> List[MenuItem]:
        """为用户生成菜单"""
        filtered_menu = []
        
        for menu_item in self.menu_definitions.values():
            filtered_item = self._filter_menu_item(menu_item, user_scopes)
            if filtered_item:
                filtered_menu.append(filtered_item)
        
        # 按order排序
        filtered_menu.sort(key=lambda x: x.order)
        return filtered_menu
    
    def _filter_menu_item(self, item: MenuItem, user_scopes: List[str]) -> Optional[MenuItem]:
        """过滤菜单项"""
        # 检查当前项是否有权限要求
        if item.required_scopes and not self._has_required_scopes(item.required_scopes, user_scopes):
            return None
        
        # 处理子菜单
        filtered_children = []
        if item.children:
            for child in item.children:
                filtered_child = self._filter_menu_item(child, user_scopes)
                if filtered_child:
                    filtered_children.append(filtered_child)
        
        # 如果是分类且没有可访问的子项，则不显示
        if item.type == MenuItemType.CATEGORY and not filtered_children:
            return None
        
        # 创建过滤后的菜单项
        filtered_item = MenuItem(
            id=item.id,
            title=item.title,
            type=item.type,
            icon=item.icon,
            route=item.route,
            required_scopes=item.required_scopes,
            children=filtered_children,
            badge=item.badge,
            description=item.description,
            order=item.order
        )
        
        return filtered_item
    
    def _has_required_scopes(self, required_scopes: List[str], user_scopes: List[str]) -> bool:
        """检查用户是否拥有所需权限"""
        return all(scope in user_scopes for scope in required_scopes)
    
    def get_available_scopes(self) -> List[str]:
        """获取系统中所有可用的权限"""
        return settings.eve_sso_scopes.split()
    
    def get_menu_item_by_id(self, item_id: str) -> Optional[MenuItem]:
        """根据ID获取菜单项"""
        for item in self.menu_definitions.values():
            found = self._find_menu_item_recursive(item, item_id)
            if found:
                return found
        return None
    
    def _find_menu_item_recursive(self, item: MenuItem, target_id: str) -> Optional[MenuItem]:
        """递归查找菜单项"""
        if item.id == target_id:
            return item
        
        if item.children:
            for child in item.children:
                found = self._find_menu_item_recursive(child, target_id)
                if found:
                    return found
        
        return None
    
    def get_breadcrumb(self, item_id: str) -> List[MenuItem]:
        """获取面包屑导航"""
        breadcrumb = []
        
        def build_breadcrumb(item: MenuItem, path: List[MenuItem]) -> bool:
            current_path = path + [item]
            
            if item.id == item_id:
                breadcrumb.extend(current_path)
                return True
            
            if item.children:
                for child in item.children:
                    if build_breadcrumb(child, current_path):
                        return True
            
            return False
        
        for item in self.menu_definitions.values():
            if build_breadcrumb(item, []):
                break
        
        return breadcrumb


# 全局菜单服务实例
menu_service = ScopeDrivenMenuService()
