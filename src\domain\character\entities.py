"""
角色领域实体
"""
from datetime import datetime
from typing import Dict, List, Optional

from ..shared.base_entity import AggregateRoot
from ..shared.value_objects import (
    AllianceId, CharacterId, CorporationId, Location, Money, SecurityStatus, SkillPoints
)
from ..shared.exceptions import DomainValidationError, InvalidCharacterStateError
from .events import CharacterCreatedEvent, CharacterUpdatedEvent, SkillTrainingCompletedEvent
from .value_objects import Attributes, Skill, SkillQueue


class Character(AggregateRoot):
    """角色聚合根"""
    
    def __init__(self, 
                 character_id: CharacterId,
                 name: str,
                 corporation_id: CorporationId,
                 birthday: datetime,
                 race_id: int,
                 bloodline_id: int,
                 gender: str,
                 alliance_id: Optional[AllianceId] = None,
                 faction_id: Optional[int] = None,
                 security_status: Optional[SecurityStatus] = None,
                 description: Optional[str] = None,
                 title: Optional[str] = None):
        super().__init__(character_id)
        
        # 基本信息验证
        if not name or not name.strip():
            raise DomainValidationError("角色名称不能为空")
        if gender not in ["male", "female"]:
            raise DomainValidationError("性别必须是male或female")
        if birthday > datetime.utcnow():
            raise DomainValidationError("生日不能是未来时间")
        
        # 基本属性
        self._character_id = character_id
        self._name = name.strip()
        self._corporation_id = corporation_id
        self._alliance_id = alliance_id
        self._faction_id = faction_id
        self._birthday = birthday
        self._race_id = race_id
        self._bloodline_id = bloodline_id
        self._gender = gender
        self._security_status = security_status or SecurityStatus(0.0)
        self._description = description or ""
        self._title = title or ""
        
        # 复杂属性
        self._attributes = Attributes()
        self._skills: Dict[int, Skill] = {}
        self._skill_queue: List[SkillQueue] = []
        self._wallet_balance = Money(0)
        self._current_location: Optional[Location] = None
        self._is_online = False
        self._last_login: Optional[datetime] = None
        
        # 发布创建事件
        self.add_domain_event(CharacterCreatedEvent(
            character_id=self._character_id.value,
            name=self._name,
            corporation_id=self._corporation_id.value
        ))
    
    @property
    def character_id(self) -> CharacterId:
        """角色ID"""
        return self._character_id
    
    @property
    def name(self) -> str:
        """角色名称"""
        return self._name
    
    @property
    def corporation_id(self) -> CorporationId:
        """公司ID"""
        return self._corporation_id
    
    @property
    def alliance_id(self) -> Optional[AllianceId]:
        """联盟ID"""
        return self._alliance_id
    
    @property
    def faction_id(self) -> Optional[int]:
        """势力ID"""
        return self._faction_id
    
    @property
    def birthday(self) -> datetime:
        """生日"""
        return self._birthday
    
    @property
    def race_id(self) -> int:
        """种族ID"""
        return self._race_id
    
    @property
    def bloodline_id(self) -> int:
        """血统ID"""
        return self._bloodline_id
    
    @property
    def gender(self) -> str:
        """性别"""
        return self._gender
    
    @property
    def security_status(self) -> SecurityStatus:
        """安全等级"""
        return self._security_status
    
    @property
    def description(self) -> str:
        """描述"""
        return self._description
    
    @property
    def title(self) -> str:
        """头衔"""
        return self._title
    
    @property
    def attributes(self) -> Attributes:
        """属性"""
        return self._attributes
    
    @property
    def skills(self) -> Dict[int, Skill]:
        """技能"""
        return self._skills.copy()
    
    @property
    def skill_queue(self) -> List[SkillQueue]:
        """技能队列"""
        return self._skill_queue.copy()
    
    @property
    def wallet_balance(self) -> Money:
        """钱包余额"""
        return self._wallet_balance
    
    @property
    def current_location(self) -> Optional[Location]:
        """当前位置"""
        return self._current_location
    
    @property
    def is_online(self) -> bool:
        """是否在线"""
        return self._is_online
    
    @property
    def last_login(self) -> Optional[datetime]:
        """最后登录时间"""
        return self._last_login
    
    def update_basic_info(self, 
                         corporation_id: Optional[CorporationId] = None,
                         alliance_id: Optional[AllianceId] = None,
                         faction_id: Optional[int] = None,
                         security_status: Optional[SecurityStatus] = None,
                         description: Optional[str] = None,
                         title: Optional[str] = None) -> None:
        """更新基本信息"""
        old_corporation_id = self._corporation_id
        old_alliance_id = self._alliance_id
        
        if corporation_id is not None:
            self._corporation_id = corporation_id
        if alliance_id is not None:
            self._alliance_id = alliance_id
        if faction_id is not None:
            self._faction_id = faction_id
        if security_status is not None:
            self._security_status = security_status
        if description is not None:
            self._description = description
        if title is not None:
            self._title = title
        
        self.mark_as_updated()
        
        # 发布更新事件
        self.add_domain_event(CharacterUpdatedEvent(
            character_id=self._character_id.value,
            old_corporation_id=old_corporation_id.value if old_corporation_id else None,
            new_corporation_id=self._corporation_id.value,
            old_alliance_id=old_alliance_id.value if old_alliance_id else None,
            new_alliance_id=self._alliance_id.value if self._alliance_id else None
        ))
    
    def update_attributes(self, attributes: Attributes) -> None:
        """更新属性"""
        self._attributes = attributes
        self.mark_as_updated()
    
    def update_skill(self, skill: Skill) -> None:
        """更新技能"""
        self._skills[skill.skill_id] = skill
        self.mark_as_updated()
    
    def add_skill_to_queue(self, skill_queue: SkillQueue) -> None:
        """添加技能到队列"""
        # 检查是否已存在相同技能
        for existing in self._skill_queue:
            if existing.skill_id == skill_queue.skill_id:
                raise InvalidCharacterStateError("技能已在队列中")
        
        self._skill_queue.append(skill_queue)
        self.mark_as_updated()
    
    def complete_skill_training(self, skill_id: int) -> None:
        """完成技能训练"""
        # 从队列中移除
        self._skill_queue = [sq for sq in self._skill_queue if sq.skill_id != skill_id]
        
        # 更新技能等级
        if skill_id in self._skills:
            skill = self._skills[skill_id]
            new_skill = skill.level_up()
            self._skills[skill_id] = new_skill
            
            # 发布技能训练完成事件
            self.add_domain_event(SkillTrainingCompletedEvent(
                character_id=self._character_id.value,
                skill_id=skill_id,
                new_level=new_skill.trained_skill_level,
                skill_points_gained=new_skill.skillpoints_in_skill.value
            ))
        
        self.mark_as_updated()
    
    def update_wallet_balance(self, balance: Money) -> None:
        """更新钱包余额"""
        self._wallet_balance = balance
        self.mark_as_updated()
    
    def update_location(self, location: Location) -> None:
        """更新位置"""
        self._current_location = location
        self.mark_as_updated()
    
    def set_online_status(self, is_online: bool, login_time: Optional[datetime] = None) -> None:
        """设置在线状态"""
        self._is_online = is_online
        if is_online and login_time:
            self._last_login = login_time
        self.mark_as_updated()
    
    def get_total_skill_points(self) -> SkillPoints:
        """获取总技能点"""
        total = 0
        for skill in self._skills.values():
            total += skill.skillpoints_in_skill.value
        return SkillPoints(total)
    
    def get_skill_by_id(self, skill_id: int) -> Optional[Skill]:
        """根据ID获取技能"""
        return self._skills.get(skill_id)
    
    def has_skill(self, skill_id: int, min_level: int = 1) -> bool:
        """检查是否拥有指定等级的技能"""
        skill = self._skills.get(skill_id)
        return skill is not None and skill.trained_skill_level >= min_level
    
    def can_train_skill(self, skill_id: int) -> bool:
        """检查是否可以训练技能"""
        # 简化的检查逻辑，实际应该检查前置技能等
        return skill_id not in [sq.skill_id for sq in self._skill_queue]
    
    def __str__(self) -> str:
        return f"Character({self._name}, ID: {self._character_id})"
