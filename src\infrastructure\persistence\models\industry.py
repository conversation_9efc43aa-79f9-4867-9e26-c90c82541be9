"""
工业相关数据库模型
"""
from sqlalchemy import (
    Column, Integer, String, BigInteger, Float, Boolean,
    DateTime, ForeignKey, Index
)
from sqlalchemy.orm import relationship

from .base import BaseModel


class IndustryJobModel(BaseModel):
    """工业任务模型"""
    __tablename__ = "industry_jobs"
    
    job_id = Column(BigInteger, primary_key=True)
    character_id = Column(BigInteger, ForeignKey('characters.character_id'), nullable=True, index=True)
    corporation_id = Column(BigInteger, nullable=True, index=True)
    
    # 任务基本信息
    installer_id = Column(BigInteger, nullable=False)
    facility_id = Column(BigInteger, nullable=False, index=True)
    station_id = Column(BigInteger, nullable=True)
    activity_id = Column(Integer, nullable=False, index=True)  # 1=制造, 3=研究等
    
    # 蓝图信息
    blueprint_id = Column(BigInteger, nullable=False)
    blueprint_type_id = Column(Integer, nullable=False, index=True)
    blueprint_location_id = Column(BigInteger, nullable=False)
    output_location_id = Column(BigInteger, nullable=False)
    
    # 生产信息
    runs = Column(Integer, nullable=False)
    cost = Column(Float, nullable=True)
    licensed_runs = Column(Integer, nullable=True)
    probability = Column(Float, nullable=True)
    product_type_id = Column(Integer, nullable=True, index=True)
    
    # 状态和时间
    status = Column(String(20), nullable=False, index=True)  # active, paused, ready, delivered
    time_in_seconds = Column(Integer, nullable=False)
    start_date = Column(DateTime, nullable=False, index=True)
    end_date = Column(DateTime, nullable=False, index=True)
    pause_date = Column(DateTime, nullable=True)
    completed_date = Column(DateTime, nullable=True)
    completed_character_id = Column(BigInteger, nullable=True)
    successful_runs = Column(Integer, nullable=True)
    
    # 数据同步
    last_sync_at = Column(DateTime, nullable=True)
    
    # 关联关系
    character = relationship("CharacterModel", back_populates="industry_jobs")
    
    # 索引
    __table_args__ = (
        Index('idx_industry_status_date', 'status', 'end_date'),
        Index('idx_industry_activity', 'activity_id', 'blueprint_type_id'),
        Index('idx_industry_facility', 'facility_id', 'start_date'),
    )
