"""
公司和联盟相关数据库模型
"""
from sqlalchemy import (
    Column, Integer, String, BigInteger, Float, Boolean,
    DateTime, Text, Index
)

from .base import BaseModel


class CorporationModel(BaseModel):
    """公司模型"""
    __tablename__ = "corporations"
    
    corporation_id = Column(BigInteger, primary_key=True)
    name = Column(String(255), nullable=False, index=True)
    ticker = Column(String(10), nullable=False, index=True)
    
    # 基本信息
    member_count = Column(Integer, nullable=False, default=0)
    description = Column(Text, default="")
    tax_rate = Column(Float, nullable=False, default=0.0)
    
    # 组织信息
    date_founded = Column(DateTime, nullable=True)
    creator_id = Column(BigInteger, nullable=False)
    ceo_id = Column(BigInteger, nullable=False)
    alliance_id = Column(BigInteger, nullable=True, index=True)
    faction_id = Column(Integer, nullable=True)
    
    # 其他信息
    home_station_id = Column(BigInteger, nullable=True)
    shares = Column(BigInteger, nullable=True)
    url = Column(String(500), default="")
    war_eligible = Column(Boolean, nullable=True)
    
    # 数据同步
    last_sync_at = Column(DateTime, nullable=True)
    
    # 索引
    __table_args__ = (
        Index('idx_corp_alliance', 'alliance_id'),
        Index('idx_corp_ceo', 'ceo_id'),
    )


class AllianceModel(BaseModel):
    """联盟模型"""
    __tablename__ = "alliances"
    
    alliance_id = Column(BigInteger, primary_key=True)
    name = Column(String(255), nullable=False, index=True)
    ticker = Column(String(10), nullable=False, index=True)
    
    # 基本信息
    creator_id = Column(BigInteger, nullable=False)
    creator_corporation_id = Column(BigInteger, nullable=False)
    executor_corporation_id = Column(BigInteger, nullable=True)
    date_founded = Column(DateTime, nullable=False)
    faction_id = Column(Integer, nullable=True)
    
    # 数据同步
    last_sync_at = Column(DateTime, nullable=True)
    
    # 索引
    __table_args__ = (
        Index('idx_alliance_executor', 'executor_corporation_id'),
        Index('idx_alliance_creator', 'creator_corporation_id'),
    )
