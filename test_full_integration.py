#!/usr/bin/env python3
"""
完整的前后端集成测试
"""
import requests
import time
import webbrowser

def test_full_integration():
    print("🔗 MythEVE 完整集成测试")
    print("=" * 60)
    
    # 1. 测试后端API
    print("\n1. 测试后端API...")
    backend_url = "http://localhost:8000"
    
    try:
        # 健康检查
        response = requests.get(f"{backend_url}/health/status", timeout=5)
        if response.status_code == 200:
            print("   ✅ 后端API正常")
        else:
            print(f"   ❌ 后端API异常: {response.status_code}")
            return False
            
        # 认证状态
        response = requests.get(f"{backend_url}/auth/status", timeout=5)
        if response.status_code == 200:
            print("   ✅ 认证服务正常")
        else:
            print(f"   ❌ 认证服务异常: {response.status_code}")
            
        # EVE SSO登录
        response = requests.post(
            f"{backend_url}/auth/eve/login",
            json={"scopes": ["esi-assets.read_assets.v1"]},
            timeout=10
        )
        if response.status_code == 200:
            data = response.json()
            if data.get("success") and data.get("data", {}).get("login_url"):
                print("   ✅ EVE SSO登录URL生成正常")
            else:
                print(f"   ❌ EVE SSO登录失败: {data}")
        else:
            print(f"   ❌ EVE SSO登录请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 后端API测试失败: {e}")
        return False
    
    # 2. 测试前端应用
    print("\n2. 测试前端应用...")
    frontend_url = "http://localhost:3000"
    
    try:
        response = requests.get(frontend_url, timeout=5)
        if response.status_code == 200:
            print("   ✅ 前端应用正常")
            
            # 检查关键元素
            content = response.text
            if "root" in content:
                print("   ✅ React根元素存在")
            if "EVE" in content or "eve" in content.lower():
                print("   ✅ EVE相关内容存在")
            if "script" in content:
                print("   ✅ JavaScript脚本正常")
                
        else:
            print(f"   ❌ 前端应用异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 前端应用测试失败: {e}")
        return False
    
    # 3. 测试前端API代理
    print("\n3. 测试前端API代理...")
    
    try:
        # 通过前端代理访问后端API
        response = requests.get(f"{frontend_url}/api/health/status", timeout=5)
        if response.status_code == 200:
            print("   ✅ 前端API代理正常")
        else:
            print(f"   ⚠️  前端API代理可能有问题: {response.status_code}")
            
    except Exception as e:
        print(f"   ⚠️  前端API代理测试失败: {e}")
        print("   (这可能是正常的，取决于代理配置)")
    
    # 4. 数据库连接测试
    print("\n4. 测试数据库连接...")
    
    try:
        import sys
        sys.path.insert(0, '.')
        
        from src.infrastructure.persistence.database import engine
        from sqlalchemy import text
        
        with engine.connect() as conn:
            result = conn.execute(text("SELECT COUNT(*) FROM users"))
            count = result.fetchone()[0]
            print(f"   ✅ 数据库连接正常，用户数: {count}")
            
    except Exception as e:
        print(f"   ❌ 数据库连接失败: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 完整集成测试通过！")
    print("=" * 60)
    
    print(f"\n📋 服务状态总结:")
    print(f"   🔧 后端API: http://localhost:8000 ✅")
    print(f"   🌐 前端应用: http://localhost:3000 ✅")
    print(f"   🗄️ 数据库: SQLite ✅")
    print(f"   🔐 EVE SSO: 已配置 ✅")
    
    print(f"\n🚀 现在可以使用完整的MythEVE应用了！")
    print(f"   1. 打开浏览器访问: http://localhost:3000")
    print(f"   2. 点击 'EVE Online 账户登录' 进行真实登录")
    print(f"   3. 完成EVE SSO授权流程")
    print(f"   4. 开始管理您的EVE角色和资产")
    
    # 询问是否打开浏览器
    print(f"\n🌐 是否要在浏览器中打开MythEVE应用？")
    user_input = input("   输入 'y' 打开浏览器，其他键跳过: ").strip().lower()
    if user_input == 'y':
        try:
            webbrowser.open(frontend_url)
            print("   ✅ 浏览器已打开MythEVE应用")
        except Exception as e:
            print(f"   ⚠️  无法打开浏览器: {e}")
            print(f"   请手动访问: {frontend_url}")
    
    return True

if __name__ == "__main__":
    success = test_full_integration()
    
    if not success:
        print("\n❌ 集成测试失败，请检查服务状态")
        exit(1)
    else:
        print("\n✅ 所有测试通过，MythEVE应用已就绪！")
