import React, { useState } from 'react'
import { Outlet, useNavigate, useLocation } from 'react-router-dom'
import { Layout, Menu, Avatar, Dropdown, But<PERSON>, Badge, Drawer } from 'antd'
import {
  DashboardOutlined,
  UserOutlined,
  WalletOutlined,
  ShoppingOutlined,
  ToolOutlined,
  SettingOutlined,
  LogoutOutlined,
  BellOutlined,
  MenuOutlined,
  MoonOutlined,
  SunOutlined,
} from '@ant-design/icons'
import { motion } from 'framer-motion'

import { useAuth } from '@/hooks/useAuth'
import { useTheme, useThemeToggle } from '@/providers/ThemeProvider'

const { Header, Sider, Content } = Layout

export function MainLayout() {
  const [collapsed, setCollapsed] = useState(false)
  const [mobileMenuVisible, setMobileMenuVisible] = useState(false)
  const { user, logout } = useAuth()
  const { isDark } = useTheme()
  const { toggleTheme } = useThemeToggle()
  const navigate = useNavigate()
  const location = useLocation()

  // 菜单项配置
  const menuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: '仪表板',
    },
    {
      key: '/characters',
      icon: <UserOutlined />,
      label: '角色管理',
    },
    {
      key: '/assets',
      icon: <WalletOutlined />,
      label: '资产管理',
    },
    {
      key: '/market',
      icon: <ShoppingOutlined />,
      label: '市场交易',
    },
    {
      key: '/industry',
      icon: <ToolOutlined />,
      label: '工业生产',
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '设置',
    },
  ]

  // 用户菜单
  const userMenuItems = [
    {
      key: 'profile',
      label: '个人资料',
      icon: <UserOutlined />,
      onClick: () => navigate('/profile'),
    },
    {
      key: 'settings',
      label: '设置',
      icon: <SettingOutlined />,
      onClick: () => navigate('/settings'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      label: '退出登录',
      icon: <LogoutOutlined />,
      onClick: logout,
    },
  ]

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key)
    setMobileMenuVisible(false)
  }

  // 侧边栏内容
  const sidebarContent = (
    <>
      {/* Logo */}
      <div className="h-16 flex items-center justify-center border-b border-gray-200 dark:border-dark-700">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ type: "spring", stiffness: 200 }}
          className="flex items-center space-x-2"
        >
          <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">E</span>
          </div>
          {!collapsed && (
            <span className="text-lg font-bold text-gray-900 dark:text-gray-100">
              EVE Assistant
            </span>
          )}
        </motion.div>
      </div>

      {/* 菜单 */}
      <Menu
        mode="inline"
        selectedKeys={[location.pathname]}
        items={menuItems}
        onClick={handleMenuClick}
        className="border-r-0 bg-transparent"
      />
    </>
  )

  return (
    <Layout className="min-h-screen">
      {/* 桌面端侧边栏 */}
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        className="hidden lg:block bg-white dark:bg-dark-800 border-r border-gray-200 dark:border-dark-700"
        width={240}
        collapsedWidth={80}
      >
        {sidebarContent}
      </Sider>

      {/* 移动端抽屉菜单 */}
      <Drawer
        title={
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">E</span>
            </div>
            <span className="text-lg font-bold">EVE Assistant</span>
          </div>
        }
        placement="left"
        onClose={() => setMobileMenuVisible(false)}
        open={mobileMenuVisible}
        className="lg:hidden"
        width={280}
      >
        <Menu
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          className="border-r-0"
        />
      </Drawer>

      <Layout>
        {/* 顶部导航栏 */}
        <Header className="bg-white dark:bg-dark-800 border-b border-gray-200 dark:border-dark-700 px-4 flex items-center justify-between h-16">
          <div className="flex items-center space-x-4">
            {/* 移动端菜单按钮 */}
            <Button
              type="text"
              icon={<MenuOutlined />}
              onClick={() => setMobileMenuVisible(true)}
              className="lg:hidden"
            />

            {/* 桌面端折叠按钮 */}
            <Button
              type="text"
              icon={collapsed ? <MenuOutlined /> : <MenuOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              className="hidden lg:flex"
            />

            {/* 页面标题 */}
            <h1 className="text-lg font-semibold text-gray-900 dark:text-gray-100 hidden sm:block">
              {menuItems.find(item => item.key === location.pathname)?.label || '页面'}
            </h1>
          </div>

          <div className="flex items-center space-x-4">
            {/* 主题切换 */}
            <Button
              type="text"
              icon={isDark ? <SunOutlined /> : <MoonOutlined />}
              onClick={toggleTheme}
              className="text-gray-600 dark:text-gray-400"
            />

            {/* 通知 */}
            <Badge count={0} size="small">
              <Button
                type="text"
                icon={<BellOutlined />}
                className="text-gray-600 dark:text-gray-400"
              />
            </Badge>

            {/* 用户菜单 */}
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              trigger={['click']}
            >
              <div className="flex items-center space-x-2 cursor-pointer hover:bg-gray-50 dark:hover:bg-dark-700 rounded-lg px-2 py-1">
                <Avatar
                  size="small"
                  src={user?.mainCharacterId ? 
                    `https://images.evetech.net/characters/${user.mainCharacterId}/portrait?size=64` : 
                    undefined
                  }
                  icon={!user?.mainCharacterId ? <UserOutlined /> : undefined}
                />
                <div className="hidden sm:block text-left">
                  <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {user?.username || '用户'}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {user?.characterCount || 0} 个角色
                  </div>
                </div>
              </div>
            </Dropdown>
          </div>
        </Header>

        {/* 主内容区域 */}
        <Content className="bg-gray-50 dark:bg-dark-900 overflow-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="h-full"
          >
            <Outlet />
          </motion.div>
        </Content>
      </Layout>
    </Layout>
  )
}
