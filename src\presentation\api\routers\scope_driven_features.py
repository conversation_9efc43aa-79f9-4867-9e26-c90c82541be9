"""
基于Scope驱动的功能API路由
展示如何根据用户权限动态提供功能和数据
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import Dict, List, Optional, Any
from pydantic import BaseModel

from ...application.services.scope_driven_menu_service import menu_service, MenuItem
from ...application.services.scope_driven_data_service import data_service, DataCategory
from ...infrastructure.config.unified_config import config_manager
from ...infrastructure.monitoring.error_monitor import error_monitor
from ..dependencies import get_current_user_scopes


router = APIRouter(prefix="/features", tags=["功能管理"])


class MenuResponse(BaseModel):
    """菜单响应模型"""
    success: bool
    data: List[Dict[str, Any]]
    total_items: int


class DataPlanResponse(BaseModel):
    """数据访问计划响应模型"""
    success: bool
    data: Dict[str, Any]


class FeatureAvailabilityResponse(BaseModel):
    """功能可用性响应模型"""
    success: bool
    data: Dict[str, Any]


@router.get("/menu", response_model=MenuResponse)
async def get_user_menu(
    user_scopes: List[str] = Depends(get_current_user_scopes)
) -> MenuResponse:
    """获取用户菜单"""
    try:
        menu_items = menu_service.generate_menu_for_user(user_scopes)
        
        # 转换为字典格式
        menu_data = []
        for item in menu_items:
            menu_data.append(_menu_item_to_dict(item))
        
        return MenuResponse(
            success=True,
            data=menu_data,
            total_items=len(menu_data)
        )
        
    except Exception as e:
        error_monitor.report_error(
            exception=e,
            context={"endpoint": "/features/menu", "scopes_count": len(user_scopes)}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取菜单失败"
        )


@router.get("/data-plan", response_model=DataPlanResponse)
async def get_data_access_plan(
    categories: Optional[List[str]] = Query(None, description="数据分类过滤"),
    user_scopes: List[str] = Depends(get_current_user_scopes)
) -> DataPlanResponse:
    """获取数据访问计划"""
    try:
        # 转换分类参数
        data_categories = None
        if categories:
            data_categories = []
            for cat in categories:
                try:
                    data_categories.append(DataCategory(cat))
                except ValueError:
                    continue
        
        plan = data_service.generate_data_access_plan(user_scopes, data_categories)
        
        return DataPlanResponse(
            success=True,
            data=plan
        )
        
    except Exception as e:
        error_monitor.report_error(
            exception=e,
            context={
                "endpoint": "/features/data-plan",
                "categories": categories,
                "scopes_count": len(user_scopes)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取数据访问计划失败"
        )


@router.get("/availability", response_model=FeatureAvailabilityResponse)
async def get_feature_availability(
    user_scopes: List[str] = Depends(get_current_user_scopes)
) -> FeatureAvailabilityResponse:
    """获取功能可用性分析"""
    try:
        # 获取所有可用的权限
        all_scopes = menu_service.get_available_scopes()
        
        # 分析功能可用性
        availability = {
            "total_scopes": len(all_scopes),
            "user_scopes": len(user_scopes),
            "coverage_percentage": round((len(user_scopes) / len(all_scopes)) * 100, 2),
            "missing_scopes": list(set(all_scopes) - set(user_scopes)),
            "categories": {}
        }
        
        # 按分类分析
        for category in DataCategory:
            category_endpoints = data_service.get_endpoints_by_category(category, all_scopes)
            available_endpoints = data_service.get_endpoints_by_category(category, user_scopes)
            
            availability["categories"][category.value] = {
                "total_endpoints": len(category_endpoints),
                "available_endpoints": len(available_endpoints),
                "coverage_percentage": round(
                    (len(available_endpoints) / len(category_endpoints)) * 100, 2
                ) if category_endpoints else 0,
                "endpoints": {
                    "available": list(available_endpoints.keys()),
                    "missing": list(set(category_endpoints.keys()) - set(available_endpoints.keys()))
                }
            }
        
        return FeatureAvailabilityResponse(
            success=True,
            data=availability
        )
        
    except Exception as e:
        error_monitor.report_error(
            exception=e,
            context={"endpoint": "/features/availability", "scopes_count": len(user_scopes)}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取功能可用性分析失败"
        )


@router.get("/recommendations")
async def get_scope_recommendations(
    focus: Optional[str] = Query(None, description="关注领域: character, corporation, industry, market"),
    user_scopes: List[str] = Depends(get_current_user_scopes)
) -> Dict[str, Any]:
    """获取权限推荐"""
    try:
        recommendations = {
            "current_scopes": len(user_scopes),
            "recommendations": []
        }
        
        # 基础推荐权限
        basic_scopes = [
            "esi-location.read_location.v1",
            "esi-location.read_online.v1", 
            "esi-skills.read_skills.v1",
            "esi-assets.read_assets.v1",
            "esi-wallet.read_character_wallet.v1"
        ]
        
        # 按领域推荐
        focus_scopes = {
            "character": [
                "esi-characters.read_contacts.v1",
                "esi-characters.read_notifications.v1",
                "esi-mail.read_mail.v1",
                "esi-clones.read_clones.v1"
            ],
            "corporation": [
                "esi-corporations.read_corporation_membership.v1",
                "esi-corporations.read_structures.v1",
                "esi-assets.read_corporation_assets.v1",
                "esi-wallet.read_corporation_wallets.v1"
            ],
            "industry": [
                "esi-industry.read_character_jobs.v1",
                "esi-industry.read_character_mining.v1",
                "esi-characters.read_blueprints.v1"
            ],
            "market": [
                "esi-markets.read_character_orders.v1",
                "esi-markets.read_corporation_orders.v1"
            ]
        }
        
        # 生成推荐
        missing_basic = [scope for scope in basic_scopes if scope not in user_scopes]
        if missing_basic:
            recommendations["recommendations"].append({
                "category": "基础功能",
                "priority": "high",
                "scopes": missing_basic,
                "description": "这些权限提供基本的角色管理功能"
            })
        
        if focus and focus in focus_scopes:
            missing_focus = [scope for scope in focus_scopes[focus] if scope not in user_scopes]
            if missing_focus:
                recommendations["recommendations"].append({
                    "category": f"{focus}专项功能",
                    "priority": "medium",
                    "scopes": missing_focus,
                    "description": f"增强{focus}相关功能的权限"
                })
        
        return {
            "success": True,
            "data": recommendations
        }
        
    except Exception as e:
        error_monitor.report_error(
            exception=e,
            context={
                "endpoint": "/features/recommendations",
                "focus": focus,
                "scopes_count": len(user_scopes)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取权限推荐失败"
        )


@router.get("/breadcrumb/{item_id}")
async def get_breadcrumb(
    item_id: str,
    user_scopes: List[str] = Depends(get_current_user_scopes)
) -> Dict[str, Any]:
    """获取面包屑导航"""
    try:
        breadcrumb = menu_service.get_breadcrumb(item_id)
        
        if not breadcrumb:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"未找到菜单项: {item_id}"
            )
        
        breadcrumb_data = []
        for item in breadcrumb:
            breadcrumb_data.append({
                "id": item.id,
                "title": item.title,
                "route": item.route
            })
        
        return {
            "success": True,
            "data": {
                "breadcrumb": breadcrumb_data,
                "current_item": breadcrumb_data[-1] if breadcrumb_data else None
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        error_monitor.report_error(
            exception=e,
            context={"endpoint": f"/features/breadcrumb/{item_id}"}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取面包屑导航失败"
        )


@router.get("/cache-strategy")
async def get_cache_strategy(
    user_scopes: List[str] = Depends(get_current_user_scopes)
) -> Dict[str, Any]:
    """获取缓存策略"""
    try:
        available_endpoints = data_service.get_available_endpoints_for_scopes(user_scopes)
        
        cache_strategy = {
            "total_endpoints": len(available_endpoints),
            "cache_levels": {},
            "recommendations": []
        }
        
        # 按缓存级别分组
        for endpoint_name, endpoint in available_endpoints.items():
            cache_level = endpoint.cache_level.value
            ttl = data_service.get_cache_ttl(endpoint_name)
            
            if cache_level not in cache_strategy["cache_levels"]:
                cache_strategy["cache_levels"][cache_level] = {
                    "endpoints": [],
                    "ttl_seconds": ttl,
                    "description": _get_cache_level_description(cache_level)
                }
            
            cache_strategy["cache_levels"][cache_level]["endpoints"].append({
                "name": endpoint_name,
                "description": endpoint.description,
                "category": endpoint.category.value
            })
        
        # 生成缓存建议
        realtime_count = len(cache_strategy["cache_levels"].get("realtime", {}).get("endpoints", []))
        if realtime_count > 10:
            cache_strategy["recommendations"].append({
                "type": "warning",
                "message": f"您有{realtime_count}个实时数据端点，可能会增加API调用频率"
            })
        
        return {
            "success": True,
            "data": cache_strategy
        }
        
    except Exception as e:
        error_monitor.report_error(
            exception=e,
            context={"endpoint": "/features/cache-strategy", "scopes_count": len(user_scopes)}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取缓存策略失败"
        )


def _menu_item_to_dict(item: MenuItem) -> Dict[str, Any]:
    """将菜单项转换为字典"""
    return {
        "id": item.id,
        "title": item.title,
        "type": item.type.value,
        "icon": item.icon,
        "route": item.route,
        "required_scopes": item.required_scopes,
        "badge": item.badge,
        "description": item.description,
        "order": item.order,
        "children": [_menu_item_to_dict(child) for child in item.children] if item.children else []
    }


def _get_cache_level_description(cache_level: str) -> str:
    """获取缓存级别描述"""
    descriptions = {
        "realtime": "实时数据，30秒缓存，适用于位置、在线状态等",
        "frequent": "频繁更新数据，5分钟缓存，适用于钱包、邮件等",
        "regular": "常规数据，1小时缓存，适用于技能、资产等",
        "daily": "日常数据，24小时缓存，适用于角色信息等",
        "static": "静态数据，7天缓存，适用于宇宙数据等"
    }
    return descriptions.get(cache_level, "未知缓存级别")
