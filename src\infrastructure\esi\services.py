"""
ESI数据服务
"""
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple

from .client import ESIClient
from .cache import cache_manager
from .models_enhanced import (
    CharacterInfo, CharacterSkills, CharacterAssets, CharacterWallet,
    MarketOrder, IndustryJob, SkillQueueItem, CharacterLocation,
    CorporationInfo, AllianceInfo, UniverseType, SolarSystem, Region
)
from ..config.logging import get_logger

logger = get_logger(__name__)


class CharacterDataService:
    """角色数据服务"""
    
    def __init__(self, esi_client: ESIClient):
        self.client = esi_client
    
    async def get_character_info(self, character_id: int, token: str) -> CharacterInfo:
        """获取角色基础信息"""
        endpoint = f"/characters/{character_id}/"
        
        # 检查缓存
        cached = cache_manager.get(endpoint, character_id=character_id)
        if cached:
            return CharacterInfo(**cached.data)
        
        # 从ESI获取数据
        data = await self.client.get(endpoint, token=token)
        
        # 获取公司信息
        corp_info = await self.get_corporation_info(data['corporation_id'])
        
        # 获取联盟信息（如果有）
        alliance_info = None
        if data.get('alliance_id'):
            alliance_info = await self.get_alliance_info(data['alliance_id'])
        
        character_info = CharacterInfo(
            character_id=character_id,
            name=data['name'],
            corporation_id=data['corporation_id'],
            corporation_name=corp_info.name if corp_info else "Unknown",
            alliance_id=data.get('alliance_id'),
            alliance_name=alliance_info.name if alliance_info else None,
            faction_id=data.get('faction_id'),
            race_id=data.get('race_id', 0),
            bloodline_id=data.get('bloodline_id', 0),
            ancestry_id=data.get('ancestry_id', 0),
            gender=data.get('gender', 'male'),
            security_status=data.get('security_status', 0.0),
            birthday=datetime.fromisoformat(data['birthday'].replace('Z', '+00:00')) if data.get('birthday') else None,
            description=data.get('description'),
            portrait_url=f"https://images.evetech.net/characters/{character_id}/portrait?size=256"
        )
        
        # 缓存结果
        cache_manager.set(endpoint, character_info.__dict__, character_id=character_id)
        
        return character_info
    
    async def get_character_skills(self, character_id: int, token: str) -> CharacterSkills:
        """获取角色技能信息"""
        endpoint = f"/characters/{character_id}/skills/"
        
        # 检查缓存
        cached = cache_manager.get(endpoint, character_id=character_id)
        if cached:
            return CharacterSkills(**cached.data)
        
        # 从ESI获取数据
        data = await self.client.get(endpoint, token=token)
        
        skills_dict = {}
        for skill in data.get('skills', []):
            skills_dict[skill['skill_id']] = skill
        
        character_skills = CharacterSkills(
            character_id=character_id,
            total_sp=data.get('total_sp', 0),
            unallocated_sp=data.get('unallocated_sp', 0),
            skills=skills_dict
        )
        
        # 缓存结果
        cache_manager.set(endpoint, character_skills.__dict__, character_id=character_id)
        
        return character_skills
    
    async def get_character_assets(self, character_id: int, token: str) -> CharacterAssets:
        """获取角色资产信息"""
        endpoint = f"/characters/{character_id}/assets/"
        
        # 检查缓存
        cached = cache_manager.get(endpoint, character_id=character_id)
        if cached:
            return CharacterAssets(**cached.data)
        
        # 从ESI获取数据
        assets_data = await self.client.get(endpoint, token=token)
        
        # 计算总价值（这里需要价格数据，暂时设为0）
        total_value = 0.0
        
        character_assets = CharacterAssets(
            character_id=character_id,
            assets=assets_data,
            total_value=total_value,
            last_updated=datetime.utcnow()
        )
        
        # 缓存结果
        cache_manager.set(endpoint, character_assets.__dict__, character_id=character_id)
        
        return character_assets
    
    async def get_character_wallet(self, character_id: int, token: str) -> CharacterWallet:
        """获取角色钱包信息"""
        # 获取钱包余额
        balance_endpoint = f"/characters/{character_id}/wallet/"
        balance = await self.client.get(balance_endpoint, token=token)
        
        # 获取钱包日志（可选）
        journal_endpoint = f"/characters/{character_id}/wallet/journal/"
        try:
            journal = await self.client.get(journal_endpoint, token=token)
        except Exception:
            journal = []
        
        # 获取交易记录（可选）
        transactions_endpoint = f"/characters/{character_id}/wallet/transactions/"
        try:
            transactions = await self.client.get(transactions_endpoint, token=token)
        except Exception:
            transactions = []
        
        character_wallet = CharacterWallet(
            character_id=character_id,
            balance=balance,
            journal=journal,
            transactions=transactions,
            last_updated=datetime.utcnow()
        )
        
        return character_wallet
    
    async def get_character_location(self, character_id: int, token: str) -> CharacterLocation:
        """获取角色位置信息"""
        # 获取位置
        location_endpoint = f"/characters/{character_id}/location/"
        location_data = await self.client.get(location_endpoint, token=token)
        
        # 获取飞船信息
        ship_endpoint = f"/characters/{character_id}/ship/"
        try:
            ship_data = await self.client.get(ship_endpoint, token=token)
        except Exception:
            ship_data = {}
        
        # 获取太阳系信息
        system_info = await UniverseDataService(self.client).get_solar_system(location_data['solar_system_id'])
        
        character_location = CharacterLocation(
            character_id=character_id,
            solar_system_id=location_data['solar_system_id'],
            solar_system_name=system_info.name if system_info else "Unknown",
            station_id=location_data.get('station_id'),
            structure_id=location_data.get('structure_id'),
            ship_type_id=ship_data.get('ship_type_id'),
            ship_name=ship_data.get('ship_name'),
            last_updated=datetime.utcnow()
        )
        
        return character_location
    
    async def get_skill_queue(self, character_id: int, token: str) -> List[SkillQueueItem]:
        """获取技能训练队列"""
        endpoint = f"/characters/{character_id}/skillqueue/"
        
        # 检查缓存
        cached = cache_manager.get(endpoint, character_id=character_id)
        if cached:
            return [SkillQueueItem(**item) for item in cached.data]
        
        # 从ESI获取数据
        queue_data = await self.client.get(endpoint, token=token)
        
        skill_queue = []
        for item in queue_data:
            skill_queue_item = SkillQueueItem(
                character_id=character_id,
                skill_id=item['skill_id'],
                queue_position=item['queue_position'],
                finished_level=item['finished_level'],
                trained_skill_level=item.get('trained_skill_level', 0),
                level_start_sp=item.get('level_start_sp', 0),
                level_end_sp=item.get('level_end_sp', 0),
                training_start_sp=item.get('training_start_sp', 0),
                start_date=datetime.fromisoformat(item['start_date'].replace('Z', '+00:00')) if item.get('start_date') else None,
                finish_date=datetime.fromisoformat(item['finish_date'].replace('Z', '+00:00')) if item.get('finish_date') else None
            )
            skill_queue.append(skill_queue_item)
        
        # 缓存结果
        cache_manager.set(endpoint, [item.__dict__ for item in skill_queue], character_id=character_id)
        
        return skill_queue
    
    async def get_corporation_info(self, corporation_id: int) -> Optional[CorporationInfo]:
        """获取公司信息"""
        endpoint = f"/corporations/{corporation_id}/"
        
        # 检查缓存
        cached = cache_manager.get(endpoint)
        if cached:
            return CorporationInfo(**cached.data)
        
        try:
            data = await self.client.get(endpoint)
            
            corp_info = CorporationInfo(
                corporation_id=corporation_id,
                name=data['name'],
                ticker=data['ticker'],
                member_count=data['member_count'],
                alliance_id=data.get('alliance_id'),
                ceo_id=data.get('ceo_id', 0),
                creator_id=data.get('creator_id', 0),
                date_founded=datetime.fromisoformat(data['date_founded'].replace('Z', '+00:00')) if data.get('date_founded') else None,
                description=data.get('description'),
                faction_id=data.get('faction_id'),
                home_station_id=data.get('home_station_id'),
                shares=data.get('shares', 0),
                tax_rate=data.get('tax_rate', 0.0),
                url=data.get('url'),
                war_eligible=data.get('war_eligible', True)
            )
            
            # 缓存结果
            cache_manager.set(endpoint, corp_info.__dict__)
            
            return corp_info
            
        except Exception as e:
            logger.warning(f"获取公司信息失败: {corporation_id}", error=str(e))
            return None
    
    async def get_alliance_info(self, alliance_id: int) -> Optional[AllianceInfo]:
        """获取联盟信息"""
        endpoint = f"/alliances/{alliance_id}/"
        
        # 检查缓存
        cached = cache_manager.get(endpoint)
        if cached:
            return AllianceInfo(**cached.data)
        
        try:
            data = await self.client.get(endpoint)
            
            alliance_info = AllianceInfo(
                alliance_id=alliance_id,
                name=data['name'],
                ticker=data['ticker'],
                corporations_count=len(data.get('corporations', [])),
                executor_corporation_id=data['executor_corporation_id'],
                creator_id=data['creator_id'],
                creator_corporation_id=data['creator_corporation_id'],
                date_founded=datetime.fromisoformat(data['date_founded'].replace('Z', '+00:00')),
                faction_id=data.get('faction_id')
            )
            
            # 缓存结果
            cache_manager.set(endpoint, alliance_info.__dict__)
            
            return alliance_info
            
        except Exception as e:
            logger.warning(f"获取联盟信息失败: {alliance_id}", error=str(e))
            return None


class MarketDataService:
    """市场数据服务"""
    
    def __init__(self, esi_client: ESIClient):
        self.client = esi_client
    
    async def get_character_orders(self, character_id: int, token: str) -> List[MarketOrder]:
        """获取角色市场订单"""
        endpoint = f"/characters/{character_id}/orders/"
        
        # 检查缓存
        cached = cache_manager.get(endpoint, character_id=character_id)
        if cached:
            return [MarketOrder(**order) for order in cached.data]
        
        # 从ESI获取数据
        orders_data = await self.client.get(endpoint, token=token)
        
        market_orders = []
        for order_data in orders_data:
            market_order = MarketOrder(
                order_id=order_data['order_id'],
                character_id=character_id,
                type_id=order_data['type_id'],
                region_id=order_data['region_id'],
                location_id=order_data['location_id'],
                is_buy_order=order_data['is_buy_order'],
                price=order_data['price'],
                volume_remain=order_data['volume_remain'],
                volume_total=order_data['volume_total'],
                min_volume=order_data['min_volume'],
                duration=order_data['duration'],
                issued=datetime.fromisoformat(order_data['issued'].replace('Z', '+00:00')),
                range=order_data['range'],
                escrow=order_data.get('escrow', 0.0),
                state=order_data.get('state', 'open')
            )
            market_orders.append(market_order)
        
        # 缓存结果
        cache_manager.set(endpoint, [order.__dict__ for order in market_orders], character_id=character_id)
        
        return market_orders


class IndustryDataService:
    """工业数据服务"""
    
    def __init__(self, esi_client: ESIClient):
        self.client = esi_client
    
    async def get_character_industry_jobs(self, character_id: int, token: str) -> List[IndustryJob]:
        """获取角色工业任务"""
        endpoint = f"/characters/{character_id}/industry/jobs/"
        
        # 检查缓存
        cached = cache_manager.get(endpoint, character_id=character_id)
        if cached:
            return [IndustryJob(**job) for job in cached.data]
        
        # 从ESI获取数据
        jobs_data = await self.client.get(endpoint, token=token)
        
        industry_jobs = []
        for job_data in jobs_data:
            industry_job = IndustryJob(
                job_id=job_data['job_id'],
                character_id=character_id,
                installer_id=job_data['installer_id'],
                facility_id=job_data['facility_id'],
                station_id=job_data['station_id'],
                activity_id=job_data['activity_id'],
                blueprint_id=job_data['blueprint_id'],
                blueprint_type_id=job_data['blueprint_type_id'],
                blueprint_location_id=job_data['blueprint_location_id'],
                output_location_id=job_data['output_location_id'],
                runs=job_data['runs'],
                cost=job_data.get('cost'),
                licensed_runs=job_data.get('licensed_runs'),
                probability=job_data.get('probability'),
                product_type_id=job_data.get('product_type_id'),
                status=job_data['status'],
                duration=job_data['duration'],
                start_date=datetime.fromisoformat(job_data['start_date'].replace('Z', '+00:00')),
                end_date=datetime.fromisoformat(job_data['end_date'].replace('Z', '+00:00')),
                pause_date=datetime.fromisoformat(job_data['pause_date'].replace('Z', '+00:00')) if job_data.get('pause_date') else None,
                completed_date=datetime.fromisoformat(job_data['completed_date'].replace('Z', '+00:00')) if job_data.get('completed_date') else None,
                completed_character_id=job_data.get('completed_character_id'),
                successful_runs=job_data.get('successful_runs')
            )
            industry_jobs.append(industry_job)
        
        # 缓存结果
        cache_manager.set(endpoint, [job.__dict__ for job in industry_jobs], character_id=character_id)
        
        return industry_jobs


class UniverseDataService:
    """宇宙数据服务"""
    
    def __init__(self, esi_client: ESIClient):
        self.client = esi_client
    
    async def get_type_info(self, type_id: int) -> Optional[UniverseType]:
        """获取物品类型信息"""
        endpoint = f"/universe/types/{type_id}/"
        
        # 检查缓存
        cached = cache_manager.get(endpoint)
        if cached:
            return UniverseType(**cached.data)
        
        try:
            data = await self.client.get(endpoint)
            
            type_info = UniverseType(
                type_id=type_id,
                name=data['name'],
                description=data.get('description', ''),
                group_id=data['group_id'],
                category_id=data.get('category_id', 0),
                published=data.get('published', True),
                mass=data.get('mass', 0.0),
                volume=data.get('volume', 0.0),
                capacity=data.get('capacity', 0.0),
                portion_size=data.get('portion_size', 1),
                radius=data.get('radius', 0.0),
                icon_id=data.get('icon_id'),
                market_group_id=data.get('market_group_id')
            )
            
            # 缓存结果
            cache_manager.set(endpoint, type_info.__dict__)
            
            return type_info
            
        except Exception as e:
            logger.warning(f"获取物品类型信息失败: {type_id}", error=str(e))
            return None
    
    async def get_solar_system(self, system_id: int) -> Optional[SolarSystem]:
        """获取太阳系信息"""
        endpoint = f"/universe/systems/{system_id}/"
        
        # 检查缓存
        cached = cache_manager.get(endpoint)
        if cached:
            return SolarSystem(**cached.data)
        
        try:
            data = await self.client.get(endpoint)
            
            system_info = SolarSystem(
                system_id=system_id,
                name=data['name'],
                constellation_id=data['constellation_id'],
                region_id=data.get('region_id', 0),
                security_status=data.get('security_status', 0.0),
                star_id=data.get('star_id'),
                stations=data.get('stations', []),
                planets=data.get('planets', [])
            )
            
            # 缓存结果
            cache_manager.set(endpoint, system_info.__dict__)
            
            return system_info
            
        except Exception as e:
            logger.warning(f"获取太阳系信息失败: {system_id}", error=str(e))
            return None
