{"name": "@storybook/channels", "version": "7.6.20", "description": "", "keywords": ["storybook"], "homepage": "https://github.com/storybookjs/storybook/tree/next/code/lib/channels", "bugs": {"url": "https://github.com/storybookjs/storybook/issues"}, "repository": {"type": "git", "url": "https://github.com/storybookjs/storybook.git", "directory": "code/lib/channels"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "license": "MIT", "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "require": "./dist/index.js", "import": "./dist/index.mjs"}, "./dist/postmessage/index": {"types": "./dist/postmessage/index.d.ts", "node": "./dist/postmessage/index.js", "require": "./dist/postmessage/index.js", "import": "./dist/postmessage/index.mjs"}, "./dist/websocket/index": {"types": "./dist/websocket/index.d.ts", "node": "./dist/websocket/index.js", "require": "./dist/websocket/index.js", "import": "./dist/websocket/index.mjs"}, "./package.json": "./package.json"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "typesVersions": {"*": {"*": ["dist/index.d.ts"], "dist/postmessage/index": ["dist/postmessage/index.d.ts"], "dist/websocket/index": ["dist/websocket/index.d.ts"]}}, "files": ["dist/**/*", "README.md", "*.js", "*.d.ts", "!src/**/*"], "scripts": {"check": "node --loader ../../../scripts/node_modules/esbuild-register/loader.js -r ../../../scripts/node_modules/esbuild-register/register.js ../../../scripts/prepare/check.ts", "prep": "node --loader ../../../scripts/node_modules/esbuild-register/loader.js -r ../../../scripts/node_modules/esbuild-register/register.js ../../../scripts/prepare/bundle.ts"}, "dependencies": {"@storybook/client-logger": "7.6.20", "@storybook/core-events": "7.6.20", "@storybook/global": "^5.0.0", "qs": "^6.10.0", "telejson": "^7.2.0", "tiny-invariant": "^1.3.1"}, "devDependencies": {"typescript": "~4.9.3"}, "publishConfig": {"access": "public"}, "bundler": {"entries": ["./src/index.ts", "./src/postmessage/index.ts", "./src/websocket/index.ts"]}, "gitHead": "e6a7fd8a655c69780bc20b9749c2699e44beae17"}