# 前端应用 - 产品需求文档 (PRD)

## 📋 模块概述

### 模块名称
前端应用 (Frontend Application)

### 模块愿景
构建现代化、响应式的EVE Online管理助手Web应用，提供直观、高效的用户界面，支持复杂的数据可视化和实时交互，为EVE玩家提供专业级的管理工具体验。

### 业务价值
- 🎯 **用户体验**: 提供直观、响应式的现代化界面
- 🎯 **数据可视化**: 复杂EVE数据的清晰展示和分析
- 🎯 **实时交互**: 支持实时数据更新和用户操作反馈
- 🎯 **跨平台**: 支持桌面和移动端访问

## 🎯 技术栈选型

### 核心技术栈
```typescript
// 前端框架
React 18.2+ with TypeScript 5.0+
Vite 5.0+ (构建工具)
pnpm (包管理器)

// 状态管理
Zustand 4.0+ (轻量级状态管理)
TanStack Query 5.0+ (服务端状态管理)

// UI框架
Ant Design 5.0+ (企业级UI组件)
Tailwind CSS 3.0+ (原子化CSS)
Framer Motion (动画库)

// 数据可视化
Apache ECharts 5.0+ (图表库)
D3.js (自定义可视化)

// 路由和导航
React Router v6
React Helmet Async (SEO)

// 开发工具
ESLint + Prettier (代码规范)
Husky + lint-staged (Git钩子)
Storybook (组件文档)
```

### 选型理由

#### 1. React + TypeScript
- **类型安全**: 与Python后端的强类型理念一致
- **生态丰富**: 大量EVE相关的React组件和工具
- **团队熟悉度**: 广泛的开发者支持
- **性能优秀**: 适合数据密集型应用

#### 2. Ant Design
- **企业级**: 专为复杂管理界面设计
- **组件丰富**: Table、Form、Chart等核心组件
- **EVE适配**: 适合游戏数据管理界面
- **国际化**: 内置多语言支持

#### 3. TanStack Query
- **API集成**: 完美配合REST + GraphQL后端
- **缓存策略**: 智能缓存和数据同步
- **离线支持**: 适合游戏工具的使用场景

## 🏗️ 应用架构设计

### 1. 项目结构
```
frontend/
├── src/
│   ├── components/          # 通用组件
│   │   ├── ui/             # 基础UI组件
│   │   ├── charts/         # 图表组件
│   │   ├── forms/          # 表单组件
│   │   └── layout/         # 布局组件
│   ├── pages/              # 页面组件
│   │   ├── auth/           # 认证页面
│   │   ├── dashboard/      # 仪表板
│   │   ├── characters/     # 角色管理
│   │   ├── assets/         # 资产管理
│   │   ├── market/         # 市场交易
│   │   └── industry/       # 工业生产
│   ├── hooks/              # 自定义Hooks
│   ├── services/           # API服务
│   ├── stores/             # 状态管理
│   ├── utils/              # 工具函数
│   ├── types/              # TypeScript类型
│   └── constants/          # 常量定义
├── public/                 # 静态资源
├── docs/                   # 组件文档
└── tests/                  # 测试文件
```

### 2. 状态管理架构
```typescript
// 全局状态结构
interface AppState {
  // 用户认证状态
  auth: {
    user: User | null
    isAuthenticated: boolean
    tokens: TokenPair | null
  }
  
  // 角色数据状态
  characters: {
    activeCharacter: Character | null
    characterList: Character[]
    characterData: Record<number, CharacterData>
  }
  
  // UI状态
  ui: {
    theme: 'light' | 'dark'
    sidebarCollapsed: boolean
    loading: Record<string, boolean>
    notifications: Notification[]
  }
  
  // 应用设置
  settings: {
    language: string
    timezone: string
    refreshIntervals: Record<string, number>
  }
}
```

### 3. 组件设计原则

#### 3.1 组件分层
```typescript
// 1. 基础组件层 (ui/)
export const Button: React.FC<ButtonProps>
export const Input: React.FC<InputProps>
export const Modal: React.FC<ModalProps>

// 2. 业务组件层 (components/)
export const CharacterCard: React.FC<CharacterCardProps>
export const SkillQueue: React.FC<SkillQueueProps>
export const AssetTable: React.FC<AssetTableProps>

// 3. 页面组件层 (pages/)
export const CharacterManagement: React.FC
export const MarketAnalysis: React.FC
export const IndustryDashboard: React.FC
```

#### 3.2 数据获取模式
```typescript
// 使用TanStack Query进行数据管理
const useCharacterData = (characterId: number) => {
  return useQuery({
    queryKey: ['character', characterId],
    queryFn: () => characterService.getCharacter(characterId),
    staleTime: 5 * 60 * 1000, // 5分钟
    refetchInterval: 30 * 1000, // 30秒自动刷新
  })
}

// 实时数据订阅
const useRealtimeData = (endpoint: string) => {
  return useSubscription({
    endpoint,
    onData: (data) => queryClient.setQueryData(['realtime', endpoint], data)
  })
}
```

## 🎨 UI/UX设计规范

### 1. 设计系统

#### 1.1 色彩系统
```scss
// EVE主题色彩
$primary-color: #1890ff;      // 主色调
$success-color: #52c41a;      // 成功色
$warning-color: #faad14;      // 警告色
$error-color: #f5222d;        // 错误色

// EVE特色色彩
$eve-gold: #ffd700;           // EVE金色
$eve-blue: #00bfff;           // EVE蓝色
$eve-red: #ff4444;            // EVE红色
$eve-green: #00ff00;          // EVE绿色

// 暗色主题
$dark-bg-primary: #141414;    // 主背景
$dark-bg-secondary: #1f1f1f;  // 次背景
$dark-text-primary: #ffffff;  // 主文字
$dark-text-secondary: #a6a6a6; // 次文字
```

#### 1.2 字体系统
```scss
// 字体族
$font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
$font-family-mono: 'JetBrains Mono', 'Fira Code', monospace;

// 字体大小
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-base: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-2xl: 24px;
$font-size-3xl: 32px;
```

### 2. 组件设计规范

#### 2.1 数据表格
```typescript
// 标准数据表格配置
const tableConfig = {
  size: 'small',
  bordered: true,
  scroll: { x: 'max-content', y: 400 },
  pagination: {
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total, range) => 
      `${range[0]}-${range[1]} of ${total} items`
  }
}
```

#### 2.2 图表样式
```typescript
// ECharts主题配置
const echartsTheme = {
  color: ['#1890ff', '#52c41a', '#faad14', '#f5222d'],
  backgroundColor: 'transparent',
  textStyle: {
    fontFamily: 'Inter, sans-serif',
    fontSize: 12
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  }
}
```

## 📱 响应式设计

### 断点系统
```scss
// Tailwind CSS断点
$breakpoints: (
  'sm': 640px,   // 手机横屏
  'md': 768px,   // 平板
  'lg': 1024px,  // 小桌面
  'xl': 1280px,  // 桌面
  '2xl': 1536px  // 大桌面
);
```

### 布局适配
```typescript
// 响应式布局Hook
const useResponsive = () => {
  const [breakpoint, setBreakpoint] = useState<string>('xl')
  
  useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth
      if (width < 640) setBreakpoint('xs')
      else if (width < 768) setBreakpoint('sm')
      else if (width < 1024) setBreakpoint('md')
      else if (width < 1280) setBreakpoint('lg')
      else setBreakpoint('xl')
    }
    
    updateBreakpoint()
    window.addEventListener('resize', updateBreakpoint)
    return () => window.removeEventListener('resize', updateBreakpoint)
  }, [])
  
  return { breakpoint, isMobile: breakpoint === 'xs' || breakpoint === 'sm' }
}
```

## 🔧 开发工具配置

### 1. 代码质量
```json
// .eslintrc.json
{
  "extends": [
    "@typescript-eslint/recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended"
  ],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "react/prop-types": "off",
    "react/react-in-jsx-scope": "off"
  }
}
```

### 2. 构建配置
```typescript
// vite.config.ts
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@components': path.resolve(__dirname, './src/components'),
      '@pages': path.resolve(__dirname, './src/pages'),
      '@services': path.resolve(__dirname, './src/services'),
      '@utils': path.resolve(__dirname, './src/utils')
    }
  },
  build: {
    target: 'es2020',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          antd: ['antd'],
          charts: ['echarts']
        }
      }
    }
  }
})
```

## 📊 性能优化策略

### 1. 代码分割
```typescript
// 路由级别的代码分割
const CharacterManagement = lazy(() => import('@pages/characters/CharacterManagement'))
const MarketAnalysis = lazy(() => import('@pages/market/MarketAnalysis'))

// 组件级别的代码分割
const HeavyChart = lazy(() => import('@components/charts/HeavyChart'))
```

### 2. 数据优化
```typescript
// 虚拟滚动大数据表格
const VirtualTable = ({ data }: { data: any[] }) => {
  return (
    <FixedSizeList
      height={400}
      itemCount={data.length}
      itemSize={50}
      itemData={data}
    >
      {Row}
    </FixedSizeList>
  )
}

// 数据缓存策略
const cacheConfig = {
  staleTime: 5 * 60 * 1000,    // 5分钟内数据视为新鲜
  cacheTime: 30 * 60 * 1000,   // 30分钟后清除缓存
  refetchOnWindowFocus: false,  // 窗口聚焦时不自动刷新
  retry: 3                     // 失败重试3次
}
```

## 🔍 测试策略

### 1. 单元测试
```typescript
// 组件测试示例
describe('CharacterCard', () => {
  it('should render character information correctly', () => {
    const character = mockCharacter()
    render(<CharacterCard character={character} />)
    
    expect(screen.getByText(character.name)).toBeInTheDocument()
    expect(screen.getByText(character.corporation)).toBeInTheDocument()
  })
})
```

### 2. 集成测试
```typescript
// API集成测试
describe('Character API Integration', () => {
  it('should fetch and display character data', async () => {
    const { result } = renderHook(() => useCharacterData(12345))
    
    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true)
      expect(result.current.data).toBeDefined()
    })
  })
})
```

## 📈 成功指标

### 技术指标
- 首屏加载时间 < 2秒
- 交互响应时间 < 100ms
- 代码覆盖率 > 80%
- Bundle大小 < 1MB (gzipped)

### 用户体验指标
- 用户满意度 > 4.5/5.0
- 任务完成率 > 95%
- 错误率 < 1%
- 移动端适配评分 > 90%

---

**文档版本**: v1.0  
**最后更新**: 2025-01-09  
**负责人**: 前端开发团队
