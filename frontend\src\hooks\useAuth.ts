import { useContext } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

import { User, LoginCredentials, RegisterData } from '@/types'
import { useAuth as useAuthContext } from '@/providers/AuthProvider'
import ApiService from '@/services/api'

// 重新导出AuthProvider的useAuth
export { useAuth } from '@/providers/AuthProvider'

// 用户信息查询Hook
export function useUserInfo() {
  const { user, isAuthenticated } = useAuthContext()

  return useQuery({
    queryKey: ['user', 'me'],
    queryFn: async () => {
      const response = await ApiService.get<User>('/auth/me')
      return response.data
    },
    enabled: isAuthenticated,
    staleTime: 5 * 60 * 1000, // 5分钟
    initialData: user,
  })
}

// 登录Mutation Hook
export function useLogin() {
  const { login } = useAuthContext()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (credentials: LoginCredentials) => login(credentials),
    onSuccess: () => {
      // 清除相关查询缓存
      queryClient.invalidateQueries({ queryKey: ['user'] })
    },
  })
}

// 注册Mutation Hook
export function useRegister() {
  const { register } = useAuthContext()

  return useMutation({
    mutationFn: (data: RegisterData) => register(data),
  })
}

// 登出Mutation Hook
export function useLogout() {
  const { logout } = useAuthContext()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: () => logout(),
    onSuccess: () => {
      // 清除所有查询缓存
      queryClient.clear()
    },
  })
}

// EVE登录Hook
export function useEVELogin() {
  const { initiateEVELogin, completeEVELogin } = useAuthContext()
  const queryClient = useQueryClient()

  const initiate = useMutation({
    mutationFn: (scopes: string[]) => initiateEVELogin(scopes),
  })

  const complete = useMutation({
    mutationFn: ({ code, state }: { code: string; state: string }) =>
      completeEVELogin(code, state),
    onSuccess: () => {
      // 刷新用户信息
      queryClient.invalidateQueries({ queryKey: ['user'] })
      queryClient.invalidateQueries({ queryKey: ['characters'] })
    },
  })

  return {
    initiate,
    complete,
  }
}

// 权限检查Hook
export function usePermissions() {
  const { user } = useAuthContext()

  const hasPermission = (permission: string): boolean => {
    // 这里需要根据实际的权限系统实现
    // 暂时简化实现
    return !!user
  }

  const hasAnyPermission = (permissions: string[]): boolean => {
    return permissions.some(permission => hasPermission(permission))
  }

  const hasAllPermissions = (permissions: string[]): boolean => {
    return permissions.every(permission => hasPermission(permission))
  }

  return {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
  }
}

// 会话管理Hook
export function useSession() {
  const { user, isAuthenticated, isLoading } = useAuthContext()

  const getSessionInfo = () => {
    if (!isAuthenticated || !user) {
      return null
    }

    return {
      userId: user.id,
      username: user.username,
      email: user.email,
      isVerified: user.isVerified,
      characterCount: user.characterCount,
      mainCharacterId: user.mainCharacterId,
      lastLoginAt: user.lastLoginAt,
    }
  }

  const getSessionDuration = () => {
    const loginTime = localStorage.getItem('eve_assistant_login_time')
    if (!loginTime) return 0

    const loginDate = new Date(loginTime)
    const now = new Date()
    return now.getTime() - loginDate.getTime()
  }

  const formatSessionDuration = () => {
    const duration = getSessionDuration()
    const hours = Math.floor(duration / (1000 * 60 * 60))
    const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60))

    if (hours > 0) {
      return `${hours}小时${minutes}分钟`
    }
    return `${minutes}分钟`
  }

  return {
    sessionInfo: getSessionInfo(),
    sessionDuration: getSessionDuration(),
    formattedSessionDuration: formatSessionDuration(),
    isActive: isAuthenticated && !isLoading,
  }
}

// 角色管理Hook
export function useCharacters() {
  const { isAuthenticated } = useAuthContext()

  return useQuery({
    queryKey: ['characters'],
    queryFn: async () => {
      const response = await ApiService.get('/characters')
      return response.data
    },
    enabled: isAuthenticated,
    staleTime: 2 * 60 * 1000, // 2分钟
  })
}

// 主角色Hook
export function useMainCharacter() {
  const { user } = useAuthContext()
  const { data: characters } = useCharacters()

  const mainCharacter = characters?.find(
    (char: any) => char.id === user?.mainCharacterId
  )

  return {
    mainCharacter,
    hasMainCharacter: !!mainCharacter,
  }
}

// 认证状态监听Hook
export function useAuthStatus() {
  const { isAuthenticated, isLoading, user } = useAuthContext()

  const status = (() => {
    if (isLoading) return 'loading'
    if (!isAuthenticated) return 'unauthenticated'
    if (!user?.isVerified) return 'unverified'
    if (user.characterCount === 0) return 'no_characters'
    return 'authenticated'
  })()

  return {
    status,
    isLoading,
    isAuthenticated,
    isVerified: user?.isVerified || false,
    hasCharacters: (user?.characterCount || 0) > 0,
    needsVerification: isAuthenticated && !user?.isVerified,
    needsCharacters: isAuthenticated && user?.isVerified && user.characterCount === 0,
  }
}

// 自动登录Hook
export function useAutoLogin() {
  const { isAuthenticated, isLoading } = useAuthContext()

  // 检查是否应该自动登录
  const shouldAutoLogin = () => {
    const rememberMe = localStorage.getItem('eve_assistant_remember_me')
    return rememberMe === 'true' && !isAuthenticated && !isLoading
  }

  return {
    shouldAutoLogin: shouldAutoLogin(),
    canAutoLogin: !isLoading,
  }
}
