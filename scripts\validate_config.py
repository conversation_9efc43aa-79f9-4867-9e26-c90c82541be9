#!/usr/bin/env python3
"""
配置验证脚本
验证EVE Online Assistant的配置是否正确
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.infrastructure.config import settings
    from src.infrastructure.config.logging import get_logger
except ImportError as e:
    print(f"❌ 导入配置模块失败: {e}")
    print("请确保您在项目根目录运行此脚本")
    sys.exit(1)

logger = get_logger(__name__)


def validate_basic_config():
    """验证基础配置"""
    print("🔍 验证基础配置...")
    
    issues = []
    
    # 检查必需的配置
    if not settings.secret_key or settings.secret_key == "development-secret-key-change-in-production":
        issues.append("⚠️  SECRET_KEY 使用默认值，生产环境请更改")
    
    if settings.debug:
        issues.append("⚠️  DEBUG 模式已启用，生产环境请设置为false")
    
    # 检查数据库配置
    if not settings.database_url:
        issues.append("❌ DATABASE_URL 未配置")
    else:
        print(f"✅ 数据库配置: {settings.database_url}")
    
    # 检查Redis配置
    if not settings.redis_url:
        issues.append("❌ REDIS_URL 未配置")
    else:
        print(f"✅ Redis配置: {settings.redis_url}")
    
    return issues


def validate_esi_config():
    """验证ESI API配置"""
    print("\n🔍 验证ESI API配置...")
    
    issues = []
    
    # 检查ESI配置
    if not settings.esi_base_url:
        issues.append("❌ ESI_BASE_URL 未配置")
    else:
        print(f"✅ ESI基础URL: {settings.esi_base_url}")
    
    if not settings.esi_user_agent:
        issues.append("❌ ESI_USER_AGENT 未配置")
    else:
        print(f"✅ ESI用户代理: {settings.esi_user_agent}")
        
        # 验证User-Agent格式
        if not ("/" in settings.esi_user_agent and "(" in settings.esi_user_agent):
            issues.append("⚠️  ESI_USER_AGENT 格式建议: 'AppName/Version (Contact Info)'")
    
    return issues


def validate_sso_config():
    """验证EVE SSO配置"""
    print("\n🔍 验证EVE SSO配置...")
    
    issues = []
    
    # 检查SSO配置
    if not settings.eve_sso_client_id:
        issues.append("❌ EVE_SSO_CLIENT_ID 未配置")
    elif settings.eve_sso_client_id == "your-client-id-here":
        issues.append("❌ EVE_SSO_CLIENT_ID 使用默认值，请设置您的实际Client ID")
    else:
        print(f"✅ EVE SSO Client ID: {settings.eve_sso_client_id}")
    
    if not settings.eve_sso_client_secret:
        issues.append("❌ EVE_SSO_CLIENT_SECRET 未配置")
    elif settings.eve_sso_client_secret == "your-client-secret-here":
        issues.append("❌ EVE_SSO_CLIENT_SECRET 使用默认值，请设置您的实际Client Secret")
    else:
        print(f"✅ EVE SSO Client Secret: {'*' * (len(settings.eve_sso_client_secret) - 4) + settings.eve_sso_client_secret[-4:]}")
    
    if not settings.eve_sso_callback_url:
        issues.append("❌ EVE_SSO_CALLBACK_URL 未配置")
    else:
        print(f"✅ EVE SSO回调URL: {settings.eve_sso_callback_url}")
        
        # 验证回调URL格式
        if not settings.eve_sso_callback_url.startswith(("http://", "https://")):
            issues.append("⚠️  EVE_SSO_CALLBACK_URL 应该以 http:// 或 https:// 开头")
    
    if not settings.eve_sso_base_url:
        issues.append("❌ EVE_SSO_BASE_URL 未配置")
    else:
        print(f"✅ EVE SSO基础URL: {settings.eve_sso_base_url}")
    
    # 检查权限范围
    if hasattr(settings, 'eve_sso_scopes'):
        print(f"✅ EVE SSO权限范围: {settings.eve_sso_scopes}")
        
        # 验证权限范围格式
        if not settings.eve_sso_scopes.startswith("esi-"):
            issues.append("⚠️  EVE_SSO_SCOPES 应该以 'esi-' 开头")
    else:
        issues.append("⚠️  EVE_SSO_SCOPES 未配置，将使用默认权限")
    
    # 检查状态超时
    if hasattr(settings, 'eve_sso_state_timeout'):
        print(f"✅ EVE SSO状态超时: {settings.eve_sso_state_timeout}秒")
        
        if settings.eve_sso_state_timeout < 60:
            issues.append("⚠️  EVE_SSO_STATE_TIMEOUT 建议设置为至少60秒")
    
    return issues


def validate_storage_config():
    """验证存储配置"""
    print("\n🔍 验证存储配置...")
    
    issues = []
    
    # 检查存储路径
    if hasattr(settings, 'storage_base_path'):
        storage_path = Path(settings.storage_base_path)
        if not storage_path.exists():
            try:
                storage_path.mkdir(parents=True, exist_ok=True)
                print(f"✅ 存储路径已创建: {storage_path}")
            except Exception as e:
                issues.append(f"❌ 无法创建存储路径 {storage_path}: {e}")
        else:
            print(f"✅ 存储路径: {storage_path}")
    
    # 检查缓存配置
    cache_configs = [
        ("CACHE_TTL_REALTIME", getattr(settings, 'cache_ttl_realtime', None)),
        ("CACHE_TTL_FREQUENT", getattr(settings, 'cache_ttl_frequent', None)),
        ("CACHE_TTL_REGULAR", getattr(settings, 'cache_ttl_regular', None)),
        ("CACHE_TTL_DAILY", getattr(settings, 'cache_ttl_daily', None)),
    ]
    
    for name, value in cache_configs:
        if value is not None:
            print(f"✅ {name}: {value}秒")
        else:
            issues.append(f"⚠️  {name} 未配置")
    
    return issues


def validate_rate_limiting():
    """验证限流配置"""
    print("\n🔍 验证限流配置...")
    
    issues = []
    
    if hasattr(settings, 'rate_limit_requests_per_second'):
        rps = settings.rate_limit_requests_per_second
        print(f"✅ 每秒请求限制: {rps}")
        
        if rps > 20:
            issues.append("⚠️  每秒请求限制过高，ESI API建议不超过20请求/秒")
    
    if hasattr(settings, 'rate_limit_burst'):
        burst = settings.rate_limit_burst
        print(f"✅ 突发请求限制: {burst}")
        
        if burst > 40:
            issues.append("⚠️  突发请求限制过高，建议不超过40")
    
    return issues


def test_config_loading():
    """测试配置加载"""
    print("\n🔍 测试配置加载...")
    
    try:
        # 尝试访问所有主要配置
        config_items = [
            ("应用名称", settings.app_name),
            ("应用版本", settings.app_version),
            ("调试模式", settings.debug),
            ("数据库URL", settings.database_url),
            ("Redis URL", settings.redis_url),
            ("ESI基础URL", settings.esi_base_url),
            ("ESI用户代理", settings.esi_user_agent),
            ("EVE SSO Client ID", settings.eve_sso_client_id),
            ("EVE SSO回调URL", settings.eve_sso_callback_url),
        ]
        
        print("✅ 配置加载成功:")
        for name, value in config_items:
            if "secret" in name.lower() or "密钥" in name:
                display_value = "*" * 8 if value else "未设置"
            else:
                display_value = value
            print(f"   {name}: {display_value}")
        
        return []
        
    except Exception as e:
        return [f"❌ 配置加载失败: {e}"]


def main():
    """主函数"""
    print("🚀 EVE Online Assistant 配置验证")
    print("=" * 50)
    
    all_issues = []
    
    # 执行各项验证
    all_issues.extend(validate_basic_config())
    all_issues.extend(validate_esi_config())
    all_issues.extend(validate_sso_config())
    all_issues.extend(validate_storage_config())
    all_issues.extend(validate_rate_limiting())
    all_issues.extend(test_config_loading())
    
    # 输出结果
    print("\n" + "=" * 50)
    print("📋 验证结果:")
    
    if not all_issues:
        print("🎉 所有配置验证通过！")
        return 0
    else:
        print(f"⚠️  发现 {len(all_issues)} 个问题:")
        for issue in all_issues:
            print(f"   {issue}")
        
        print("\n💡 建议:")
        print("   1. 检查 .env 文件中的配置")
        print("   2. 确保所有必需的环境变量都已设置")
        print("   3. 验证EVE Developer应用的配置")
        print("   4. 检查网络连接和服务状态")
        
        return 1


if __name__ == "__main__":
    sys.exit(main())
