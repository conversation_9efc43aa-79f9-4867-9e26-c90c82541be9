# 项目结构说明

## 目录结构概览

```
eve-online-assistant/
├── src/                                    # 源代码根目录
│   ├── __init__.py
│   ├── domain/                            # 领域层 (Domain Layer)
│   │   ├── __init__.py
│   │   ├── character/                     # 角色管理域
│   │   │   ├── __init__.py
│   │   │   ├── entities.py               # 角色实体
│   │   │   ├── value_objects.py          # 角色值对象
│   │   │   ├── repositories.py           # 角色仓储接口
│   │   │   ├── services.py               # 角色领域服务
│   │   │   └── events.py                 # 角色领域事件
│   │   ├── asset/                        # 资产管理域
│   │   │   ├── __init__.py
│   │   │   ├── entities.py               # 资产实体
│   │   │   ├── value_objects.py          # 资产值对象
│   │   │   ├── repositories.py           # 资产仓储接口
│   │   │   ├── services.py               # 资产领域服务
│   │   │   └── events.py                 # 资产领域事件
│   │   ├── market/                       # 市场交易域
│   │   │   ├── __init__.py
│   │   │   ├── entities.py               # 市场实体
│   │   │   ├── value_objects.py          # 市场值对象
│   │   │   ├── repositories.py           # 市场仓储接口
│   │   │   ├── services.py               # 市场领域服务
│   │   │   └── events.py                 # 市场领域事件
│   │   ├── industry/                     # 工业生产域
│   │   │   ├── __init__.py
│   │   │   ├── entities.py               # 工业实体
│   │   │   ├── value_objects.py          # 工业值对象
│   │   │   ├── repositories.py           # 工业仓储接口
│   │   │   ├── services.py               # 工业领域服务
│   │   │   └── events.py                 # 工业领域事件
│   │   ├── corporation/                  # 公司管理域
│   │   │   ├── __init__.py
│   │   │   ├── entities.py               # 公司实体
│   │   │   ├── value_objects.py          # 公司值对象
│   │   │   ├── repositories.py           # 公司仓储接口
│   │   │   ├── services.py               # 公司领域服务
│   │   │   └── events.py                 # 公司领域事件
│   │   └── shared/                       # 共享领域组件
│   │       ├── __init__.py
│   │       ├── base_entity.py            # 基础实体类
│   │       ├── value_objects.py          # 通用值对象
│   │       ├── domain_events.py          # 领域事件基类
│   │       ├── exceptions.py             # 领域异常
│   │       └── specifications.py         # 规约模式
│   ├── application/                      # 应用层 (Application Layer)
│   │   ├── __init__.py
│   │   ├── character/                    # 角色应用服务
│   │   │   ├── __init__.py
│   │   │   ├── services.py               # 角色应用服务
│   │   │   ├── dtos.py                   # 数据传输对象
│   │   │   └── handlers.py               # 事件处理器
│   │   ├── asset/                        # 资产应用服务
│   │   │   ├── __init__.py
│   │   │   ├── services.py               # 资产应用服务
│   │   │   ├── dtos.py                   # 数据传输对象
│   │   │   └── handlers.py               # 事件处理器
│   │   ├── market/                       # 市场应用服务
│   │   │   ├── __init__.py
│   │   │   ├── services.py               # 市场应用服务
│   │   │   ├── dtos.py                   # 数据传输对象
│   │   │   └── handlers.py               # 事件处理器
│   │   ├── industry/                     # 工业应用服务
│   │   │   ├── __init__.py
│   │   │   ├── services.py               # 工业应用服务
│   │   │   ├── dtos.py                   # 数据传输对象
│   │   │   └── handlers.py               # 事件处理器
│   │   ├── corporation/                  # 公司应用服务
│   │   │   ├── __init__.py
│   │   │   ├── services.py               # 公司应用服务
│   │   │   ├── dtos.py                   # 数据传输对象
│   │   │   └── handlers.py               # 事件处理器
│   │   └── shared/                       # 共享应用组件
│   │       ├── __init__.py
│   │       ├── event_bus.py              # 事件总线
│   │       ├── unit_of_work.py           # 工作单元
│   │       └── exceptions.py             # 应用异常
│   ├── infrastructure/                   # 基础设施层 (Infrastructure Layer)
│   │   ├── __init__.py
│   │   ├── esi/                          # ESI API 客户端
│   │   │   ├── __init__.py
│   │   │   ├── client.py                 # ESI客户端
│   │   │   ├── auth.py                   # SSO认证
│   │   │   ├── models.py                 # API模型
│   │   │   ├── exceptions.py             # API异常
│   │   │   └── rate_limiter.py           # 限流器
│   │   ├── persistence/                  # 数据持久化
│   │   │   ├── __init__.py
│   │   │   ├── database.py               # 数据库配置
│   │   │   ├── models/                   # SQLAlchemy模型
│   │   │   │   ├── __init__.py
│   │   │   │   ├── character.py          # 角色模型
│   │   │   │   ├── asset.py              # 资产模型
│   │   │   │   ├── market.py             # 市场模型
│   │   │   │   ├── industry.py           # 工业模型
│   │   │   │   └── corporation.py        # 公司模型
│   │   │   └── repositories/             # 仓储实现
│   │   │       ├── __init__.py
│   │   │       ├── character.py          # 角色仓储
│   │   │       ├── asset.py              # 资产仓储
│   │   │       ├── market.py             # 市场仓储
│   │   │       ├── industry.py           # 工业仓储
│   │   │       └── corporation.py        # 公司仓储
│   │   ├── cache/                        # 缓存服务
│   │   │   ├── __init__.py
│   │   │   ├── redis_client.py           # Redis客户端
│   │   │   └── cache_service.py          # 缓存服务
│   │   ├── messaging/                    # 消息队列
│   │   │   ├── __init__.py
│   │   │   ├── celery_app.py             # Celery应用
│   │   │   └── tasks.py                  # 异步任务
│   │   └── config/                       # 配置管理
│   │       ├── __init__.py
│   │       ├── settings.py               # 应用设置
│   │       └── logging.py                # 日志配置
│   └── presentation/                     # 表现层 (Presentation Layer)
│       ├── __init__.py
│       └── api/                          # Web API
│           ├── __init__.py
│           ├── main.py                   # FastAPI应用入口
│           ├── dependencies.py           # 依赖注入
│           ├── middleware.py             # 中间件
│           ├── routers/                  # API路由
│           │   ├── __init__.py
│           │   ├── auth.py               # 认证路由
│           │   ├── character.py          # 角色路由
│           │   ├── asset.py              # 资产路由
│           │   ├── market.py             # 市场路由
│           │   ├── industry.py           # 工业路由
│           │   └── corporation.py        # 公司路由
│           ├── schemas/                  # API模式
│           │   ├── __init__.py
│           │   ├── character.py          # 角色模式
│           │   ├── asset.py              # 资产模式
│           │   ├── market.py             # 市场模式
│           │   ├── industry.py           # 工业模式
│           │   └── corporation.py        # 公司模式
│           └── exceptions.py             # API异常处理
├── tests/                                # 测试代码
│   ├── __init__.py
│   ├── unit/                            # 单元测试
│   │   ├── __init__.py
│   │   ├── domain/                      # 领域层测试
│   │   ├── application/                 # 应用层测试
│   │   └── infrastructure/              # 基础设施层测试
│   ├── integration/                     # 集成测试
│   │   ├── __init__.py
│   │   ├── api/                         # API测试
│   │   └── database/                    # 数据库测试
│   ├── e2e/                            # 端到端测试
│   │   ├── __init__.py
│   │   └── scenarios/                   # 测试场景
│   ├── fixtures/                        # 测试夹具
│   │   ├── __init__.py
│   │   └── data/                        # 测试数据
│   └── conftest.py                      # pytest配置
├── docs/                                # 文档
│   ├── EVE_Online_ESI_API_Knowledge_Base.md
│   ├── DDD_Architecture_Design.md
│   ├── project_structure.md
│   ├── api_interface_documentation.md   # API接口文档
│   ├── deployment.md                    # 部署文档
│   └── development.md                   # 开发指南
├── alembic/                            # 数据库迁移
│   ├── versions/                        # 迁移版本
│   ├── env.py                          # 迁移环境
│   └── script.py.mako                  # 迁移模板
├── frontend/                           # 前端代码 (待开发)
├── deployment/                         # 部署配置
│   ├── docker/                         # Docker配置
│   ├── kubernetes/                     # K8s配置
│   └── nginx/                          # Nginx配置
├── .env.example                        # 环境变量示例
├── .gitignore                          # Git忽略文件
├── requirements.txt                    # Python依赖
├── pyproject.toml                      # 项目配置
├── alembic.ini                         # Alembic配置
└── README.md                           # 项目说明
```

## 架构层次说明

### 1. 领域层 (Domain Layer)
- **职责**: 包含业务逻辑的核心，定义领域实体、值对象、聚合根、领域服务和领域事件
- **特点**: 不依赖任何外部框架，纯粹的业务逻辑
- **组件**:
  - `entities.py`: 领域实体和聚合根
  - `value_objects.py`: 值对象
  - `repositories.py`: 仓储接口定义
  - `services.py`: 领域服务
  - `events.py`: 领域事件

### 2. 应用层 (Application Layer)
- **职责**: 协调领域对象完成用例，处理事务和安全
- **特点**: 薄薄的一层，不包含业务逻辑
- **组件**:
  - `services.py`: 应用服务，编排用例
  - `dtos.py`: 数据传输对象
  - `handlers.py`: 事件处理器

### 3. 基础设施层 (Infrastructure Layer)
- **职责**: 提供技术实现，如数据库访问、外部API调用等
- **特点**: 实现领域层定义的接口
- **组件**:
  - `esi/`: ESI API客户端实现
  - `persistence/`: 数据持久化实现
  - `cache/`: 缓存服务
  - `messaging/`: 消息队列

### 4. 表现层 (Presentation Layer)
- **职责**: 处理用户交互，提供API接口
- **特点**: 将外部请求转换为应用层调用
- **组件**:
  - `api/`: Web API实现
  - `routers/`: API路由定义
  - `schemas/`: API数据模式

## 依赖关系

```
Presentation Layer
       ↓
Application Layer
       ↓
Domain Layer
       ↑
Infrastructure Layer
```

- 表现层依赖应用层
- 应用层依赖领域层
- 基础设施层实现领域层接口
- 领域层不依赖任何其他层
