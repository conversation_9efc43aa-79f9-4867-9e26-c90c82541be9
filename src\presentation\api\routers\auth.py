"""
认证相关路由
"""
from fastapi import APIRouter, Depends, HTTPException, Query, Request
from fastapi.responses import RedirectResponse
from sqlalchemy.orm import Session
from typing import Dict, Any, List, Optional
from pydantic import BaseModel

from ....application.auth_simple import SimpleAuthenticationService
from ....infrastructure.esi import EVESSOClient, ESI_SCOPES
from ....infrastructure.config.logging import get_logger
from ..dependencies import get_database_session, get_auth_service, get_current_user
from ..schemas.auth import LoginResponse, CallbackRequest, TokenResponse, UserInfo

logger = get_logger(__name__)

router = APIRouter()


@router.get("/status")
async def auth_status(auth_service: SimpleAuthenticationService = Depends(get_auth_service)):
    """获取认证状态"""
    return auth_service.get_auth_status()


class LoginRequest(BaseModel):
    """登录请求"""
    scopes: Optional[List[str]] = None
    redirect_url: Optional[str] = None


class EVELoginRequest(BaseModel):
    """EVE SSO登录请求"""
    scopes: List[str]


@router.post("/login")
async def initiate_login(
    request: LoginRequest,
    auth_service: SimpleAuthenticationService = Depends(get_auth_service)
):
    """发起EVE SSO登录"""
    try:
        result = await auth_service.initiate_eve_login(
            scopes=request.scopes,
            redirect_url=request.redirect_url
        )

        # 返回符合前端期望的格式
        return {
            "success": True,
            "data": {
                "login_url": result["login_url"],
                "state": result["state"],
                "scopes": result["scopes"]
            }
        }

    except Exception as e:
        logger.error("发起登录失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to initiate login")


@router.post("/eve/login")
async def initiate_eve_login(
    request: EVELoginRequest,
    auth_service: SimpleAuthenticationService = Depends(get_auth_service)
):
    """发起EVE SSO登录 - 前端专用接口"""
    try:
        logger.info("收到EVE登录请求", scopes=request.scopes)

        # 检查是否为开发模式（回调URL包含过期的ngrok地址）
        from ....infrastructure.config import settings

        # 如果回调URL是ngrok地址但可能已过期，使用开发模式
        is_dev_mode = (settings.debug and
                      ("ngrok" in settings.eve_sso_callback_url or
                       settings.eve_sso_callback_url.startswith("http://localhost")))

        if is_dev_mode:
            # 开发模式：使用模拟响应
            logger.info("开发模式：使用模拟EVE登录响应")

            # 生成一个模拟的登录URL，指向本地的模拟页面
            mock_login_url = f"http://localhost:3000/auth/mock-eve-login?scopes={','.join(request.scopes)}&state=dev_state_123"

            return {
                "success": True,
                "data": {
                    "login_url": mock_login_url,
                    "state": "dev_state_123",
                    "expires_in": 300,
                    "scopes": request.scopes
                }
            }
        else:
            # 生产模式：使用真实的认证服务
            result = await auth_service.initiate_eve_login(
                scopes=request.scopes
            )

            return {
                "success": True,
                "data": {
                    "login_url": result["login_url"],
                    "state": result["state"],
                    "expires_in": 300,
                    "scopes": result["scopes"]
                }
            }

    except Exception as e:
        logger.error("发起EVE登录失败", error=str(e))
        return {
            "success": False,
            "message": f"Failed to initiate EVE login: {str(e)}"
        }


@router.get("/callback")
async def handle_callback(
    code: str = Query(..., description="授权码"),
    state: str = Query(..., description="状态参数"),
    auth_service: SimpleAuthenticationService = Depends(get_auth_service)
):
    """处理EVE SSO回调"""
    try:
        # 检查是否为开发模式的模拟状态
        if state == "dev_state_123":
            logger.info("开发模式：处理模拟EVE回调")

            # 返回模拟的成功响应，重定向到前端
            return RedirectResponse(
                url="http://localhost:3000/dashboard?mock_login=true&character_name=Test%20Character"
            )

        # 正常的EVE SSO回调处理
        result = await auth_service.handle_eve_callback(code, state)

        if result["success"]:
            # 如果有重定向URL，重定向到指定页面
            if result.get("redirect_url"):
                response = RedirectResponse(url=result["redirect_url"])
            else:
                # 默认重定向到前端主页
                response = RedirectResponse(url="/")

            # 设置会话cookie
            response.set_cookie(
                key="session_token",
                value=result["session_token"],
                httponly=True,
                secure=True,
                samesite="lax",
                max_age=86400  # 24小时
            )

            return response
        else:
            raise HTTPException(status_code=400, detail="Authentication failed")

    except Exception as e:
        logger.error("处理回调失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to handle callback")


@router.post("/eve/callback")
async def handle_eve_callback(request: CallbackRequest):
    """处理EVE SSO回调 - 前端专用接口（简化版）"""
    try:
        # 暂时返回模拟数据用于测试
        return {
            "success": True,
            "data": {
                "character_id": 12345,
                "character_name": "Demo Character",
                "scopes": ["esi-characters.read_characters.v1"]
            },
            "message": "EVE character linked successfully"
        }

    except Exception as e:
        logger.error("处理EVE回调失败", error=str(e))
        return {
            "success": False,
            "message": f"Failed to handle EVE callback: {str(e)}"
        }


@router.post("/refresh", response_model=TokenResponse)
async def refresh_token(
    character_id: int,
    auth_service: SimpleAuthenticationService = Depends(get_auth_service),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """刷新角色令牌"""
    try:
        result = await auth_service.refresh_character_token(character_id)
        
        return TokenResponse(
            success=result["success"],
            character_id=result["character_id"],
            expires_at=result["expires_at"]
        )
        
    except Exception as e:
        logger.error("刷新令牌失败", character_id=character_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to refresh token")


@router.post("/revoke")
async def revoke_token(
    character_id: int,
    auth_service: SimpleAuthenticationService = Depends(get_auth_service),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """撤销角色令牌"""
    try:
        result = await auth_service.revoke_character_token(character_id)
        
        return {
            "success": result["success"],
            "message": result["message"]
        }
        
    except Exception as e:
        logger.error("撤销令牌失败", character_id=character_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to revoke token")


@router.get("/me", response_model=UserInfo)
async def get_current_user_info(
    current_user: Dict[str, Any] = Depends(get_current_user),
    auth_service: SimpleAuthenticationService = Depends(get_auth_service)
):
    """获取当前用户信息"""
    try:
        user_id = current_user["user_id"]
        characters = await auth_service.get_user_characters(user_id)
        
        return UserInfo(
            user_id=user_id,
            characters=characters
        )
        
    except Exception as e:
        logger.error("获取用户信息失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get user info")


@router.get("/characters")
async def get_user_characters(
    current_user: Dict[str, Any] = Depends(get_current_user),
    auth_service: SimpleAuthenticationService = Depends(get_auth_service)
):
    """获取用户的角色列表"""
    try:
        user_id = current_user["user_id"]
        characters = await auth_service.get_user_characters(user_id)
        
        return {
            "characters": characters
        }
        
    except Exception as e:
        logger.error("获取角色列表失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get characters")


@router.get("/permissions/{character_id}")
async def get_character_permissions(
    character_id: int,
    auth_service: SimpleAuthenticationService = Depends(get_auth_service),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取角色权限"""
    try:
        permissions = await auth_service.get_character_permissions(character_id)
        
        return {
            "character_id": character_id,
            "permissions": permissions
        }
        
    except Exception as e:
        logger.error("获取角色权限失败", character_id=character_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get permissions")


@router.post("/logout")
async def logout(request: Request):
    """登出"""
    response = RedirectResponse(url="/")
    response.delete_cookie(key="session_token")
    
    logger.info("用户登出")
    
    return response


@router.get("/scopes")
async def get_available_scopes():
    """获取可用的权限范围"""
    return {
        "scopes": {
            # 基本角色信息在OAuth过程中自动提供，无需特殊scope
            "character_location": {
                "scope": ESI_SCOPES["character_location"],
                "description": "读取角色位置"
            },
            "character_online": {
                "scope": ESI_SCOPES["character_online"],
                "description": "读取在线状态"
            },
            "character_skills": {
                "scope": ESI_SCOPES["character_skills"],
                "description": "读取技能信息"
            },
            "character_wallet": {
                "scope": ESI_SCOPES["character_wallet"],
                "description": "读取钱包信息"
            },
            "character_assets": {
                "scope": ESI_SCOPES["character_assets"],
                "description": "读取角色资产"
            },
            "market_orders": {
                "scope": ESI_SCOPES["market_orders"],
                "description": "读取市场订单"
            },
            "industry_jobs": {
                "scope": ESI_SCOPES["character_industry"],
                "description": "读取工业任务"
            }
        }
    }
