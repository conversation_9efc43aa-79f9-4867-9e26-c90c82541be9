# EVE SSO集成知识库

## 核心原理理解

### EVE SSO基本机制
- **基本角色信息自动提供**：角色ID、姓名、公司等信息在OAuth认证过程中自动返回
- **特殊功能需要scope**：技能、资产、位置、钱包等功能需要对应的ESI scope
- **JWT令牌包含基本信息**：认证成功后的JWT令牌包含角色基本信息，无需额外API调用

### 官方权威资源
- **ESI文档**: https://esi.evetech.net/ui/
- **开发者门户**: https://developers.eveonline.com/
- **SSO指南**: https://docs.esi.evetech.net/docs/sso/

## 常见错误和解决方案

### 1. invalid_scope错误

#### 错误信息
```json
{"error":"invalid_scope","error_description":"The requested 'esi-characters.read_characters.v1' scope is not valid."}
```

#### 根本原因
- 使用了不存在的ESI scope
- 常见的无效scope：`esi-characters.read_characters.v1`

#### 解决方案
1. **移除无效scope**：删除所有不存在的scope引用
2. **查阅官方文档**：在ESI文档中确认scope的正确名称
3. **理解基本原理**：基本角色信息无需特殊scope

#### 有效的ESI Scopes列表
```javascript
// 位置相关
'esi-location.read_location.v1'      // 角色当前位置
'esi-location.read_online.v1'        // 在线状态
'esi-location.read_ship_type.v1'     // 当前飞船

// 技能相关
'esi-skills.read_skills.v1'          // 技能信息
'esi-skills.read_skillqueue.v1'      // 技能队列

// 资产相关
'esi-assets.read_assets.v1'          // 角色资产
'esi-wallet.read_character_wallet.v1' // 钱包余额

// 其他功能
'esi-clones.read_clones.v1'          // 克隆体信息
'esi-mail.read_mail.v1'              // 邮件
'esi-contracts.read_character_contracts.v1' // 合同
```

### 2. 前后端配置不一致

#### 问题表现
- 前端请求的scope与后端配置不匹配
- 登录流程中断或失败

#### 解决方案
1. **统一配置管理**：建立单一的scope配置源
2. **配置验证**：添加前后端配置一致性检查
3. **文档同步**：确保所有文档使用相同的scope列表

### 3. 回调URL配置错误

#### 常见问题
- 开发环境和生产环境URL不匹配
- ngrok域名变更导致回调失败

#### 解决方案
1. **环境隔离**：不同环境使用不同的EVE应用配置
2. **固定域名**：生产环境使用固定的ngrok域名
3. **配置验证**：启动时验证回调URL配置

## 最佳实践

### 开发流程
1. **需求分析**：明确需要哪些EVE数据
2. **Scope选择**：根据需求选择最小必要的scope集合
3. **官方验证**：在ESI文档中验证所有scope的有效性
4. **前后端同步**：确保前后端使用相同的scope配置
5. **集成测试**：完整测试登录流程和数据获取

### 配置管理
```typescript
// 推荐的配置管理方式
export const EVE_SSO_SCOPES = {
  // 基础功能
  BASIC: [
    'esi-location.read_location.v1',
    'esi-skills.read_skills.v1',
    'esi-wallet.read_character_wallet.v1'
  ],
  
  // 扩展功能
  EXTENDED: [
    'esi-assets.read_assets.v1',
    'esi-clones.read_clones.v1',
    'esi-mail.read_mail.v1'
  ],
  
  // 获取所有scope
  ALL: function() {
    return [...this.BASIC, ...this.EXTENDED]
  }
}
```

### 错误处理
```typescript
// 推荐的错误处理方式
const handleEVELogin = async () => {
  try {
    const scopes = EVE_SSO_SCOPES.BASIC
    const loginUrl = await initiateEVELogin(scopes)
    window.location.href = loginUrl
  } catch (error) {
    if (error.message.includes('invalid_scope')) {
      console.error('EVE SSO scope配置错误:', error)
      toast.error('登录配置错误，请联系管理员')
    } else {
      console.error('EVE登录失败:', error)
      toast.error('登录失败，请稍后重试')
    }
  }
}
```

### 测试策略
1. **单元测试**：测试scope配置的正确性
2. **集成测试**：测试完整的登录流程
3. **端到端测试**：测试用户完整的认证体验

```javascript
// 测试示例
describe('EVE SSO Integration', () => {
  test('should use valid scopes only', () => {
    const scopes = EVE_SSO_SCOPES.ALL()
    const invalidScopes = ['esi-characters.read_characters.v1']
    
    invalidScopes.forEach(scope => {
      expect(scopes).not.toContain(scope)
    })
  })
  
  test('should complete login flow', async () => {
    const loginUrl = await initiateEVELogin(EVE_SSO_SCOPES.BASIC)
    expect(loginUrl).toContain('login.eveonline.com')
    expect(loginUrl).not.toContain('esi-characters.read_characters.v1')
  })
})
```

## 故障排查清单

### 登录失败排查步骤
1. **检查scope有效性**
   - [ ] 所有scope都在ESI文档中存在
   - [ ] 没有使用已废弃的scope
   - [ ] 前后端scope配置一致

2. **检查配置正确性**
   - [ ] Client ID和Secret正确
   - [ ] 回调URL配置正确
   - [ ] 环境变量加载正常

3. **检查网络连接**
   - [ ] 能够访问login.eveonline.com
   - [ ] 回调URL可以从外网访问
   - [ ] 防火墙和代理设置正确

4. **检查日志信息**
   - [ ] 查看详细的错误日志
   - [ ] 检查OAuth参数是否正确
   - [ ] 验证状态参数匹配

### 常用调试命令
```bash
# 检查scope配置
grep -r "esi-characters.read_characters.v1" .

# 验证配置文件
python -c "from src.infrastructure.config import settings; print(settings.eve_sso_scopes)"

# 测试登录URL生成
curl -X POST http://localhost:8000/auth/login \
  -H "Content-Type: application/json" \
  -d '{"scopes":["esi-location.read_location.v1"]}'
```

## 更新维护

### 定期检查事项
1. **ESI文档更新**：定期检查ESI文档的scope变更
2. **废弃通知**：关注EVE官方的API废弃通知
3. **配置审查**：定期审查scope配置的必要性
4. **测试验证**：定期运行完整的集成测试

### 版本兼容性
- 优先使用最新版本的ESI endpoint
- 保持向后兼容性，逐步迁移废弃的scope
- 建立scope版本管理机制

## 参考资源

### 官方文档
- [EVE ESI Documentation](https://esi.evetech.net/ui/)
- [EVE Developer Resources](https://developers.eveonline.com/)
- [EVE SSO Guide](https://docs.esi.evetech.net/docs/sso/)

### 社区资源
- [EVE Tech Discord](https://discord.gg/evetech)
- [r/Eve Programming](https://reddit.com/r/eveprogramming)
- [EVE University Wiki](https://wiki.eveuniversity.org/)
