# EVE Online 游戏管理助手 - 环境变量配置示例

# 应用配置
APP_NAME="EVE Online Assistant"
APP_VERSION="0.1.0"
DEBUG=true
SECRET_KEY="your-secret-key-here"

# 数据库配置
DATABASE_URL="postgresql://username:password@localhost:5432/eve_assistant"
DATABASE_ECHO=false

# Redis配置
REDIS_URL="redis://localhost:6379/0"

# ESI API配置
ESI_BASE_URL="https://esi.evetech.net"
ESI_USER_AGENT="EVE-Assistant/0.1.0 (<EMAIL>)"

# EVE SSO配置
EVE_SSO_CLIENT_ID="your-client-id"
EVE_SSO_CLIENT_SECRET="your-client-secret"
EVE_SSO_CALLBACK_URL="http://localhost:8000/auth/callback"

# 日志配置
LOG_LEVEL="INFO"
LOG_FORMAT="json"

# Celery配置
CELERY_BROKER_URL="redis://localhost:6379/1"
CELERY_RESULT_BACKEND="redis://localhost:6379/2"

# 缓存配置
CACHE_TTL_DEFAULT=3600
CACHE_TTL_CHARACTER=120
CACHE_TTL_MARKET=300

# API限流配置
RATE_LIMIT_REQUESTS_PER_SECOND=10
RATE_LIMIT_BURST=20
