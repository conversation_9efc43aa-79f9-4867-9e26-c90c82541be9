"""
RBAC权限管理值对象
"""
from dataclasses import dataclass
from enum import Enum
from typing import List, Optional, Set, Dict, Any
from uuid import UUID, uuid4

from ..shared.base_entity import ValueObject
from ..shared.value_objects import UserId


@dataclass(frozen=True)
class RoleId(ValueObject):
    """角色ID值对象"""
    value: UUID
    
    @classmethod
    def generate(cls) -> 'RoleId':
        return cls(uuid4())
    
    def __str__(self) -> str:
        return str(self.value)


@dataclass(frozen=True)
class PermissionId(ValueObject):
    """权限ID值对象"""
    value: UUID
    
    @classmethod
    def generate(cls) -> 'PermissionId':
        return cls(uuid4())
    
    def __str__(self) -> str:
        return str(self.value)


@dataclass(frozen=True)
class RoleName(ValueObject):
    """角色名称值对象"""
    value: str
    
    def __post_init__(self):
        if not self.value or len(self.value.strip()) == 0:
            raise ValueError("角色名称不能为空")
        
        if len(self.value) > 50:
            raise ValueError("角色名称长度不能超过50个字符")
        
        # 检查角色名称格式
        if not self.value.replace('_', '').replace('-', '').isalnum():
            raise ValueError("角色名称只能包含字母、数字、下划线和连字符")
    
    def __str__(self) -> str:
        return self.value


@dataclass(frozen=True)
class PermissionName(ValueObject):
    """权限名称值对象"""
    value: str
    
    def __post_init__(self):
        if not self.value or len(self.value.strip()) == 0:
            raise ValueError("权限名称不能为空")
        
        if len(self.value) > 100:
            raise ValueError("权限名称长度不能超过100个字符")
        
        # 权限名称格式：resource:action 或 resource:action:scope
        parts = self.value.split(':')
        if len(parts) < 2 or len(parts) > 3:
            raise ValueError("权限名称格式应为 'resource:action' 或 'resource:action:scope'")
    
    def __str__(self) -> str:
        return self.value
    
    @property
    def resource(self) -> str:
        """获取资源类型"""
        return self.value.split(':')[0]
    
    @property
    def action(self) -> str:
        """获取操作类型"""
        return self.value.split(':')[1]
    
    @property
    def scope(self) -> Optional[str]:
        """获取权限范围"""
        parts = self.value.split(':')
        return parts[2] if len(parts) > 2 else None


class ResourceType(Enum):
    """资源类型枚举"""
    USER = "user"
    CHARACTER = "character"
    ASSET = "asset"
    MARKET = "market"
    INDUSTRY = "industry"
    CORPORATION = "corporation"
    ALLIANCE = "alliance"
    SKILL = "skill"
    WALLET = "wallet"
    CONTRACT = "contract"
    MAIL = "mail"
    CALENDAR = "calendar"
    FITTING = "fitting"
    KILLMAIL = "killmail"
    LOCATION = "location"
    SYSTEM = "system"
    ADMIN = "admin"


class ActionType(Enum):
    """操作类型枚举"""
    CREATE = "create"
    READ = "read"
    UPDATE = "update"
    DELETE = "delete"
    LIST = "list"
    SEARCH = "search"
    EXPORT = "export"
    IMPORT = "import"
    MANAGE = "manage"
    ADMIN = "admin"


@dataclass(frozen=True)
class PermissionScope(ValueObject):
    """权限范围值对象"""
    resource_type: ResourceType
    action_type: ActionType
    scope_filter: Optional[Dict[str, Any]] = None
    
    def matches(self, resource: str, action: str, context: Dict[str, Any] = None) -> bool:
        """检查权限是否匹配"""
        # 检查资源类型
        if self.resource_type.value != resource:
            return False
        
        # 检查操作类型
        if self.action_type.value != action:
            return False
        
        # 检查范围过滤器
        if self.scope_filter and context:
            for key, expected_value in self.scope_filter.items():
                if key not in context or context[key] != expected_value:
                    return False
        
        return True
    
    def to_permission_name(self) -> PermissionName:
        """转换为权限名称"""
        if self.scope_filter:
            scope_str = ','.join(f"{k}={v}" for k, v in self.scope_filter.items())
            return PermissionName(f"{self.resource_type.value}:{self.action_type.value}:{scope_str}")
        else:
            return PermissionName(f"{self.resource_type.value}:{self.action_type.value}")


@dataclass(frozen=True)
class RoleHierarchy(ValueObject):
    """角色层次结构值对象"""
    parent_role_id: RoleId
    child_role_id: RoleId
    depth: int = 1
    
    def __post_init__(self):
        if self.parent_role_id == self.child_role_id:
            raise ValueError("角色不能继承自己")
        
        if self.depth < 1:
            raise ValueError("角色层次深度必须大于0")
        
        if self.depth > 10:
            raise ValueError("角色层次深度不能超过10级")


@dataclass(frozen=True)
class UserRoleAssignment(ValueObject):
    """用户角色分配值对象"""
    user_id: UserId
    role_id: RoleId
    assigned_by: UserId
    assigned_at: str  # ISO格式时间字符串
    expires_at: Optional[str] = None  # 可选的过期时间
    is_active: bool = True
    
    def is_expired(self) -> bool:
        """检查分配是否已过期"""
        if not self.expires_at:
            return False
        
        from datetime import datetime
        try:
            expires = datetime.fromisoformat(self.expires_at.replace('Z', '+00:00'))
            return datetime.utcnow().replace(tzinfo=expires.tzinfo) > expires
        except:
            return False
    
    def is_valid(self) -> bool:
        """检查分配是否有效"""
        return self.is_active and not self.is_expired()


@dataclass(frozen=True)
class PermissionContext(ValueObject):
    """权限上下文值对象"""
    user_id: UserId
    resource_type: str
    action_type: str
    resource_id: Optional[str] = None
    character_id: Optional[int] = None
    corporation_id: Optional[int] = None
    alliance_id: Optional[int] = None
    additional_context: Dict[str, Any] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        context = {
            'user_id': str(self.user_id.value),
            'resource_type': self.resource_type,
            'action_type': self.action_type
        }
        
        if self.resource_id:
            context['resource_id'] = self.resource_id
        if self.character_id:
            context['character_id'] = self.character_id
        if self.corporation_id:
            context['corporation_id'] = self.corporation_id
        if self.alliance_id:
            context['alliance_id'] = self.alliance_id
        if self.additional_context:
            context.update(self.additional_context)
        
        return context


# 预定义的权限范围
class PredefinedPermissions:
    """预定义权限"""
    
    # 用户管理权限
    USER_READ = PermissionScope(ResourceType.USER, ActionType.READ)
    USER_CREATE = PermissionScope(ResourceType.USER, ActionType.CREATE)
    USER_UPDATE = PermissionScope(ResourceType.USER, ActionType.UPDATE)
    USER_DELETE = PermissionScope(ResourceType.USER, ActionType.DELETE)
    USER_MANAGE = PermissionScope(ResourceType.USER, ActionType.MANAGE)
    
    # 角色管理权限
    CHARACTER_READ = PermissionScope(ResourceType.CHARACTER, ActionType.READ)
    CHARACTER_UPDATE = PermissionScope(ResourceType.CHARACTER, ActionType.UPDATE)
    CHARACTER_MANAGE = PermissionScope(ResourceType.CHARACTER, ActionType.MANAGE)
    
    # 资产管理权限
    ASSET_READ = PermissionScope(ResourceType.ASSET, ActionType.READ)
    ASSET_EXPORT = PermissionScope(ResourceType.ASSET, ActionType.EXPORT)
    
    # 市场权限
    MARKET_READ = PermissionScope(ResourceType.MARKET, ActionType.READ)
    MARKET_CREATE = PermissionScope(ResourceType.MARKET, ActionType.CREATE)
    MARKET_UPDATE = PermissionScope(ResourceType.MARKET, ActionType.UPDATE)
    
    # 工业权限
    INDUSTRY_READ = PermissionScope(ResourceType.INDUSTRY, ActionType.READ)
    INDUSTRY_CREATE = PermissionScope(ResourceType.INDUSTRY, ActionType.CREATE)
    INDUSTRY_MANAGE = PermissionScope(ResourceType.INDUSTRY, ActionType.MANAGE)
    
    # 系统管理权限
    SYSTEM_ADMIN = PermissionScope(ResourceType.SYSTEM, ActionType.ADMIN)
    
    @classmethod
    def get_all_permissions(cls) -> List[PermissionScope]:
        """获取所有预定义权限"""
        return [
            cls.USER_READ, cls.USER_CREATE, cls.USER_UPDATE, cls.USER_DELETE, cls.USER_MANAGE,
            cls.CHARACTER_READ, cls.CHARACTER_UPDATE, cls.CHARACTER_MANAGE,
            cls.ASSET_READ, cls.ASSET_EXPORT,
            cls.MARKET_READ, cls.MARKET_CREATE, cls.MARKET_UPDATE,
            cls.INDUSTRY_READ, cls.INDUSTRY_CREATE, cls.INDUSTRY_MANAGE,
            cls.SYSTEM_ADMIN
        ]


# 预定义角色
class PredefinedRoles:
    """预定义角色"""
    
    # 基础角色
    GUEST = "guest"
    USER = "user"
    PREMIUM_USER = "premium_user"
    
    # 管理角色
    MODERATOR = "moderator"
    ADMIN = "admin"
    SUPER_ADMIN = "super_admin"
    
    # 功能角色
    MARKET_ANALYST = "market_analyst"
    INDUSTRY_MANAGER = "industry_manager"
    CORPORATION_MANAGER = "corporation_manager"
    
    @classmethod
    def get_default_role_permissions(cls) -> Dict[str, List[PermissionScope]]:
        """获取默认角色权限映射"""
        return {
            cls.GUEST: [
                PredefinedPermissions.CHARACTER_READ,
                PredefinedPermissions.ASSET_READ,
                PredefinedPermissions.MARKET_READ
            ],
            cls.USER: [
                PredefinedPermissions.CHARACTER_READ,
                PredefinedPermissions.CHARACTER_UPDATE,
                PredefinedPermissions.ASSET_READ,
                PredefinedPermissions.ASSET_EXPORT,
                PredefinedPermissions.MARKET_READ,
                PredefinedPermissions.INDUSTRY_READ
            ],
            cls.PREMIUM_USER: [
                PredefinedPermissions.CHARACTER_READ,
                PredefinedPermissions.CHARACTER_UPDATE,
                PredefinedPermissions.ASSET_READ,
                PredefinedPermissions.ASSET_EXPORT,
                PredefinedPermissions.MARKET_READ,
                PredefinedPermissions.MARKET_CREATE,
                PredefinedPermissions.MARKET_UPDATE,
                PredefinedPermissions.INDUSTRY_READ,
                PredefinedPermissions.INDUSTRY_CREATE
            ],
            cls.ADMIN: [
                *PredefinedPermissions.get_all_permissions()
            ]
        }
