"""
认证相关的数据传输对象
"""
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime


class LoginResponse(BaseModel):
    """登录响应"""
    login_url: str = Field(..., description="EVE SSO登录URL")
    state: str = Field(..., description="状态参数")
    scopes: List[str] = Field(..., description="请求的权限范围")


class CallbackRequest(BaseModel):
    """回调请求"""
    code: str = Field(..., description="授权码")
    state: str = Field(..., description="状态参数")


class TokenResponse(BaseModel):
    """令牌响应"""
    success: bool = Field(..., description="操作是否成功")
    character_id: int = Field(..., description="角色ID")
    expires_at: str = Field(..., description="过期时间")


class CharacterInfo(BaseModel):
    """角色信息"""
    character_id: int = Field(..., description="角色ID")
    character_name: str = Field(..., description="角色名称")
    corporation_id: int = Field(..., description="公司ID")
    alliance_id: Optional[int] = Field(None, description="联盟ID")
    is_online: bool = Field(..., description="是否在线")
    token_expires_at: str = Field(..., description="令牌过期时间")
    token_active: bool = Field(..., description="令牌是否活跃")


class UserInfo(BaseModel):
    """用户信息"""
    user_id: int = Field(..., description="用户ID")
    characters: List[CharacterInfo] = Field(..., description="角色列表")


class PermissionInfo(BaseModel):
    """权限信息"""
    character_id: int = Field(..., description="角色ID")
    permissions: List[str] = Field(..., description="权限列表")


class ScopeInfo(BaseModel):
    """权限范围信息"""
    scope: str = Field(..., description="权限范围")
    description: str = Field(..., description="权限描述")


class AvailableScopes(BaseModel):
    """可用权限范围"""
    scopes: Dict[str, ScopeInfo] = Field(..., description="权限范围字典")
