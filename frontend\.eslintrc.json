{"root": true, "env": {"browser": true, "es2020": true, "node": true}, "extends": ["eslint:recommended", "@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended", "plugin:@typescript-eslint/recommended"], "ignorePatterns": ["dist", ".eslintrc.cjs", "node_modules", "coverage", "storybook-static"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "plugins": ["react", "react-hooks", "react-refresh", "@typescript-eslint"], "rules": {"react/react-in-jsx-scope": "off", "react/prop-types": "off", "react-refresh/only-export-components": ["warn", {"allowConstantExport": true}], "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-non-null-assertion": "warn", "prefer-const": "error", "no-var": "error", "object-shorthand": "error", "prefer-template": "error"}, "settings": {"react": {"version": "detect"}}}