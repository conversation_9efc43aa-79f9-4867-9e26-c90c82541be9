#!/usr/bin/env python3
"""
EVE SSO配置验证脚本
"""
import sys
import os
import urllib.request
import json

def check_ngrok_status():
    """检查ngrok状态"""
    print("🔍 检查ngrok状态...")
    try:
        # 检查ngrok API
        with urllib.request.urlopen('http://127.0.0.1:4040/api/tunnels', timeout=5) as response:
            data = json.loads(response.read().decode())
            tunnels = data.get('tunnels', [])
            
            if tunnels:
                for tunnel in tunnels:
                    if tunnel.get('proto') == 'https':
                        public_url = tunnel.get('public_url')
                        print(f"✅ ngrok隧道运行中")
                        print(f"   公网地址: {public_url}")
                        print(f"   本地地址: {tunnel.get('config', {}).get('addr', 'N/A')}")
                        return public_url
                        
            print("❌ 未找到HTTPS隧道")
            return None
    except Exception as e:
        print(f"❌ ngrok未运行或不可访问: {e}")
        return None

def check_eve_sso_config():
    """检查EVE SSO配置"""
    print("\n🔍 检查EVE SSO配置...")
    
    try:
        sys.path.insert(0, '.')
        from src.infrastructure.config import settings
        
        client_id = getattr(settings, 'eve_sso_client_id', None)
        client_secret = getattr(settings, 'eve_sso_client_secret', None)
        callback_url = getattr(settings, 'eve_sso_callback_url', None)
        
        if client_id and client_id != "your_eve_sso_client_id":
            print(f"✅ Client ID: {client_id[:8]}...")
        else:
            print("❌ Client ID 未配置或使用默认值")
            
        if client_secret and client_secret != "your_eve_sso_client_secret":
            print("✅ Client Secret: 已配置")
        else:
            print("❌ Client Secret 未配置或使用默认值")
            
        if callback_url:
            print(f"✅ 回调URL: {callback_url}")
        else:
            print("❌ 回调URL 未配置")
            
        return bool(client_id and client_secret and callback_url)
        
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return False

def test_api_endpoints(base_url="http://127.0.0.1:8000"):
    """测试API端点"""
    print(f"\n🔍 测试API端点 ({base_url})...")
    
    endpoints = [
        ("/health/", "健康检查"),
        ("/", "根端点"),
        ("/docs", "API文档"),
        ("/auth/login", "EVE SSO登录"),
    ]
    
    working_endpoints = []
    for endpoint, name in endpoints:
        try:
            with urllib.request.urlopen(f"{base_url}{endpoint}", timeout=10) as response:
                if response.status == 200:
                    print(f"   ✅ {name} ({endpoint})")
                    working_endpoints.append(endpoint)
                else:
                    print(f"   ❌ {name} ({endpoint}) - 状态码: {response.status}")
        except Exception as e:
            print(f"   ❌ {name} ({endpoint}) - {e}")
    
    return len(working_endpoints) == len(endpoints)

def generate_setup_instructions(public_url=None):
    """生成设置说明"""
    print("\n" + "=" * 60)
    print("🚀 EVE SSO 配置说明")
    print("=" * 60)
    
    if public_url:
        print(f"\n📍 您的公网地址: {public_url}")
        print(f"📍 回调URL: {public_url}/auth/callback")
        print(f"📍 登录URL: {public_url}/auth/login")
    else:
        print("\n⚠️  ngrok未运行，使用本地地址进行测试:")
        print("📍 本地地址: http://127.0.0.1:8000")
        print("📍 回调URL: http://127.0.0.1:8000/auth/callback")
        print("📍 登录URL: http://127.0.0.1:8000/auth/login")
    
    print("\n🔧 配置步骤:")
    print("1. 访问 EVE Developer Portal: https://developers.eveonline.com/")
    print("2. 登录并创建/编辑应用")
    print("3. 设置回调URL（见上方）")
    print("4. 复制 Client ID 和 Secret Key")
    print("5. 更新 .env 文件或配置文件")
    print("6. 重启应用")
    
    print("\n📖 详细说明请查看: EVE_SSO_SETUP_GUIDE.md")

def main():
    """主函数"""
    print("🚀 EVE SSO 配置验证")
    print("=" * 60)
    
    # 检查ngrok
    public_url = check_ngrok_status()
    
    # 检查配置
    config_ok = check_eve_sso_config()
    
    # 测试API端点
    api_ok = test_api_endpoints()
    
    # 如果有公网地址，也测试公网端点
    if public_url:
        print(f"\n🔍 测试公网端点...")
        public_api_ok = test_api_endpoints(public_url)
    else:
        public_api_ok = False
    
    # 生成设置说明
    generate_setup_instructions(public_url)
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 验证结果:")
    print(f"   ngrok隧道: {'✅' if public_url else '❌'}")
    print(f"   EVE SSO配置: {'✅' if config_ok else '❌'}")
    print(f"   本地API: {'✅' if api_ok else '❌'}")
    print(f"   公网API: {'✅' if public_api_ok else '❌'}")
    
    if config_ok and api_ok:
        print("\n🎉 配置验证通过！可以开始使用EVE SSO")
        if public_url:
            print(f"   访问: {public_url}/auth/login")
        else:
            print("   访问: http://127.0.0.1:8000/auth/login")
    else:
        print("\n⚠️  请完成配置后重新验证")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
