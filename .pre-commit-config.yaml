# Pre-commit hooks configuration
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-json
      - id: check-toml
      - id: check-merge-conflict
      - id: debug-statements
      - id: check-docstring-first

  - repo: https://github.com/psf/black
    rev: 23.11.0
    hooks:
      - id: black
        language_version: python3.11
        args: [--line-length=88]

  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: [--profile=black, --line-length=88]

  - repo: https://github.com/pycqa/flake8
    rev: 6.1.0
    hooks:
      - id: flake8
        args: [--max-line-length=88, --extend-ignore=E203,W503]

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.7.1
    hooks:
      - id: mypy
        additional_dependencies: [types-all]
        args: [--ignore-missing-imports, --no-strict-optional]

  - repo: https://github.com/pycqa/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: [-r, src]
        exclude: tests/

  - repo: https://github.com/asottile/pyupgrade
    rev: v3.15.0
    hooks:
      - id: pyupgrade
        args: [--py311-plus]

  # 自定义钩子：模块导入检查
  - repo: local
    hooks:
      - id: module-import-check
        name: Module Import Check
        entry: python -c "
          print('🔍 Checking critical module imports...');
          import sys;
          modules = [
            'src.presentation.api.main',
            'src.infrastructure.persistence.repositories'
          ];
          failed = [];
          for m in modules:
            try:
              __import__(m);
              print(f'✅ {m}')
            except Exception as e:
              print(f'❌ {m}: {e}');
              failed.append(m);
          if failed:
            print(f'\\n❌ {len(failed)} modules failed import!');
            sys.exit(1);
          print('🎉 All critical modules imported successfully!')
          "
        language: system
        pass_filenames: false
        always_run: true

      - id: init-consistency-check
        name: __init__.py Consistency Check
        entry: python -c "
          import ast;
          from pathlib import Path;
          print('🔍 Checking __init__.py consistency...');
          issues = [];
          for init_file in Path('src').rglob('__init__.py'):
            try:
              with open(init_file, 'r') as f:
                content = f.read();
              if not content.strip():
                continue;
              tree = ast.parse(content);
              imports = [];
              for node in ast.walk(tree):
                if isinstance(node, ast.ImportFrom) and node.module is None:
                  imports.extend([f'.{alias.name}' for alias in node.names]);
              init_dir = init_file.parent;
              for imp in imports:
                if imp.startswith('.'):
                  module_file = init_dir / f'{imp[1:]}.py';
                  if not module_file.exists():
                    issues.append(f'{init_file}: imports non-existent {imp}');
            except Exception as e:
              issues.append(f'{init_file}: {e}');
          if issues:
            print('\\n'.join(issues));
            exit(1);
          print('✅ All __init__.py files are consistent!')
          "
        language: system
        pass_filenames: false
        files: __init__.py$

# 全局配置
default_stages: [commit]
fail_fast: false
