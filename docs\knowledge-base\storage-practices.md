# EVE Online Assistant 存储最佳实践

## 📋 存储架构概览

EVE Online Assistant 采用多层存储架构，针对不同类型的数据使用最适合的存储方案：

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   应用服务层     │    │   智能缓存层     │    │   存储适配器     │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ CharacterService│    │ SmartCache      │    │ EVEStorageAdapter│
│ SyncService     │────│ PerformanceMonitor│────│ OptimizationAdvisor│
│ AuthService     │    │ CacheManager    │    │ UnifiedStorage  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
        ┌───────────────────────────────────────────────┼───────────────────────────────────────────────┐
        │                                               │                                               │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Redis缓存      │    │  Pickle文件      │    │   JSON配置      │    │   SQLite数据库   │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ 实时数据(30s)    │    │ 复杂对象存储     │    │ 用户设置        │    │ 核心业务数据     │
│ 频繁数据(5min)   │    │ 计算结果缓存     │    │ 系统配置        │    │ 关系型数据      │
│ 会话数据        │    │ 模型持久化      │    │ 导出数据        │    │ 事务保证        │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🎯 数据分类和存储策略

### 1. 实时数据 (Redis, TTL: 30秒)
- **角色位置**: `esi_character_location`
- **在线状态**: `esi_character_online`
- **使用场景**: 需要实时更新的数据

```python
# 缓存角色位置
await eve_storage.cache_character_location(character_id, location_data)

# 获取角色位置
location = await eve_storage.get_character_location(character_id)
```

### 2. 频繁数据 (Redis, TTL: 5分钟)
- **市场订单**: `esi_market_orders`
- **角色邮件**: `esi_character_mail`
- **通知消息**: `esi_character_notifications`

```python
# 批量缓存频繁数据
await eve_storage.batch_cache_character_data(character_id, {
    "market_orders": orders_data,
    "mail": mail_data
})
```

### 3. 常规数据 (Redis + Pickle双层, TTL: 1小时)
- **角色技能**: `esi_character_skills`
- **角色资产**: `esi_character_assets`
- **工业任务**: `esi_industry_jobs`

```python
# 智能缓存，自动选择最优存储
await smart_cache.set("skills", skills_data, DataCategory.REGULAR, character_id)
```

### 4. 每日数据 (Pickle, TTL: 24小时)
- **角色信息**: `esi_character_info`
- **公司信息**: `esi_corporation_info`
- **宇宙数据**: `esi_universe_types`

### 5. 计算结果 (Pickle压缩, TTL: 30分钟-1小时)
- **角色分析**: `character_analysis`
- **技能规划**: `skill_training_plan`
- **市场分析**: `market_analysis`

```python
# 缓存计算结果
await eve_storage.cache_character_analysis(character_id, analysis_data)
```

### 6. 配置数据 (JSON, 持久化)
- **用户设置**: 仪表板配置、告警规则
- **系统配置**: 同步规则、系统参数

```python
# 保存用户配置
eve_storage.save_user_dashboard_config(user_id, dashboard_config)
```

### 7. 核心业务数据 (SQLite/PostgreSQL, 持久化)
- **角色数据**: 完整的角色信息
- **用户账户**: 认证和权限数据
- **历史记录**: 长期存储的历史数据

## 🚀 使用示例

### 基础使用

```python
from src.infrastructure.storage import eve_storage, storage_manager

# 初始化存储
await storage_manager.initialize()

# 缓存角色数据
await eve_storage.cache_character_location(12345, {
    "solar_system_id": 30000142,
    "station_id": 60003760
})

# 获取缓存数据
location = await eve_storage.get_character_location(12345)

# 批量操作
data_bundle = {
    "location": location_data,
    "online": online_data,
    "skills": skills_data
}
results = await eve_storage.batch_cache_character_data(12345, data_bundle)
```

### 高级使用

```python
from src.infrastructure.storage.smart_cache import smart_cache, DataCategory

# 智能缓存 - 自动选择最优存储
await smart_cache.set("analysis_result", analysis_data, 
                     DataCategory.COMPUTED, character_id, ttl=3600)

# 性能监控
from src.infrastructure.storage.performance_monitor import performance_monitor

# 获取性能统计
stats = performance_monitor.get_stats()
recent_performance = performance_monitor.get_recent_performance(30)

# 优化建议
from src.infrastructure.storage.optimization_advisor import optimization_advisor
report = optimization_advisor.generate_report()
```

## 📊 性能优化建议

### 1. 缓存策略优化

```python
# ✅ 好的做法：根据数据特点选择合适的TTL
await eve_storage.cache_character_location(char_id, data)  # 30秒TTL
await eve_storage.cache_character_skills(char_id, data)    # 1小时TTL

# ❌ 避免：所有数据使用相同TTL
```

### 2. 批量操作

```python
# ✅ 好的做法：使用批量操作
character_data = await eve_storage.batch_get_character_data(
    character_id, ["location", "online", "skills", "assets"]
)

# ❌ 避免：多次单独调用
location = await eve_storage.get_character_location(character_id)
online = await eve_storage.get_character_online_status(character_id)
skills = await eve_storage.get_character_skills(character_id)
```

### 3. 错误处理

```python
# ✅ 好的做法：优雅降级
try:
    cached_data = await eve_storage.get_character_skills(character_id)
    if cached_data is None:
        # 从ESI API获取
        fresh_data = await esi_service.get_character_skills(character_id)
        await eve_storage.cache_character_skills(character_id, fresh_data)
        return fresh_data
    return cached_data
except Exception as e:
    logger.error("获取技能数据失败", error=str(e))
    # 返回默认值或抛出业务异常
```

### 4. 内存管理

```python
# ✅ 好的做法：及时清理不需要的缓存
await eve_storage.invalidate_character_cache(character_id, ["analysis"])

# 定期优化
await eve_storage.optimize_storage()
```

## 🔧 配置优化

### 环境变量配置

```env
# 存储基础配置
STORAGE_BASE_PATH=./data
STORAGE_ENABLE_COMPRESSION=true
STORAGE_AUTO_CLEANUP=true

# 缓存TTL配置（秒）
CACHE_TTL_REALTIME=30
CACHE_TTL_FREQUENT=300
CACHE_TTL_REGULAR=3600
CACHE_TTL_DAILY=86400
CACHE_TTL_COMPUTED=1800

# 性能优化
CACHE_OPTIMIZATION_ENABLED=true
CACHE_MAX_MEMORY_USAGE=512
CACHE_EVICTION_POLICY=lru
```

### Redis配置建议

```
# redis.conf
maxmemory 512mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

## 📈 监控和告警

### API端点

```bash
# 获取存储统计
GET /storage/stats

# 获取性能数据
GET /storage/performance

# 获取优化报告
GET /storage/optimization-report

# 清理缓存
DELETE /storage/cache/{character_id}
```

### 性能指标

- **响应时间**: < 100ms (Redis), < 500ms (Pickle), < 1s (JSON)
- **错误率**: < 1%
- **缓存命中率**: > 80%
- **内存使用**: < 512MB

### 告警阈值

```python
# 设置监控阈值
performance_monitor.set_thresholds({
    "max_duration": 5000,      # 5秒
    "max_error_rate": 0.05,    # 5%
    "min_throughput": 1024     # 1KB/s
})
```

## 🛠️ 故障排除

### 常见问题

1. **Redis连接失败**
   - 检查Redis服务状态
   - 验证连接配置
   - 查看网络连接

2. **缓存命中率低**
   - 调整TTL策略
   - 检查数据访问模式
   - 优化缓存键设计

3. **存储空间不足**
   - 启用数据压缩
   - 清理过期数据
   - 调整清理策略

4. **性能下降**
   - 查看性能监控
   - 分析慢操作
   - 优化数据结构

### 调试工具

```python
# 获取详细统计
stats = await storage_manager.get_storage_stats()

# 生成优化报告
report = optimization_advisor.generate_report()

# 查看告警
alerts = performance_monitor.get_alerts()
```

## 📚 最佳实践总结

1. **数据分层**: 根据访问频率和重要性选择存储方案
2. **智能缓存**: 使用智能缓存管理器自动优化
3. **批量操作**: 减少网络开销，提高效率
4. **错误处理**: 实现优雅降级和重试机制
5. **性能监控**: 持续监控和优化存储性能
6. **定期清理**: 自动清理过期数据，保持系统健康
7. **配置优化**: 根据实际使用情况调整配置参数

通过遵循这些最佳实践，您可以充分发挥EVE Online Assistant存储系统的性能优势，确保应用的高效运行。
