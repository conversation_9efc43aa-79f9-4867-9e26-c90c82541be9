import { CheckCircleOutlined, CloseCircleOutlined, HomeOutlined } from '@ant-design/icons'
import { Alert, Button, Result, Spin } from 'antd'
import { motion } from 'framer-motion'
import { useEffect, useState } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'

import { LoadingScreen } from '@/components/ui/LoadingScreen'
import { useEVELogin } from '@/hooks/useAuth'

export default function CallbackPage() {
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const { complete } = useEVELogin()

  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading')
  const [errorMessage, setErrorMessage] = useState<string>('')
  const [characterInfo, setCharacterInfo] = useState<{
    name: string
    id: number
  } | null>(null)

  useEffect(() => {
    handleCallback()
  }, [])

  const handleCallback = async () => {
    try {
      // 获取URL参数
      const code = searchParams.get('code')
      const state = searchParams.get('state')
      const error = searchParams.get('error')
      const errorDescription = searchParams.get('error_description')

      // 检查是否有错误
      if (error) {
        setStatus('error')
        setErrorMessage(errorDescription || `认证错误: ${error}`)
        return
      }

      // 检查必需参数
      if (!code || !state) {
        setStatus('error')
        setErrorMessage('缺少必需的认证参数')
        return
      }

      // 完成EVE登录
      await complete.mutateAsync({ code, state })

      // 设置模拟角色信息用于显示
      setCharacterInfo({
        name: 'Demo Character',
        id: 12345
      })
      setStatus('success')

      // 3秒后自动跳转
      setTimeout(() => {
        navigate('/dashboard')
      }, 3000)

    } catch (error: any) {
      console.error('EVE callback error:', error)
      setStatus('error')
      setErrorMessage(error.message || '认证过程中发生错误')
    }
  }

  const handleRetry = () => {
    navigate('/auth/login')
  }

  const handleGoHome = () => {
    navigate('/dashboard')
  }

  // 加载状态
  if (status === 'loading') {
    return (
      <LoadingScreen tip="正在处理EVE认证..." />
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-dark-900 dark:via-dark-800 dark:to-dark-900 py-12 px-4 sm:px-6 lg:px-8">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="max-w-md w-full"
      >
        {status === 'success' ? (
          <div className="bg-white dark:bg-dark-800 rounded-xl shadow-xl border border-gray-200 dark:border-dark-700 p-8">
            <Result
              icon={
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                >
                  <CheckCircleOutlined className="text-green-500" />
                </motion.div>
              }
              title={
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  <span className="text-gray-900 dark:text-gray-100">
                    EVE角色绑定成功！
                  </span>
                </motion.div>
              }
              subTitle={
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                  className="space-y-2"
                >
                  {characterInfo && (
                    <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
                      <div className="flex items-center justify-center space-x-3">
                        <img
                          src={`https://images.evetech.net/characters/${characterInfo.id}/portrait?size=64`}
                          alt={characterInfo.name}
                          className="w-12 h-12 rounded-full border-2 border-blue-300"
                        />
                        <div className="text-left">
                          <div className="font-semibold text-blue-900 dark:text-blue-100">
                            {characterInfo.name}
                          </div>
                          <div className="text-sm text-blue-600 dark:text-blue-400">
                            角色ID: {characterInfo.id}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                  <p className="text-gray-600 dark:text-gray-400">
                    您的EVE角色已成功绑定到账户，正在跳转到仪表板...
                  </p>
                </motion.div>
              }
              extra={
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                  className="space-y-3"
                >
                  <Button
                    type="primary"
                    size="large"
                    icon={<HomeOutlined />}
                    onClick={handleGoHome}
                    className="w-full"
                  >
                    立即进入仪表板
                  </Button>

                  {/* 自动跳转倒计时 */}
                  <div className="text-center">
                    <Spin size="small" />
                    <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">
                      3秒后自动跳转...
                    </span>
                  </div>
                </motion.div>
              }
            />
          </div>
        ) : (
          <div className="bg-white dark:bg-dark-800 rounded-xl shadow-xl border border-gray-200 dark:border-dark-700 p-8">
            <Result
              icon={
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                >
                  <CloseCircleOutlined className="text-red-500" />
                </motion.div>
              }
              title={
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  <span className="text-gray-900 dark:text-gray-100">
                    认证失败
                  </span>
                </motion.div>
              }
              subTitle={
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                >
                  <Alert
                    message="EVE认证过程中发生错误"
                    description={errorMessage}
                    type="error"
                    showIcon
                    className="text-left mb-4"
                  />
                  <p className="text-gray-600 dark:text-gray-400">
                    请重试或联系技术支持
                  </p>
                </motion.div>
              }
              extra={
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                  className="space-y-3"
                >
                  <Button
                    type="primary"
                    size="large"
                    onClick={handleRetry}
                    className="w-full"
                  >
                    重新登录
                  </Button>
                  <Button
                    size="large"
                    icon={<HomeOutlined />}
                    onClick={handleGoHome}
                    className="w-full"
                  >
                    返回首页
                  </Button>
                </motion.div>
              }
            />
          </div>
        )}

        {/* 帮助信息 */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6 }}
          className="mt-6 text-center"
        >
          <div className="bg-gray-50 dark:bg-dark-700 rounded-lg p-4 border border-gray-200 dark:border-dark-600">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-2">
              关于EVE角色绑定
            </h3>
            <p className="text-xs text-gray-600 dark:text-gray-400 leading-relaxed">
              绑定EVE角色后，您可以查看角色数据、管理资产、分析市场等。
              我们使用EVE官方API，确保数据安全可靠。
            </p>
          </div>
        </motion.div>
      </motion.div>
    </div>
  )
}
