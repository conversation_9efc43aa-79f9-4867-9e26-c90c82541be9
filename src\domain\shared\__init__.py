"""
共享领域组件 - 值对象、基础实体、领域事件等
"""
from .base_entity import Entity, AggregateRoot, ValueObject, DomainService, Specification
from .specifications import (
    AndSpecification, OrSpecification, NotSpecification,
    AttributeEqualsSpecification, AttributeInRangeSpecification,
    DateTimeRangeSpecification, IsActiveSpecification, IsExpiredSpecification,
    HasPermissionSpecification, CharacterOwnershipSpecification, TokenValiditySpecification,
    CommonSpecifications, and_spec, or_spec, not_spec
)
from .domain_events import DomainEvent, DomainEventHandler, EventBus
from .exceptions import (
    DomainException, DomainValidationError, BusinessRuleViolationError,
    AggregateNotFoundError, ConcurrencyError, InvariantViolationError
)
from .value_objects import (
    CharacterId, CorporationId, AllianceId, TypeId, SystemId,
    Money, Quantity, Location, SecurityStatus, SkillPoints
)

__all__ = [
    # 基础实体
    "Entity",
    "AggregateRoot",
    "ValueObject",
    "DomainService",
    "Specification",

    # 规约模式
    "AndSpecification",
    "OrSpecification",
    "NotSpecification",
    "AttributeEqualsSpecification",
    "AttributeInRangeSpecification",
    "DateTimeRangeSpecification",
    "IsActiveSpecification",
    "IsExpiredSpecification",
    "HasPermissionSpecification",
    "CharacterOwnershipSpecification",
    "TokenValiditySpecification",
    "CommonSpecifications",
    "and_spec",
    "or_spec",
    "not_spec",

    # 领域事件
    "DomainEvent",
    "DomainEventHandler",
    "EventBus",

    # 异常
    "DomainException",
    "DomainValidationError",
    "BusinessRuleViolationError",
    "AggregateNotFoundError",
    "ConcurrencyError",
    "InvariantViolationError",

    # 值对象
    "CharacterId",
    "CorporationId",
    "AllianceId",
    "TypeId",
    "SystemId",
    "Money",
    "Quantity",
    "Location",
    "SecurityStatus",
    "SkillPoints",
]
