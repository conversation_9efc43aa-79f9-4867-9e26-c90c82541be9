@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* 全局样式 */
@layer base {
  html {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    @apply bg-gray-50 text-gray-900;
    margin: 0;
    padding: 0;
    min-height: 100vh;
  }

  /* 暗色主题 */
  .dark body {
    @apply bg-dark-900 text-gray-100;
  }

  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }

  .dark ::-webkit-scrollbar-track {
    @apply bg-dark-800;
  }

  .dark ::-webkit-scrollbar-thumb {
    @apply bg-dark-600;
  }

  .dark ::-webkit-scrollbar-thumb:hover {
    @apply bg-dark-500;
  }

  /* 选择文本样式 */
  ::selection {
    @apply bg-primary-200 text-primary-900;
  }

  .dark ::selection {
    @apply bg-primary-800 text-primary-100;
  }
}

/* 组件样式 */
@layer components {
  /* 卡片样式 */
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
  }

  .dark .card {
    @apply bg-dark-800 border-dark-700;
  }

  /* 按钮样式增强 */
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium px-4 py-2 rounded-md transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-900 font-medium px-4 py-2 rounded-md transition-colors duration-200;
  }

  .dark .btn-secondary {
    @apply bg-dark-700 hover:bg-dark-600 text-gray-100;
  }

  /* 输入框样式 */
  .input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }

  .dark .input {
    @apply bg-dark-800 border-dark-600 text-gray-100;
  }

  /* 表格样式 */
  .table {
    @apply w-full border-collapse;
  }

  .table th {
    @apply bg-gray-50 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }

  .dark .table th {
    @apply bg-dark-800 text-gray-400;
  }

  .table td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }

  .dark .table td {
    @apply text-gray-100;
  }

  /* 加载动画 */
  .loading-spinner {
    @apply animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600;
  }

  /* EVE特色样式 */
  .eve-gradient {
    background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
  }

  .eve-card {
    @apply bg-gradient-to-br from-blue-50 to-purple-50 border border-blue-200;
  }

  .dark .eve-card {
    @apply from-blue-900/20 to-purple-900/20 border-blue-800;
  }

  /* 状态指示器 */
  .status-online {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800;
  }

  .dark .status-online {
    @apply bg-green-900/20 text-green-400;
  }

  .status-offline {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800;
  }

  .dark .status-offline {
    @apply bg-gray-900/20 text-gray-400;
  }

  /* 数据展示 */
  .data-value {
    @apply text-2xl font-bold text-gray-900;
  }

  .dark .data-value {
    @apply text-gray-100;
  }

  .data-label {
    @apply text-sm text-gray-500 font-medium;
  }

  .dark .data-label {
    @apply text-gray-400;
  }

  /* 响应式隐藏 */
  .mobile-hidden {
    @apply hidden md:block;
  }

  .desktop-hidden {
    @apply block md:hidden;
  }
}

/* 工具类 */
@layer utilities {
  /* 文本截断 */
  .text-truncate {
    @apply truncate;
  }

  .text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .text-truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 玻璃效果 */
  .glass {
    @apply backdrop-blur-sm bg-white/80 border border-white/20;
  }

  .dark .glass {
    @apply bg-dark-900/80 border-dark-700/50;
  }

  /* 阴影效果 */
  .shadow-eve {
    box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.15);
  }

  .shadow-eve-lg {
    box-shadow: 0 10px 25px -3px rgba(59, 130, 246, 0.1);
  }

  /* 动画效果 */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-in {
    animation: slideIn 0.3s ease-out;
  }

  .animate-bounce-in {
    animation: bounceIn 0.6s ease-out;
  }

  /* 焦点样式 */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .dark .focus-ring {
    @apply focus:ring-offset-dark-900;
  }
}

/* Ant Design样式覆盖 */
.ant-layout {
  min-height: 100vh;
}

.ant-layout-sider-collapsed .ant-menu-item-icon {
  font-size: 16px;
}

.ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
}

.dark .ant-table-thead > tr > th {
  background-color: #1f2937;
  color: #f3f4f6;
}

.ant-card {
  border-radius: 8px;
}

.ant-btn {
  border-radius: 6px;
  font-weight: 500;
}

/* 图表容器样式 */
.chart-container {
  @apply w-full h-64 md:h-80 lg:h-96;
}

.chart-container canvas {
  @apply rounded-lg;
}

/* 响应式表格 */
@media (max-width: 768px) {
  .ant-table-wrapper {
    overflow-x: auto;
  }
  
  .ant-table {
    min-width: 600px;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break {
    page-break-after: always;
  }
}
