import React from 'react'
import { Spin } from 'antd'
import { LoadingOutlined } from '@ant-design/icons'

interface LoadingScreenProps {
  tip?: string
  size?: 'small' | 'default' | 'large'
  className?: string
}

export function LoadingScreen({
  tip = '加载中...',
  size = 'large',
  className = ''
}: LoadingScreenProps) {
  const antIcon = <LoadingOutlined style={{ fontSize: 48 }} spin />

  return (
    <div className={`
      flex flex-col items-center justify-center min-h-screen
      bg-gradient-to-br from-blue-50 to-purple-50
      dark:from-dark-900 dark:to-dark-800
      ${className}
    `}>
      <div className="text-center">
        {/* Logo */}
        <div className="mb-8">
          <div className="w-16 h-16 mx-auto mb-4 bg-primary-600 rounded-full flex items-center justify-center">
            <span className="text-2xl font-bold text-white">E</span>
          </div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            EVE Assistant
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            EVE Online 管理助手
          </p>
        </div>

        {/* 加载动画 */}
        <div className="mb-4">
          <Spin
            indicator={antIcon}
            size={size}
            tip={tip}
          />
        </div>

        {/* 加载提示 */}
        <div className="text-sm text-gray-500 dark:text-gray-400">
          正在初始化应用程序...
        </div>

        {/* 进度条动画 */}
        <div className="mt-6 w-64 mx-auto">
          <div className="h-1 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
            <div className="h-full bg-gradient-to-r from-primary-500 to-purple-500 rounded-full animate-pulse"></div>
          </div>
        </div>
      </div>
    </div>
  )
}

// 简化版加载组件
export function SimpleLoading({ tip = '加载中...' }: { tip?: string }) {
  return (
    <div className="flex items-center justify-center p-8">
      <Spin tip={tip} />
    </div>
  )
}

// 页面加载组件
export function PageLoading({ tip = '页面加载中...' }: { tip?: string }) {
  return (
    <div className="flex items-center justify-center min-h-96">
      <Spin size="large" tip={tip} />
    </div>
  )
}

// 内容加载组件
export function ContentLoading({ tip = '内容加载中...' }: { tip?: string }) {
  return (
    <div className="flex items-center justify-center py-12">
      <Spin tip={tip} />
    </div>
  )
}
