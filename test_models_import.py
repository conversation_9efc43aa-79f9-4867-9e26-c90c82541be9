#!/usr/bin/env python3
"""
测试模型导入
"""
import sys
sys.path.insert(0, '.')

def test_models_import():
    print("🔍 测试模型导入")
    print("=" * 40)
    
    try:
        print("1. 导入Base...")
        from src.infrastructure.persistence.database import Base
        print(f"   ✅ Base导入成功: {Base}")
        
        print("2. 导入所有模型...")
        from src.infrastructure.persistence.models import (
            CharacterModel, SkillModel, SkillQueueModel, AttributesModel,
            AssetModel, MarketOrderModel, MarketHistoryModel, IndustryJobModel,
            CorporationModel, AllianceModel, UniverseTypeModel, UniverseSystemModel,
            UserModel, TokenModel
        )
        print("   ✅ 所有模型导入成功")
        
        print("3. 检查Base.metadata...")
        print(f"   表数量: {len(Base.metadata.tables)}")
        print("   表列表:")
        for table_name in Base.metadata.tables.keys():
            print(f"     - {table_name}")
        
        print("4. 检查关键表...")
        key_tables = ['users', 'tokens', 'characters']
        for table_name in key_tables:
            if table_name in Base.metadata.tables:
                table = Base.metadata.tables[table_name]
                print(f"   ✅ {table_name}: {len(table.columns)} 列")
            else:
                print(f"   ❌ {table_name}: 不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_models_import()
    if success:
        print("\n✅ 模型导入测试通过")
    else:
        print("\n❌ 模型导入测试失败")
