#!/usr/bin/env python3
"""
修复依赖问题的脚本
"""
import sys
import subprocess
import os

def install_package(package, python_path=None):
    """安装包到指定Python环境"""
    if python_path is None:
        python_path = sys.executable
    
    try:
        print(f"正在安装 {package} 到 {python_path}...")
        result = subprocess.run([
            python_path, "-m", "pip", "install", package
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print(f"✅ {package} 安装成功")
            return True
        else:
            print(f"❌ {package} 安装失败:")
            print(f"   stdout: {result.stdout}")
            print(f"   stderr: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print(f"❌ {package} 安装超时")
        return False
    except Exception as e:
        print(f"❌ {package} 安装异常: {e}")
        return False

def test_import(module_name):
    """测试模块导入"""
    try:
        __import__(module_name)
        print(f"✅ {module_name} 导入成功")
        return True
    except ImportError as e:
        print(f"❌ {module_name} 导入失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 修复依赖问题")
    print("=" * 50)
    
    # 显示Python信息
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print()
    
    # 需要安装的包 - 完整的生产级依赖列表
    packages = [
        "pydantic",
        "pydantic-settings",
        "fastapi",
        "uvicorn",
        "python-dotenv",
        "structlog",
        "httpx",
        "sqlalchemy",
        "alembic",
        "python-jose[cryptography]",
        "pyngrok",
        "pyjwt"
    ]
    
    # 测试当前环境
    print("🔍 测试当前环境...")
    all_available = True
    for pkg in packages:
        module_name = pkg.replace("-", "_")
        if not test_import(module_name):
            all_available = False
    
    if all_available:
        print("\n🎉 所有依赖都可用！")
        return 0
    
    print("\n📦 安装缺失的依赖...")
    
    # 安装到当前Python环境
    failed_packages = []
    for package in packages:
        if not install_package(package):
            failed_packages.append(package)
    
    print("\n" + "=" * 50)
    
    if failed_packages:
        print(f"❌ 以下包安装失败: {', '.join(failed_packages)}")
        return 1
    else:
        print("🎉 所有依赖安装完成!")
        
        # 再次测试
        print("\n🔍 验证安装...")
        for pkg in packages:
            module_name = pkg.replace("-", "_")
            test_import(module_name)
        
        return 0

if __name__ == "__main__":
    sys.exit(main())
