# EVE SSO OAuth 2.0 集成架构设计

## 📋 架构概述

本文档详细描述了EVE Online管理助手与EVE SSO (Single Sign-On) 系统的OAuth 2.0集成架构设计，确保安全、可靠、高效的用户认证和授权流程。

## 🏗️ 架构设计原则

### 1. 安全优先
- 遵循OAuth 2.0安全最佳实践
- 实施PKCE (Proof Key for Code Exchange) 增强安全性
- 使用JWT令牌验证和状态管理
- 实现令牌自动刷新和安全撤销

### 2. DDD架构集成
- 认证逻辑封装在领域层
- 应用服务协调认证流程
- 基础设施层处理外部API调用
- 清晰的职责分离

### 3. 高可用性
- 令牌自动刷新机制
- 错误处理和重试策略
- 状态持久化和恢复
- 监控和告警集成

## 🔧 核心组件架构

### 1. 领域层 (Domain Layer)

#### 1.1 认证聚合根
```python
# src/domain/auth/aggregates.py
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from ..shared.base_entity import AggregateRoot
from ..shared.value_objects import UserId, CharacterId
from .value_objects import AccessToken, RefreshToken, Scope
from .events import UserAuthenticatedEvent, TokenRefreshedEvent

@dataclass
class UserAuthentication(AggregateRoot):
    """用户认证聚合根"""
    user_id: UserId
    main_character_id: CharacterId
    character_bindings: Dict[CharacterId, 'CharacterBinding'] = field(default_factory=dict)
    session_tokens: List['SessionToken'] = field(default_factory=list)
    
    def bind_character(self, character_id: CharacterId, access_token: AccessToken, 
                      refresh_token: RefreshToken, scopes: List[Scope]) -> None:
        """绑定EVE角色"""
        binding = CharacterBinding(
            character_id=character_id,
            access_token=access_token,
            refresh_token=refresh_token,
            scopes=scopes,
            bound_at=datetime.utcnow()
        )
        
        self.character_bindings[character_id] = binding
        self.add_domain_event(CharacterBoundEvent(self.id, character_id))
        self.mark_as_updated()
    
    def refresh_character_token(self, character_id: CharacterId, 
                               new_access_token: AccessToken,
                               new_refresh_token: Optional[RefreshToken] = None) -> None:
        """刷新角色令牌"""
        if character_id not in self.character_bindings:
            raise CharacterNotBoundError(character_id)
        
        binding = self.character_bindings[character_id]
        binding.refresh_token(new_access_token, new_refresh_token)
        
        self.add_domain_event(TokenRefreshedEvent(self.id, character_id))
        self.mark_as_updated()
    
    def create_session(self, character_id: CharacterId, expires_in: timedelta) -> 'SessionToken':
        """创建用户会话"""
        session = SessionToken.create(self.user_id, character_id, expires_in)
        self.session_tokens.append(session)
        
        self.add_domain_event(SessionCreatedEvent(self.id, session.token_id))
        return session
```

#### 1.2 值对象定义
```python
# src/domain/auth/value_objects.py
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import List, Optional
from ..shared.base_entity import ValueObject

@dataclass(frozen=True)
class AccessToken(ValueObject):
    """访问令牌值对象"""
    token: str
    expires_at: datetime
    
    def is_expired(self) -> bool:
        return datetime.utcnow() >= self.expires_at
    
    def expires_in_seconds(self) -> int:
        delta = self.expires_at - datetime.utcnow()
        return max(0, int(delta.total_seconds()))

@dataclass(frozen=True)
class RefreshToken(ValueObject):
    """刷新令牌值对象"""
    token: str
    
@dataclass(frozen=True)
class Scope(ValueObject):
    """权限范围值对象"""
    name: str
    description: str
    
@dataclass(frozen=True)
class AuthorizationCode(ValueObject):
    """授权码值对象"""
    code: str
    state: str
    expires_at: datetime
    
    def is_expired(self) -> bool:
        return datetime.utcnow() >= self.expires_at
```

#### 1.3 领域服务
```python
# src/domain/auth/services.py
from abc import ABC, abstractmethod
from typing import List, Optional, Tuple
from ..shared.base_entity import DomainService
from .value_objects import AccessToken, RefreshToken, Scope, AuthorizationCode

class AuthenticationDomainService(DomainService):
    """认证领域服务"""
    
    def validate_authorization_flow(self, code: AuthorizationCode, 
                                  expected_state: str) -> bool:
        """验证授权流程"""
        if code.is_expired():
            return False
        
        if code.state != expected_state:
            return False
        
        return True
    
    def calculate_token_refresh_time(self, access_token: AccessToken) -> datetime:
        """计算令牌刷新时间"""
        # 在令牌过期前5分钟刷新
        refresh_buffer = timedelta(minutes=5)
        return access_token.expires_at - refresh_buffer
```

### 2. 应用层 (Application Layer)

#### 2.1 认证应用服务
```python
# src/application/auth/authentication_service.py
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from ..domain.auth.aggregates import UserAuthentication
from ..domain.auth.services import AuthenticationDomainService
from ..domain.auth.repositories import UserAuthenticationRepository
from ..infrastructure.esi.auth import EVESSOClient

class AuthenticationApplicationService:
    """认证应用服务"""
    
    def __init__(self,
                 user_auth_repository: UserAuthenticationRepository,
                 auth_domain_service: AuthenticationDomainService,
                 sso_client: EVESSOClient,
                 unit_of_work: UnitOfWork):
        self._user_auth_repository = user_auth_repository
        self._auth_domain_service = auth_domain_service
        self._sso_client = sso_client
        self._unit_of_work = unit_of_work
    
    async def initiate_eve_login(self, scopes: List[str]) -> Dict[str, Any]:
        """发起EVE SSO登录"""
        login_url, state = self._sso_client.generate_login_url(scopes)
        
        # 缓存认证状态
        await self._cache_auth_state(state, scopes)
        
        return {
            "login_url": login_url,
            "state": state,
            "expires_in": 300  # 5分钟
        }
    
    async def handle_eve_callback(self, authorization_code: str, 
                                 state: str) -> Dict[str, Any]:
        """处理EVE SSO回调"""
        async with self._unit_of_work:
            # 验证状态
            cached_state = await self._get_cached_auth_state(state)
            if not cached_state:
                raise InvalidAuthStateError("Invalid or expired state")
            
            # 交换令牌
            token_data = await self._sso_client.exchange_code_for_tokens(
                authorization_code, state
            )
            
            # 获取或创建用户认证
            character_id = CharacterId(int(token_data["character_id"]))
            user_auth = await self._get_or_create_user_auth(character_id)
            
            # 绑定角色
            access_token = AccessToken(
                token=token_data["access_token"],
                expires_at=token_data["expires_at"]
            )
            refresh_token = RefreshToken(token=token_data["refresh_token"])
            scopes = [Scope(name=scope, description="") for scope in token_data["scopes"]]
            
            user_auth.bind_character(character_id, access_token, refresh_token, scopes)
            
            # 创建会话
            session = user_auth.create_session(character_id, timedelta(hours=24))
            
            # 保存
            await self._user_auth_repository.save(user_auth)
            
            return {
                "success": True,
                "user_id": user_auth.user_id.value,
                "character_id": character_id.value,
                "session_token": session.token,
                "expires_at": session.expires_at.isoformat()
            }
```

### 3. 基础设施层 (Infrastructure Layer)

#### 3.1 EVE SSO客户端增强
```python
# src/infrastructure/esi/auth_enhanced.py
import asyncio
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import httpx
from jose import jwt
from ..config import settings
from ..config.logging import get_logger

class EnhancedEVESSOClient:
    """增强的EVE SSO客户端"""
    
    def __init__(self):
        self._client = httpx.AsyncClient(
            timeout=httpx.Timeout(30.0),
            limits=httpx.Limits(max_connections=10, max_keepalive_connections=5)
        )
        self._jwks_cache: Optional[Dict] = None
        self._jwks_cache_expires: Optional[datetime] = None
        
    async def generate_login_url_with_pkce(self, scopes: List[str]) -> Tuple[str, str, str]:
        """生成带PKCE的登录URL"""
        import base64
        import hashlib
        import secrets
        
        # 生成PKCE参数
        code_verifier = base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')
        code_challenge = base64.urlsafe_b64encode(
            hashlib.sha256(code_verifier.encode('utf-8')).digest()
        ).decode('utf-8').rstrip('=')
        
        state = secrets.token_urlsafe(32)
        
        params = {
            "response_type": "code",
            "client_id": settings.eve_sso_client_id,
            "redirect_uri": settings.eve_sso_callback_url,
            "scope": " ".join(scopes),
            "state": state,
            "code_challenge": code_challenge,
            "code_challenge_method": "S256"
        }
        
        login_url = f"{settings.eve_sso_base_url}/v2/oauth/authorize?" + \
                   "&".join(f"{k}={v}" for k, v in params.items())
        
        return login_url, state, code_verifier
    
    async def exchange_code_with_pkce(self, authorization_code: str, 
                                     code_verifier: str) -> Dict[str, Any]:
        """使用PKCE交换授权码"""
        data = {
            "grant_type": "authorization_code",
            "code": authorization_code,
            "redirect_uri": settings.eve_sso_callback_url,
            "client_id": settings.eve_sso_client_id,
            "code_verifier": code_verifier
        }
        
        response = await self._client.post(
            f"{settings.eve_sso_base_url}/v2/oauth/token",
            data=data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        if response.status_code != 200:
            raise ESIAuthenticationError(f"Token exchange failed: {response.text}")
        
        token_data = response.json()
        
        # 验证访问令牌
        character_info = await self._verify_access_token(token_data["access_token"])
        
        return {
            **token_data,
            "character_id": character_info["sub"].split(":")[-1],
            "character_name": character_info["name"],
            "scopes": character_info.get("scp", []),
            "expires_at": datetime.utcnow() + timedelta(seconds=token_data["expires_in"])
        }
```

## 🔄 认证流程设计

### 1. 用户登录流程
```mermaid
sequenceDiagram
    participant User as 用户
    participant Frontend as 前端
    participant API as API服务
    participant SSO as EVE SSO
    participant DB as 数据库
    
    User->>Frontend: 点击EVE登录
    Frontend->>API: POST /auth/eve/login
    API->>API: 生成state和PKCE参数
    API->>DB: 缓存认证状态
    API->>Frontend: 返回登录URL
    Frontend->>User: 重定向到EVE SSO
    User->>SSO: 授权应用
    SSO->>Frontend: 回调带授权码
    Frontend->>API: POST /auth/eve/callback
    API->>SSO: 交换访问令牌
    SSO->>API: 返回令牌和角色信息
    API->>DB: 保存用户和令牌
    API->>Frontend: 返回会话令牌
    Frontend->>User: 登录成功
```

### 2. 令牌刷新流程
```mermaid
sequenceDiagram
    participant Scheduler as 调度器
    participant API as API服务
    participant SSO as EVE SSO
    participant DB as 数据库
    
    Scheduler->>API: 检查即将过期的令牌
    API->>DB: 查询需要刷新的令牌
    API->>SSO: 使用refresh_token刷新
    SSO->>API: 返回新的访问令牌
    API->>DB: 更新令牌信息
    API->>Scheduler: 刷新完成
```

## 📊 安全考虑

### 1. 令牌安全
- 访问令牌存储加密
- 刷新令牌单独存储
- 令牌传输使用HTTPS
- 实施令牌轮换策略

### 2. 状态管理
- 使用加密的状态参数
- 状态有效期限制
- 防止CSRF攻击
- 实施重放攻击防护

### 3. 错误处理
- 不泄露敏感信息
- 统一错误响应格式
- 详细的安全日志
- 异常情况告警

## 🔧 配置管理

### 环境变量配置
```bash
# EVE SSO配置
EVE_SSO_CLIENT_ID=your_client_id
EVE_SSO_CLIENT_SECRET=your_client_secret
EVE_SSO_CALLBACK_URL=https://your-domain.com/auth/eve/callback
EVE_SSO_BASE_URL=https://login.eveonline.com

# JWT配置
JWT_SECRET_KEY=your_secret_key
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=1440

# 安全配置
ENABLE_PKCE=true
STATE_TIMEOUT_SECONDS=300
TOKEN_REFRESH_BUFFER_MINUTES=5
```

## 📈 监控和指标

### 关键指标
- 认证成功率
- 令牌刷新成功率
- 平均认证时间
- 错误率统计

### 告警规则
- 认证失败率 > 5%
- 令牌刷新失败率 > 2%
- SSO服务不可用
- 异常登录尝试

---

**文档版本**: v1.0  
**最后更新**: 2025-01-09  
**负责人**: 认证架构团队
