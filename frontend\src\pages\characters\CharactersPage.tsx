import React from 'react'
import { Card, Empty, Button } from 'antd'
import { PlusOutlined } from '@ant-design/icons'

export default function CharactersPage() {
  return (
    <div className="p-6">
      <Card title="角色管理">
        <Empty
          description="角色管理功能开发中..."
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        >
          <Button type="primary" icon={<PlusOutlined />}>
            绑定新角色
          </Button>
        </Empty>
      </Card>
    </div>
  )
}
