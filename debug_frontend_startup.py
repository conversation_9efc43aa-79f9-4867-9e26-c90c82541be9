#!/usr/bin/env python3
"""
调试前端启动问题
"""
import subprocess
import time
import os
import sys
from pathlib import Path

def debug_frontend_startup():
    """调试前端启动"""
    print("🔧 调试前端启动问题")
    print("=" * 60)
    
    frontend_dir = Path("frontend")
    
    # 检查基础环境
    print("📋 环境检查:")
    print(f"   工作目录: {os.getcwd()}")
    print(f"   前端目录: {frontend_dir.absolute()}")
    print(f"   前端目录存在: {frontend_dir.exists()}")
    
    if not frontend_dir.exists():
        print("❌ frontend目录不存在")
        return False
    
    # 检查Node.js和npm
    print("\n🔍 Node.js环境检查:")
    
    # 测试不同的npm命令
    npm_commands = [
        "npm",
        "npm.cmd", 
        r"C:\Program Files\nodejs\npm.cmd",
        r"C:\Program Files\nodejs\npm"
    ]
    
    working_npm = None
    for npm_cmd in npm_commands:
        try:
            result = subprocess.run(
                [npm_cmd, "--version"],
                cwd=frontend_dir,
                capture_output=True,
                text=True,
                timeout=10,
                shell=True
            )
            
            if result.returncode == 0:
                print(f"   ✅ {npm_cmd}: {result.stdout.strip()}")
                working_npm = npm_cmd
                break
            else:
                print(f"   ❌ {npm_cmd}: 返回码 {result.returncode}")
                
        except FileNotFoundError:
            print(f"   ❌ {npm_cmd}: 文件未找到")
        except Exception as e:
            print(f"   ❌ {npm_cmd}: {e}")
    
    if not working_npm:
        print("❌ 没有找到可用的npm命令")
        return False
    
    print(f"\n✅ 使用npm命令: {working_npm}")
    
    # 检查package.json
    print("\n📄 检查package.json:")
    package_json = frontend_dir / "package.json"
    if package_json.exists():
        try:
            import json
            with open(package_json, 'r', encoding='utf-8') as f:
                pkg = json.load(f)
                scripts = pkg.get('scripts', {})
                print(f"   ✅ dev脚本: {scripts.get('dev', '未找到')}")
                print(f"   📋 依赖数量: {len(pkg.get('dependencies', {}))}")
                print(f"   📋 开发依赖数量: {len(pkg.get('devDependencies', {}))}")
        except Exception as e:
            print(f"   ❌ 读取package.json失败: {e}")
    else:
        print("   ❌ package.json不存在")
        return False
    
    # 尝试启动前端（详细模式）
    print(f"\n🚀 尝试启动前端服务器...")
    print(f"   命令: {working_npm} run dev")
    print(f"   工作目录: {frontend_dir.absolute()}")
    
    try:
        # 启动前端进程
        process = subprocess.Popen(
            [working_npm, "run", "dev"],
            cwd=frontend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            shell=True,
            bufsize=1,
            universal_newlines=True
        )
        
        print("   ⏳ 等待前端启动...")
        
        # 实时读取输出
        startup_timeout = 30
        start_time = time.time()
        
        while time.time() - start_time < startup_timeout:
            # 检查进程是否还在运行
            if process.poll() is not None:
                # 进程已退出
                stdout, stderr = process.communicate()
                print("   ❌ 前端进程已退出")
                print(f"   📋 返回码: {process.returncode}")
                if stdout:
                    print(f"   📋 标准输出:\n{stdout}")
                if stderr:
                    print(f"   📋 错误输出:\n{stderr}")
                return False
            
            # 尝试连接前端服务器
            try:
                import urllib.request
                response = urllib.request.urlopen('http://localhost:3000', timeout=2)
                if response.getcode() == 200:
                    print("   ✅ 前端服务器启动成功！")
                    print(f"   📋 PID: {process.pid}")
                    
                    # 读取一些启动输出
                    try:
                        # 非阻塞读取
                        import select
                        if hasattr(select, 'select'):
                            ready, _, _ = select.select([process.stdout], [], [], 0.1)
                            if ready:
                                output = process.stdout.readline()
                                if output:
                                    print(f"   📋 启动信息: {output.strip()}")
                    except:
                        pass
                    
                    return process
                    
            except Exception:
                # 连接失败，继续等待
                pass
            
            time.sleep(1)
        
        # 超时
        print("   ❌ 前端启动超时")
        
        # 尝试获取输出
        try:
            stdout, stderr = process.communicate(timeout=5)
            if stdout:
                print(f"   📋 标准输出:\n{stdout}")
            if stderr:
                print(f"   📋 错误输出:\n{stderr}")
        except subprocess.TimeoutExpired:
            print("   ⚠️  无法获取进程输出")
            process.kill()
        
        return False
        
    except Exception as e:
        print(f"   ❌ 启动前端失败: {e}")
        return False

def main():
    """主函数"""
    result = debug_frontend_startup()
    
    if result:
        print("\n🎉 前端启动成功！")
        print("📝 现在可以访问: http://localhost:3000")
        
        # 保持进程运行
        try:
            print("\n⏳ 前端服务器正在运行...")
            print("   按 Ctrl+C 停止服务器")
            result.wait()
        except KeyboardInterrupt:
            print("\n🛑 停止前端服务器...")
            result.terminate()
            try:
                result.wait(timeout=5)
                print("✅ 前端服务器已停止")
            except subprocess.TimeoutExpired:
                result.kill()
                print("⚠️  强制终止前端服务器")
        
        return 0
    else:
        print("\n❌ 前端启动失败")
        print("\n💡 可能的解决方案:")
        print("1. 检查Node.js版本兼容性")
        print("2. 重新安装前端依赖: cd frontend && npm install")
        print("3. 检查端口3000是否被占用")
        print("4. 查看上面的详细错误信息")
        return 1

if __name__ == "__main__":
    sys.exit(main())
