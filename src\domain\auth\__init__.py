"""
认证领域模块
"""

from .aggregates import UserAuthentication
from .value_objects import AccessToken, RefreshToken, Scope, SessionToken
from .events import (
    UserAuthenticatedEvent, 
    CharacterBoundEvent, 
    TokenRefreshedEvent,
    SessionCreatedEvent,
    SessionExpiredEvent
)
from .services import AuthenticationDomainService
from .repositories import UserAuthenticationRepository
from .exceptions import (
    AuthenticationDomainError,
    CharacterNotBoundError,
    InvalidTokenError,
    SessionExpiredError
)

__all__ = [
    # 聚合根
    'UserAuthentication',
    
    # 值对象
    'AccessToken',
    'RefreshToken', 
    'Scope',
    'SessionToken',
    
    # 领域事件
    'UserAuthenticatedEvent',
    'CharacterBoundEvent',
    'TokenRefreshedEvent', 
    'SessionCreatedEvent',
    'SessionExpiredEvent',
    
    # 领域服务
    'AuthenticationDomainService',
    
    # 仓储接口
    'UserAuthenticationRepository',
    
    # 异常
    'AuthenticationDomainError',
    'CharacterNotBoundError',
    'InvalidTokenError',
    'SessionExpiredError'
]
