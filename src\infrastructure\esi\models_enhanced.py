"""
增强的ESI数据模型
"""
from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum

from ...domain.shared.base_entity import ValueObject


class OrderType(Enum):
    """订单类型"""
    BUY = "buy"
    SELL = "sell"


class IndustryActivityType(Enum):
    """工业活动类型"""
    MANUFACTURING = 1
    RESEARCHING_TECHNOLOGY = 2
    RESEARCHING_TIME_EFFICIENCY = 3
    RESEARCHING_MATERIAL_EFFICIENCY = 4
    COPYING = 5
    DUPLICATING = 6
    REVERSE_ENGINEERING = 7
    INVENTION = 8
    REACTIONS = 11


class JobStatus(Enum):
    """工业任务状态"""
    ACTIVE = "active"
    PAUSED = "paused"
    READY = "ready"
    DELIVERED = "delivered"
    CANCELLED = "cancelled"
    REVERTED = "reverted"


@dataclass(frozen=True)
class CharacterInfo(ValueObject):
    """角色基础信息"""
    character_id: int
    name: str
    corporation_id: int
    corporation_name: str
    alliance_id: Optional[int] = None
    alliance_name: Optional[str] = None
    faction_id: Optional[int] = None
    race_id: int = 0
    bloodline_id: int = 0
    ancestry_id: int = 0
    gender: str = "male"
    security_status: float = 0.0
    birthday: Optional[datetime] = None
    description: Optional[str] = None
    portrait_url: Optional[str] = None
    
    def __post_init__(self):
        if self.character_id <= 0:
            raise ValueError("Character ID must be positive")
        if not self.name:
            raise ValueError("Character name cannot be empty")


@dataclass(frozen=True)
class CharacterSkills(ValueObject):
    """角色技能信息"""
    character_id: int
    total_sp: int
    unallocated_sp: int = 0
    skills: Dict[int, Dict[str, Any]] = field(default_factory=dict)
    
    def get_skill_level(self, skill_id: int) -> int:
        """获取技能等级"""
        return self.skills.get(skill_id, {}).get('trained_skill_level', 0)
    
    def get_skill_sp(self, skill_id: int) -> int:
        """获取技能SP"""
        return self.skills.get(skill_id, {}).get('skillpoints_in_skill', 0)


@dataclass(frozen=True)
class CharacterAssets(ValueObject):
    """角色资产信息"""
    character_id: int
    assets: List[Dict[str, Any]] = field(default_factory=list)
    total_value: float = 0.0
    last_updated: Optional[datetime] = None
    
    def get_assets_by_location(self, location_id: int) -> List[Dict[str, Any]]:
        """按位置获取资产"""
        return [asset for asset in self.assets if asset.get('location_id') == location_id]
    
    def get_assets_by_type(self, type_id: int) -> List[Dict[str, Any]]:
        """按类型获取资产"""
        return [asset for asset in self.assets if asset.get('type_id') == type_id]


@dataclass(frozen=True)
class CharacterWallet(ValueObject):
    """角色钱包信息"""
    character_id: int
    balance: float
    journal: List[Dict[str, Any]] = field(default_factory=list)
    transactions: List[Dict[str, Any]] = field(default_factory=list)
    last_updated: Optional[datetime] = None


@dataclass(frozen=True)
class MarketOrder(ValueObject):
    """市场订单"""
    order_id: int
    character_id: int
    type_id: int
    region_id: int
    location_id: int
    is_buy_order: bool
    price: float
    volume_remain: int
    volume_total: int
    min_volume: int
    duration: int
    issued: datetime
    range: str
    escrow: float = 0.0
    state: str = "open"
    
    @property
    def order_type(self) -> OrderType:
        return OrderType.BUY if self.is_buy_order else OrderType.SELL
    
    @property
    def filled_percentage(self) -> float:
        """已成交百分比"""
        if self.volume_total == 0:
            return 0.0
        return (self.volume_total - self.volume_remain) / self.volume_total * 100


@dataclass(frozen=True)
class IndustryJob(ValueObject):
    """工业任务"""
    job_id: int
    character_id: int
    installer_id: int
    facility_id: int
    station_id: int
    activity_id: int
    blueprint_id: int
    blueprint_type_id: int
    blueprint_location_id: int
    output_location_id: int
    runs: int
    cost: Optional[float]
    licensed_runs: Optional[int]
    probability: Optional[float]
    product_type_id: Optional[int]
    status: str
    duration: int
    start_date: datetime
    end_date: datetime
    pause_date: Optional[datetime] = None
    completed_date: Optional[datetime] = None
    completed_character_id: Optional[int] = None
    successful_runs: Optional[int] = None
    
    @property
    def activity_type(self) -> IndustryActivityType:
        return IndustryActivityType(self.activity_id)
    
    @property
    def job_status(self) -> JobStatus:
        return JobStatus(self.status)
    
    @property
    def is_completed(self) -> bool:
        return self.status in ["ready", "delivered"]
    
    @property
    def progress_percentage(self) -> float:
        """任务进度百分比"""
        if self.status == "delivered":
            return 100.0
        
        now = datetime.utcnow()
        if now >= self.end_date:
            return 100.0
        
        if now <= self.start_date:
            return 0.0
        
        total_duration = (self.end_date - self.start_date).total_seconds()
        elapsed_duration = (now - self.start_date).total_seconds()
        
        return min(100.0, (elapsed_duration / total_duration) * 100)


@dataclass(frozen=True)
class SkillQueueItem(ValueObject):
    """技能训练队列项"""
    character_id: int
    skill_id: int
    queue_position: int
    finished_level: int
    trained_skill_level: int
    level_start_sp: int
    level_end_sp: int
    training_start_sp: int
    start_date: Optional[datetime] = None
    finish_date: Optional[datetime] = None
    
    @property
    def is_active(self) -> bool:
        """是否正在训练"""
        return self.queue_position == 0
    
    @property
    def progress_percentage(self) -> float:
        """训练进度百分比"""
        if not self.start_date or not self.finish_date:
            return 0.0
        
        now = datetime.utcnow()
        if now >= self.finish_date:
            return 100.0
        
        if now <= self.start_date:
            return 0.0
        
        total_duration = (self.finish_date - self.start_date).total_seconds()
        elapsed_duration = (now - self.start_date).total_seconds()
        
        return min(100.0, (elapsed_duration / total_duration) * 100)


@dataclass(frozen=True)
class CharacterLocation(ValueObject):
    """角色位置信息"""
    character_id: int
    solar_system_id: int
    solar_system_name: str
    station_id: Optional[int] = None
    station_name: Optional[str] = None
    structure_id: Optional[int] = None
    structure_name: Optional[str] = None
    ship_type_id: Optional[int] = None
    ship_name: Optional[str] = None
    last_updated: Optional[datetime] = None


@dataclass(frozen=True)
class CorporationInfo(ValueObject):
    """公司信息"""
    corporation_id: int
    name: str
    ticker: str
    member_count: int
    alliance_id: Optional[int] = None
    alliance_name: Optional[str] = None
    ceo_id: int = 0
    creator_id: int = 0
    date_founded: Optional[datetime] = None
    description: Optional[str] = None
    faction_id: Optional[int] = None
    home_station_id: Optional[int] = None
    shares: int = 0
    tax_rate: float = 0.0
    url: Optional[str] = None
    war_eligible: bool = True


@dataclass(frozen=True)
class AllianceInfo(ValueObject):
    """联盟信息"""
    alliance_id: int
    name: str
    ticker: str
    corporations_count: int
    executor_corporation_id: int
    creator_id: int
    creator_corporation_id: int
    date_founded: datetime
    faction_id: Optional[int] = None


@dataclass(frozen=True)
class UniverseType(ValueObject):
    """宇宙物品类型"""
    type_id: int
    name: str
    description: str
    group_id: int
    category_id: int
    published: bool = True
    mass: float = 0.0
    volume: float = 0.0
    capacity: float = 0.0
    portion_size: int = 1
    radius: float = 0.0
    icon_id: Optional[int] = None
    market_group_id: Optional[int] = None
    
    @property
    def icon_url(self) -> str:
        """物品图标URL"""
        return f"https://images.evetech.net/types/{self.type_id}/icon?size=64"


@dataclass(frozen=True)
class UniverseGroup(ValueObject):
    """宇宙物品组"""
    group_id: int
    name: str
    category_id: int
    published: bool = True
    types: List[int] = field(default_factory=list)


@dataclass(frozen=True)
class UniverseCategory(ValueObject):
    """宇宙物品分类"""
    category_id: int
    name: str
    published: bool = True
    groups: List[int] = field(default_factory=list)


@dataclass(frozen=True)
class SolarSystem(ValueObject):
    """太阳系信息"""
    system_id: int
    name: str
    constellation_id: int
    region_id: int
    security_status: float
    star_id: Optional[int] = None
    stations: List[int] = field(default_factory=list)
    planets: List[int] = field(default_factory=list)
    
    @property
    def security_class(self) -> str:
        """安全等级分类"""
        if self.security_status >= 0.5:
            return "highsec"
        elif self.security_status > 0.0:
            return "lowsec"
        else:
            return "nullsec"


@dataclass(frozen=True)
class Region(ValueObject):
    """星域信息"""
    region_id: int
    name: str
    description: Optional[str] = None
    constellations: List[int] = field(default_factory=list)
