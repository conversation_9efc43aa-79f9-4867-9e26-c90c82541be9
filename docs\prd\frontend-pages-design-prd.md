# 前端页面设计 - 产品需求文档 (PRD)

## 📋 页面架构概述

### 整体布局结构
```
┌─────────────────────────────────────────────────────────────┐
│                    顶部导航栏 (Header)                       │
├─────────────────┬───────────────────────────────────────────┤
│                 │                                           │
│   侧边导航栏     │            主内容区域                      │
│   (Sidebar)     │         (Main Content)                   │
│                 │                                           │
│                 │                                           │
├─────────────────┴───────────────────────────────────────────┤
│                    底部信息栏 (Footer)                       │
└─────────────────────────────────────────────────────────────┘
```

## 🎯 核心页面设计

### 1. 认证页面 (Authentication Pages)

#### 1.1 登录页面 (/login)
```typescript
interface LoginPageProps {
  onLogin: (credentials: LoginCredentials) => Promise<void>
  loading: boolean
  error?: string
}

// 页面布局
const LoginPage: React.FC = () => (
  <div className="min-h-screen bg-gradient-to-br from-blue-900 to-purple-900">
    <div className="flex items-center justify-center min-h-screen">
      <Card className="w-full max-w-md">
        <div className="text-center mb-8">
          <img src="/logo.svg" alt="EVE Assistant" className="h-12 mx-auto mb-4" />
          <h1 className="text-2xl font-bold">EVE Online 管理助手</h1>
          <p className="text-gray-600">使用EVE账户登录</p>
        </div>
        
        <Tabs defaultActiveKey="eve-sso">
          <TabPane tab="EVE SSO" key="eve-sso">
            <EVESSOLogin />
          </TabPane>
          <TabPane tab="账户登录" key="account">
            <AccountLogin />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  </div>
)
```

#### 1.2 EVE SSO登录组件
```typescript
const EVESSOLogin: React.FC = () => {
  const [scopes, setScopes] = useState<string[]>([
    'esi-characters.read_characters.v1',
    'esi-assets.read_assets.v1',
    'esi-skills.read_skills.v1'
  ])
  
  return (
    <div className="space-y-4">
      <div className="bg-blue-50 p-4 rounded-lg">
        <h3 className="font-semibold mb-2">权限说明</h3>
        <div className="space-y-2 text-sm">
          {scopes.map(scope => (
            <div key={scope} className="flex items-center">
              <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
              <span>{getScopeDescription(scope)}</span>
            </div>
          ))}
        </div>
      </div>
      
      <Button 
        type="primary" 
        size="large" 
        block
        icon={<LoginOutlined />}
        onClick={handleEVELogin}
      >
        使用EVE账户登录
      </Button>
    </div>
  )
}
```

### 2. 仪表板页面 (Dashboard)

#### 2.1 主仪表板 (/dashboard)
```typescript
const Dashboard: React.FC = () => {
  const { data: overview } = useCharacterOverview()
  const { data: notifications } = useNotifications()
  
  return (
    <div className="p-6 space-y-6">
      {/* 角色选择器 */}
      <CharacterSelector />
      
      {/* 快速统计卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} lg={6}>
          <StatCard
            title="总资产价值"
            value={overview?.totalAssetValue}
            format="currency"
            trend={overview?.assetValueTrend}
            icon={<WalletOutlined />}
          />
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <StatCard
            title="技能点"
            value={overview?.totalSkillPoints}
            format="number"
            icon={<TrophyOutlined />}
          />
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <StatCard
            title="活跃订单"
            value={overview?.activeOrders}
            format="number"
            icon={<ShoppingOutlined />}
          />
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <StatCard
            title="工业任务"
            value={overview?.industryJobs}
            format="number"
            icon={<ToolOutlined />}
          />
        </Col>
      </Row>
      
      {/* 主要内容区域 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={16}>
          <Card title="资产分布">
            <AssetDistributionChart data={overview?.assetDistribution} />
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card title="最新通知" extra={<Button type="link">查看全部</Button>}>
            <NotificationList notifications={notifications} />
          </Card>
        </Col>
      </Row>
      
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="技能训练队列">
            <SkillQueueWidget />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="市场活动">
            <MarketActivityWidget />
          </Card>
        </Col>
      </Row>
    </div>
  )
}
```

### 3. 角色管理页面 (Character Management)

#### 3.1 角色列表 (/characters)
```typescript
const CharacterManagement: React.FC = () => {
  const { data: characters } = useCharacters()
  const [selectedCharacter, setSelectedCharacter] = useState<Character | null>(null)
  
  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">角色管理</h1>
        <Button type="primary" icon={<PlusOutlined />}>
          绑定新角色
        </Button>
      </div>
      
      <Row gutter={[16, 16]}>
        {characters?.map(character => (
          <Col key={character.id} xs={24} sm={12} lg={8} xl={6}>
            <CharacterCard
              character={character}
              onClick={() => setSelectedCharacter(character)}
              actions={[
                <Button key="view" type="link">查看详情</Button>,
                <Button key="refresh" type="link">刷新数据</Button>
              ]}
            />
          </Col>
        ))}
      </Row>
      
      {/* 角色详情抽屉 */}
      <Drawer
        title="角色详情"
        placement="right"
        width={720}
        open={!!selectedCharacter}
        onClose={() => setSelectedCharacter(null)}
      >
        {selectedCharacter && (
          <CharacterDetails character={selectedCharacter} />
        )}
      </Drawer>
    </div>
  )
}
```

#### 3.2 角色详情页面 (/characters/:id)
```typescript
const CharacterDetails: React.FC<{ characterId: number }> = ({ characterId }) => {
  const { data: character } = useCharacter(characterId)
  const { data: skills } = useCharacterSkills(characterId)
  
  return (
    <div className="p-6">
      <div className="mb-6">
        <CharacterHeader character={character} />
      </div>
      
      <Tabs defaultActiveKey="overview">
        <TabPane tab="概览" key="overview">
          <CharacterOverview character={character} />
        </TabPane>
        <TabPane tab="技能" key="skills">
          <SkillManagement skills={skills} characterId={characterId} />
        </TabPane>
        <TabPane tab="资产" key="assets">
          <AssetManagement characterId={characterId} />
        </TabPane>
        <TabPane tab="钱包" key="wallet">
          <WalletManagement characterId={characterId} />
        </TabPane>
      </Tabs>
    </div>
  )
}
```

### 4. 资产管理页面 (Asset Management)

#### 4.1 资产概览 (/assets)
```typescript
const AssetManagement: React.FC = () => {
  const [filters, setFilters] = useState<AssetFilters>({})
  const [viewMode, setViewMode] = useState<'table' | 'cards'>('table')
  
  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">资产管理</h1>
        <Space>
          <Radio.Group value={viewMode} onChange={e => setViewMode(e.target.value)}>
            <Radio.Button value="table">表格视图</Radio.Button>
            <Radio.Button value="cards">卡片视图</Radio.Button>
          </Radio.Group>
          <Button icon={<ReloadOutlined />}>刷新数据</Button>
        </Space>
      </div>
      
      {/* 筛选器 */}
      <Card className="mb-6">
        <AssetFilters filters={filters} onChange={setFilters} />
      </Card>
      
      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col span={6}>
          <Statistic title="总资产价值" value={1234567890} prefix="ISK " />
        </Col>
        <Col span={6}>
          <Statistic title="物品数量" value={15234} />
        </Col>
        <Col span={6}>
          <Statistic title="存储位置" value={42} />
        </Col>
        <Col span={6}>
          <Statistic title="最后更新" value="2分钟前" />
        </Col>
      </Row>
      
      {/* 资产列表 */}
      {viewMode === 'table' ? (
        <AssetTable filters={filters} />
      ) : (
        <AssetCards filters={filters} />
      )}
    </div>
  )
}
```

### 5. 市场交易页面 (Market Trading)

#### 5.1 市场概览 (/market)
```typescript
const MarketTrading: React.FC = () => {
  const [selectedRegion, setSelectedRegion] = useState(10000002) // The Forge
  
  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">市场交易</h1>
        <Space>
          <Select
            value={selectedRegion}
            onChange={setSelectedRegion}
            style={{ width: 200 }}
            placeholder="选择区域"
          >
            <Option value={10000002}>The Forge</Option>
            <Option value={10000043}>Domain</Option>
            <Option value={10000032}>Sinq Laison</Option>
          </Select>
          <Button type="primary">新建订单</Button>
        </Space>
      </div>
      
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={16}>
          <Card title="价格走势">
            <PriceChart regionId={selectedRegion} />
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card title="热门商品">
            <PopularItems regionId={selectedRegion} />
          </Card>
        </Col>
      </Row>
      
      <Row gutter={[16, 16]} className="mt-4">
        <Col xs={24} lg={12}>
          <Card title="我的订单">
            <MyOrders />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="套利机会">
            <ArbitrageOpportunities />
          </Card>
        </Col>
      </Row>
    </div>
  )
}
```

### 6. 工业生产页面 (Industry Production)

#### 6.1 生产概览 (/industry)
```typescript
const IndustryProduction: React.FC = () => {
  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">工业生产</h1>
        <Button type="primary" icon={<PlusOutlined />}>
          新建生产任务
        </Button>
      </div>
      
      {/* 生产线状态 */}
      <Card title="生产线状态" className="mb-6">
        <ProductionLineStatus />
      </Card>
      
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={16}>
          <Card title="活跃任务">
            <ActiveJobs />
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card title="蓝图库">
            <BlueprintLibrary />
          </Card>
        </Col>
      </Row>
    </div>
  )
}
```

## 🎨 组件设计规范

### 1. 通用组件

#### 1.1 角色卡片组件
```typescript
interface CharacterCardProps {
  character: Character
  onClick?: () => void
  actions?: React.ReactNode[]
  size?: 'small' | 'default' | 'large'
}

const CharacterCard: React.FC<CharacterCardProps> = ({
  character,
  onClick,
  actions,
  size = 'default'
}) => (
  <Card
    hoverable
    onClick={onClick}
    actions={actions}
    cover={
      <div className="relative">
        <img
          src={`https://images.evetech.net/characters/${character.id}/portrait?size=256`}
          alt={character.name}
          className="w-full h-48 object-cover"
        />
        <div className="absolute top-2 right-2">
          <Tag color={character.isOnline ? 'green' : 'default'}>
            {character.isOnline ? '在线' : '离线'}
          </Tag>
        </div>
      </div>
    }
  >
    <Card.Meta
      title={character.name}
      description={
        <div>
          <div>{character.corporation}</div>
          <div className="text-xs text-gray-500">
            SP: {formatNumber(character.totalSkillPoints)}
          </div>
        </div>
      }
    />
  </Card>
)
```

#### 1.2 统计卡片组件
```typescript
interface StatCardProps {
  title: string
  value: number | string
  format?: 'number' | 'currency' | 'percentage'
  trend?: number
  icon?: React.ReactNode
  loading?: boolean
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  format = 'number',
  trend,
  icon,
  loading
}) => (
  <Card loading={loading}>
    <div className="flex items-center justify-between">
      <div>
        <div className="text-sm text-gray-500 mb-1">{title}</div>
        <div className="text-2xl font-bold">
          {formatValue(value, format)}
        </div>
        {trend !== undefined && (
          <div className={`text-sm ${trend >= 0 ? 'text-green-500' : 'text-red-500'}`}>
            {trend >= 0 ? '↑' : '↓'} {Math.abs(trend)}%
          </div>
        )}
      </div>
      {icon && (
        <div className="text-3xl text-gray-400">
          {icon}
        </div>
      )}
    </div>
  </Card>
)
```

### 2. 数据展示组件

#### 2.1 资产表格组件
```typescript
const AssetTable: React.FC<{ filters: AssetFilters }> = ({ filters }) => {
  const { data, loading } = useAssets(filters)
  
  const columns = [
    {
      title: '物品',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: Asset) => (
        <div className="flex items-center">
          <img
            src={`https://images.evetech.net/types/${record.typeId}/icon?size=32`}
            alt={name}
            className="w-8 h-8 mr-2"
          />
          <div>
            <div className="font-medium">{name}</div>
            <div className="text-xs text-gray-500">{record.group}</div>
          </div>
        </div>
      )
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      render: (quantity: number) => formatNumber(quantity)
    },
    {
      title: '单价',
      dataIndex: 'price',
      key: 'price',
      render: (price: number) => formatCurrency(price)
    },
    {
      title: '总价值',
      dataIndex: 'totalValue',
      key: 'totalValue',
      render: (value: number) => formatCurrency(value)
    },
    {
      title: '位置',
      dataIndex: 'location',
      key: 'location'
    }
  ]
  
  return (
    <Table
      columns={columns}
      dataSource={data}
      loading={loading}
      rowKey="id"
      pagination={{
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) => 
          `${range[0]}-${range[1]} of ${total} items`
      }}
    />
  )
}
```

## 📱 响应式适配

### 移动端适配策略
```typescript
// 响应式布局Hook
const useResponsiveLayout = () => {
  const { breakpoint } = useResponsive()
  
  return {
    isMobile: breakpoint === 'xs' || breakpoint === 'sm',
    sidebarCollapsed: breakpoint === 'xs' || breakpoint === 'sm',
    cardSpan: {
      xs: 24,
      sm: 12,
      md: 8,
      lg: 6,
      xl: 4
    },
    tableScroll: {
      x: breakpoint === 'xs' ? 800 : 'max-content'
    }
  }
}
```

## 🔧 开发规范

### 1. 文件命名规范
```
components/
├── ui/                 # 基础UI组件 (kebab-case)
│   ├── button.tsx
│   ├── input.tsx
│   └── modal.tsx
├── business/           # 业务组件 (PascalCase)
│   ├── CharacterCard.tsx
│   ├── AssetTable.tsx
│   └── SkillQueue.tsx
└── layout/            # 布局组件
    ├── Header.tsx
    ├── Sidebar.tsx
    └── Footer.tsx
```

### 2. 代码组织规范
```typescript
// 组件文件结构
import React from 'react'
import { Card, Button } from 'antd'
import { useQuery } from '@tanstack/react-query'

// 类型定义
interface ComponentProps {
  // props定义
}

// 组件实现
const Component: React.FC<ComponentProps> = (props) => {
  // hooks
  // 状态管理
  // 事件处理
  // 渲染逻辑
  
  return (
    // JSX
  )
}

// 默认导出
export default Component

// 命名导出（如果需要）
export { Component }
export type { ComponentProps }
```

---

**文档版本**: v1.0  
**最后更新**: 2025-01-09  
**负责人**: 前端设计团队
