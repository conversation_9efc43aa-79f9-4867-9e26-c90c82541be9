# 测试体系框架知识库

## 测试体系架构

### 1. 测试金字塔

```
    /\
   /  \     E2E Tests (端到端测试)
  /____\    Integration Tests (集成测试)
 /______\   Unit Tests (单元测试)
```

#### 测试比例建议
- 单元测试：70%
- 集成测试：20%
- 端到端测试：10%

### 2. 测试分类

#### 2.1 单元测试 (Unit Tests)
**目标**：测试单个函数、类或模块的功能
**工具**：pytest, unittest
**覆盖范围**：
- 业务逻辑函数
- 数据模型验证
- 工具函数
- 异常处理

#### 2.2 集成测试 (Integration Tests)
**目标**：测试模块间的交互
**工具**：pytest, testcontainers
**覆盖范围**：
- API端点测试
- 数据库操作
- 外部服务集成
- 认证授权流程

#### 2.3 端到端测试 (E2E Tests)
**目标**：测试完整的用户流程
**工具**：Playwright, Selenium
**覆盖范围**：
- 用户登录流程
- 关键业务流程
- 跨系统交互

## 测试环境配置

### 1. 测试依赖

```toml
# pyproject.toml
[project.optional-dependencies]
test = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
    "httpx>=0.24.0",
    "testcontainers>=3.7.0",
    "factory-boy>=3.2.0",
    "freezegun>=1.2.0",
]
```

### 2. pytest配置

```ini
# pytest.ini
[tool:pytest]
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*
addopts = 
    --strict-markers
    --strict-config
    --cov=src
    --cov-report=html
    --cov-report=term-missing
    --cov-fail-under=80
markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    slow: Slow running tests
    external: Tests requiring external services
```

### 3. 测试目录结构

```
tests/
├── unit/                   # 单元测试
│   ├── test_auth.py
│   ├── test_models.py
│   └── test_utils.py
├── integration/            # 集成测试
│   ├── test_api_auth.py
│   ├── test_database.py
│   └── test_external_apis.py
├── e2e/                    # 端到端测试
│   ├── test_login_flow.py
│   └── test_user_journey.py
├── fixtures/               # 测试数据
│   ├── __init__.py
│   └── sample_data.py
└── conftest.py            # pytest配置
```

## 测试实现示例

### 1. 单元测试示例

```python
# tests/unit/test_auth.py
import pytest
from unittest.mock import Mock, patch
from src.application.services.auth import AuthenticationService

class TestAuthenticationService:
    
    @pytest.fixture
    def auth_service(self):
        """创建认证服务实例"""
        return AuthenticationService()
    
    def test_validate_eve_scopes_valid(self, auth_service):
        """测试有效的EVE权限验证"""
        scopes = ["esi-characters.read_characters.v1"]
        result = auth_service.validate_eve_scopes(scopes)
        assert result is True
    
    def test_validate_eve_scopes_invalid(self, auth_service):
        """测试无效的EVE权限验证"""
        scopes = ["invalid-scope"]
        result = auth_service.validate_eve_scopes(scopes)
        assert result is False
    
    @patch('src.application.services.auth.requests.post')
    def test_initiate_eve_login_success(self, mock_post, auth_service):
        """测试EVE登录发起成功"""
        mock_response = Mock()
        mock_response.json.return_value = {"access_token": "test_token"}
        mock_post.return_value = mock_response
        
        result = auth_service.initiate_eve_login(["esi-characters.read_characters.v1"])
        
        assert "login_url" in result
        assert "state" in result
        mock_post.assert_called_once()
```

### 2. 集成测试示例

```python
# tests/integration/test_api_auth.py
import pytest
from httpx import AsyncClient
from src.presentation.api.main import app

@pytest.mark.asyncio
class TestAuthAPI:
    
    async def test_eve_login_endpoint(self):
        """测试EVE登录API端点"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.post(
                "/auth/login",
                json={"scopes": ["esi-characters.read_characters.v1"]}
            )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "login_url" in data["data"]
        assert "state" in data["data"]
    
    async def test_eve_login_invalid_scopes(self):
        """测试无效权限的EVE登录"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.post(
                "/auth/login",
                json={"scopes": ["invalid-scope"]}
            )
        
        assert response.status_code == 400
        data = response.json()
        assert data["success"] is False
```

### 3. 端到端测试示例

```python
# tests/e2e/test_login_flow.py
import pytest
from playwright.async_api import async_playwright

@pytest.mark.e2e
@pytest.mark.asyncio
class TestLoginFlow:
    
    async def test_eve_login_flow(self):
        """测试完整的EVE登录流程"""
        async with async_playwright() as p:
            browser = await p.chromium.launch()
            page = await browser.new_page()
            
            # 访问登录页面
            await page.goto("http://localhost:3000")
            
            # 点击EVE登录按钮
            await page.click('[data-testid="eve-login-button"]')
            
            # 验证跳转到EVE登录页面
            await page.wait_for_url("**/oauth/authorize**")
            
            # 验证URL包含必要参数
            url = page.url
            assert "client_id" in url
            assert "scope" in url
            assert "state" in url
            
            await browser.close()
```

## 测试数据管理

### 1. 工厂模式

```python
# tests/fixtures/factories.py
import factory
from src.domain.entities.user import User
from src.domain.entities.character import Character

class UserFactory(factory.Factory):
    class Meta:
        model = User
    
    id = factory.Sequence(lambda n: f"user_{n}")
    email = factory.LazyAttribute(lambda obj: f"{obj.id}@example.com")
    username = factory.LazyAttribute(lambda obj: obj.id)
    is_verified = True

class CharacterFactory(factory.Factory):
    class Meta:
        model = Character
    
    character_id = factory.Sequence(lambda n: 1000000 + n)
    character_name = factory.Faker('name')
    corporation_id = 1000001
    alliance_id = None
```

### 2. 测试数据库

```python
# tests/conftest.py
import pytest
import asyncio
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from src.infrastructure.persistence.database import Base

@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture(scope="session")
async def test_engine():
    """创建测试数据库引擎"""
    engine = create_async_engine("sqlite+aiosqlite:///:memory:")
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    yield engine
    await engine.dispose()

@pytest.fixture
async def db_session(test_engine):
    """创建数据库会话"""
    async with AsyncSession(test_engine) as session:
        yield session
        await session.rollback()
```

## 测试执行策略

### 1. 本地开发测试

```bash
# 运行所有测试
pytest

# 运行特定类型的测试
pytest -m unit
pytest -m integration
pytest -m e2e

# 运行特定文件
pytest tests/unit/test_auth.py

# 运行特定测试
pytest tests/unit/test_auth.py::TestAuthenticationService::test_validate_eve_scopes_valid

# 生成覆盖率报告
pytest --cov=src --cov-report=html
```

### 2. CI/CD集成

```yaml
# .github/workflows/test.yml
name: Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        pip install -e .[test]
    
    - name: Run unit tests
      run: pytest -m unit --cov=src
    
    - name: Run integration tests
      run: pytest -m integration
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
```

### 3. 测试报告

```python
# scripts/test_report.py
#!/usr/bin/env python3
"""生成测试报告"""
import subprocess
import json
from datetime import datetime

def run_tests():
    """运行测试并生成报告"""
    result = subprocess.run([
        "pytest", "--json-report", "--json-report-file=test_report.json"
    ], capture_output=True, text=True)
    
    with open("test_report.json") as f:
        report = json.load(f)
    
    print(f"测试总数: {report['summary']['total']}")
    print(f"通过: {report['summary']['passed']}")
    print(f"失败: {report['summary']['failed']}")
    print(f"跳过: {report['summary']['skipped']}")
    print(f"覆盖率: {report.get('coverage', 'N/A')}")
    
    return result.returncode == 0

if __name__ == "__main__":
    success = run_tests()
    exit(0 if success else 1)
```

## 测试最佳实践

### 1. 测试命名规范

- 测试文件：`test_*.py` 或 `*_test.py`
- 测试类：`Test*`
- 测试方法：`test_*`
- 描述性命名：`test_should_return_error_when_invalid_scope`

### 2. 测试结构 (AAA模式)

```python
def test_validate_eve_scopes_invalid():
    # Arrange (准备)
    auth_service = AuthenticationService()
    invalid_scopes = ["invalid-scope"]
    
    # Act (执行)
    result = auth_service.validate_eve_scopes(invalid_scopes)
    
    # Assert (断言)
    assert result is False
```

### 3. Mock使用原则

- 只mock外部依赖
- 避免过度mock
- 使用真实数据进行集成测试
- 验证mock的调用

### 4. 测试隔离

- 每个测试独立运行
- 清理测试数据
- 避免测试间的依赖
- 使用事务回滚

---

*最后更新：2025-08-10*
*相关问题：建立完整的测试体系框架*
