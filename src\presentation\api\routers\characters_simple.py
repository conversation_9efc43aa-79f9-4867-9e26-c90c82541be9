"""
简化版角色管理路由 - 用于基础功能恢复
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Dict, Any, List, Optional

from ....application.character_simple import SimpleCharacterService
from ....infrastructure.config.logging import get_logger
from ..dependencies import get_character_service, get_current_user

logger = get_logger(__name__)

router = APIRouter()


@router.get("/{character_id}")
async def get_character(
    character_id: int,
    character_service: SimpleCharacterService = Depends(get_character_service),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取角色详细信息"""
    try:
        character_data = await character_service.get_character_by_id(character_id)
        
        if not character_data:
            raise HTTPException(status_code=404, detail="Character not found")
        
        return character_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("获取角色信息失败", character_id=character_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get character")


@router.get("/")
async def get_user_characters(
    current_user: Dict[str, Any] = Depends(get_current_user),
    character_service: SimpleCharacterService = Depends(get_character_service)
):
    """获取用户的所有角色"""
    try:
        user_id = current_user.get("user_id", 1)  # 默认用户ID
        result = await character_service.get_user_characters(user_id)
        return result
        
    except Exception as e:
        logger.error("获取用户角色列表失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get user characters")


@router.post("/{character_id}/sync")
async def sync_character(
    character_id: int,
    character_service: SimpleCharacterService = Depends(get_character_service),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """同步角色数据"""
    try:
        result = await character_service.sync_character(character_id)
        return result
        
    except Exception as e:
        logger.error("同步角色数据失败", character_id=character_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to sync character")


@router.get("/{character_id}/skills")
async def get_character_skills(
    character_id: int,
    character_service: SimpleCharacterService = Depends(get_character_service),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取角色技能"""
    try:
        result = await character_service.get_character_skills(character_id)
        return result
        
    except Exception as e:
        logger.error("获取角色技能失败", character_id=character_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get character skills")


@router.get("/search")
async def search_characters(
    query: str = Query(..., min_length=2, description="搜索关键词"),
    limit: int = Query(10, ge=1, le=50, description="结果数量限制"),
    character_service: SimpleCharacterService = Depends(get_character_service),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """搜索角色"""
    try:
        result = await character_service.search_characters(query, limit)
        return result
        
    except Exception as e:
        logger.error("搜索角色失败", query=query, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to search characters")


@router.get("/status")
async def character_service_status():
    """角色服务状态"""
    return {
        "status": "healthy",
        "service": "SimpleCharacterService",
        "message": "简化版角色服务运行中",
        "features": [
            "角色信息查看",
            "角色列表",
            "角色搜索",
            "技能查看",
            "数据同步（模拟）"
        ]
    }
