#!/usr/bin/env python3
"""
测试前端服务器
"""
import requests
import time

def test_frontend():
    print("🌐 测试前端服务器")
    print("=" * 40)
    
    # 等待前端启动
    print("等待前端服务器启动...")
    time.sleep(5)
    
    # 测试前端端口
    frontend_url = "http://localhost:3000"
    
    try:
        response = requests.get(frontend_url, timeout=5)
        print(f"前端服务器状态: HTTP {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 前端服务器正常运行")
            print(f"内容长度: {len(response.text)} 字符")
            
            # 检查是否包含React应用
            if "root" in response.text and "script" in response.text:
                print("✅ React应用正常加载")
            else:
                print("⚠️  可能不是React应用")
                
        else:
            print(f"❌ 前端服务器异常: {response.text[:200]}")
            
    except Exception as e:
        print(f"❌ 前端服务器连接失败: {e}")
        
        # 尝试检查是否有其他端口
        for port in [3000, 3001, 5173, 5174]:
            try:
                test_url = f"http://localhost:{port}"
                response = requests.get(test_url, timeout=2)
                print(f"✅ 发现服务在端口 {port}: HTTP {response.status_code}")
                break
            except:
                continue
        else:
            print("❌ 没有发现前端服务器")

if __name__ == "__main__":
    test_frontend()
