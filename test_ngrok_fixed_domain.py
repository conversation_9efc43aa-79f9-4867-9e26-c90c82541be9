#!/usr/bin/env python3
"""
直接测试ngrok固定域名功能
"""
import subprocess
import time
import urllib.request
import json
import os
from dotenv import load_dotenv

def test_ngrok_fixed_domain():
    """测试ngrok固定域名"""
    print("🔧 直接测试ngrok固定域名")
    print("=" * 50)
    
    # 加载环境变量
    load_dotenv()
    fixed_domain = os.getenv('NGROK_FIXED_DOMAIN')
    
    print(f"📋 固定域名: {fixed_domain}")
    
    if not fixed_domain:
        print("❌ 未配置固定域名")
        return False
    
    try:
        # 构建ngrok命令
        ngrok_cmd = ['ngrok', 'http', '8000', '--domain', fixed_domain]
        print(f"🚀 启动命令: {' '.join(ngrok_cmd)}")
        
        # 启动ngrok
        process = subprocess.Popen(
            ngrok_cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print("⏳ 等待ngrok启动...")
        time.sleep(10)
        
        # 检查ngrok API
        try:
            response = urllib.request.urlopen('http://localhost:4040/api/tunnels', timeout=5)
            data = json.loads(response.read().decode())
            
            if data['tunnels']:
                tunnel = data['tunnels'][0]
                public_url = tunnel['public_url']
                
                print(f"✅ ngrok隧道启动成功")
                print(f"📋 公网URL: {public_url}")
                
                # 验证固定域名
                if fixed_domain in public_url:
                    print(f"🎯 固定域名配置成功！")
                    print(f"   期望: {fixed_domain}")
                    print(f"   实际: {public_url}")
                    
                    # 测试隧道连通性
                    try:
                        test_response = urllib.request.urlopen(f"{public_url}/health", timeout=10)
                        if test_response.getcode() == 200:
                            print("🌐 隧道连通性测试通过")
                        else:
                            print(f"⚠️  隧道连通性测试失败: {test_response.getcode()}")
                    except Exception as e:
                        print(f"⚠️  隧道连通性测试失败: {e}")
                    
                    result = True
                else:
                    print(f"❌ 固定域名未生效")
                    print(f"   期望: {fixed_domain}")
                    print(f"   实际: {public_url}")
                    print(f"💡 可能的原因:")
                    print(f"   1. 域名未在ngrok仪表板中创建")
                    print(f"   2. authtoken权限不足")
                    print(f"   3. 域名已被其他人使用")
                    result = False
            else:
                print("❌ 未找到ngrok隧道")
                result = False
                
        except Exception as e:
            print(f"❌ 检查ngrok API失败: {e}")
            result = False
        
        # 停止ngrok进程
        process.terminate()
        try:
            process.wait(timeout=5)
            print("✅ ngrok进程已停止")
        except subprocess.TimeoutExpired:
            process.kill()
            print("⚠️  强制终止ngrok进程")
        
        return result
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    if test_ngrok_fixed_domain():
        print("\n🎉 固定域名测试成功！")
        print("📝 现在可以在production-start.py中使用固定域名")
        return 0
    else:
        print("\n❌ 固定域名测试失败")
        print("💡 请检查:")
        print("   1. ngrok仪表板中是否已创建域名")
        print("   2. authtoken是否正确")
        print("   3. 域名是否可用")
        return 1

if __name__ == "__main__":
    exit(main())
