import {
  PlusOutlined,
  ReloadOutlined,
  ShoppingOutlined,
  ToolOutlined,
  TrophyOutlined,
  UserOutlined,
  WalletOutlined
} from '@ant-design/icons'
import { Button, Card, Col, Empty, Row, Statistic } from 'antd'
import { motion } from 'framer-motion'

import { ContentLoading } from '@/components/ui/LoadingScreen'
import { useAuth } from '@/hooks/useAuth'

export default function DashboardPage() {
  const { user } = useAuth()

  // 模拟数据加载状态
  const isLoading = false

  if (isLoading) {
    return <ContentLoading tip="加载仪表板数据..." />
  }

  // 如果用户没有绑定角色
  if (!user?.characterCount || user.characterCount === 0) {
    return (
      <div className="p-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="text-center">
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    欢迎使用 EVE Assistant！
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    您还没有绑定任何EVE角色。请先绑定角色以开始使用管理功能。
                  </p>
                  <Button
                    type="primary"
                    size="large"
                    icon={<PlusOutlined />}
                    onClick={() => window.location.href = '/characters'}
                  >
                    绑定EVE角色
                  </Button>
                </div>
              }
            />
          </Card>
        </motion.div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* 欢迎信息 */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white">
          <h1 className="text-2xl font-bold mb-2">
            欢迎回来, {user?.username}！
          </h1>
          <p className="text-blue-100">
            您有 {user?.characterCount} 个EVE角色正在管理中
          </p>
        </div>
      </motion.div>

      {/* 统计卡片 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="总资产价值"
                value={0}
                precision={0}
                valueStyle={{ color: '#3f8600' }}
                prefix={<WalletOutlined />}
                suffix="ISK"
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="技能点"
                value={0}
                precision={0}
                valueStyle={{ color: '#1890ff' }}
                prefix={<TrophyOutlined />}
                suffix="SP"
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="活跃订单"
                value={0}
                valueStyle={{ color: '#722ed1' }}
                prefix={<ShoppingOutlined />}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="工业任务"
                value={0}
                valueStyle={{ color: '#fa8c16' }}
                prefix={<ToolOutlined />}
              />
            </Card>
          </Col>
        </Row>
      </motion.div>

      {/* 主要内容区域 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={16}>
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card
              title="资产分布"
              extra={
                <Button
                  type="text"
                  icon={<ReloadOutlined />}
                  size="small"
                >
                  刷新
                </Button>
              }
            >
              <div className="h-64 flex items-center justify-center">
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description="暂无数据"
                />
              </div>
            </Card>
          </motion.div>
        </Col>

        <Col xs={24} lg={8}>
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="space-y-4"
          >
            <Card title="最新通知" size="small">
              <div className="space-y-2">
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  暂无新通知
                </div>
              </div>
            </Card>

            <Card title="技能训练队列" size="small">
              <div className="text-sm text-gray-600 dark:text-gray-400">
                暂无训练中的技能
              </div>
            </Card>

            <Card title="市场活动" size="small">
              <div className="text-sm text-gray-600 dark:text-gray-400">
                暂无市场订单
              </div>
            </Card>
          </motion.div>
        </Col>
      </Row>

      {/* 快速操作 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        <Card title="快速操作">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button
              type="dashed"
              className="h-20 flex flex-col items-center justify-center"
              onClick={() => window.location.href = '/characters'}
            >
              <UserOutlined className="text-xl mb-2" />
              <span className="text-sm">管理角色</span>
            </Button>
            <Button
              type="dashed"
              className="h-20 flex flex-col items-center justify-center"
              onClick={() => window.location.href = '/assets'}
            >
              <WalletOutlined className="text-xl mb-2" />
              <span className="text-sm">查看资产</span>
            </Button>
            <Button
              type="dashed"
              className="h-20 flex flex-col items-center justify-center"
              onClick={() => window.location.href = '/market'}
            >
              <ShoppingOutlined className="text-xl mb-2" />
              <span className="text-sm">市场交易</span>
            </Button>
            <Button
              type="dashed"
              className="h-20 flex flex-col items-center justify-center"
              onClick={() => window.location.href = '/industry'}
            >
              <ToolOutlined className="text-xl mb-2" />
              <span className="text-sm">工业生产</span>
            </Button>
          </div>
        </Card>
      </motion.div>
    </div>
  )
}
