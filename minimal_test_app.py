#!/usr/bin/env python3
"""
最简单的测试应用
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from fastapi import FastAPI

# 创建最简单的FastAPI应用
app = FastAPI(title="MythEVE Test", version="1.0.0")

@app.get("/")
async def root():
    return {"message": "Hello World"}

@app.get("/health")
async def health():
    return {"status": "ok"}

if __name__ == "__main__":
    import uvicorn
    print("🚀 启动最简单的测试应用...")
    uvicorn.run(app, host="0.0.0.0", port=8000)
