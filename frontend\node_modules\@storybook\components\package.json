{"name": "@storybook/components", "version": "7.6.20", "description": "Core Storybook Components", "keywords": ["storybook"], "homepage": "https://github.com/storybookjs/storybook/tree/next/code/ui/components", "bugs": {"url": "https://github.com/storybookjs/storybook/issues"}, "repository": {"type": "git", "url": "https://github.com/storybookjs/storybook.git", "directory": "code/ui/components"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "license": "MIT", "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "require": "./dist/index.js", "import": "./dist/index.mjs"}, "./html": {"types": "./dist/html.d.ts", "require": "./dist/html.js", "import": "./dist/html.mjs"}, "./package.json": "./package.json"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "typesVersions": {"*": {"*": ["dist/index.d.ts"], "html": ["dist/html.d.ts"]}}, "files": ["dist/**/*", "README.md", "*.js", "*.d.ts", "!src/**/*"], "scripts": {"check": "node --loader ../../../scripts/node_modules/esbuild-register/loader.js -r ../../../scripts/node_modules/esbuild-register/register.js ../../../scripts/prepare/check.ts", "prep": "node --loader ../../../scripts/node_modules/esbuild-register/loader.js -r ../../../scripts/node_modules/esbuild-register/register.js ../../../scripts/prepare/bundle.ts"}, "dependencies": {"@radix-ui/react-select": "^1.2.2", "@radix-ui/react-toolbar": "^1.0.4", "@storybook/client-logger": "7.6.20", "@storybook/csf": "^0.1.2", "@storybook/global": "^5.0.0", "@storybook/theming": "7.6.20", "@storybook/types": "7.6.20", "memoizerific": "^1.11.3", "use-resize-observer": "^9.1.0", "util-deprecate": "^1.0.2"}, "devDependencies": {"@popperjs/core": "^2.6.0", "@radix-ui/react-scroll-area": "^1.0.5", "@storybook/icons": "^1.1.6", "@types/react-syntax-highlighter": "11.0.5", "@types/util-deprecate": "^1.0.0", "css": "^3.0.0", "polished": "^4.2.2", "prettier": "^2.8.0", "react-popper-tooltip": "^4.4.2", "react-syntax-highlighter": "^15.4.5", "react-textarea-autosize": "^8.3.0", "ts-dedent": "^2.0.0", "typescript": "~4.9.3"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0"}, "publishConfig": {"access": "public"}, "bundler": {"entries": ["./src/index.ts"], "platform": "neutral"}, "gitHead": "e6a7fd8a655c69780bc20b9749c2699e44beae17"}