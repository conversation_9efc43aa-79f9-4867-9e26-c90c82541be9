"""
FastAPI依赖注入
"""
from fastapi import Depends, HTTPException, Request, <PERSON><PERSON>
from sqlalchemy.orm import Session
from typing import Dict, Any, Optional, List
import jwt

from ...infrastructure.config import settings
from ...infrastructure.config.logging import get_logger
from ...infrastructure.persistence.database import get_db
from ...infrastructure.esi import ESIClient, ESIService, EVESSOClient
from ...application.character import CharacterApplicationService
from ...application.sync import DataSyncService
from ...application.auth import AuthenticationService
from ...domain.character.services import SkillTrainingService, CharacterAnalysisService

logger = get_logger(__name__)


# 数据库依赖
def get_database_session() -> Session:
    """获取数据库会话"""
    db = next(get_db())
    try:
        yield db
    finally:
        db.close()


# ESI客户端依赖（简化版）
def get_esi_client() -> ESIClient:
    """获取ESI客户端"""
    return ESIClient()


def get_esi_service() -> ESIService:
    """获取ESI服务"""
    client = ESIClient()
    return ESIService(client)


def get_sso_client() -> EVESSOClient:
    """获取SSO客户端"""
    return EVESSOClient()


# 领域服务依赖
def get_skill_training_service() -> SkillTrainingService:
    """获取技能训练服务"""
    return SkillTrainingService()


def get_character_analysis_service() -> CharacterAnalysisService:
    """获取角色分析服务"""
    return CharacterAnalysisService()


# 应用服务依赖
def get_character_service(
    db: Session = Depends(get_database_session)
) -> CharacterApplicationService:
    """获取角色应用服务"""
    # 延迟导入避免循环依赖
    from ...infrastructure.persistence.repositories.character import CharacterRepositoryImpl
    from ...infrastructure.esi import ESIClient, ESIService

    character_repository = CharacterRepositoryImpl(db)

    # 创建ESI服务（简化版）
    esi_client = ESIClient()
    esi_service = ESIService(esi_client)

    # 创建领域服务
    skill_training_service = SkillTrainingService()
    character_analysis_service = CharacterAnalysisService()

    return CharacterApplicationService(
        character_repository=character_repository,
        esi_service=esi_service,
        skill_training_service=skill_training_service,
        character_analysis_service=character_analysis_service
    )


def get_sync_service(
    db: Session = Depends(get_database_session)
) -> DataSyncService:
    """获取数据同步服务"""
    from ...infrastructure.persistence.repositories.character import CharacterRepositoryImpl
    from ...infrastructure.esi import ESIClient, ESIService

    character_repository = CharacterRepositoryImpl(db)

    # 创建ESI服务
    esi_client = ESIClient()
    esi_service = ESIService(esi_client)

    # 这里需要实现token_repository
    token_repository = None  # TODO: 实现TokenRepository

    return DataSyncService(
        character_repository=character_repository,
        esi_service=esi_service,
        token_repository=token_repository
    )


def get_auth_service(
    db: Session = Depends(get_database_session)
) -> AuthenticationService:
    """获取认证服务"""
    from ...infrastructure.persistence.repositories.character import CharacterRepositoryImpl

    # 创建SSO客户端
    sso_client = EVESSOClient()

    # 这里需要实现相关的仓储
    user_repository = None  # TODO: 实现UserRepository
    token_repository = None  # TODO: 实现TokenRepository
    character_repository = CharacterRepositoryImpl(db)

    return AuthenticationService(
        user_repository=user_repository,
        token_repository=token_repository,
        character_repository=character_repository,
        sso_client=sso_client
    )


# 认证依赖
async def get_current_user(
    request: Request,
    session_token: Optional[str] = Cookie(None)
) -> Dict[str, Any]:
    """获取当前用户"""
    if not session_token:
        raise HTTPException(status_code=401, detail="Not authenticated")
    
    try:
        # 验证JWT令牌
        payload = jwt.decode(
            session_token,
            settings.secret_key,
            algorithms=[settings.jwt_algorithm]
        )
        
        user_id = payload.get("user_id")
        character_id = payload.get("character_id")
        
        if not user_id or not character_id:
            raise HTTPException(status_code=401, detail="Invalid token")
        
        return {
            "user_id": user_id,
            "character_id": character_id
        }
        
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=401, detail="Token expired")
    except jwt.JWTError:
        raise HTTPException(status_code=401, detail="Invalid token")
    except Exception as e:
        logger.error("验证用户令牌失败", error=str(e))
        raise HTTPException(status_code=401, detail="Authentication failed")


async def get_optional_current_user(
    request: Request,
    session_token: Optional[str] = Cookie(None)
) -> Optional[Dict[str, Any]]:
    """获取当前用户（可选）"""
    if not session_token:
        return None
    
    try:
        return await get_current_user(request, session_token)
    except HTTPException:
        return None


# 权限检查依赖
def require_permission(required_scope: str):
    """要求特定权限的依赖工厂"""
    async def permission_checker(
        current_user: Dict[str, Any] = Depends(get_current_user),
        auth_service: AuthenticationService = Depends(get_auth_service)
    ):
        character_id = current_user["character_id"]
        
        has_permission = await auth_service.check_character_permission(
            character_id, required_scope
        )
        
        if not has_permission:
            raise HTTPException(
                status_code=403,
                detail=f"Missing required permission: {required_scope}"
            )
        
        return current_user
    
    return permission_checker


# 角色访问权限检查
async def check_character_access(
    character_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user),
    auth_service: AuthenticationService = Depends(get_auth_service)
) -> bool:
    """检查用户是否有访问指定角色的权限"""
    user_id = current_user["user_id"]
    user_characters = await auth_service.get_user_characters(user_id)
    
    # 检查角色是否属于当前用户
    character_ids = [char["character_id"] for char in user_characters]
    
    if character_id not in character_ids:
        raise HTTPException(
            status_code=403,
            detail="Access denied: Character does not belong to current user"
        )
    
    return True


# 管理员权限检查
async def require_admin(
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """要求管理员权限"""
    # 这里需要实现管理员权限检查逻辑
    # 临时实现：检查用户ID是否为1（假设ID为1的是管理员）
    if current_user["user_id"] != 1:
        raise HTTPException(
            status_code=403,
            detail="Admin privileges required"
        )
    
    return current_user


# 请求限流依赖
class RateLimiter:
    """简单的请求限流器"""
    
    def __init__(self, max_requests: int = 100, window_seconds: int = 60):
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests = {}
    
    async def __call__(self, request: Request):
        import time
        
        client_ip = request.client.host if request.client else "unknown"
        current_time = time.time()
        
        # 清理过期的请求记录
        if client_ip in self.requests:
            self.requests[client_ip] = [
                req_time for req_time in self.requests[client_ip]
                if current_time - req_time < self.window_seconds
            ]
        else:
            self.requests[client_ip] = []
        
        # 检查是否超过限制
        if len(self.requests[client_ip]) >= self.max_requests:
            raise HTTPException(
                status_code=429,
                detail="Too many requests"
            )
        
        # 记录当前请求
        self.requests[client_ip].append(current_time)


# 创建限流器实例
rate_limiter = RateLimiter(max_requests=100, window_seconds=60)
strict_rate_limiter = RateLimiter(max_requests=10, window_seconds=60)


# Scope权限相关依赖
async def get_current_user_scopes(
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> List[str]:
    """获取当前用户的ESI权限"""
    try:
        # 暂时返回配置中的所有权限
        # TODO: 从数据库或缓存中获取用户实际的权限
        scopes = settings.eve_sso_scopes.split()
        return scopes

    except Exception as e:
        logger.error("获取用户权限失败", error=str(e), character_id=current_user.get("character_id"))
        # 返回空权限列表，让上层处理
        return []


async def get_optional_user_scopes(
    current_user: Optional[Dict[str, Any]] = Depends(get_optional_current_user)
) -> List[str]:
    """获取当前用户的ESI权限（可选）"""
    if not current_user:
        return []

    try:
        # 这里可以调用get_current_user_scopes，但需要处理依赖
        # 简化实现：返回配置中的权限
        return settings.eve_sso_scopes.split()
    except Exception as e:
        logger.error("获取可选用户权限失败", error=str(e))
        return []


def require_scopes(*required_scopes: str):
    """要求特定权限的依赖工厂"""
    async def scopes_checker(
        user_scopes: List[str] = Depends(get_current_user_scopes)
    ):
        missing_scopes = []
        for scope in required_scopes:
            if scope not in user_scopes:
                missing_scopes.append(scope)

        if missing_scopes:
            raise HTTPException(
                status_code=403,
                detail=f"Missing required scopes: {', '.join(missing_scopes)}"
            )

        return user_scopes

    return scopes_checker


def require_any_scope(*required_scopes: str):
    """要求任意一个权限的依赖工厂"""
    async def any_scope_checker(
        user_scopes: List[str] = Depends(get_current_user_scopes)
    ):
        has_any_scope = any(scope in user_scopes for scope in required_scopes)

        if not has_any_scope:
            raise HTTPException(
                status_code=403,
                detail=f"Missing any of required scopes: {', '.join(required_scopes)}"
            )

        return user_scopes

    return any_scope_checker
