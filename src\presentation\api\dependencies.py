"""
FastAPI依赖注入
"""
from fastapi import Depends, HTTPException, Request, Cookie
from sqlalchemy.orm import Session
from typing import Dict, Any, Optional, List
import jwt

from ...infrastructure.config import settings
from ...infrastructure.config.logging import get_logger
from ...infrastructure.persistence.database import get_db
# 恢复EVE SSO相关导入
from ...infrastructure.esi import EVESSOClient
from ...application.auth import AuthenticationService

logger = get_logger(__name__)


# 数据库依赖
def get_database_session() -> Session:
    """获取数据库会话"""
    db = next(get_db())
    try:
        yield db
    finally:
        db.close()


# ESI客户端依赖（简化版）- 暂时返回None
def get_esi_client():
    """获取ESI客户端 - 简化版本"""
    return None

def get_esi_service():
    """获取ESI服务 - 简化版本"""
    return None


def get_sso_client() -> EVESSOClient:
    """获取SSO客户端"""
    return EVESSOClient()

# 领域服务依赖 - 简化版本
def get_skill_training_service():
    """获取技能训练服务 - 简化版本"""
    return None

def get_character_analysis_service():
    """获取角色分析服务 - 简化版本"""
    return None


# 暂时简化所有复杂的依赖注入
# TODO: 重新实现应用服务的依赖注入

def get_simple_service():
    """简化的服务获取 - 暂时返回None"""
    return None


# 暂时简化所有复杂的服务依赖注入
def get_sync_service():
    """简化的同步服务 - 暂时返回None"""
    return None

def get_auth_service() -> AuthenticationService:
    """获取认证服务"""
    sso_client = get_sso_client()
    return AuthenticationService(sso_client=sso_client)


# 简化的认证依赖 - 暂时返回模拟用户
async def get_current_user(
    request: Request,
    session_token: Optional[str] = Cookie(None)
) -> Dict[str, Any]:
    """获取当前用户 - 简化版本"""
    # 暂时返回模拟用户，避免复杂的JWT验证
    return {
        "user_id": 1,
        "character_id": 123456789,
        "character_name": "Test Character"
    }

async def get_optional_current_user(
    request: Request,
    session_token: Optional[str] = Cookie(None)
) -> Optional[Dict[str, Any]]:
    """获取当前用户（可选）- 简化版本"""
    try:
        return await get_current_user(request, session_token)
    except:
        return None


# 简化的权限检查 - 暂时允许所有访问
def require_permission(required_scope: str):
    """简化的权限检查 - 暂时允许所有访问"""
    async def permission_checker(
        current_user: Dict[str, Any] = Depends(get_current_user)
    ):
        # 暂时跳过权限检查
        return current_user
    return permission_checker

async def check_character_access(
    character_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> bool:
    """简化的角色访问检查 - 暂时允许所有访问"""
    return True


# 简化的管理员检查
async def require_admin(
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """简化的管理员检查 - 暂时允许所有用户"""
    return current_user

# 简化的限流器
async def rate_limiter(request: Request):
    """简化的限流器 - 暂时不限制"""
    pass


# 简化的权限相关依赖
async def get_current_user_scopes(
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> List[str]:
    """获取当前用户的ESI权限 - 简化版本"""
    try:
        # 返回基本的测试权限
        return [
            "publicData",
            "esi-location.read_location.v1",
            "esi-skills.read_skills.v1",
            "esi-assets.read_assets.v1",
            "esi-wallet.read_character_wallet.v1"
        ]
    except Exception as e:
        logger.error("获取用户权限失败", error=str(e))
        return []

async def get_optional_user_scopes(
    current_user: Optional[Dict[str, Any]] = Depends(get_optional_current_user)
) -> List[str]:
    """获取当前用户的ESI权限（可选）- 简化版本"""
    if not current_user:
        return []
    return await get_current_user_scopes(current_user)

def require_scopes(*required_scopes: str):
    """简化的权限检查 - 暂时允许所有访问"""
    async def scopes_checker(
        user_scopes: List[str] = Depends(get_current_user_scopes)
    ):
        # 暂时跳过权限检查
        return user_scopes
    return scopes_checker

def require_any_scope(*required_scopes: str):
    """简化的权限检查 - 暂时允许所有访问"""
    async def any_scope_checker(
        user_scopes: List[str] = Depends(get_current_user_scopes)
    ):
        # 暂时跳过权限检查
        return user_scopes
    return any_scope_checker
