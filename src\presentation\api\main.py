"""
EVE Online Assistant - FastAPI 应用入口
"""
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import J<PERSON>NResponse

from src.infrastructure.config import settings
from src.infrastructure.config.simple_logging import get_logger
from src.infrastructure.persistence.database import db_manager

logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """应用生命周期管理"""
    # 启动时执行
    logger.info(f"启动 EVE Online Assistant API v{settings.app_version}")
    
    # 数据库健康检查（可选）
    try:
        if db_manager.health_check():
            logger.info("数据库连接正常")
        else:
            logger.warning("数据库连接检查失败，但继续启动")
    except Exception as e:
        logger.warning("数据库健康检查异常", error=str(e))
    
    yield
    
    # 关闭时执行
    logger.info("关闭 EVE Online Assistant API")


# 创建 FastAPI 应用
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="基于DDD架构的EVE Online游戏综合管理助手",
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
    openapi_url="/openapi.json" if settings.debug else None,
    lifespan=lifespan,
)

# 添加中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=settings.cors_allow_credentials,
    allow_methods=settings.cors_allow_methods,
    allow_headers=settings.cors_allow_headers,
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"] if settings.debug else ["localhost", "127.0.0.1"],
)


@app.middleware("http")
async def log_requests(request: Request, call_next):
    """请求日志中间件"""
    logger.info(
        f"收到请求 {request.method} {request.url} from {request.client.host if request.client else 'unknown'}"
    )
    
    response = await call_next(request)
    
    logger.info(
        f"响应请求 {request.method} {request.url} - {response.status_code}"
    )
    
    return response


# 异常处理器
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理器"""
    logger.error(
        f"未处理的异常 {request.method} {request.url}: {str(exc)}",
        exc_info=True
    )
    
    return JSONResponse(
        status_code=500,
        content={
            "error": "内部服务器错误",
            "message": "服务器遇到了一个错误，请稍后重试",
            "request_id": getattr(request.state, "request_id", None),
        },
    )


# 基础路由
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "欢迎使用 EVE Online Assistant API",
        "version": settings.app_version,
        "docs": "/docs" if settings.debug else "文档在生产环境中不可用",
    }


@app.get("/health")
async def health_check():
    """健康检查端点"""
    db_healthy = db_manager.health_check()
    
    return {
        "status": "healthy" if db_healthy else "unhealthy",
        "version": settings.app_version,
        "database": "connected" if db_healthy else "disconnected",
        "debug": settings.debug,
    }


@app.get("/info")
async def app_info():
    """应用信息端点"""
    return {
        "name": settings.app_name,
        "version": settings.app_version,
        "debug": settings.debug,
        "environment": "development" if settings.debug else "production",
    }


# 导入并注册路由
from .routers import health, auth, characters, sync, monitoring, storage, ngrok, config, scope_driven_features

app.include_router(health.router, prefix="/health", tags=["健康检查"])
app.include_router(auth.router, prefix="/auth", tags=["认证"])
app.include_router(characters.router, prefix="/characters", tags=["角色管理"])
app.include_router(sync.router, prefix="/sync", tags=["数据同步"])
app.include_router(monitoring.router, prefix="/monitoring", tags=["监控"])
app.include_router(storage.router, prefix="/storage", tags=["存储管理"])
app.include_router(ngrok.router, prefix="/ngrok", tags=["ngrok管理"])
app.include_router(config.router, prefix="/api", tags=["配置管理"])
app.include_router(scope_driven_features.router, prefix="/api", tags=["功能管理"])


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "src.presentation.api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
    )
