"""
存储性能监控器
"""
import time
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from collections import defaultdict, deque

from ..config.logging import get_logger

logger = get_logger(__name__)


@dataclass
class OperationMetrics:
    """操作指标"""
    operation_type: str
    storage_type: str
    start_time: float
    end_time: float
    success: bool
    data_size: int = 0
    error_message: Optional[str] = None
    
    @property
    def duration(self) -> float:
        """操作耗时（毫秒）"""
        return (self.end_time - self.start_time) * 1000
    
    @property
    def throughput(self) -> float:
        """吞吐量（字节/秒）"""
        if self.duration > 0 and self.data_size > 0:
            return self.data_size / (self.duration / 1000)
        return 0


@dataclass
class StorageStats:
    """存储统计"""
    total_operations: int = 0
    successful_operations: int = 0
    failed_operations: int = 0
    total_duration: float = 0
    total_data_size: int = 0
    avg_duration: float = 0
    avg_throughput: float = 0
    error_rate: float = 0
    recent_operations: deque = field(default_factory=lambda: deque(maxlen=1000))
    
    def update(self, metric: OperationMetrics):
        """更新统计"""
        self.total_operations += 1
        self.total_duration += metric.duration
        self.total_data_size += metric.data_size
        
        if metric.success:
            self.successful_operations += 1
        else:
            self.failed_operations += 1
        
        self.recent_operations.append(metric)
        
        # 计算平均值
        if self.total_operations > 0:
            self.avg_duration = self.total_duration / self.total_operations
            self.error_rate = self.failed_operations / self.total_operations
            
            if self.total_duration > 0:
                self.avg_throughput = self.total_data_size / (self.total_duration / 1000)


class StoragePerformanceMonitor:
    """存储性能监控器"""
    
    def __init__(self):
        self.stats: Dict[str, Dict[str, StorageStats]] = defaultdict(lambda: defaultdict(StorageStats))
        self.alerts: List[Dict[str, Any]] = []
        self.monitoring_enabled = True
        
        # 性能阈值
        self.thresholds = {
            "max_duration": 5000,  # 5秒
            "max_error_rate": 0.1,  # 10%
            "min_throughput": 1024,  # 1KB/s
        }
    
    def start_operation(self, operation_type: str, storage_type: str, 
                       data_size: int = 0) -> str:
        """开始监控操作"""
        if not self.monitoring_enabled:
            return ""
        
        operation_id = f"{operation_type}_{storage_type}_{time.time()}"
        start_time = time.time()
        
        # 存储操作开始信息
        setattr(self, f"_op_{operation_id}", {
            "operation_type": operation_type,
            "storage_type": storage_type,
            "start_time": start_time,
            "data_size": data_size
        })
        
        return operation_id
    
    def end_operation(self, operation_id: str, success: bool = True, 
                     error_message: Optional[str] = None):
        """结束监控操作"""
        if not self.monitoring_enabled or not operation_id:
            return
        
        end_time = time.time()
        op_info = getattr(self, f"_op_{operation_id}", None)
        
        if not op_info:
            return
        
        # 创建指标
        metric = OperationMetrics(
            operation_type=op_info["operation_type"],
            storage_type=op_info["storage_type"],
            start_time=op_info["start_time"],
            end_time=end_time,
            success=success,
            data_size=op_info["data_size"],
            error_message=error_message
        )
        
        # 更新统计
        self.stats[op_info["storage_type"]][op_info["operation_type"]].update(metric)
        
        # 检查性能告警
        self._check_performance_alerts(metric)
        
        # 清理操作信息
        delattr(self, f"_op_{operation_id}")
        
        # 记录慢操作
        if metric.duration > self.thresholds["max_duration"]:
            logger.warning("检测到慢存储操作",
                         operation_type=metric.operation_type,
                         storage_type=metric.storage_type,
                         duration=metric.duration,
                         data_size=metric.data_size)
    
    def get_stats(self, storage_type: Optional[str] = None, 
                  operation_type: Optional[str] = None) -> Dict[str, Any]:
        """获取性能统计"""
        if storage_type and operation_type:
            # 获取特定操作的统计
            stats = self.stats.get(storage_type, {}).get(operation_type)
            if stats:
                return self._format_stats(stats)
            return {}
        
        elif storage_type:
            # 获取特定存储类型的统计
            storage_stats = self.stats.get(storage_type, {})
            return {
                op_type: self._format_stats(stats)
                for op_type, stats in storage_stats.items()
            }
        
        else:
            # 获取所有统计
            return {
                storage: {
                    op_type: self._format_stats(stats)
                    for op_type, stats in ops.items()
                }
                for storage, ops in self.stats.items()
            }
    
    def get_recent_performance(self, minutes: int = 5) -> Dict[str, Any]:
        """获取最近的性能数据"""
        cutoff_time = time.time() - (minutes * 60)
        recent_stats = defaultdict(lambda: defaultdict(list))
        
        for storage_type, operations in self.stats.items():
            for operation_type, stats in operations.items():
                recent_metrics = [
                    metric for metric in stats.recent_operations
                    if metric.start_time >= cutoff_time
                ]
                
                if recent_metrics:
                    recent_stats[storage_type][operation_type] = {
                        "count": len(recent_metrics),
                        "avg_duration": sum(m.duration for m in recent_metrics) / len(recent_metrics),
                        "success_rate": sum(1 for m in recent_metrics if m.success) / len(recent_metrics),
                        "total_data": sum(m.data_size for m in recent_metrics)
                    }
        
        return dict(recent_stats)
    
    def get_alerts(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取性能告警"""
        return self.alerts[-limit:] if limit else self.alerts
    
    def clear_alerts(self):
        """清除告警"""
        self.alerts.clear()
    
    def set_thresholds(self, thresholds: Dict[str, float]):
        """设置性能阈值"""
        self.thresholds.update(thresholds)
    
    def enable_monitoring(self):
        """启用监控"""
        self.monitoring_enabled = True
        logger.info("存储性能监控已启用")
    
    def disable_monitoring(self):
        """禁用监控"""
        self.monitoring_enabled = False
        logger.info("存储性能监控已禁用")
    
    def _format_stats(self, stats: StorageStats) -> Dict[str, Any]:
        """格式化统计数据"""
        return {
            "total_operations": stats.total_operations,
            "successful_operations": stats.successful_operations,
            "failed_operations": stats.failed_operations,
            "avg_duration_ms": round(stats.avg_duration, 2),
            "avg_throughput_bps": round(stats.avg_throughput, 2),
            "error_rate": round(stats.error_rate, 4),
            "total_data_size": stats.total_data_size
        }
    
    def _check_performance_alerts(self, metric: OperationMetrics):
        """检查性能告警"""
        alerts = []
        
        # 检查操作耗时
        if metric.duration > self.thresholds["max_duration"]:
            alerts.append({
                "type": "slow_operation",
                "message": f"操作耗时过长: {metric.duration:.2f}ms",
                "threshold": self.thresholds["max_duration"],
                "actual": metric.duration
            })
        
        # 检查吞吐量
        if metric.throughput > 0 and metric.throughput < self.thresholds["min_throughput"]:
            alerts.append({
                "type": "low_throughput",
                "message": f"吞吐量过低: {metric.throughput:.2f} bytes/s",
                "threshold": self.thresholds["min_throughput"],
                "actual": metric.throughput
            })
        
        # 检查错误率
        storage_stats = self.stats[metric.storage_type][metric.operation_type]
        if storage_stats.error_rate > self.thresholds["max_error_rate"]:
            alerts.append({
                "type": "high_error_rate",
                "message": f"错误率过高: {storage_stats.error_rate:.2%}",
                "threshold": self.thresholds["max_error_rate"],
                "actual": storage_stats.error_rate
            })
        
        # 添加告警
        for alert in alerts:
            alert.update({
                "timestamp": datetime.utcnow().isoformat(),
                "operation_type": metric.operation_type,
                "storage_type": metric.storage_type
            })
            self.alerts.append(alert)
            
            logger.warning("存储性能告警",
                         alert_type=alert["type"],
                         message=alert["message"],
                         operation=metric.operation_type,
                         storage=metric.storage_type)


# 全局性能监控器
performance_monitor = StoragePerformanceMonitor()


class MonitoredOperation:
    """监控装饰器上下文管理器"""
    
    def __init__(self, operation_type: str, storage_type: str, data_size: int = 0):
        self.operation_type = operation_type
        self.storage_type = storage_type
        self.data_size = data_size
        self.operation_id = ""
    
    def __enter__(self):
        self.operation_id = performance_monitor.start_operation(
            self.operation_type, self.storage_type, self.data_size
        )
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        success = exc_type is None
        error_message = str(exc_val) if exc_val else None
        performance_monitor.end_operation(self.operation_id, success, error_message)


def monitor_storage_operation(operation_type: str, storage_type: str):
    """存储操作监控装饰器"""
    def decorator(func):
        async def async_wrapper(*args, **kwargs):
            with MonitoredOperation(operation_type, storage_type):
                return await func(*args, **kwargs)
        
        def sync_wrapper(*args, **kwargs):
            with MonitoredOperation(operation_type, storage_type):
                return func(*args, **kwargs)
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    
    return decorator
