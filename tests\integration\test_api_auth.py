"""
认证API集成测试
"""
import pytest
from httpx import AsyncClient


@pytest.mark.integration
class TestAuthAPI:
    """认证API集成测试类"""
    
    @pytest.mark.asyncio
    async def test_health_check(self, client: AsyncClient):
        """测试健康检查端点"""
        response = await client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "ok"
        assert "message" in data
    
    @pytest.mark.asyncio
    async def test_eve_login_endpoint_success(self, client: AsyncClient, sample_eve_scopes):
        """测试EVE登录API端点成功"""
        response = await client.post(
            "/auth/login",
            json={"scopes": sample_eve_scopes}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "login_url" in data["data"]
        assert "state" in data["data"]
        assert "expires_in" in data["data"]
        assert data["data"]["scopes"] == sample_eve_scopes
        
        # 验证登录URL格式
        login_url = data["data"]["login_url"]
        assert "login.eveonline.com" in login_url
        assert "oauth/authorize" in login_url
        assert "response_type=code" in login_url
    
    @pytest.mark.asyncio
    async def test_eve_login_endpoint_invalid_scopes(self, client: AsyncClient):
        """测试无效权限的EVE登录"""
        response = await client.post(
            "/auth/login",
            json={"scopes": ["invalid-scope"]}
        )
        
        assert response.status_code == 400
        data = response.json()
        assert data["success"] is False
        assert "Invalid scopes" in data["message"]
    
    @pytest.mark.asyncio
    async def test_eve_login_endpoint_empty_scopes(self, client: AsyncClient):
        """测试空权限列表"""
        response = await client.post(
            "/auth/login",
            json={"scopes": []}
        )
        
        assert response.status_code == 400
        data = response.json()
        assert data["success"] is False
    
    @pytest.mark.asyncio
    async def test_eve_login_endpoint_missing_scopes(self, client: AsyncClient):
        """测试缺少权限参数"""
        response = await client.post(
            "/auth/login",
            json={}
        )
        
        assert response.status_code == 422  # Validation error
    
    @pytest.mark.asyncio
    async def test_eve_login_endpoint_invalid_json(self, client: AsyncClient):
        """测试无效JSON格式"""
        response = await client.post(
            "/auth/login",
            content="invalid json",
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code == 422
    
    @pytest.mark.asyncio
    async def test_eve_callback_endpoint_success(self, client: AsyncClient):
        """测试EVE回调端点成功（模拟）"""
        # 注意：这是一个模拟测试，实际的回调需要真实的EVE授权码
        response = await client.get(
            "/auth/eve/callback",
            params={
                "code": "test_auth_code",
                "state": "test_state"
            }
        )
        
        # 根据实际实现调整断言
        # 这里假设回调端点会返回重定向或者错误信息
        assert response.status_code in [200, 302, 400]
    
    @pytest.mark.asyncio
    async def test_eve_callback_endpoint_missing_code(self, client: AsyncClient):
        """测试缺少授权码的回调"""
        response = await client.get(
            "/auth/eve/callback",
            params={"state": "test_state"}
        )
        
        assert response.status_code == 400
    
    @pytest.mark.asyncio
    async def test_eve_callback_endpoint_missing_state(self, client: AsyncClient):
        """测试缺少状态参数的回调"""
        response = await client.get(
            "/auth/eve/callback",
            params={"code": "test_code"}
        )
        
        assert response.status_code == 400
    
    @pytest.mark.asyncio
    async def test_protected_endpoint_without_token(self, client: AsyncClient):
        """测试未授权访问受保护端点"""
        response = await client.get("/auth/profile")
        
        # 假设需要认证的端点返回401
        assert response.status_code == 401
    
    @pytest.mark.asyncio
    async def test_protected_endpoint_with_invalid_token(self, client: AsyncClient):
        """测试使用无效令牌访问受保护端点"""
        response = await client.get(
            "/auth/profile",
            headers={"Authorization": "Bearer invalid_token"}
        )
        
        assert response.status_code == 401
    
    @pytest.mark.asyncio
    async def test_logout_endpoint(self, client: AsyncClient):
        """测试登出端点"""
        response = await client.post("/auth/logout")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
    
    @pytest.mark.asyncio
    async def test_api_cors_headers(self, client: AsyncClient):
        """测试CORS头部设置"""
        response = await client.options("/auth/login")
        
        assert response.status_code == 200
        assert "Access-Control-Allow-Origin" in response.headers
        assert "Access-Control-Allow-Methods" in response.headers
        assert "Access-Control-Allow-Headers" in response.headers
    
    @pytest.mark.asyncio
    async def test_api_rate_limiting(self, client: AsyncClient, sample_eve_scopes):
        """测试API速率限制（如果实现了）"""
        # 快速发送多个请求
        responses = []
        for _ in range(10):
            response = await client.post(
                "/auth/login",
                json={"scopes": sample_eve_scopes}
            )
            responses.append(response)
        
        # 检查是否有速率限制响应
        status_codes = [r.status_code for r in responses]
        
        # 大部分请求应该成功，可能有一些被限制
        success_count = sum(1 for code in status_codes if code == 200)
        assert success_count >= 5  # 至少一半的请求成功
    
    @pytest.mark.asyncio
    async def test_api_error_handling(self, client: AsyncClient):
        """测试API错误处理"""
        # 测试不存在的端点
        response = await client.get("/auth/nonexistent")
        assert response.status_code == 404
        
        # 测试错误的HTTP方法
        response = await client.delete("/auth/login")
        assert response.status_code == 405
    
    @pytest.mark.asyncio
    async def test_api_response_format(self, client: AsyncClient, sample_eve_scopes):
        """测试API响应格式一致性"""
        response = await client.post(
            "/auth/login",
            json={"scopes": sample_eve_scopes}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证响应格式
        assert "success" in data
        assert isinstance(data["success"], bool)
        
        if data["success"]:
            assert "data" in data
            assert isinstance(data["data"], dict)
        else:
            assert "message" in data
            assert isinstance(data["message"], str)
