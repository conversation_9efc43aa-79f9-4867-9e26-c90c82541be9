"""
角色相关的数据传输对象
"""
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime


class AttributesResponse(BaseModel):
    """属性响应"""
    charisma: int = Field(..., description="魅力")
    intelligence: int = Field(..., description="智力")
    memory: int = Field(..., description="记忆")
    perception: int = Field(..., description="感知")
    willpower: int = Field(..., description="意志")
    total: int = Field(..., description="总属性")


class SkillResponse(BaseModel):
    """技能响应"""
    skill_id: int = Field(..., description="技能ID")
    skillpoints_in_skill: int = Field(..., description="技能点数")
    trained_skill_level: int = Field(..., description="已训练等级")
    active_skill_level: int = Field(..., description="激活等级")


class SkillsResponse(BaseModel):
    """技能集合响应"""
    total_sp: int = Field(..., description="总技能点")
    skill_count: int = Field(..., description="技能数量")
    skills: List[SkillResponse] = Field(..., description="技能列表")


class SkillQueueResponse(BaseModel):
    """技能队列响应"""
    skill_id: int = Field(..., description="技能ID")
    finished_level: int = Field(..., description="完成等级")
    queue_position: int = Field(..., description="队列位置")
    start_date: Optional[str] = Field(None, description="开始时间")
    finish_date: Optional[str] = Field(None, description="完成时间")


class LocationResponse(BaseModel):
    """位置响应"""
    system_id: int = Field(..., description="星系ID")
    station_id: Optional[int] = Field(None, description="空间站ID")
    structure_id: Optional[int] = Field(None, description="建筑ID")


class CharacterResponse(BaseModel):
    """角色响应"""
    character_id: int = Field(..., description="角色ID")
    name: str = Field(..., description="角色名称")
    description: str = Field(..., description="角色描述")
    corporation_id: int = Field(..., description="公司ID")
    alliance_id: Optional[int] = Field(None, description="联盟ID")
    faction_id: Optional[int] = Field(None, description="势力ID")
    race_id: int = Field(..., description="种族ID")
    bloodline_id: int = Field(..., description="血统ID")
    gender: str = Field(..., description="性别")
    birthday: str = Field(..., description="生日")
    security_status: float = Field(..., description="安全等级")
    title: str = Field(..., description="头衔")
    wallet_balance: float = Field(..., description="钱包余额")
    is_online: bool = Field(..., description="是否在线")
    last_login: Optional[str] = Field(None, description="最后登录时间")
    current_location: Optional[LocationResponse] = Field(None, description="当前位置")
    attributes: AttributesResponse = Field(..., description="属性")
    skills: SkillsResponse = Field(..., description="技能")
    skill_queue: List[SkillQueueResponse] = Field(..., description="技能队列")


class CharacterSummaryResponse(BaseModel):
    """角色摘要响应"""
    character_id: int = Field(..., description="角色ID")
    name: str = Field(..., description="角色名称")
    corporation_id: int = Field(..., description="公司ID")
    alliance_id: Optional[int] = Field(None, description="联盟ID")
    is_online: bool = Field(..., description="是否在线")
    total_sp: int = Field(..., description="总技能点")
    wallet_balance: float = Field(..., description="钱包余额")


class CharacterListResponse(BaseModel):
    """角色列表响应"""
    characters: List[CharacterSummaryResponse] = Field(..., description="角色列表")
    total: int = Field(..., description="总数量")


class CharacterSyncRequest(BaseModel):
    """角色同步请求"""
    sync_types: Optional[List[str]] = Field(None, description="同步类型")
    force_refresh: bool = Field(False, description="强制刷新")


class SkillTrainingTimeRequest(BaseModel):
    """技能训练时间请求"""
    skill_id: int = Field(..., description="技能ID")
    target_level: int = Field(..., ge=1, le=5, description="目标等级")


class SkillTrainingTimeResponse(BaseModel):
    """技能训练时间响应"""
    character_id: int = Field(..., description="角色ID")
    skill_id: int = Field(..., description="技能ID")
    target_level: int = Field(..., description="目标等级")
    training_time_seconds: int = Field(..., description="训练时间(秒)")
    training_time_days: int = Field(..., description="训练时间(天)")
    training_time_hours: int = Field(..., description="训练时间(小时)")
    training_time_minutes: int = Field(..., description="训练时间(分钟)")


class CharacterAnalysisResponse(BaseModel):
    """角色分析响应"""
    character_id: int = Field(..., description="角色ID")
    character_name: str = Field(..., description="角色名称")
    analysis: Dict[str, Any] = Field(..., description="分析结果")


class CharacterSearchRequest(BaseModel):
    """角色搜索请求"""
    name: Optional[str] = Field(None, description="角色名称")
    corporation_id: Optional[int] = Field(None, description="公司ID")
    alliance_id: Optional[int] = Field(None, description="联盟ID")
    skill_id: Optional[int] = Field(None, description="技能ID")
    min_skill_level: Optional[int] = Field(None, description="最低技能等级")
    min_sp: Optional[int] = Field(None, description="最低技能点")
    is_online: Optional[bool] = Field(None, description="是否在线")


class CharacterSearchResponse(BaseModel):
    """角色搜索响应"""
    characters: List[CharacterSummaryResponse] = Field(..., description="搜索结果")
    total: int = Field(..., description="总数量")
    search_criteria: Dict[str, Any] = Field(..., description="搜索条件")


class CorporationMembersResponse(BaseModel):
    """公司成员响应"""
    corporation_id: int = Field(..., description="公司ID")
    members: List[CharacterSummaryResponse] = Field(..., description="成员列表")
    total: int = Field(..., description="总数量")


class AllianceMembersResponse(BaseModel):
    """联盟成员响应"""
    alliance_id: int = Field(..., description="联盟ID")
    members: List[CharacterSummaryResponse] = Field(..., description="成员列表")
    total: int = Field(..., description="总数量")


class OnlineCharactersResponse(BaseModel):
    """在线角色响应"""
    characters: List[CharacterSummaryResponse] = Field(..., description="在线角色")
    total: int = Field(..., description="总数量")
    timestamp: str = Field(..., description="查询时间")
