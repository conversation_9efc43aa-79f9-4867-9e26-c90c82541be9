#!/usr/bin/env python3
"""
测试Python环境
"""
import sys
import subprocess

def main():
    print("🔍 Python环境检查")
    print("=" * 40)
    
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"当前工作目录: {sys.path[0]}")
    
    print("\n🔍 检查pip...")
    try:
        result = subprocess.run([sys.executable, "-m", "pip", "--version"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ pip可用: {result.stdout.strip()}")
        else:
            print(f"❌ pip不可用: {result.stderr}")
            return 1
    except Exception as e:
        print(f"❌ pip检查失败: {e}")
        return 1
    
    print("\n🔍 尝试安装pydantic...")
    try:
        result = subprocess.run([sys.executable, "-m", "pip", "install", "pydantic"], 
                              capture_output=True, text=True, timeout=60)
        if result.returncode == 0:
            print("✅ pydantic安装成功")
        else:
            print(f"❌ pydantic安装失败:")
            print(f"   stdout: {result.stdout}")
            print(f"   stderr: {result.stderr}")
            return 1
    except subprocess.TimeoutExpired:
        print("❌ pydantic安装超时")
        return 1
    except Exception as e:
        print(f"❌ pydantic安装异常: {e}")
        return 1
    
    print("\n🔍 验证pydantic导入...")
    try:
        import pydantic
        print(f"✅ pydantic导入成功，版本: {pydantic.__version__}")
    except ImportError as e:
        print(f"❌ pydantic导入失败: {e}")
        return 1
    
    print("\n🎉 环境检查完成！")
    return 0

if __name__ == "__main__":
    sys.exit(main())
