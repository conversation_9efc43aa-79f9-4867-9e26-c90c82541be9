#!/usr/bin/env python3
"""
测试EVE SSO scope修复
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_esi_scopes_import():
    """测试ESI scopes导入"""
    print("🔍 测试ESI scopes导入...")
    
    try:
        from src.infrastructure.esi import ESI_SCOPES
        print("✅ ESI_SCOPES导入成功")
        
        # 检查character_info是否已被移除
        if "character_info" in ESI_SCOPES:
            print(f"❌ character_info仍然存在: {ESI_SCOPES['character_info']}")
            return False
        else:
            print("✅ character_info已正确移除")
        
        # 检查其他有效的scopes
        valid_scopes = ["character_location", "character_skills", "character_wallet"]
        for scope in valid_scopes:
            if scope in ESI_SCOPES:
                print(f"✅ {scope}: {ESI_SCOPES[scope]}")
            else:
                print(f"❌ 缺少scope: {scope}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ ESI_SCOPES导入失败: {e}")
        return False

def test_auth_service_import():
    """测试认证服务导入"""
    print("\n🔍 测试认证服务导入...")
    
    try:
        from src.application.auth import AuthenticationService
        print("✅ AuthenticationService导入成功")
        return True
        
    except Exception as e:
        print(f"❌ AuthenticationService导入失败: {e}")
        return False

def test_default_scopes():
    """测试默认scopes配置"""
    print("\n🔍 测试默认scopes配置...")
    
    try:
        from src.application.auth import AuthenticationService
        from src.infrastructure.esi import EVESSOClient
        
        # 创建认证服务实例
        sso_client = EVESSOClient()
        auth_service = AuthenticationService(sso_client)
        
        # 测试默认scopes（通过检查代码逻辑）
        print("✅ 默认scopes配置测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 默认scopes配置测试失败: {e}")
        return False

def test_eve_sso_config():
    """测试EVE SSO配置"""
    print("\n🔍 测试EVE SSO配置...")
    
    try:
        from src.infrastructure.config import settings
        
        # 检查配置的scopes是否包含无效的scope
        configured_scopes = settings.eve_sso_scopes.split()
        
        invalid_scope = "esi-characters.read_characters.v1"
        if invalid_scope in configured_scopes:
            print(f"❌ 配置中仍包含无效scope: {invalid_scope}")
            return False
        else:
            print(f"✅ 配置中不包含无效scope: {invalid_scope}")
        
        print(f"✅ 配置的scopes数量: {len(configured_scopes)}")
        print("✅ EVE SSO配置测试通过")
        return True
        
    except Exception as e:
        print(f"❌ EVE SSO配置测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 EVE SSO Scope修复验证测试")
    print("=" * 50)
    
    tests = [
        test_esi_scopes_import,
        test_auth_service_import,
        test_default_scopes,
        test_eve_sso_config
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print("❌ 测试失败")
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！EVE SSO scope修复成功！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
