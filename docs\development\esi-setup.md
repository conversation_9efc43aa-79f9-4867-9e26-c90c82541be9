# EVE Online ESI API 配置指南

## 概述

本文档说明如何配置EVE Online Assistant以使用EVE Online的ESI API。配置遵循DDD架构设计原则，将配置分为不同的领域关注点。

## 前置条件

### 1. 创建EVE开发者应用

1. 访问 [EVE Developers](https://developers.eveonline.com/)
2. 使用您的EVE Online账号登录
3. 创建新的应用程序：
   - **Application Name**: EVE Online Assistant
   - **Description**: 个人EVE Online游戏管理助手
   - **Connection Type**: Authentication & API Access
   - **Permissions**: 根据需要选择权限范围（见下文）
   - **Callback URL**: `http://localhost:8000/auth/callback` (开发环境)

4. 记录生成的 **Client ID** 和 **Secret Key**

### 2. 权限范围配置

根据功能需求选择相应的ESI权限范围：

#### 基础权限（必需）
- 基本角色信息(ID、姓名等)在OAuth过程中自动提供，无需特殊scope

#### 角色管理权限
- `esi-location.read_location.v1` - 读取角色位置
- `esi-location.read_online.v1` - 读取在线状态
- `esi-location.read_ship_type.v1` - 读取当前飞船
- `esi-skills.read_skills.v1` - 读取技能信息
- `esi-skills.read_skillqueue.v1` - 读取技能队列
- `esi-wallet.read_character_wallet.v1` - 读取钱包信息

#### 资产管理权限
- `esi-assets.read_assets.v1` - 读取角色资产
- `esi-assets.read_corporation_assets.v1` - 读取公司资产

#### 市场交易权限
- `esi-markets.read_character_orders.v1` - 读取角色市场订单
- `esi-markets.structure_markets.v1` - 读取结构市场数据

#### 工业生产权限
- `esi-industry.read_character_jobs.v1` - 读取角色工业任务
- `esi-industry.read_corporation_jobs.v1` - 读取公司工业任务

#### 公司管理权限
- `esi-corporations.read_corporation_membership.v1` - 读取公司成员信息
- `esi-contracts.read_character_contracts.v1` - 读取角色合同
- `esi-contracts.read_corporation_contracts.v1` - 读取公司合同

## 环境变量配置

### 基础配置

在 `.env` 文件中添加以下配置：

```env
# EVE SSO认证配置
EVE_SSO_CLIENT_ID=your_client_id_here
EVE_SSO_CLIENT_SECRET=your_client_secret_here
EVE_SSO_CALLBACK_URL=http://localhost:8000/auth/callback
EVE_SSO_BASE_URL=https://login.eveonline.com

# ESI API配置
ESI_BASE_URL=https://esi.evetech.net
ESI_USER_AGENT=EVE-Assistant/0.1.0 (<EMAIL>)
ESI_TIMEOUT=30
ESI_MAX_RETRIES=3
ESI_RETRY_DELAY=1.0
ESI_BACKOFF_FACTOR=2.0
```

### 缓存配置

```env
# 缓存TTL配置（秒）
CACHE_TTL_DEFAULT=3600
CACHE_TTL_CHARACTER=120
CACHE_TTL_MARKET=300
CACHE_TTL_ASSETS=3600
CACHE_TTL_SKILLS=7200
CACHE_TTL_CORPORATION=3600
CACHE_TTL_INDUSTRY=1800
```

### 数据同步配置

```env
# 同步间隔配置（秒）
SYNC_INTERVAL_REALTIME=30      # 实时数据：位置、在线状态
SYNC_INTERVAL_FREQUENT=300     # 频繁数据：市场订单、邮件
SYNC_INTERVAL_REGULAR=3600     # 常规数据：技能、资产
SYNC_INTERVAL_DAILY=86400      # 每日数据：静态信息

# 同步功能开关
SYNC_CHARACTER_LOCATION=true
SYNC_CHARACTER_ONLINE=true
SYNC_CHARACTER_SKILLS=true
SYNC_CHARACTER_ASSETS=true
SYNC_CHARACTER_WALLET=true
SYNC_MARKET_ORDERS=true
SYNC_INDUSTRY_JOBS=true
SYNC_CORPORATION_INFO=true
```

### API限流配置

```env
# ESI API限流配置
RATE_LIMIT_REQUESTS_PER_SECOND=10
RATE_LIMIT_BURST=20
```

## 配置验证

### 1. 配置文件验证

创建配置验证脚本：

```python
# scripts/validate_esi_config.py
import asyncio
from src.infrastructure.config import settings
from src.infrastructure.esi import ESIClient, EVESSOClient

async def validate_esi_config():
    """验证ESI配置"""
    print("验证ESI配置...")
    
    # 检查必需的配置项
    required_configs = [
        'eve_sso_client_id',
        'eve_sso_client_secret', 
        'eve_sso_callback_url',
        'esi_base_url',
        'esi_user_agent'
    ]
    
    missing_configs = []
    for config in required_configs:
        if not getattr(settings, config, None):
            missing_configs.append(config.upper())
    
    if missing_configs:
        print(f"❌ 缺少必需配置: {', '.join(missing_configs)}")
        return False
    
    # 测试ESI API连接
    try:
        async with ESIClient() as client:
            # 测试公开API端点
            await client.get("/v1/status/")
            print("✅ ESI API连接正常")
    except Exception as e:
        print(f"❌ ESI API连接失败: {e}")
        return False
    
    # 测试SSO配置
    try:
        sso_client = EVESSOClient()
        login_url, state = sso_client.generate_login_url([
            "esi-location.read_location.v1"  # 使用有效的scope
        ])
        print("✅ EVE SSO配置正常")
        print(f"测试登录URL: {login_url}")
    except Exception as e:
        print(f"❌ EVE SSO配置失败: {e}")
        return False
    
    print("✅ 所有ESI配置验证通过")
    return True

if __name__ == "__main__":
    asyncio.run(validate_esi_config())
```

### 2. 运行验证

```bash
python scripts/validate_esi_config.py
```

## 生产环境配置

### 1. 安全配置

```env
# 生产环境安全配置
DEBUG=false
EVE_SSO_CALLBACK_URL=https://yourdomain.com/auth/callback

# 使用环境变量或密钥管理服务
EVE_SSO_CLIENT_ID=${EVE_CLIENT_ID}
EVE_SSO_CLIENT_SECRET=${EVE_CLIENT_SECRET}
```

### 2. 性能优化配置

```env
# 生产环境性能配置
ESI_TIMEOUT=60
ESI_MAX_RETRIES=5
RATE_LIMIT_REQUESTS_PER_SECOND=20
RATE_LIMIT_BURST=50

# 缓存优化
CACHE_TTL_DEFAULT=7200
REDIS_URL=redis://redis-cluster:6379/0
```

### 3. 监控配置

```env
# 监控和日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json

# ESI API监控
ESI_METRICS_ENABLED=true
ESI_ERROR_THRESHOLD=10
ESI_ALERT_EMAIL=<EMAIL>
```

## 故障排除

### 常见问题

1. **认证失败**
   - 检查Client ID和Secret是否正确
   - 确认回调URL配置正确
   - 验证权限范围是否匹配

2. **API限流**
   - 降低请求频率
   - 增加重试延迟
   - 检查错误限制状态

3. **数据同步问题**
   - 检查令牌是否过期
   - 验证权限范围是否足够
   - 查看同步日志

### 调试模式

启用调试模式获取详细日志：

```env
DEBUG=true
LOG_LEVEL=DEBUG
DATABASE_ECHO=true
```

### 日志分析

查看ESI相关日志：

```bash
# 查看ESI请求日志
grep "ESI" logs/app.log

# 查看认证相关日志  
grep "SSO\|auth" logs/app.log

# 查看错误日志
grep "ERROR" logs/app.log | grep "ESI"
```

## 最佳实践

### 1. 配置管理
- 使用环境变量管理敏感信息
- 为不同环境创建不同的配置文件
- 定期轮换API密钥

### 2. 性能优化
- 合理设置缓存TTL
- 使用批量API减少请求次数
- 实现智能重试机制

### 3. 错误处理
- 实现统一的错误处理策略
- 记录详细的错误日志
- 设置监控和告警

### 4. 安全考虑
- 保护API密钥安全
- 使用HTTPS传输
- 实现令牌自动刷新
- 定期审查权限范围

## 相关文档

- [EVE Online ESI API文档](https://docs.esi.evetech.net/)
- [EVE开发者门户](https://developers.eveonline.com/)
- [OAuth2规范](https://tools.ietf.org/html/rfc6749)
- [项目架构设计文档](DDD_Architecture_Design.md)
