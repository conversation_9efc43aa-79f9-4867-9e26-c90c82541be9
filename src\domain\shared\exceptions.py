"""
领域异常定义
"""


class DomainException(Exception):
    """领域异常基类"""
    
    def __init__(self, message: str, error_code: str = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__


class DomainValidationError(DomainException):
    """领域验证错误"""
    pass


class BusinessRuleViolationError(DomainException):
    """业务规则违反错误"""
    pass


class AggregateNotFoundError(DomainException):
    """聚合根未找到错误"""
    
    def __init__(self, aggregate_type: str, aggregate_id: str):
        message = f"{aggregate_type} with id {aggregate_id} not found"
        super().__init__(message, "AGGREGATE_NOT_FOUND")
        self.aggregate_type = aggregate_type
        self.aggregate_id = aggregate_id


class ConcurrencyError(DomainException):
    """并发冲突错误"""
    
    def __init__(self, aggregate_type: str, aggregate_id: str, expected_version: int, actual_version: int):
        message = f"Concurrency conflict for {aggregate_type} {aggregate_id}: expected version {expected_version}, but was {actual_version}"
        super().__init__(message, "CONCURRENCY_CONFLICT")
        self.aggregate_type = aggregate_type
        self.aggregate_id = aggregate_id
        self.expected_version = expected_version
        self.actual_version = actual_version


class InvariantViolationError(DomainException):
    """不变量违反错误"""
    pass


# 角色相关异常
class CharacterException(DomainException):
    """角色相关异常基类"""
    pass


class InvalidCharacterStateError(CharacterException):
    """无效角色状态错误"""
    pass


class CharacterNotFoundError(CharacterException):
    """角色未找到错误"""
    
    def __init__(self, character_id: int):
        message = f"Character with id {character_id} not found"
        super().__init__(message, "CHARACTER_NOT_FOUND")
        self.character_id = character_id


# 资产相关异常
class AssetException(DomainException):
    """资产相关异常基类"""
    pass


class InsufficientAssetError(AssetException):
    """资产不足错误"""
    
    def __init__(self, asset_type: str, required: int, available: int):
        message = f"Insufficient {asset_type}: required {required}, available {available}"
        super().__init__(message, "INSUFFICIENT_ASSET")
        self.asset_type = asset_type
        self.required = required
        self.available = available


class AssetNotFoundError(AssetException):
    """资产未找到错误"""
    
    def __init__(self, asset_id: int):
        message = f"Asset with id {asset_id} not found"
        super().__init__(message, "ASSET_NOT_FOUND")
        self.asset_id = asset_id


# 市场相关异常
class MarketException(DomainException):
    """市场相关异常基类"""
    pass


class InvalidOrderError(MarketException):
    """无效订单错误"""
    pass


class OrderNotFoundError(MarketException):
    """订单未找到错误"""
    
    def __init__(self, order_id: int):
        message = f"Market order with id {order_id} not found"
        super().__init__(message, "ORDER_NOT_FOUND")
        self.order_id = order_id


# 工业相关异常
class IndustryException(DomainException):
    """工业相关异常基类"""
    pass


class InvalidJobError(IndustryException):
    """无效工业任务错误"""
    pass


class JobNotFoundError(IndustryException):
    """工业任务未找到错误"""
    
    def __init__(self, job_id: int):
        message = f"Industry job with id {job_id} not found"
        super().__init__(message, "JOB_NOT_FOUND")
        self.job_id = job_id


# 公司相关异常
class CorporationException(DomainException):
    """公司相关异常基类"""
    pass


class CorporationNotFoundError(CorporationException):
    """公司未找到错误"""
    
    def __init__(self, corporation_id: int):
        message = f"Corporation with id {corporation_id} not found"
        super().__init__(message, "CORPORATION_NOT_FOUND")
        self.corporation_id = corporation_id


class InsufficientPermissionError(CorporationException):
    """权限不足错误"""

    def __init__(self, required_role: str):
        message = f"Insufficient permission: {required_role} role required"
        super().__init__(message, "INSUFFICIENT_PERMISSION")
        self.required_role = required_role


# ESI API相关异常
class ESIException(DomainException):
    """ESI API相关异常基类"""
    pass


class ESIAuthenticationError(ESIException):
    """ESI认证错误"""

    def __init__(self, message: str = "ESI authentication failed"):
        super().__init__(message, "ESI_AUTHENTICATION_ERROR")


class ESIAuthorizationError(ESIException):
    """ESI授权错误"""

    def __init__(self, scope: str):
        message = f"ESI authorization failed: missing scope '{scope}'"
        super().__init__(message, "ESI_AUTHORIZATION_ERROR")
        self.scope = scope


class ESIRateLimitError(ESIException):
    """ESI限流错误"""

    def __init__(self, retry_after: int):
        message = f"ESI rate limit exceeded, retry after {retry_after} seconds"
        super().__init__(message, "ESI_RATE_LIMIT_ERROR")
        self.retry_after = retry_after


class ESIServerError(ESIException):
    """ESI服务器错误"""

    def __init__(self, status_code: int, message: str = "ESI server error"):
        super().__init__(f"{message} (HTTP {status_code})", "ESI_SERVER_ERROR")
        self.status_code = status_code


class ESITimeoutError(ESIException):
    """ESI超时错误"""

    def __init__(self, timeout: int):
        message = f"ESI request timeout after {timeout} seconds"
        super().__init__(message, "ESI_TIMEOUT_ERROR")
        self.timeout = timeout


class ESIDataError(ESIException):
    """ESI数据错误"""

    def __init__(self, message: str = "ESI data format error"):
        super().__init__(message, "ESI_DATA_ERROR")


class TokenExpiredError(ESIException):
    """令牌过期错误"""

    def __init__(self, character_id: int):
        message = f"Access token expired for character {character_id}"
        super().__init__(message, "TOKEN_EXPIRED")
        self.character_id = character_id


class TokenRefreshError(ESIException):
    """令牌刷新错误"""

    def __init__(self, character_id: int, reason: str):
        message = f"Failed to refresh token for character {character_id}: {reason}"
        super().__init__(message, "TOKEN_REFRESH_ERROR")
        self.character_id = character_id
        self.reason = reason


# 认证相关异常
class AuthenticationError(DomainException):
    """认证错误"""

    def __init__(self, message: str = "Authentication failed"):
        super().__init__(message, "AUTHENTICATION_ERROR")


class AuthorizationError(DomainException):
    """授权错误"""

    def __init__(self, message: str = "Authorization failed"):
        super().__init__(message, "AUTHORIZATION_ERROR")
