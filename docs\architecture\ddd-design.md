# EVE Online 游戏管理助手 - DDD架构设计

## 项目概述

基于领域驱动设计(DDD)原则，构建一个EVE Online游戏综合管理助手，通过ESI API提供角色管理、资产跟踪、市场分析、工业生产等功能。

## 领域划分

### 1. 核心领域 (Core Domain)

#### 角色管理域 (Character Domain)
- **职责**: 管理角色信息、技能、属性、位置等
- **核心实体**: Character, Skill, Attribute, Location
- **主要用例**: 角色信息查询、技能规划、位置跟踪

#### 资产管理域 (Asset Domain)
- **职责**: 管理角色和公司资产、库存跟踪
- **核心实体**: Asset, Inventory, Location
- **主要用例**: 资产查询、库存统计、资产分布分析

#### 市场交易域 (Market Domain)
- **职责**: 市场数据分析、订单管理、价格跟踪
- **核心实体**: MarketOrder, PriceHistory, TradeAnalysis
- **主要用例**: 市场分析、价格预测、交易建议

### 2. 支撑领域 (Supporting Domain)

#### 工业生产域 (Industry Domain)
- **职责**: 工业任务管理、生产规划、蓝图管理
- **核心实体**: IndustryJob, Blueprint, ProductionPlan
- **主要用例**: 生产管理、成本计算、利润分析

#### 公司管理域 (Corporation Domain)
- **职责**: 公司信息、成员管理、结构管理
- **核心实体**: Corporation, Member, Structure
- **主要用例**: 公司管理、成员跟踪、结构监控

#### 通信域 (Communication Domain)
- **职责**: 游戏内邮件、通知、日历事件
- **核心实体**: Mail, Notification, CalendarEvent
- **主要用例**: 消息管理、事件提醒、通知推送

### 3. 通用领域 (Generic Domain)

#### 认证授权域 (Authentication Domain)
- **职责**: SSO认证、令牌管理、权限控制
- **核心实体**: User, Token, Permission
- **主要用例**: 用户登录、令牌刷新、权限验证

#### 数据同步域 (Synchronization Domain)
- **职责**: ESI API调用、数据缓存、同步策略
- **核心实体**: SyncJob, CacheEntry, APICall
- **主要用例**: 数据同步、缓存管理、API限流

## 架构层次

```
┌─────────────────────────────────────────┐
│           Presentation Layer            │
│  (Web API, Web UI, Mobile App)         │
├─────────────────────────────────────────┤
│           Application Layer             │
│  (Application Services, DTOs)          │
├─────────────────────────────────────────┤
│             Domain Layer                │
│  (Entities, Value Objects, Services)   │
├─────────────────────────────────────────┤
│          Infrastructure Layer           │
│  (Repositories, External APIs)         │
└─────────────────────────────────────────┘
```

## 聚合根设计

### Character 聚合
```python
class Character:
    def __init__(self, character_id: CharacterId, name: str):
        self.character_id = character_id
        self.name = name
        self.skills = Skills()
        self.attributes = Attributes()
        self.location = Location()
        self.wallet = Wallet()
    
    def update_skills(self, skill_data: dict):
        # 更新技能信息
        pass
    
    def update_location(self, location_data: dict):
        # 更新位置信息
        pass
```

### Asset 聚合
```python
class AssetCollection:
    def __init__(self, owner_id: int, owner_type: str):
        self.owner_id = owner_id
        self.owner_type = owner_type  # 'character' or 'corporation'
        self.assets = []
    
    def add_asset(self, asset: Asset):
        # 添加资产
        pass
    
    def get_assets_by_location(self, location_id: int):
        # 按位置获取资产
        pass
    
    def calculate_total_value(self):
        # 计算总价值
        pass
```

### MarketOrder 聚合
```python
class MarketOrder:
    def __init__(self, order_id: OrderId, type_id: int, region_id: int):
        self.order_id = order_id
        self.type_id = type_id
        self.region_id = region_id
        self.price = Money()
        self.volume = Volume()
        self.is_buy_order = bool()
    
    def update_volume(self, new_volume: int):
        # 更新数量
        pass
    
    def is_expired(self) -> bool:
        # 检查是否过期
        pass
```

## 值对象设计

```python
@dataclass(frozen=True)
class CharacterId:
    value: int
    
    def __post_init__(self):
        if self.value <= 0:
            raise ValueError("Character ID must be positive")

@dataclass(frozen=True)
class Money:
    amount: Decimal
    currency: str = "ISK"
    
    def add(self, other: 'Money') -> 'Money':
        if self.currency != other.currency:
            raise ValueError("Currency mismatch")
        return Money(self.amount + other.amount, self.currency)

@dataclass(frozen=True)
class Location:
    system_id: int
    station_id: Optional[int] = None
    structure_id: Optional[int] = None
    
    def is_in_space(self) -> bool:
        return self.station_id is None and self.structure_id is None
```

## 领域服务

### 市场分析服务
```python
class MarketAnalysisService:
    def __init__(self, market_repo: MarketRepository):
        self.market_repo = market_repo
    
    def calculate_profit_margin(self, type_id: int, region_id: int) -> Decimal:
        # 计算利润率
        pass
    
    def find_arbitrage_opportunities(self, regions: List[int]) -> List[ArbitrageOpportunity]:
        # 寻找套利机会
        pass
    
    def predict_price_trend(self, type_id: int, region_id: int) -> PriceTrend:
        # 价格趋势预测
        pass
```

### 生产规划服务
```python
class ProductionPlanningService:
    def __init__(self, blueprint_repo: BlueprintRepository, market_service: MarketAnalysisService):
        self.blueprint_repo = blueprint_repo
        self.market_service = market_service
    
    def calculate_production_cost(self, blueprint_id: int, runs: int) -> ProductionCost:
        # 计算生产成本
        pass
    
    def optimize_production_chain(self, target_item: int, quantity: int) -> ProductionPlan:
        # 优化生产链
        pass
```

## 应用服务

### 角色管理应用服务
```python
class CharacterApplicationService:
    def __init__(self, 
                 character_repo: CharacterRepository,
                 esi_client: ESIClient,
                 event_bus: EventBus):
        self.character_repo = character_repo
        self.esi_client = esi_client
        self.event_bus = event_bus
    
    def sync_character_data(self, character_id: int, access_token: str) -> CharacterDto:
        # 同步角色数据
        character_data = self.esi_client.get_character(character_id)
        character = self.character_repo.get_by_id(character_id)
        
        if character is None:
            character = Character.create_from_esi_data(character_data)
        else:
            character.update_from_esi_data(character_data)
        
        self.character_repo.save(character)
        self.event_bus.publish(CharacterUpdatedEvent(character_id))
        
        return CharacterDto.from_entity(character)
```

## 基础设施层

### ESI API 客户端
```python
class ESIClient:
    def __init__(self, base_url: str, user_agent: str):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({'User-Agent': user_agent})
    
    async def get_character(self, character_id: int) -> dict:
        # 获取角色信息
        pass
    
    async def get_character_assets(self, character_id: int, token: str) -> List[dict]:
        # 获取角色资产
        pass
    
    async def get_market_orders(self, region_id: int, type_id: int) -> List[dict]:
        # 获取市场订单
        pass
```

### 仓储实现
```python
class SqlCharacterRepository(CharacterRepository):
    def __init__(self, db_session: Session):
        self.db_session = db_session
    
    def get_by_id(self, character_id: CharacterId) -> Optional[Character]:
        # 从数据库获取角色
        pass
    
    def save(self, character: Character) -> None:
        # 保存角色到数据库
        pass
    
    def find_by_corporation(self, corporation_id: int) -> List[Character]:
        # 按公司查找角色
        pass
```

## 事件驱动架构

### 领域事件
```python
@dataclass
class CharacterUpdatedEvent(DomainEvent):
    character_id: int
    updated_at: datetime
    
@dataclass
class AssetValueChangedEvent(DomainEvent):
    owner_id: int
    old_value: Decimal
    new_value: Decimal
    
@dataclass
class MarketOpportunityFoundEvent(DomainEvent):
    type_id: int
    region_id: int
    profit_margin: Decimal
```

### 事件处理器
```python
class AssetValueChangedEventHandler:
    def __init__(self, notification_service: NotificationService):
        self.notification_service = notification_service
    
    def handle(self, event: AssetValueChangedEvent):
        if event.new_value > event.old_value * Decimal('1.1'):  # 增值10%以上
            self.notification_service.send_notification(
                event.owner_id,
                f"您的资产价值增长了 {event.new_value - event.old_value:,.2f} ISK"
            )
```

## 技术栈建议

### 后端
- **语言**: Python 3.11+
- **框架**: FastAPI / Django
- **数据库**: PostgreSQL
- **缓存**: Redis
- **消息队列**: RabbitMQ / Apache Kafka
- **任务调度**: Celery

### 前端
- **框架**: React / Vue.js
- **状态管理**: Redux / Vuex
- **UI组件**: Ant Design / Element UI
- **图表**: ECharts / D3.js

### 部署
- **容器化**: Docker
- **编排**: Kubernetes
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack
