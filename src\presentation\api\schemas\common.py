"""
通用数据传输对象
"""
from pydantic import BaseModel, Field
from typing import Any, Dict, List, Optional


class ErrorResponse(BaseModel):
    """错误响应"""
    error: Dict[str, Any] = Field(..., description="错误信息")


class SuccessResponse(BaseModel):
    """成功响应"""
    success: bool = Field(True, description="是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")


class PaginationInfo(BaseModel):
    """分页信息"""
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页")
    page_size: int = Field(..., description="每页大小")
    total_pages: int = Field(..., description="总页数")


class PaginatedResponse(BaseModel):
    """分页响应"""
    items: List[Any] = Field(..., description="数据项")
    pagination: PaginationInfo = Field(..., description="分页信息")


class HealthStatus(BaseModel):
    """健康状态"""
    status: str = Field(..., description="状态")
    details: Optional[str] = Field(None, description="详细信息")


class ComponentHealth(BaseModel):
    """组件健康状态"""
    database: HealthStatus = Field(..., description="数据库状态")
    esi_api: HealthStatus = Field(..., description="ESI API状态")
    configuration: HealthStatus = Field(..., description="配置状态")
