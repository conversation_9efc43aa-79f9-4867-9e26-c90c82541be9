#!/usr/bin/env python3
"""
EVE SSO认证功能测试脚本
"""
import requests
import time
import json

def test_eve_sso():
    base_url = "http://localhost:8000"
    
    print("🔐 EVE SSO认证功能测试")
    print("=" * 50)
    
    # 等待服务器启动
    print("等待服务器启动...")
    time.sleep(3)
    
    # 测试1: 健康检查
    print("\n1. 测试健康检查...")
    try:
        response = requests.get(f"{base_url}/health/status", timeout=5)
        if response.status_code == 200:
            print("✅ 健康检查正常")
        else:
            print(f"❌ 健康检查失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查连接失败: {e}")
        return False
    
    # 测试2: 认证状态
    print("\n2. 测试认证状态...")
    try:
        response = requests.get(f"{base_url}/auth/status", timeout=5)
        if response.status_code == 200:
            print("✅ 认证状态端点正常")
            print(f"   响应: {response.json()}")
        else:
            print(f"❌ 认证状态失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ 认证状态连接失败: {e}")
    
    # 测试3: EVE SSO登录发起
    print("\n3. 测试EVE SSO登录发起...")
    try:
        test_scopes = [
            "esi-assets.read_assets.v1",
            "esi-skills.read_skills.v1",
            "esi-wallet.read_character_wallet.v1"
        ]
        
        response = requests.post(
            f"{base_url}/auth/eve/login",
            json={"scopes": test_scopes},
            timeout=10
        )
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ EVE SSO登录发起成功")
            print(f"   响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            # 检查响应格式 - 支持嵌套的data字段
            if "data" in data and isinstance(data["data"], dict):
                inner_data = data["data"]
                if "login_url" in inner_data and "state" in inner_data:
                    print("✅ 响应格式正确，包含login_url和state")
                    return True
            elif "login_url" in data and "state" in data:
                print("✅ 响应格式正确，包含login_url和state")
                return True

            print("❌ 响应格式不正确，缺少必要字段")
            return False
        else:
            print(f"❌ EVE SSO登录发起失败: HTTP {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ EVE SSO登录发起连接失败: {e}")
        return False
    
    # 测试4: 回调端点
    print("\n4. 测试EVE SSO回调端点...")
    try:
        response = requests.get(f"{base_url}/auth/callback", timeout=5)
        print(f"   状态码: {response.status_code}")
        if response.status_code in [200, 400, 422]:  # 400/422是正常的，因为没有提供参数
            print("✅ 回调端点存在且可访问")
        else:
            print(f"❌ 回调端点异常: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ 回调端点连接失败: {e}")

if __name__ == "__main__":
    success = test_eve_sso()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 EVE SSO认证功能测试通过！")
        print("✅ 基础认证流程已恢复")
    else:
        print("❌ EVE SSO认证功能测试失败")
        print("⚠️  需要进一步调试")
    print("=" * 50)
