#!/usr/bin/env python3
"""
简单的前端启动脚本
"""
import subprocess
import sys
import os
from pathlib import Path

def start_frontend():
    """启动前端服务器"""
    print("🚀 启动前端服务器")
    print("=" * 40)
    
    frontend_dir = Path("frontend")
    
    if not frontend_dir.exists():
        print("❌ frontend目录不存在")
        return False
    
    # 切换到frontend目录并启动
    try:
        print(f"📁 切换到目录: {frontend_dir.absolute()}")
        
        # 使用系统命令启动
        if os.name == 'nt':  # Windows
            cmd = 'cd frontend && npm run dev'
        else:  # Unix/Linux
            cmd = 'cd frontend && npm run dev'
        
        print(f"🔧 执行命令: {cmd}")
        
        # 使用shell=True直接执行
        process = subprocess.Popen(
            cmd,
            shell=True,
            cwd=os.getcwd(),
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1
        )
        
        print("⏳ 前端服务器启动中...")
        print("📝 如果看到 'Local: http://localhost:3000' 说明启动成功")
        print("📝 按 Ctrl+C 停止服务器")
        
        # 实时显示输出
        try:
            for line in iter(process.stdout.readline, ''):
                if line:
                    print(f"   {line.rstrip()}")
                    
                    # 检查是否启动成功
                    if "Local:" in line and "3000" in line:
                        print("\n🎉 前端服务器启动成功！")
                        print("📝 现在可以访问: http://localhost:3000")
                        break
                    
                    # 检查是否有错误
                    if "error" in line.lower() or "failed" in line.lower():
                        print(f"\n❌ 检测到错误: {line.rstrip()}")
                        
        except KeyboardInterrupt:
            print("\n🛑 用户中断")
            process.terminate()
            return False
        
        # 保持进程运行
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n🛑 停止前端服务器...")
            process.terminate()
            try:
                process.wait(timeout=5)
                print("✅ 前端服务器已停止")
            except subprocess.TimeoutExpired:
                process.kill()
                print("⚠️  强制终止前端服务器")
        
        return True
        
    except Exception as e:
        print(f"❌ 启动前端失败: {e}")
        return False

def main():
    """主函数"""
    if start_frontend():
        print("🎉 前端启动完成")
        return 0
    else:
        print("❌ 前端启动失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
