"""
EVE SSO认证服务
"""
import base64
import secrets
import hashlib
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from urllib.parse import urlencode, urlparse, parse_qs

import httpx
from jose import jwt, JWTError

from ..config import settings
from ..config.logging import get_logger
from ...domain.shared.exceptions import (
    ESIAuthenticationError, ESIAuthorizationError, TokenExpiredError, TokenRefreshError
)

logger = get_logger(__name__)


class EVESSOClient:
    """EVE SSO客户端"""

    def __init__(self):
        self.client_id = settings.eve_sso_client_id
        self.client_secret = settings.eve_sso_client_secret
        self.callback_url = settings.eve_sso_callback_url
        self.base_url = settings.eve_sso_base_url

        # SSO端点
        self.authorize_url = f"{self.base_url}/v2/oauth/authorize"
        self.token_url = f"{self.base_url}/v2/oauth/token"
        self.revoke_url = f"{self.base_url}/v2/oauth/revoke"
        self.jwks_url = f"{self.base_url}/oauth/jwks"

        # HTTP客户端配置
        self._client = httpx.AsyncClient(
            timeout=httpx.Timeout(30.0),
            limits=httpx.Limits(max_connections=10, max_keepalive_connections=5),
            headers={"User-Agent": settings.esi_user_agent}
        )

        # JWT验证密钥缓存
        self._jwks_cache: Optional[Dict] = None
        self._jwks_cache_expires: Optional[datetime] = None

        # PKCE支持
        self._enable_pkce = getattr(settings, 'enable_pkce', True)
    
    def generate_login_url(self,
                          scopes: List[str],
                          state: Optional[str] = None) -> Tuple[str, str]:
        """生成登录URL（兼容性方法）"""
        if self._enable_pkce:
            login_url, state, _ = self.generate_login_url_with_pkce(scopes, state)
            return login_url, state
        else:
            return self._generate_basic_login_url(scopes, state)

    def generate_login_url_with_pkce(self,
                                   scopes: List[str],
                                   state: Optional[str] = None) -> Tuple[str, str, str]:
        """生成带PKCE的登录URL"""
        if state is None:
            state = secrets.token_urlsafe(32)

        # 生成PKCE参数
        code_verifier = base64.urlsafe_b64encode(
            secrets.token_bytes(32)
        ).decode('utf-8').rstrip('=')

        code_challenge = base64.urlsafe_b64encode(
            hashlib.sha256(code_verifier.encode('utf-8')).digest()
        ).decode('utf-8').rstrip('=')

        params = {
            "response_type": "code",
            "redirect_uri": self.callback_url,
            "client_id": self.client_id,
            "scope": " ".join(scopes),
            "state": state,
            "code_challenge": code_challenge,
            "code_challenge_method": "S256"
        }

        login_url = f"{self.authorize_url}?{urlencode(params)}"

        logger.info(
            "生成EVE SSO登录URL (PKCE)",
            scopes=scopes,
            state=state,
            callback_url=self.callback_url,
            pkce_enabled=True
        )

        return login_url, state, code_verifier

    def _generate_basic_login_url(self,
                                 scopes: List[str],
                                 state: Optional[str] = None) -> Tuple[str, str]:
        """生成基础登录URL（无PKCE）"""
        if state is None:
            state = secrets.token_urlsafe(32)

        params = {
            "response_type": "code",
            "redirect_uri": self.callback_url,
            "client_id": self.client_id,
            "scope": " ".join(scopes),
            "state": state
        }

        login_url = f"{self.authorize_url}?{urlencode(params)}"

        logger.info(
            "生成EVE SSO登录URL (Basic)",
            scopes=scopes,
            state=state,
            callback_url=self.callback_url,
            pkce_enabled=False
        )

        return login_url, state
    
    async def exchange_code_for_tokens(self,
                                     authorization_code: str,
                                     state: str,
                                     code_verifier: Optional[str] = None) -> Dict[str, Any]:
        """交换授权码获取令牌"""
        if self._enable_pkce and code_verifier:
            return await self._exchange_code_with_pkce(authorization_code, code_verifier)
        else:
            return await self._exchange_code_basic(authorization_code)

    async def _exchange_code_with_pkce(self,
                                     authorization_code: str,
                                     code_verifier: str) -> Dict[str, Any]:
        """使用PKCE交换授权码"""
        data = {
            "grant_type": "authorization_code",
            "code": authorization_code,
            "redirect_uri": self.callback_url,
            "client_id": self.client_id,
            "code_verifier": code_verifier
        }

        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }

        try:
            response = await self._client.post(
                self.token_url,
                data=data,
                headers=headers
            )

            if response.status_code != 200:
                logger.error(
                    "EVE SSO令牌交换失败 (PKCE)",
                    status_code=response.status_code,
                    response_text=response.text
                )
                raise ESIAuthenticationError(f"Token exchange failed: {response.text}")

            return await self._process_token_response(response.json())

        except httpx.RequestError as e:
            logger.error("EVE SSO请求失败 (PKCE)", error=str(e))
            raise ESIAuthenticationError(f"SSO request failed: {str(e)}")

    async def _exchange_code_basic(self, authorization_code: str) -> Dict[str, Any]:
        """基础授权码交换（无PKCE）"""
        data = {
            "grant_type": "authorization_code",
            "code": authorization_code,
            "redirect_uri": self.callback_url
        }

        # 准备认证头
        auth_string = f"{self.client_id}:{self.client_secret}"
        auth_bytes = auth_string.encode('ascii')
        auth_b64 = base64.b64encode(auth_bytes).decode('ascii')

        headers = {
            "Authorization": f"Basic {auth_b64}",
            "Content-Type": "application/x-www-form-urlencoded"
        }

        try:
            response = await self._client.post(
                self.token_url,
                data=data,
                headers=headers
            )

            if response.status_code != 200:
                logger.error(
                    "EVE SSO令牌交换失败 (Basic)",
                    status_code=response.status_code,
                    response_text=response.text
                )
                raise ESIAuthenticationError(f"Token exchange failed: {response.text}")

            return await self._process_token_response(response.json())

        except httpx.RequestError as e:
            logger.error("EVE SSO请求失败 (Basic)", error=str(e))
            raise ESIAuthenticationError(f"SSO request failed: {str(e)}")

    async def _process_token_response(self, token_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理令牌响应"""
        # 验证访问令牌
        character_info = await self._verify_access_token(token_data["access_token"])

        # 组合返回数据
        result = {
            "access_token": token_data["access_token"],
            "refresh_token": token_data["refresh_token"],
            "expires_in": token_data["expires_in"],
            "token_type": token_data.get("token_type", "Bearer"),
            "character_id": character_info["sub"].split(":")[-1],
            "character_name": character_info["name"],
            "scopes": character_info.get("scp", []),
            "expires_at": datetime.utcnow() + timedelta(seconds=token_data["expires_in"])
        }

        logger.info(
            "EVE SSO令牌交换成功",
            character_id=result["character_id"],
            character_name=result["character_name"],
            scopes=result["scopes"]
        )

        return result
    
    async def refresh_access_token(self, refresh_token: str) -> Dict[str, Any]:
        """刷新访问令牌"""
        data = {
            "grant_type": "refresh_token",
            "refresh_token": refresh_token
        }

        # 准备认证头
        auth_string = f"{self.client_id}:{self.client_secret}"
        auth_bytes = auth_string.encode('ascii')
        auth_b64 = base64.b64encode(auth_bytes).decode('ascii')

        headers = {
            "Authorization": f"Basic {auth_b64}",
            "Content-Type": "application/x-www-form-urlencoded"
        }

        try:
            response = await self._client.post(
                self.token_url,
                data=data,
                headers=headers
            )

            if response.status_code != 200:
                logger.error(
                    "EVE SSO令牌刷新失败",
                    status_code=response.status_code,
                    response_text=response.text
                )
                raise TokenRefreshError(0, f"Token refresh failed: {response.text}")

            token_data = response.json()

            # 验证新的访问令牌
            character_info = await self._verify_access_token(token_data["access_token"])

            result = {
                "access_token": token_data["access_token"],
                "refresh_token": token_data.get("refresh_token", refresh_token),
                "expires_in": token_data["expires_in"],
                "token_type": token_data.get("token_type", "Bearer"),
                "character_id": character_info["sub"].split(":")[-1],
                "character_name": character_info["name"],
                "scopes": character_info.get("scp", []),
                "expires_at": datetime.utcnow() + timedelta(seconds=token_data["expires_in"])
            }

            logger.info(
                "EVE SSO令牌刷新成功",
                character_id=result["character_id"],
                character_name=result["character_name"]
            )

            return result

        except httpx.RequestError as e:
            logger.error("EVE SSO刷新请求失败", error=str(e))
            raise TokenRefreshError(0, f"SSO refresh request failed: {str(e)}")
    
    async def revoke_token(self, token: str, token_type: str = "refresh_token"):
        """撤销令牌"""
        data = {
            "token": token,
            "token_type_hint": token_type
        }

        # 准备认证头
        auth_string = f"{self.client_id}:{self.client_secret}"
        auth_bytes = auth_string.encode('ascii')
        auth_b64 = base64.b64encode(auth_bytes).decode('ascii')

        headers = {
            "Authorization": f"Basic {auth_b64}",
            "Content-Type": "application/x-www-form-urlencoded"
        }

        try:
            response = await self._client.post(
                self.revoke_url,
                data=data,
                headers=headers
            )

            if response.status_code == 200:
                logger.info("EVE SSO令牌撤销成功", token_type=token_type)
            else:
                logger.warning(
                    "EVE SSO令牌撤销失败",
                    status_code=response.status_code,
                    response_text=response.text
                )

        except httpx.RequestError as e:
            logger.error("EVE SSO撤销请求失败", error=str(e))
    
    async def _verify_access_token(self, access_token: str) -> Dict[str, any]:
        """验证访问令牌"""
        try:
            # 获取JWT签名密钥
            jwks = await self._get_jwks()
            
            # 解码JWT令牌
            header = jwt.get_unverified_header(access_token)
            key = None
            
            for jwk in jwks["keys"]:
                if jwk["kid"] == header["kid"]:
                    key = jwk
                    break
            
            if not key:
                raise ESIAuthenticationError("JWT key not found")
            
            # 验证令牌
            payload = jwt.decode(
                access_token,
                key,
                algorithms=["RS256"],
                audience="EVE Online",
                issuer="login.eveonline.com"
            )
            
            return payload
            
        except JWTError as e:
            logger.error("JWT令牌验证失败", error=str(e))
            raise ESIAuthenticationError(f"JWT verification failed: {str(e)}")
    
    async def _get_jwks(self) -> Dict[str, Any]:
        """获取JWT签名密钥"""
        # 检查缓存
        if (self._jwks_cache and
            self._jwks_cache_expires and
            datetime.utcnow() < self._jwks_cache_expires):
            return self._jwks_cache

        try:
            response = await self._client.get(self.jwks_url)

            if response.status_code != 200:
                raise ESIAuthenticationError(f"JWKS fetch failed: {response.text}")

            jwks = response.json()

            # 缓存1小时
            self._jwks_cache = jwks
            self._jwks_cache_expires = datetime.utcnow() + timedelta(hours=1)

            logger.debug("JWKS缓存已更新")
            return jwks

        except httpx.RequestError as e:
            logger.error("JWKS获取失败", error=str(e))
            raise ESIAuthenticationError(f"JWKS request failed: {str(e)}")

    async def close(self):
        """关闭HTTP客户端"""
        await self._client.aclose()

    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()


# 常用的ESI权限范围
ESI_SCOPES = {
    # 角色相关
    "character_info": "esi-characters.read_characters.v1",
    "character_location": "esi-location.read_location.v1",
    "character_online": "esi-location.read_online.v1",
    "character_ship": "esi-location.read_ship_type.v1",
    "character_skills": "esi-skills.read_skills.v1",
    "character_skillqueue": "esi-skills.read_skillqueue.v1",
    "character_attributes": "esi-skills.read_skills.v1",
    "character_wallet": "esi-wallet.read_character_wallet.v1",
    "character_assets": "esi-assets.read_assets.v1",
    "character_contracts": "esi-contracts.read_character_contracts.v1",
    "character_industry": "esi-industry.read_character_jobs.v1",
    "character_mail": "esi-mail.read_mail.v1",
    
    # 公司相关
    "corporation_info": "esi-corporations.read_corporation_membership.v1",
    "corporation_assets": "esi-assets.read_corporation_assets.v1",
    "corporation_contracts": "esi-contracts.read_corporation_contracts.v1",
    "corporation_industry": "esi-industry.read_corporation_jobs.v1",
    
    # 市场相关
    "market_orders": "esi-markets.read_character_orders.v1",
    "market_structures": "esi-markets.structure_markets.v1",
    
    # 宇宙相关
    "universe_structures": "esi-universe.read_structures.v1",
}
