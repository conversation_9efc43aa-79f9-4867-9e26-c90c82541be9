"""
EVE SSO认证服务
"""
import base64
import secrets
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from urllib.parse import urlencode, urlparse, parse_qs

import httpx
from jose import jwt, JWTError

from ..config import settings
from ..config.logging import get_logger
from ...domain.shared.exceptions import (
    ESIAuthenticationError, ESIAuthorizationError, TokenExpiredError, TokenRefreshError
)

logger = get_logger(__name__)


class EVESSOClient:
    """EVE SSO客户端"""
    
    def __init__(self):
        self.client_id = settings.eve_sso_client_id
        self.client_secret = settings.eve_sso_client_secret
        self.callback_url = settings.eve_sso_callback_url
        self.base_url = settings.eve_sso_base_url
        
        # SSO端点
        self.authorize_url = f"{self.base_url}/v2/oauth/authorize"
        self.token_url = f"{self.base_url}/v2/oauth/token"
        self.revoke_url = f"{self.base_url}/v2/oauth/revoke"
        self.jwks_url = f"{self.base_url}/oauth/jwks"
        
        # JWT验证密钥缓存
        self._jwks_cache: Optional[Dict] = None
        self._jwks_cache_expires: Optional[datetime] = None
    
    def generate_login_url(self, 
                          scopes: List[str],
                          state: Optional[str] = None) -> Tuple[str, str]:
        """生成登录URL"""
        if state is None:
            state = secrets.token_urlsafe(32)
        
        params = {
            "response_type": "code",
            "redirect_uri": self.callback_url,
            "client_id": self.client_id,
            "scope": " ".join(scopes),
            "state": state
        }
        
        login_url = f"{self.authorize_url}?{urlencode(params)}"
        
        logger.info(
            "生成EVE SSO登录URL",
            scopes=scopes,
            state=state,
            callback_url=self.callback_url
        )
        
        return login_url, state
    
    async def exchange_code_for_tokens(self, 
                                     authorization_code: str,
                                     state: str) -> Dict[str, any]:
        """交换授权码获取令牌"""
        # 准备请求数据
        data = {
            "grant_type": "authorization_code",
            "code": authorization_code,
            "redirect_uri": self.callback_url
        }
        
        # 准备认证头
        auth_string = f"{self.client_id}:{self.client_secret}"
        auth_bytes = auth_string.encode('ascii')
        auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
        
        headers = {
            "Authorization": f"Basic {auth_b64}",
            "Content-Type": "application/x-www-form-urlencoded",
            "User-Agent": settings.esi_user_agent
        }
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.token_url,
                    data=data,
                    headers=headers,
                    timeout=30.0
                )
                
                if response.status_code != 200:
                    logger.error(
                        "EVE SSO令牌交换失败",
                        status_code=response.status_code,
                        response_text=response.text
                    )
                    raise ESIAuthenticationError(f"Token exchange failed: {response.text}")
                
                token_data = response.json()
                
                # 验证访问令牌
                character_info = await self._verify_access_token(token_data["access_token"])
                
                # 组合返回数据
                result = {
                    "access_token": token_data["access_token"],
                    "refresh_token": token_data["refresh_token"],
                    "expires_in": token_data["expires_in"],
                    "token_type": token_data.get("token_type", "Bearer"),
                    "character_id": character_info["sub"].split(":")[-1],
                    "character_name": character_info["name"],
                    "scopes": character_info.get("scp", []),
                    "expires_at": datetime.utcnow() + timedelta(seconds=token_data["expires_in"])
                }
                
                logger.info(
                    "EVE SSO令牌交换成功",
                    character_id=result["character_id"],
                    character_name=result["character_name"],
                    scopes=result["scopes"]
                )
                
                return result
                
        except httpx.RequestError as e:
            logger.error("EVE SSO请求失败", error=str(e))
            raise ESIAuthenticationError(f"SSO request failed: {str(e)}")
    
    async def refresh_access_token(self, refresh_token: str) -> Dict[str, any]:
        """刷新访问令牌"""
        data = {
            "grant_type": "refresh_token",
            "refresh_token": refresh_token
        }
        
        # 准备认证头
        auth_string = f"{self.client_id}:{self.client_secret}"
        auth_bytes = auth_string.encode('ascii')
        auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
        
        headers = {
            "Authorization": f"Basic {auth_b64}",
            "Content-Type": "application/x-www-form-urlencoded",
            "User-Agent": settings.esi_user_agent
        }
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.token_url,
                    data=data,
                    headers=headers,
                    timeout=30.0
                )
                
                if response.status_code != 200:
                    logger.error(
                        "EVE SSO令牌刷新失败",
                        status_code=response.status_code,
                        response_text=response.text
                    )
                    raise TokenRefreshError(0, f"Token refresh failed: {response.text}")
                
                token_data = response.json()
                
                # 验证新的访问令牌
                character_info = await self._verify_access_token(token_data["access_token"])
                
                result = {
                    "access_token": token_data["access_token"],
                    "refresh_token": token_data.get("refresh_token", refresh_token),
                    "expires_in": token_data["expires_in"],
                    "token_type": token_data.get("token_type", "Bearer"),
                    "character_id": character_info["sub"].split(":")[-1],
                    "character_name": character_info["name"],
                    "scopes": character_info.get("scp", []),
                    "expires_at": datetime.utcnow() + timedelta(seconds=token_data["expires_in"])
                }
                
                logger.info(
                    "EVE SSO令牌刷新成功",
                    character_id=result["character_id"],
                    character_name=result["character_name"]
                )
                
                return result
                
        except httpx.RequestError as e:
            logger.error("EVE SSO刷新请求失败", error=str(e))
            raise TokenRefreshError(0, f"SSO refresh request failed: {str(e)}")
    
    async def revoke_token(self, token: str, token_type: str = "refresh_token"):
        """撤销令牌"""
        data = {
            "token": token,
            "token_type_hint": token_type
        }
        
        # 准备认证头
        auth_string = f"{self.client_id}:{self.client_secret}"
        auth_bytes = auth_string.encode('ascii')
        auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
        
        headers = {
            "Authorization": f"Basic {auth_b64}",
            "Content-Type": "application/x-www-form-urlencoded",
            "User-Agent": settings.esi_user_agent
        }
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.revoke_url,
                    data=data,
                    headers=headers,
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    logger.info("EVE SSO令牌撤销成功", token_type=token_type)
                else:
                    logger.warning(
                        "EVE SSO令牌撤销失败",
                        status_code=response.status_code,
                        response_text=response.text
                    )
                    
        except httpx.RequestError as e:
            logger.error("EVE SSO撤销请求失败", error=str(e))
    
    async def _verify_access_token(self, access_token: str) -> Dict[str, any]:
        """验证访问令牌"""
        try:
            # 获取JWT签名密钥
            jwks = await self._get_jwks()
            
            # 解码JWT令牌
            header = jwt.get_unverified_header(access_token)
            key = None
            
            for jwk in jwks["keys"]:
                if jwk["kid"] == header["kid"]:
                    key = jwk
                    break
            
            if not key:
                raise ESIAuthenticationError("JWT key not found")
            
            # 验证令牌
            payload = jwt.decode(
                access_token,
                key,
                algorithms=["RS256"],
                audience="EVE Online",
                issuer="login.eveonline.com"
            )
            
            return payload
            
        except JWTError as e:
            logger.error("JWT令牌验证失败", error=str(e))
            raise ESIAuthenticationError(f"JWT verification failed: {str(e)}")
    
    async def _get_jwks(self) -> Dict[str, any]:
        """获取JWT签名密钥"""
        # 检查缓存
        if (self._jwks_cache and 
            self._jwks_cache_expires and 
            datetime.utcnow() < self._jwks_cache_expires):
            return self._jwks_cache
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    self.jwks_url,
                    headers={"User-Agent": settings.esi_user_agent},
                    timeout=30.0
                )
                
                if response.status_code != 200:
                    raise ESIAuthenticationError(f"JWKS fetch failed: {response.text}")
                
                jwks = response.json()
                
                # 缓存1小时
                self._jwks_cache = jwks
                self._jwks_cache_expires = datetime.utcnow() + timedelta(hours=1)
                
                return jwks
                
        except httpx.RequestError as e:
            logger.error("JWKS获取失败", error=str(e))
            raise ESIAuthenticationError(f"JWKS request failed: {str(e)}")


# 常用的ESI权限范围
ESI_SCOPES = {
    # 角色相关
    "character_info": "esi-characters.read_characters.v1",
    "character_location": "esi-location.read_location.v1",
    "character_online": "esi-location.read_online.v1",
    "character_ship": "esi-location.read_ship_type.v1",
    "character_skills": "esi-skills.read_skills.v1",
    "character_skillqueue": "esi-skills.read_skillqueue.v1",
    "character_attributes": "esi-skills.read_skills.v1",
    "character_wallet": "esi-wallet.read_character_wallet.v1",
    "character_assets": "esi-assets.read_assets.v1",
    "character_contracts": "esi-contracts.read_character_contracts.v1",
    "character_industry": "esi-industry.read_character_jobs.v1",
    "character_mail": "esi-mail.read_mail.v1",
    
    # 公司相关
    "corporation_info": "esi-corporations.read_corporation_membership.v1",
    "corporation_assets": "esi-assets.read_corporation_assets.v1",
    "corporation_contracts": "esi-contracts.read_corporation_contracts.v1",
    "corporation_industry": "esi-industry.read_corporation_jobs.v1",
    
    # 市场相关
    "market_orders": "esi-markets.read_character_orders.v1",
    "market_structures": "esi-markets.structure_markets.v1",
    
    # 宇宙相关
    "universe_structures": "esi-universe.read_structures.v1",
}
