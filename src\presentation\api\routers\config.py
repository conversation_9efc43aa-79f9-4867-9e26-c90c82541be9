"""
配置管理API路由
提供配置查询和管理接口
"""
from fastapi import APIRouter, Depends, HTTPException, status
from typing import Dict, Any, List
from pydantic import BaseModel

from ...infrastructure.config.unified_config import config_manager
from ...infrastructure.monitoring.error_monitor import error_monitor
from ..dependencies import get_current_user, require_admin


router = APIRouter(prefix="/config", tags=["配置管理"])


class ConfigUpdateRequest(BaseModel):
    """配置更新请求"""
    key: str
    value: str
    source_type: str = "environment"


class ConfigValidationResponse(BaseModel):
    """配置验证响应"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    suggestions: List[str]


@router.get("/frontend")
async def get_frontend_config() -> Dict[str, Any]:
    """获取前端配置"""
    try:
        config = config_manager.sync_frontend_config()
        return {
            "success": True,
            "data": config
        }
    except Exception as e:
        error_monitor.report_error(
            exception=e,
            context={"endpoint": "/config/frontend"}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取前端配置失败"
        )


@router.get("/oauth/{provider}")
async def get_oauth_config(
    provider: str,
    current_user = Depends(get_current_user)
) -> Dict[str, Any]:
    """获取OAuth配置（敏感信息已脱敏）"""
    try:
        oauth_config = config_manager.get_oauth_config(provider)
        
        if not oauth_config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"未找到 {provider} 的OAuth配置"
            )
        
        # 脱敏处理
        safe_config = {
            "client_id": oauth_config.client_id,
            "callback_url": oauth_config.callback_url,
            "scopes": oauth_config.scopes,
            "authorization_url": oauth_config.authorization_url,
            "token_url": oauth_config.token_url,
            "verify_url": oauth_config.verify_url
        }
        
        return {
            "success": True,
            "data": safe_config
        }
        
    except HTTPException:
        raise
    except Exception as e:
        error_monitor.report_error(
            exception=e,
            context={"endpoint": f"/config/oauth/{provider}", "user_id": str(current_user.id)}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取OAuth配置失败"
        )


@router.get("/validation")
async def validate_config(
    current_user = Depends(get_current_user),
    _: None = Depends(require_admin)
) -> ConfigValidationResponse:
    """验证配置一致性"""
    try:
        result = config_manager.validate_config_consistency()
        
        return ConfigValidationResponse(
            is_valid=result.is_valid,
            errors=result.errors,
            warnings=result.warnings,
            suggestions=result.suggestions
        )
        
    except Exception as e:
        error_monitor.report_error(
            exception=e,
            context={"endpoint": "/config/validation", "user_id": str(current_user.id)}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="配置验证失败"
        )


@router.post("/update")
async def update_config(
    request: ConfigUpdateRequest,
    current_user = Depends(get_current_user),
    _: None = Depends(require_admin)
) -> Dict[str, Any]:
    """更新配置"""
    try:
        # 安全检查：不允许更新敏感配置
        sensitive_keys = [
            'EVE_CLIENT_SECRET',
            'DATABASE_URL',
            'SECRET_KEY'
        ]
        
        if request.key in sensitive_keys:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"不允许通过API更新敏感配置: {request.key}"
            )
        
        success = config_manager.update_config(
            key=request.key,
            value=request.value,
            source_type=request.source_type
        )
        
        if success:
            return {
                "success": True,
                "message": f"配置 {request.key} 更新成功"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="配置更新失败"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        error_monitor.report_error(
            exception=e,
            context={
                "endpoint": "/config/update",
                "user_id": str(current_user.id),
                "config_key": request.key
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="配置更新失败"
        )


@router.get("/history/{key}")
async def get_config_history(
    key: str,
    current_user = Depends(get_current_user),
    _: None = Depends(require_admin)
) -> Dict[str, Any]:
    """获取配置变更历史"""
    try:
        history = config_manager.get_config_history(key)
        
        return {
            "success": True,
            "data": {
                "key": key,
                "history": history
            }
        }
        
    except Exception as e:
        error_monitor.report_error(
            exception=e,
            context={
                "endpoint": f"/config/history/{key}",
                "user_id": str(current_user.id)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取配置历史失败"
        )


@router.get("/export")
async def export_config(
    format: str = "json",
    current_user = Depends(get_current_user),
    _: None = Depends(require_admin)
) -> Dict[str, Any]:
    """导出配置"""
    try:
        if format not in ["json", "env"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不支持的导出格式，支持: json, env"
            )
        
        config_data = config_manager.export_config(format)
        
        return {
            "success": True,
            "data": {
                "format": format,
                "config": config_data
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        error_monitor.report_error(
            exception=e,
            context={
                "endpoint": "/config/export",
                "user_id": str(current_user.id),
                "format": format
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="配置导出失败"
        )


@router.get("/sync-status")
async def get_sync_status(
    current_user = Depends(get_current_user),
    _: None = Depends(require_admin)
) -> Dict[str, Any]:
    """获取配置同步状态"""
    try:
        # 运行配置同步检查
        from ....scripts.validation.config_sync_checker import ConfigSyncChecker
        
        checker = ConfigSyncChecker()
        report = checker.generate_sync_report()
        
        return {
            "success": True,
            "data": report
        }
        
    except Exception as e:
        error_monitor.report_error(
            exception=e,
            context={
                "endpoint": "/config/sync-status",
                "user_id": str(current_user.id)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取同步状态失败"
        )


@router.post("/validate-third-party")
async def validate_third_party_apis(
    current_user = Depends(get_current_user),
    _: None = Depends(require_admin)
) -> Dict[str, Any]:
    """验证第三方API配置"""
    try:
        # 运行第三方API验证
        from ....scripts.validation.third_party_api_validator import ThirdPartyAPIValidator
        
        validator = ThirdPartyAPIValidator()
        report = validator.generate_validation_report()
        
        return {
            "success": True,
            "data": report
        }
        
    except Exception as e:
        error_monitor.report_error(
            exception=e,
            context={
                "endpoint": "/config/validate-third-party",
                "user_id": str(current_user.id)
            }
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="第三方API验证失败"
        )
