import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';

const MockEVELogin: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);
  
  const scopes = searchParams.get('scopes')?.split(',') || [];
  const state = searchParams.get('state') || '';

  const handleMockLogin = async () => {
    setIsLoading(true);
    
    // 模拟登录延迟
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 模拟成功的EVE登录，重定向到回调
    const callbackUrl = `http://localhost:8000/auth/callback?code=mock_auth_code_123&state=${state}`;
    window.location.href = callbackUrl;
  };

  const handleCancel = () => {
    navigate('/');
  };

  return (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center px-4">
      <div className="max-w-md w-full bg-gray-800 rounded-lg shadow-xl p-8">
        {/* EVE Online Logo */}
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-orange-500 rounded-full mx-auto mb-4 flex items-center justify-center">
            <span className="text-white font-bold text-xl">EVE</span>
          </div>
          <h1 className="text-2xl font-bold text-white mb-2">EVE Online SSO</h1>
          <p className="text-gray-400 text-sm">开发模式 - 模拟登录</p>
        </div>

        {/* Application Info */}
        <div className="bg-gray-700 rounded-lg p-4 mb-6">
          <h2 className="text-white font-semibold mb-2">应用程序请求权限</h2>
          <p className="text-gray-300 text-sm mb-3">EVE Industrial Assistant</p>
          
          <div className="space-y-2">
            <p className="text-gray-400 text-xs">请求的权限范围：</p>
            <div className="max-h-32 overflow-y-auto">
              {scopes.map((scope, index) => (
                <div key={index} className="text-xs text-gray-300 bg-gray-600 rounded px-2 py-1 mb-1">
                  {scope}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Mock Character Selection */}
        <div className="bg-gray-700 rounded-lg p-4 mb-6">
          <h3 className="text-white font-semibold mb-3">选择角色</h3>
          <div className="space-y-2">
            <div className="flex items-center p-2 bg-gray-600 rounded hover:bg-gray-500 cursor-pointer">
              <div className="w-8 h-8 bg-blue-500 rounded-full mr-3 flex items-center justify-center">
                <span className="text-white text-xs font-bold">TC</span>
              </div>
              <div>
                <p className="text-white text-sm font-medium">Test Character</p>
                <p className="text-gray-400 text-xs">Caldari State</p>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-3">
          <button
            onClick={handleMockLogin}
            disabled={isLoading}
            className="w-full bg-orange-600 hover:bg-orange-700 disabled:bg-orange-800 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center"
          >
            {isLoading ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                授权中...
              </>
            ) : (
              '授权访问'
            )}
          </button>
          
          <button
            onClick={handleCancel}
            disabled={isLoading}
            className="w-full bg-gray-600 hover:bg-gray-700 disabled:bg-gray-800 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200"
          >
            取消
          </button>
        </div>

        {/* Development Notice */}
        <div className="mt-6 p-3 bg-yellow-900 border border-yellow-600 rounded-lg">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            <div>
              <p className="text-yellow-200 text-xs font-medium">开发模式</p>
              <p className="text-yellow-300 text-xs">这是一个模拟的EVE登录页面，用于开发和测试。</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MockEVELogin;
