#!/usr/bin/env python3
"""
EVE SSO配置助手 - 实时获取ngrok地址并提供配置指导
"""
import sys
import time
import urllib.request
import json

def get_ngrok_url():
    """获取ngrok公网地址"""
    try:
        with urllib.request.urlopen('http://127.0.0.1:4040/api/tunnels', timeout=5) as response:
            data = json.loads(response.read().decode())
            tunnels = data.get('tunnels', [])
            
            for tunnel in tunnels:
                if tunnel.get('proto') == 'https':
                    return tunnel.get('public_url')
            return None
    except:
        return None

def display_config_instructions(public_url):
    """显示配置说明"""
    print("\n" + "=" * 70)
    print("🔧 EVE Developer Portal 配置说明")
    print("=" * 70)
    
    print(f"\n📍 您的ngrok地址: {public_url}")
    print(f"📍 回调URL: {public_url}/auth/callback")
    print(f"📍 登录URL: {public_url}/auth/login")
    
    print("\n🚀 配置步骤:")
    print("1. 访问 EVE Developer Portal: https://developers.eveonline.com/")
    print("2. 使用EVE Online账号登录")
    print("3. 点击 'Manage Applications'")
    print("4. 选择现有应用或点击 'Create New Application'")
    
    print("\n⚙️ 应用配置:")
    print("   Application Name: EVE Online Assistant")
    print("   Description: EVE角色管理和数据分析工具")
    print("   Connection Type: Authentication & API Access")
    
    print(f"\n🔑 重要配置项:")
    print("   Callback URL: " + "=" * 20)
    print(f"   {public_url}/auth/callback")
    print("   " + "=" * 50)
    
    print("\n✅ 推荐权限范围:")
    scopes = [
        "esi-characters.read_characters.v1",
        "esi-characters.read_corporation_roles.v1", 
        "esi-location.read_location.v1",
        "esi-location.read_online.v1",
        "esi-skills.read_skills.v1",
        "esi-skills.read_skillqueue.v1",
        "esi-assets.read_assets.v1",
        "esi-mail.read_mail.v1",
        "esi-clones.read_clones.v1",
        "esi-wallet.read_character_wallet.v1",
        "esi-markets.read_character_orders.v1",
        "esi-industry.read_character_jobs.v1"
    ]
    
    for scope in scopes:
        print(f"   ✅ {scope}")
    
    print("\n🎯 配置完成后:")
    print("1. 复制 Client ID 和 Secret Key")
    print("2. 更新应用配置文件")
    print("3. 重启应用")
    print(f"4. 访问登录页面: {public_url}/auth/login")
    
    print("\n🌐 测试地址:")
    print(f"   API文档: {public_url}/docs")
    print(f"   健康检查: {public_url}/health/")
    print(f"   EVE SSO登录: {public_url}/auth/login")

def wait_for_ngrok():
    """等待ngrok启动"""
    print("🔍 等待ngrok隧道启动...")
    print("   这可能需要几秒钟时间...")
    
    for i in range(30):  # 等待最多30秒
        url = get_ngrok_url()
        if url:
            print(f"✅ ngrok隧道已启动!")
            return url
        
        print(f"   等待中... ({i+1}/30)")
        time.sleep(1)
    
    return None

def main():
    """主函数"""
    print("🚀 EVE SSO配置助手")
    print("=" * 50)
    
    # 等待ngrok启动
    public_url = wait_for_ngrok()
    
    if public_url:
        # 显示配置说明
        display_config_instructions(public_url)
        
        # 打开EVE Developer Portal
        print("\n🌐 正在打开EVE Developer Portal...")
        try:
            import webbrowser
            webbrowser.open('https://developers.eveonline.com/')
            print("✅ EVE Developer Portal已在浏览器中打开")
        except:
            print("❌ 无法自动打开浏览器，请手动访问:")
            print("   https://developers.eveonline.com/")
        
        # 等待用户配置
        print("\n⏳ 请在EVE Developer Portal中完成配置...")
        print("   配置完成后按 Enter 键继续...")
        input()
        
        # 测试配置
        print("\n🧪 测试EVE SSO配置...")
        print(f"   访问登录页面: {public_url}/auth/login")
        
        try:
            webbrowser.open(f'{public_url}/auth/login')
            print("✅ 登录页面已在浏览器中打开")
        except:
            print("❌ 无法自动打开浏览器，请手动访问登录页面")
        
        print("\n🎉 配置完成！")
        print("   如果登录成功，您就可以开始使用EVE Online Assistant了！")
        
    else:
        print("❌ ngrok隧道启动失败或超时")
        print("\n💡 备用方案 - 使用本地地址:")
        print("   本地地址: http://127.0.0.1:8000")
        print("   回调URL: http://127.0.0.1:8000/auth/callback")
        print("   登录URL: http://127.0.0.1:8000/auth/login")
        print("\n   注意: 本地地址只能在本机测试，无法用于EVE SSO回调")

if __name__ == "__main__":
    main()
