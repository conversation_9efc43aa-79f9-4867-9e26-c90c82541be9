"""
认证领域异常
"""
from ..shared.value_objects import UserId, CharacterId
from ..shared.exceptions import DomainError


class AuthenticationDomainError(DomainError):
    """认证领域错误基类"""
    pass


class UserNotFoundError(AuthenticationDomainError):
    """用户未找到错误"""
    
    def __init__(self, user_id: UserId):
        self.user_id = user_id
        super().__init__(f"User {user_id} not found")


class UserAlreadyExistsError(AuthenticationDomainError):
    """用户已存在错误"""
    
    def __init__(self, identifier: str, identifier_type: str = "email"):
        self.identifier = identifier
        self.identifier_type = identifier_type
        super().__init__(f"User with {identifier_type} '{identifier}' already exists")


class InvalidCredentialsError(AuthenticationDomainError):
    """无效凭证错误"""
    
    def __init__(self, message: str = "Invalid credentials"):
        super().__init__(message)


class AccountLockedError(AuthenticationDomainError):
    """账户锁定错误"""
    
    def __init__(self, user_id: UserId, locked_until: str):
        self.user_id = user_id
        self.locked_until = locked_until
        super().__init__(f"Account {user_id} is locked until {locked_until}")


class AccountInactiveError(AuthenticationDomainError):
    """账户未激活错误"""
    
    def __init__(self, user_id: UserId):
        self.user_id = user_id
        super().__init__(f"Account {user_id} is inactive")


class EmailNotVerifiedError(AuthenticationDomainError):
    """邮箱未验证错误"""
    
    def __init__(self, user_id: UserId, email: str):
        self.user_id = user_id
        self.email = email
        super().__init__(f"Email {email} for user {user_id} is not verified")


class CharacterNotBoundError(AuthenticationDomainError):
    """角色未绑定错误"""
    
    def __init__(self, character_id: CharacterId):
        self.character_id = character_id
        super().__init__(f"Character {character_id} is not bound to this user")


class DuplicateCharacterBindingError(AuthenticationDomainError):
    """重复角色绑定错误"""
    
    def __init__(self, character_id: CharacterId):
        self.character_id = character_id
        super().__init__(f"Character {character_id} is already bound to this user")


class MaxCharacterBindingsExceededError(AuthenticationDomainError):
    """超过最大角色绑定数错误"""
    
    def __init__(self, max_bindings: int):
        self.max_bindings = max_bindings
        super().__init__(f"Maximum character bindings ({max_bindings}) exceeded")


class InvalidTokenError(AuthenticationDomainError):
    """无效令牌错误"""
    
    def __init__(self, message: str = "Invalid token"):
        super().__init__(message)


class TokenExpiredError(AuthenticationDomainError):
    """令牌过期错误"""
    
    def __init__(self, message: str = "Token has expired"):
        super().__init__(message)


class TokenRevokedError(AuthenticationDomainError):
    """令牌已撤销错误"""
    
    def __init__(self, message: str = "Token has been revoked"):
        super().__init__(message)


class SessionExpiredError(AuthenticationDomainError):
    """会话过期错误"""
    
    def __init__(self, session_id: str):
        self.session_id = session_id
        super().__init__(f"Session {session_id} has expired")


class SessionNotFoundError(AuthenticationDomainError):
    """会话未找到错误"""
    
    def __init__(self, session_id: str):
        self.session_id = session_id
        super().__init__(f"Session {session_id} not found")


class MaxActiveSessionsExceededError(AuthenticationDomainError):
    """超过最大活跃会话数错误"""
    
    def __init__(self, max_sessions: int):
        self.max_sessions = max_sessions
        super().__init__(f"Maximum active sessions ({max_sessions}) exceeded")


class InsufficientPermissionsError(AuthenticationDomainError):
    """权限不足错误"""
    
    def __init__(self, required_permissions: list, user_permissions: list = None):
        self.required_permissions = required_permissions
        self.user_permissions = user_permissions or []
        missing = set(required_permissions) - set(self.user_permissions)
        super().__init__(f"Insufficient permissions. Missing: {list(missing)}")


class InvalidScopeError(AuthenticationDomainError):
    """无效权限范围错误"""
    
    def __init__(self, scope: str):
        self.scope = scope
        super().__init__(f"Invalid scope: {scope}")


class ScopeNotGrantedError(AuthenticationDomainError):
    """权限范围未授予错误"""
    
    def __init__(self, scope: str, character_id: CharacterId):
        self.scope = scope
        self.character_id = character_id
        super().__init__(f"Scope '{scope}' not granted for character {character_id}")


class AuthenticationStateError(AuthenticationDomainError):
    """认证状态错误"""
    
    def __init__(self, state: str, message: str = "Invalid authentication state"):
        self.state = state
        super().__init__(f"{message}: {state}")


class PKCEVerificationError(AuthenticationDomainError):
    """PKCE验证错误"""
    
    def __init__(self, message: str = "PKCE verification failed"):
        super().__init__(message)


class RateLimitExceededError(AuthenticationDomainError):
    """速率限制超出错误"""
    
    def __init__(self, limit: int, window: str, retry_after: int = None):
        self.limit = limit
        self.window = window
        self.retry_after = retry_after
        message = f"Rate limit exceeded: {limit} requests per {window}"
        if retry_after:
            message += f". Retry after {retry_after} seconds"
        super().__init__(message)


class WeakPasswordError(AuthenticationDomainError):
    """弱密码错误"""
    
    def __init__(self, requirements: list):
        self.requirements = requirements
        super().__init__(f"Password does not meet requirements: {', '.join(requirements)}")


class PasswordReuseError(AuthenticationDomainError):
    """密码重用错误"""
    
    def __init__(self, message: str = "Password has been used recently"):
        super().__init__(message)


class TwoFactorRequiredError(AuthenticationDomainError):
    """需要双因素认证错误"""
    
    def __init__(self, user_id: UserId, methods: list):
        self.user_id = user_id
        self.methods = methods
        super().__init__(f"Two-factor authentication required. Available methods: {', '.join(methods)}")


class InvalidTwoFactorCodeError(AuthenticationDomainError):
    """无效双因素认证码错误"""
    
    def __init__(self, method: str):
        self.method = method
        super().__init__(f"Invalid two-factor authentication code for method: {method}")


class ApiKeyNotFoundError(AuthenticationDomainError):
    """API密钥未找到错误"""
    
    def __init__(self, key_id: str):
        self.key_id = key_id
        super().__init__(f"API key {key_id} not found")


class ApiKeyExpiredError(AuthenticationDomainError):
    """API密钥过期错误"""
    
    def __init__(self, key_id: str, expired_at: str):
        self.key_id = key_id
        self.expired_at = expired_at
        super().__init__(f"API key {key_id} expired at {expired_at}")


class ApiKeyRevokedError(AuthenticationDomainError):
    """API密钥已撤销错误"""
    
    def __init__(self, key_id: str):
        self.key_id = key_id
        super().__init__(f"API key {key_id} has been revoked")


class SecurityViolationError(AuthenticationDomainError):
    """安全违规错误"""
    
    def __init__(self, violation_type: str, details: dict = None):
        self.violation_type = violation_type
        self.details = details or {}
        super().__init__(f"Security violation detected: {violation_type}")


class ConcurrentLoginError(AuthenticationDomainError):
    """并发登录错误"""
    
    def __init__(self, user_id: UserId, max_concurrent: int):
        self.user_id = user_id
        self.max_concurrent = max_concurrent
        super().__init__(f"User {user_id} exceeded maximum concurrent logins ({max_concurrent})")
