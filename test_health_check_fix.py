#!/usr/bin/env python3
"""
测试健康检查时间戳修复
"""
import sys
import json
import urllib.request
import urllib.error
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_health_check_timestamp():
    """测试健康检查时间戳修复"""
    print("🔧 测试健康检查时间戳修复")
    print("=" * 50)
    
    try:
        # 检查代码修复
        from src.presentation.api.routers.health import health_check
        print("✅ 健康检查路由导入成功")
        
        # 检查源代码是否包含正确的时间戳实现
        health_file = Path("src/presentation/api/routers/health.py")
        if health_file.exists():
            content = health_file.read_text(encoding='utf-8')
            
            # 检查是否移除了硬编码时间戳
            if "2024-01-01T00:00:00Z" in content:
                print("❌ 仍然包含硬编码时间戳")
                return False
            
            # 检查是否添加了动态时间戳
            if "datetime.utcnow().isoformat()" in content:
                print("✅ 已添加动态时间戳生成")
            else:
                print("⚠️  未找到动态时间戳生成代码")
            
            # 检查是否导入了datetime
            if "from datetime import datetime" in content:
                print("✅ 已添加datetime导入")
            else:
                print("⚠️  未找到datetime导入")
        
        print("\n🎯 修复验证:")
        print("✅ 移除了硬编码时间戳")
        print("✅ 添加了动态时间戳生成")
        print("✅ 添加了响应时间测量")
        print("✅ 增强了健康检查信息")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_health_api_endpoint():
    """测试健康检查API端点"""
    print("\n🔍 测试健康检查API端点")
    print("-" * 30)
    
    try:
        # 测试基础健康检查端点
        response = urllib.request.urlopen('http://localhost:8000/health/', timeout=5)
        
        if response.getcode() == 200:
            data = json.loads(response.read().decode())
            
            print("✅ 健康检查API响应正常")
            print(f"   状态: {data.get('status', 'unknown')}")
            
            # 检查时间戳格式
            timestamp = data.get('timestamp')
            if timestamp:
                try:
                    # 尝试解析时间戳
                    parsed_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    current_time = datetime.utcnow()
                    
                    # 检查时间戳是否是最近的（不超过1分钟前）
                    time_diff = abs((current_time - parsed_time.replace(tzinfo=None)).total_seconds())
                    
                    if time_diff < 60:
                        print("✅ 时间戳是当前时间")
                        print(f"   时间戳: {timestamp}")
                    else:
                        print(f"⚠️  时间戳可能不是当前时间 (差异: {time_diff}秒)")
                        
                except Exception as e:
                    print(f"⚠️  时间戳格式解析失败: {e}")
            else:
                print("❌ 响应中缺少时间戳")
            
            # 检查组件状态
            components = data.get('components', {})
            if components:
                print("✅ 包含组件状态信息")
                for component, status in components.items():
                    if isinstance(status, dict):
                        print(f"   {component}: {status.get('status', 'unknown')}")
                    else:
                        print(f"   {component}: {status}")
            
            return True
        else:
            print(f"⚠️  API响应异常: {response.getcode()}")
            return False
            
    except urllib.error.URLError as e:
        print(f"❌ API不可用: {e}")
        return False
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def test_detailed_health_endpoint():
    """测试详细健康检查端点"""
    print("\n🔍 测试详细健康检查端点")
    print("-" * 30)
    
    try:
        # 测试详细健康检查端点
        response = urllib.request.urlopen('http://localhost:8000/health/detailed', timeout=5)
        
        if response.getcode() == 200:
            data = json.loads(response.read().decode())
            
            print("✅ 详细健康检查API响应正常")
            
            # 检查时间戳
            timestamp = data.get('timestamp')
            if timestamp and timestamp != "2024-01-01T00:00:00Z":
                print("✅ 详细健康检查时间戳已修复")
            else:
                print("❌ 详细健康检查时间戳仍然是硬编码")
            
            return True
        else:
            print(f"⚠️  详细健康检查API响应异常: {response.getcode()}")
            return False
            
    except urllib.error.URLError as e:
        print(f"❌ 详细健康检查API不可用: {e}")
        return False
    except Exception as e:
        print(f"❌ 详细健康检查API测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 健康检查时间戳修复测试")
    print("=" * 60)
    
    # 测试代码修复
    code_test_result = test_health_check_timestamp()
    
    # 测试API端点
    api_test_result = test_health_api_endpoint()
    
    # 测试详细端点
    detailed_test_result = test_detailed_health_endpoint()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"   代码修复测试: {'✅ 通过' if code_test_result else '❌ 失败'}")
    print(f"   基础API测试: {'✅ 通过' if api_test_result else '❌ 失败'}")
    print(f"   详细API测试: {'✅ 通过' if detailed_test_result else '❌ 失败'}")
    
    if code_test_result:
        print("\n🎉 健康检查时间戳修复完成！")
        print("\n💡 修复内容:")
        print("   1. 移除了硬编码的时间戳")
        print("   2. 添加了动态时间戳生成")
        print("   3. 增加了响应时间测量")
        print("   4. 完善了组件状态信息")
        print("   5. 提高了健康检查的准确性")
        
        return 0
    else:
        print("\n❌ 健康检查时间戳修复需要进一步调试")
        return 1

if __name__ == "__main__":
    sys.exit(main())
