"""
认证相关数据库模型
"""
from sqlalchemy import (
    Column, Integer, String, BigInteger, Boolean,
    DateTime, Text, ForeignKey, Index
)
from sqlalchemy.orm import relationship

from .base import BaseModel


class UserModel(BaseModel):
    """用户模型"""
    __tablename__ = "users"
    
    # 用户基本信息
    username = Column(String(255), nullable=False, unique=True, index=True)
    email = Column(String(255), nullable=True, unique=True, index=True)
    hashed_password = Column(String(255), nullable=True)  # 本地账户密码
    
    # 用户状态
    is_active = Column(Boolean, default=True, index=True)
    is_superuser = Column(Boolean, default=False)
    
    # EVE相关
    main_character_id = Column(BigInteger, nullable=True, index=True)
    
    # 登录信息
    last_login = Column(DateTime, nullable=True)
    login_count = Column(Integer, default=0)
    
    # 关联关系
    tokens = relationship("TokenModel", back_populates="user")
    
    # 索引
    __table_args__ = (
        Index('idx_user_active', 'is_active'),
        Index('idx_user_main_char', 'main_character_id'),
    )


class TokenModel(BaseModel):
    """令牌模型"""
    __tablename__ = "tokens"
    
    # 关联信息
    user_id = Column(Integer, ForeignKey('users.id'), nullable=True, index=True)
    character_id = Column(BigInteger, ForeignKey('characters.character_id'), nullable=False, index=True)
    
    # 令牌信息
    access_token = Column(Text, nullable=False)
    refresh_token = Column(Text, nullable=False)
    token_type = Column(String(20), default="Bearer")
    
    # 权限范围
    scopes = Column(Text, nullable=False)  # JSON array of scopes
    
    # 过期时间
    expires_at = Column(DateTime, nullable=False, index=True)
    
    # 令牌状态
    is_active = Column(Boolean, default=True, index=True)
    last_used_at = Column(DateTime, nullable=True)
    use_count = Column(Integer, default=0)
    
    # 刷新信息
    last_refresh_at = Column(DateTime, nullable=True)
    refresh_count = Column(Integer, default=0)
    refresh_error_count = Column(Integer, default=0)
    refresh_error_message = Column(Text, nullable=True)
    
    # 关联关系
    user = relationship("UserModel", back_populates="tokens")
    character = relationship("CharacterModel", back_populates="tokens")
    
    # 索引
    __table_args__ = (
        Index('idx_token_expires', 'expires_at', 'is_active'),
        Index('idx_token_character', 'character_id', 'is_active'),
        Index('idx_token_refresh', 'last_refresh_at', 'refresh_error_count'),
    )
