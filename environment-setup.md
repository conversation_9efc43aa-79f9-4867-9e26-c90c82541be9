# EVE Online Assistant - 环境配置指南

## 🎯 当前环境状态

### ✅ 已创建的隔离环境
- **环境名称**: `eve-assistant`
- **Python版本**: 3.11.13
- **环境类型**: Conda环境
- **位置**: `C:\Users\<USER>\anaconda3\envs\eve-assistant`

## 🔧 如何使用隔离环境

### 方法1: 激活环境后使用（推荐）
```bash
# 激活conda环境
conda activate eve-assistant

# 启动应用
python scripts/start.py

# 或直接使用uvicorn
python -m uvicorn src.presentation.api.main:app --host 127.0.0.1 --port 8000 --reload

# 工作完成后退出环境
conda deactivate
```

### 方法2: 直接使用环境Python（无需激活）
```bash
# Windows路径
/c/Users/<USER>/anaconda3/envs/eve-assistant/python.exe scripts/start.py

# 或使用Windows格式
C:\Users\<USER>\anaconda3\envs\eve-assistant\python.exe scripts/start.py
```

## 📦 依赖管理

### 当前配置文件
1. **pyproject.toml** - 现代Python项目配置（主要）
2. **requirements.txt** - 传统依赖列表（备用）

### 安装依赖的方法

#### 使用pyproject.toml（推荐）
```bash
# 激活环境
conda activate eve-assistant

# 安装项目依赖
pip install -e .

# 安装开发依赖
pip install -e .[dev]
```

#### 使用requirements.txt（备用）
```bash
# 激活环境
conda activate eve-assistant

# 安装依赖
pip install -r requirements.txt
```

## 🛠️ IDE配置

### VS Code配置
创建 `.vscode/settings.json`:
```json
{
    "python.defaultInterpreterPath": "C:/Users/<USER>/anaconda3/envs/eve-assistant/python.exe",
    "python.terminal.activateEnvironment": true,
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": false,
    "python.linting.flake8Enabled": true,
    "python.formatting.provider": "black"
}
```

### PyCharm配置
1. File → Settings → Project → Python Interpreter
2. 选择 "Existing environment"
3. 路径: `C:\Users\<USER>\anaconda3\envs\eve-assistant\python.exe`

## 🚀 启动脚本配置

### 当前启动脚本
- `scripts/start.py` - 主启动脚本（自动检测环境）
- `scripts/start-simple.py` - 简化启动脚本
- `scripts/env-manager.bat` - 环境管理器

### 启动脚本自动检测
当前的 `scripts/start.py` 会自动检测并安装依赖，但建议修改为优先使用隔离环境。

## 📋 环境管理最佳实践

### 日常开发流程
```bash
# 1. 激活环境
conda activate eve-assistant

# 2. 更新代码
git pull

# 3. 安装/更新依赖（如有需要）
pip install -e .

# 4. 启动应用
python scripts/start.py

# 5. 开发完成后退出
conda deactivate
```

### 依赖管理
```bash
# 添加新依赖到pyproject.toml，然后：
pip install -e .

# 或直接安装并更新配置文件
pip install new-package
pip freeze > requirements.txt  # 更新requirements.txt
```

### 环境维护
```bash
# 查看环境列表
conda env list

# 查看已安装包
conda activate eve-assistant
pip list

# 导出环境配置
conda env export > environment.yml

# 清理环境（如需要）
conda remove -n eve-assistant --all
```

## 🔄 环境切换

### 从系统环境切换到隔离环境
```bash
# 检查当前Python
python --version
which python  # 或 where python

# 激活隔离环境
conda activate eve-assistant

# 确认切换成功
python --version
which python
```

## 🎯 推荐配置

### 1. 创建环境激活脚本
创建 `activate-env.bat`:
```batch
@echo off
echo 🚀 激活EVE Assistant开发环境...
call conda activate eve-assistant
echo ✅ 环境已激活: eve-assistant
echo 💡 使用 'python scripts/start.py' 启动应用
echo 💡 使用 'conda deactivate' 退出环境
cmd /k
```

### 2. 使用智能启动脚本
直接使用改进后的启动脚本:
```bash
# 智能启动（自动检测和切换环境）
python scripts/start.py
```

## ❓ 常见问题

### Q: 为什么不使用.venv文件夹？
A: 我们使用Conda环境管理，它将环境存储在Conda目录中，不需要项目根目录的.venv文件夹。

### Q: requirements.txt还需要吗？
A: 保留作为备用，但主要使用pyproject.toml进行依赖管理。

### Q: 如何确认使用的是隔离环境？
A: 运行 `python --version` 和 `which python`，确认路径包含 `eve-assistant`。

### Q: 可以删除系统环境中的包吗？
A: 可以，隔离环境完全独立，不依赖系统环境的包。

## 🎉 总结

您的项目现在使用：
- ✅ **Conda隔离环境** - 完全独立的Python环境
- ✅ **pyproject.toml** - 现代依赖管理
- ✅ **requirements.txt** - 传统备用方案
- ✅ **自动化脚本** - 简化环境管理

**推荐工作流程**: 直接运行 `python scripts/start.py` (自动环境检测和切换)
