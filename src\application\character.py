"""
角色应用服务
"""
from datetime import datetime
from typing import List, Optional, Dict, Any
from decimal import Decimal

from ..domain.character.entities import Character
from ..domain.character.repositories import CharacterRepository
from ..domain.character.services import SkillTrainingService, CharacterAnalysisService
from ..domain.shared.value_objects import CharacterId, CorporationId, AllianceId, Money
from ..domain.shared.exceptions import CharacterNotFoundError, DomainValidationError
from ..infrastructure.esi import ESIService, ESIClient
from ..infrastructure.config.logging import get_logger

logger = get_logger(__name__)


class CharacterApplicationService:
    """角色应用服务"""
    
    def __init__(self, 
                 character_repository: CharacterRepository,
                 esi_service: ESIService,
                 skill_training_service: SkillTrainingService,
                 character_analysis_service: CharacterAnalysisService):
        self.character_repository = character_repository
        self.esi_service = esi_service
        self.skill_training_service = skill_training_service
        self.character_analysis_service = character_analysis_service
    
    async def get_character_by_id(self, character_id: int) -> Optional[Dict[str, Any]]:
        """获取角色信息"""
        try:
            char_id = CharacterId(character_id)
            character = await self.character_repository.get_by_id(char_id)
            
            if not character:
                return None
            
            return self._character_to_dict(character)
            
        except Exception as e:
            logger.error("获取角色信息失败", character_id=character_id, error=str(e))
            raise
    
    async def get_character_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """根据名称获取角色"""
        try:
            character = await self.character_repository.get_by_name(name)
            
            if not character:
                return None
            
            return self._character_to_dict(character)
            
        except Exception as e:
            logger.error("根据名称获取角色失败", name=name, error=str(e))
            raise
    
    async def sync_character_from_esi(self, character_id: int, access_token: str) -> Dict[str, Any]:
        """从ESI同步角色数据"""
        try:
            logger.info("开始同步角色数据", character_id=character_id)
            
            # 获取角色基本信息
            esi_character = await self.esi_service.get_character_info(character_id)
            
            # 获取现有角色或创建新角色
            char_id = CharacterId(character_id)
            character = await self.character_repository.get_by_id(char_id)
            
            if not character:
                # 创建新角色
                character = Character(
                    character_id=char_id,
                    name=esi_character.name,
                    corporation_id=CorporationId(esi_character.corporation_id),
                    birthday=esi_character.birthday,
                    race_id=esi_character.race_id,
                    bloodline_id=esi_character.bloodline_id,
                    gender=esi_character.gender,
                    alliance_id=AllianceId(esi_character.alliance_id) if esi_character.alliance_id else None,
                    faction_id=esi_character.faction_id,
                    description=esi_character.description or "",
                    title=esi_character.title or ""
                )
            else:
                # 更新现有角色
                character.update_basic_info(
                    corporation_id=CorporationId(esi_character.corporation_id),
                    alliance_id=AllianceId(esi_character.alliance_id) if esi_character.alliance_id else None,
                    faction_id=esi_character.faction_id,
                    description=esi_character.description or "",
                    title=esi_character.title or ""
                )
            
            # 同步详细数据
            await self._sync_character_details(character, access_token)
            
            # 保存角色
            await self.character_repository.save(character)
            
            logger.info("角色数据同步完成", character_id=character_id)
            
            return self._character_to_dict(character)
            
        except Exception as e:
            logger.error("同步角色数据失败", character_id=character_id, error=str(e))
            raise
    
    async def get_corporation_members(self, corporation_id: int) -> List[Dict[str, Any]]:
        """获取公司成员列表"""
        try:
            corp_id = CorporationId(corporation_id)
            characters = await self.character_repository.find_by_corporation(corp_id)
            
            return [self._character_to_dict(char) for char in characters]
            
        except Exception as e:
            logger.error("获取公司成员失败", corporation_id=corporation_id, error=str(e))
            raise
    
    async def get_alliance_members(self, alliance_id: int) -> List[Dict[str, Any]]:
        """获取联盟成员列表"""
        try:
            alliance_id_vo = AllianceId(alliance_id)
            characters = await self.character_repository.find_by_alliance(alliance_id_vo)
            
            return [self._character_to_dict(char) for char in characters]
            
        except Exception as e:
            logger.error("获取联盟成员失败", alliance_id=alliance_id, error=str(e))
            raise
    
    async def get_online_characters(self) -> List[Dict[str, Any]]:
        """获取在线角色列表"""
        try:
            characters = await self.character_repository.find_online_characters()
            
            return [self._character_to_dict(char) for char in characters]
            
        except Exception as e:
            logger.error("获取在线角色失败", error=str(e))
            raise
    
    async def calculate_skill_training_time(self, 
                                          character_id: int, 
                                          skill_id: int, 
                                          target_level: int) -> Dict[str, Any]:
        """计算技能训练时间"""
        try:
            char_id = CharacterId(character_id)
            character = await self.character_repository.get_by_id(char_id)
            
            if not character:
                raise CharacterNotFoundError(character_id)
            
            training_time = self.skill_training_service.calculate_training_time(
                character, skill_id, target_level
            )
            
            return {
                "character_id": character_id,
                "skill_id": skill_id,
                "target_level": target_level,
                "training_time_seconds": int(training_time.total_seconds()),
                "training_time_days": training_time.days,
                "training_time_hours": training_time.seconds // 3600,
                "training_time_minutes": (training_time.seconds % 3600) // 60
            }
            
        except Exception as e:
            logger.error("计算技能训练时间失败", 
                        character_id=character_id, 
                        skill_id=skill_id, 
                        target_level=target_level, 
                        error=str(e))
            raise
    
    async def get_character_analysis(self, character_id: int) -> Dict[str, Any]:
        """获取角色分析"""
        try:
            char_id = CharacterId(character_id)
            character = await self.character_repository.get_by_id(char_id)
            
            if not character:
                raise CharacterNotFoundError(character_id)
            
            # 计算角色价值
            character_value = self.character_analysis_service.calculate_character_value(character)
            
            # 获取训练建议
            recommendations = self.character_analysis_service.get_training_recommendations(character)
            
            return {
                "character_id": character_id,
                "character_name": character.name,
                "analysis": {
                    "value": character_value,
                    "recommendations": recommendations,
                    "total_skill_points": character.get_total_skill_points().value,
                    "skill_count": len(character.skills),
                    "queue_length": len(character.skill_queue)
                }
            }
            
        except Exception as e:
            logger.error("获取角色分析失败", character_id=character_id, error=str(e))
            raise
    
    async def search_characters_by_skill(self, skill_id: int, min_level: int = 1) -> List[Dict[str, Any]]:
        """根据技能搜索角色"""
        try:
            characters = await self.character_repository.find_characters_with_skill(skill_id, min_level)
            
            return [
                {
                    "character_id": char.character_id.value,
                    "character_name": char.name,
                    "corporation_id": char.corporation_id.value,
                    "alliance_id": char.alliance_id.value if char.alliance_id else None,
                    "skill_level": char.get_skill_by_id(skill_id).trained_skill_level if char.get_skill_by_id(skill_id) else 0
                }
                for char in characters
            ]
            
        except Exception as e:
            logger.error("根据技能搜索角色失败", skill_id=skill_id, min_level=min_level, error=str(e))
            raise
    
    async def _sync_character_details(self, character: Character, access_token: str) -> None:
        """同步角色详细数据"""
        character_id = character.character_id.value
        
        try:
            # 同步属性
            esi_attributes = await self.esi_service.get_character_attributes(character_id, access_token)
            from ..domain.character.value_objects import Attributes
            attributes = Attributes(
                charisma=esi_attributes.charisma,
                intelligence=esi_attributes.intelligence,
                memory=esi_attributes.memory,
                perception=esi_attributes.perception,
                willpower=esi_attributes.willpower
            )
            character.update_attributes(attributes)
            
        except Exception as e:
            logger.warning("同步角色属性失败", character_id=character_id, error=str(e))
        
        try:
            # 同步技能
            esi_skills = await self.esi_service.get_character_skills(character_id, access_token)
            from ..domain.character.value_objects import Skill
            from ..domain.shared.value_objects import SkillPoints
            
            for esi_skill in esi_skills:
                skill = Skill(
                    skill_id=esi_skill.skill_id,
                    skillpoints_in_skill=SkillPoints(esi_skill.skillpoints_in_skill),
                    trained_skill_level=esi_skill.trained_skill_level,
                    active_skill_level=esi_skill.active_skill_level
                )
                character.update_skill(skill)
                
        except Exception as e:
            logger.warning("同步角色技能失败", character_id=character_id, error=str(e))
        
        try:
            # 同步位置
            esi_location = await self.esi_service.get_character_location(character_id, access_token)
            from ..domain.shared.value_objects import Location, SystemId
            location = Location(
                system_id=SystemId(esi_location.solar_system_id),
                station_id=esi_location.station_id,
                structure_id=esi_location.structure_id
            )
            character.update_location(location)
            
        except Exception as e:
            logger.warning("同步角色位置失败", character_id=character_id, error=str(e))
        
        try:
            # 同步在线状态
            esi_online = await self.esi_service.get_character_online(character_id, access_token)
            character.set_online_status(esi_online.online, esi_online.last_login)
            
        except Exception as e:
            logger.warning("同步角色在线状态失败", character_id=character_id, error=str(e))
        
        try:
            # 同步钱包余额
            wallet_balance = await self.esi_service.get_character_wallet_balance(character_id, access_token)
            character.update_wallet_balance(Money(Decimal(str(wallet_balance))))
            
        except Exception as e:
            logger.warning("同步角色钱包失败", character_id=character_id, error=str(e))
    
    def _character_to_dict(self, character: Character) -> Dict[str, Any]:
        """将角色实体转换为字典"""
        return {
            "character_id": character.character_id.value,
            "name": character.name,
            "description": character.description,
            "corporation_id": character.corporation_id.value,
            "alliance_id": character.alliance_id.value if character.alliance_id else None,
            "faction_id": character.faction_id,
            "race_id": character.race_id,
            "bloodline_id": character.bloodline_id,
            "gender": character.gender,
            "birthday": character.birthday.isoformat(),
            "security_status": character.security_status.value,
            "title": character.title,
            "wallet_balance": float(character.wallet_balance.amount),
            "is_online": character.is_online,
            "last_login": character.last_login.isoformat() if character.last_login else None,
            "current_location": {
                "system_id": character.current_location.system_id.value,
                "station_id": character.current_location.station_id,
                "structure_id": character.current_location.structure_id
            } if character.current_location else None,
            "attributes": {
                "charisma": character.attributes.charisma,
                "intelligence": character.attributes.intelligence,
                "memory": character.attributes.memory,
                "perception": character.attributes.perception,
                "willpower": character.attributes.willpower,
                "total": character.attributes.get_total_attributes()
            },
            "skills": {
                "total_sp": character.get_total_skill_points().value,
                "skill_count": len(character.skills),
                "skills": [
                    {
                        "skill_id": skill.skill_id,
                        "skillpoints_in_skill": skill.skillpoints_in_skill.value,
                        "trained_skill_level": skill.trained_skill_level,
                        "active_skill_level": skill.active_skill_level
                    }
                    for skill in character.skills.values()
                ]
            },
            "skill_queue": [
                {
                    "skill_id": queue_item.skill_id,
                    "finished_level": queue_item.finished_level,
                    "queue_position": queue_item.queue_position,
                    "start_date": queue_item.start_date.isoformat() if queue_item.start_date else None,
                    "finish_date": queue_item.finish_date.isoformat() if queue_item.finish_date else None
                }
                for queue_item in character.skill_queue
            ]
        }
