"""
应用配置设置
"""
from functools import lru_cache
from typing import Optional

from pydantic import Field, validator

# 尝试导入pydantic_settings，如果失败则使用pydantic的BaseSettings
try:
    from pydantic_settings import BaseSettings
except ImportError:
    try:
        from pydantic import BaseSettings
    except ImportError:
        # 如果都失败，创建一个简单的BaseSettings类
        class BaseSettings:
            def __init__(self, **kwargs):
                for key, value in kwargs.items():
                    setattr(self, key, value)


class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基础配置
    app_name: str = Field(default="EVE Online Assistant", description="应用名称")
    app_version: str = Field(default="0.1.0", description="应用版本")
    debug: bool = Field(default=False, description="调试模式")
    secret_key: str = Field(..., description="应用密钥")
    
    # 数据库配置
    database_url: str = Field(..., description="数据库连接URL")
    database_echo: bool = Field(default=False, description="数据库SQL日志")
    
    # Redis配置
    redis_url: str = Field(default="redis://localhost:6379/0", description="Redis连接URL")
    
    # ESI API配置
    esi_base_url: str = Field(default="https://esi.evetech.net", description="ESI API基础URL")
    esi_user_agent: str = Field(..., description="ESI API用户代理")
    
    # EVE SSO配置
    eve_sso_client_id: str = Field(..., description="EVE SSO客户端ID")
    eve_sso_client_secret: str = Field(..., description="EVE SSO客户端密钥")
    eve_sso_callback_url: str = Field(..., description="EVE SSO回调URL")
    eve_sso_base_url: str = Field(default="https://login.eveonline.com", description="EVE SSO基础URL")
    eve_sso_scopes: str = Field(default="esi-assets.read_assets.v1", description="EVE SSO权限范围")
    eve_sso_state_timeout: int = Field(default=300, description="EVE SSO状态超时时间(秒)")
    
    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    log_format: str = Field(default="json", description="日志格式")
    
    # Celery配置
    celery_broker_url: str = Field(default="redis://localhost:6379/1", description="Celery消息代理URL")
    celery_result_backend: str = Field(default="redis://localhost:6379/2", description="Celery结果后端URL")
    
    # 存储配置
    storage_base_path: str = Field(default="data", description="存储基础路径")
    storage_enable_compression: bool = Field(default=True, description="启用数据压缩")
    storage_auto_cleanup: bool = Field(default=True, description="自动清理过期数据")
    storage_backup_enabled: bool = Field(default=True, description="启用数据备份")

    # 缓存配置 - 按数据类型优化
    cache_ttl_realtime: int = Field(default=30, description="实时数据缓存TTL(秒)")
    cache_ttl_frequent: int = Field(default=300, description="频繁数据缓存TTL(秒)")
    cache_ttl_regular: int = Field(default=3600, description="常规数据缓存TTL(秒)")
    cache_ttl_daily: int = Field(default=86400, description="每日数据缓存TTL(秒)")
    cache_ttl_computed: int = Field(default=1800, description="计算结果缓存TTL(秒)")

    # 传统缓存配置（向后兼容）
    cache_ttl_default: int = Field(default=3600, description="默认缓存TTL(秒)")
    cache_ttl_character: int = Field(default=120, description="角色缓存TTL(秒)")
    cache_ttl_market: int = Field(default=300, description="市场缓存TTL(秒)")
    cache_ttl_assets: int = Field(default=3600, description="资产缓存TTL(秒)")
    cache_ttl_skills: int = Field(default=7200, description="技能缓存TTL(秒)")
    cache_ttl_corporation: int = Field(default=3600, description="公司缓存TTL(秒)")
    cache_ttl_industry: int = Field(default=1800, description="工业缓存TTL(秒)")

    # 智能缓存配置
    cache_optimization_enabled: bool = Field(default=True, description="启用缓存优化")
    cache_optimization_interval: int = Field(default=3600, description="缓存优化间隔(秒)")
    cache_max_memory_usage: int = Field(default=512, description="最大内存使用(MB)")
    cache_eviction_policy: str = Field(default="lru", description="缓存淘汰策略")

    # ESI API配置
    esi_timeout: int = Field(default=30, description="ESI API请求超时时间(秒)")
    esi_max_retries: int = Field(default=3, description="ESI API最大重试次数")
    esi_retry_delay: float = Field(default=1.0, description="ESI API重试延迟(秒)")
    esi_backoff_factor: float = Field(default=2.0, description="ESI API重试退避因子")

    # 数据同步配置
    sync_interval_realtime: int = Field(default=30, description="实时数据同步间隔(秒)")
    sync_interval_frequent: int = Field(default=300, description="频繁数据同步间隔(秒)")
    sync_interval_regular: int = Field(default=3600, description="常规数据同步间隔(秒)")
    sync_interval_daily: int = Field(default=86400, description="每日数据同步间隔(秒)")

    # 同步数据类型配置
    sync_character_location: bool = Field(default=True, description="同步角色位置")
    sync_character_online: bool = Field(default=True, description="同步角色在线状态")
    sync_character_skills: bool = Field(default=True, description="同步角色技能")
    sync_character_assets: bool = Field(default=True, description="同步角色资产")
    sync_character_wallet: bool = Field(default=True, description="同步角色钱包")
    sync_market_orders: bool = Field(default=True, description="同步市场订单")
    sync_industry_jobs: bool = Field(default=True, description="同步工业任务")
    sync_corporation_info: bool = Field(default=True, description="同步公司信息")
    
    # API限流配置
    rate_limit_requests_per_second: int = Field(default=10, description="每秒请求限制")
    rate_limit_burst: int = Field(default=20, description="突发请求限制")
    
    # CORS配置
    cors_origins: str = Field(default="http://localhost:3000", description="CORS允许的源(逗号分隔)")
    cors_allow_credentials: bool = Field(default=True, description="CORS允许凭证")
    cors_allow_methods: str = Field(default="*", description="CORS允许的方法")
    cors_allow_headers: str = Field(default="*", description="CORS允许的头部")
    
    # JWT配置
    jwt_algorithm: str = Field(default="HS256", description="JWT算法")
    jwt_expire_minutes: int = Field(default=30, description="JWT过期时间(分钟)")
    jwt_refresh_expire_days: int = Field(default=7, description="JWT刷新令牌过期时间(天)")
    
    # 分页配置
    default_page_size: int = Field(default=20, description="默认分页大小")
    max_page_size: int = Field(default=100, description="最大分页大小")

    # ngrok配置
    ngrok_enabled: bool = Field(default=False, description="启用ngrok隧道")
    ngrok_auth_token: Optional[str] = Field(default=None, description="ngrok认证令牌")
    ngrok_region: str = Field(default="us", description="ngrok区域")
    ngrok_subdomain: Optional[str] = Field(default=None, description="ngrok子域名")
    ngrok_domain: Optional[str] = Field(default=None, description="ngrok自定义域名")
    ngrok_auto_update_callback: bool = Field(default=True, description="自动更新回调URL")
    server_port: int = Field(default=8000, description="服务器端口")
    
    @validator("log_level")
    def validate_log_level(cls, v):
        """验证日志级别"""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"日志级别必须是 {valid_levels} 之一")
        return v.upper()
    
    @validator("log_format")
    def validate_log_format(cls, v):
        """验证日志格式"""
        valid_formats = ["json", "text"]
        if v.lower() not in valid_formats:
            raise ValueError(f"日志格式必须是 {valid_formats} 之一")
        return v.lower()
    
    def get_cors_origins_list(self) -> list[str]:
        """获取CORS源列表"""
        if self.cors_origins:
            return [origin.strip() for origin in self.cors_origins.split(",")]
        return ["http://localhost:3000"]
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


class TestSettings(Settings):
    """测试环境配置"""
    
    debug: bool = True
    database_url: str = "sqlite:///./test.db"
    redis_url: str = "redis://localhost:6379/15"  # 使用不同的Redis数据库
    cache_ttl_default: int = 60  # 测试时使用较短的缓存时间
    
    class Config:
        env_file = ".env.test"


@lru_cache()
def get_settings() -> Settings:
    """获取应用配置单例"""
    return Settings()


@lru_cache()
def get_test_settings() -> TestSettings:
    """获取测试配置单例"""
    return TestSettings()


# 导出配置实例
settings = get_settings()
