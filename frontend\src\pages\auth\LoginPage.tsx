import React, { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { Form, Input, Button, Checkbox, Divider, Alert } from 'antd'
import { UserOutlined, LockOutlined, EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons'
import { motion } from 'framer-motion'

import { LoginCredentials } from '@/types'
import { useLogin, useEVELogin } from '@/hooks/useAuth'
import { GuestRoute } from '@/components/auth/ProtectedRoute'

export default function LoginPage() {
  const [form] = Form.useForm()
  const [showPassword, setShowPassword] = useState(false)
  
  const loginMutation = useLogin()
  const { initiate: initiateEVELogin } = useEVELogin()

  const handleLogin = async (values: LoginCredentials) => {
    try {
      await loginMutation.mutateAsync(values)
    } catch (error) {
      // 错误已在mutation中处理
    }
  }

  const handleEVELogin = async () => {
    try {
      const scopes = [
        'esi-characters.read_characters.v1',
        'esi-assets.read_assets.v1',
        'esi-skills.read_skills.v1',
        'esi-wallet.read_character_wallet.v1',
        'esi-clones.read_clones.v1',
        'esi-location.read_location.v1',
        'esi-location.read_ship_type.v1',
      ]
      
      const loginUrl = await initiateEVELogin.mutateAsync(scopes)
      window.location.href = loginUrl
    } catch (error) {
      // 错误已在mutation中处理
    }
  }

  return (
    <GuestRoute>
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-dark-900 dark:via-dark-800 dark:to-dark-900 py-12 px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-md w-full space-y-8"
        >
          {/* Logo和标题 */}
          <div className="text-center">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              className="mx-auto h-16 w-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mb-4"
            >
              <span className="text-2xl font-bold text-white">E</span>
            </motion.div>
            <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
              欢迎回来
            </h2>
            <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
              登录到 EVE Online 管理助手
            </p>
          </div>

          {/* 登录表单 */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
            className="bg-white dark:bg-dark-800 py-8 px-6 shadow-xl rounded-xl border border-gray-200 dark:border-dark-700"
          >
            {/* EVE SSO登录 */}
            <div className="mb-6">
              <Button
                type="primary"
                size="large"
                block
                loading={initiateEVELogin.isPending}
                onClick={handleEVELogin}
                className="h-12 bg-gradient-to-r from-blue-600 to-purple-600 border-none hover:from-blue-700 hover:to-purple-700"
              >
                <span className="flex items-center justify-center">
                  <img 
                    src="/eve-logo.png" 
                    alt="EVE Online" 
                    className="w-5 h-5 mr-2"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none'
                    }}
                  />
                  使用 EVE Online 账户登录
                </span>
              </Button>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2 text-center">
                推荐方式，安全快捷
              </p>
            </div>

            <Divider className="text-gray-400 dark:text-gray-500">
              或使用邮箱登录
            </Divider>

            {/* 错误提示 */}
            {loginMutation.isError && (
              <Alert
                message="登录失败"
                description={loginMutation.error?.message || '请检查邮箱和密码是否正确'}
                type="error"
                showIcon
                className="mb-4"
              />
            )}

            <Form
              form={form}
              name="login"
              onFinish={handleLogin}
              autoComplete="off"
              size="large"
            >
              <Form.Item
                name="email"
                rules={[
                  { required: true, message: '请输入邮箱地址' },
                  { type: 'email', message: '请输入有效的邮箱地址' }
                ]}
              >
                <Input
                  prefix={<UserOutlined className="text-gray-400" />}
                  placeholder="邮箱地址"
                  autoComplete="email"
                />
              </Form.Item>

              <Form.Item
                name="password"
                rules={[
                  { required: true, message: '请输入密码' },
                  { min: 6, message: '密码至少6位' }
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined className="text-gray-400" />}
                  placeholder="密码"
                  autoComplete="current-password"
                  iconRender={(visible) => 
                    visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                  }
                />
              </Form.Item>

              <Form.Item>
                <div className="flex items-center justify-between">
                  <Form.Item name="rememberMe" valuePropName="checked" noStyle>
                    <Checkbox className="text-sm text-gray-600 dark:text-gray-400">
                      记住我
                    </Checkbox>
                  </Form.Item>
                  <Link
                    to="/auth/forgot-password"
                    className="text-sm text-blue-600 hover:text-blue-500 dark:text-blue-400"
                  >
                    忘记密码？
                  </Link>
                </div>
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  size="large"
                  block
                  loading={loginMutation.isPending}
                  className="h-12"
                >
                  登录
                </Button>
              </Form.Item>
            </Form>

            {/* 注册链接 */}
            <div className="text-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                还没有账户？{' '}
                <Link
                  to="/auth/register"
                  className="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400"
                >
                  立即注册
                </Link>
              </span>
            </div>
          </motion.div>

          {/* 功能特色 */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="text-center"
          >
            <div className="grid grid-cols-3 gap-4 text-xs text-gray-500 dark:text-gray-400">
              <div className="flex flex-col items-center">
                <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mb-2">
                  <span className="text-blue-600 dark:text-blue-400">📊</span>
                </div>
                <span>数据分析</span>
              </div>
              <div className="flex flex-col items-center">
                <div className="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mb-2">
                  <span className="text-green-600 dark:text-green-400">🔒</span>
                </div>
                <span>安全可靠</span>
              </div>
              <div className="flex flex-col items-center">
                <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-full flex items-center justify-center mb-2">
                  <span className="text-purple-600 dark:text-purple-400">⚡</span>
                </div>
                <span>实时同步</span>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </GuestRoute>
  )
}
