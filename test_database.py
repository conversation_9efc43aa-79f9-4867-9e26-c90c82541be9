#!/usr/bin/env python3
"""
数据库连接和功能测试脚本
"""
import requests
import time
import json

def test_database():
    base_url = "http://localhost:8000"
    
    print("🗄️ 数据库连接和功能测试")
    print("=" * 50)
    
    # 等待服务器启动
    print("等待服务器启动...")
    time.sleep(3)
    
    # 测试1: 健康检查（包含数据库检查）
    print("\n1. 测试健康检查（包含数据库）...")
    try:
        response = requests.get(f"{base_url}/health/status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ 健康检查端点正常")
            print(f"   响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            # 检查数据库状态
            if "components" in data and "database" in data["components"]:
                db_component = data["components"]["database"]
                db_status = db_component.get("status", "unknown")
                if db_status == "healthy":
                    print("✅ 数据库连接正常")
                    return True
                else:
                    print(f"❌ 数据库状态异常: {db_status}")
                    return False
            elif "services" in data:
                db_status = data["services"].get("database", "unknown")
                if db_status == "healthy":
                    print("✅ 数据库连接正常")
                    return True
                elif db_status == "not_configured":
                    print("⚠️  数据库未配置（简化版本）")
                    return False
                else:
                    print(f"❌ 数据库状态异常: {db_status}")
                    return False
            else:
                print("❌ 健康检查响应格式异常")
                return False
        else:
            print(f"❌ 健康检查失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查连接失败: {e}")
        return False
    
    # 测试2: 详细健康检查
    print("\n2. 测试详细健康检查...")
    try:
        response = requests.get(f"{base_url}/health/detailed", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ 详细健康检查正常")
            print(f"   响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"⚠️  详细健康检查: HTTP {response.status_code}")
    except Exception as e:
        print(f"⚠️  详细健康检查失败: {e}")
    
    # 测试3: 就绪检查
    print("\n3. 测试就绪检查...")
    try:
        response = requests.get(f"{base_url}/health/ready", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ 就绪检查正常")
            print(f"   响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"⚠️  就绪检查: HTTP {response.status_code}")
    except Exception as e:
        print(f"⚠️  就绪检查失败: {e}")

if __name__ == "__main__":
    success = test_database()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 数据库连接测试通过！")
        print("✅ 数据库功能已恢复")
    else:
        print("❌ 数据库连接测试失败")
        print("⚠️  需要进一步配置数据库")
    print("=" * 50)
