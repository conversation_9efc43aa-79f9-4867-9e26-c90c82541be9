# 市场交易模块 - 产品需求文档 (PRD)

## 📋 模块概述

### 模块名称
市场交易模块 (Market Trading Module)

### 模块愿景
为EVE Online玩家提供专业的市场分析工具和交易管理平台，通过数据驱动的洞察帮助用户做出更明智的交易决策，提升交易效率和盈利能力。

### 业务价值
- 🎯 **市场洞察**: 提供深度的市场数据分析和趋势预测
- 🎯 **交易优化**: 智能订单管理和交易策略建议
- 🎯 **利润最大化**: 识别套利机会和最优交易时机
- 🎯 **风险控制**: 市场风险评估和预警机制

## 🎯 功能需求

### 1. 市场数据分析

#### 1.1 价格监控
**功能描述**: 实时监控和分析市场价格变化

**核心功能**:
- ✅ 实时价格数据展示
- ✅ 历史价格趋势图表
- ✅ 价格变化预警
- ✅ 多区域价格对比
- ✅ 价格异常检测

**数据模型**:
```python
class MarketPrice(ValueObject):
    type_id: TypeId
    region_id: RegionId
    system_id: SystemId
    buy_price: Money
    sell_price: Money
    spread: Money
    volume: Quantity
    timestamp: datetime
```

#### 1.2 市场深度分析
**功能描述**: 分析市场订单深度和流动性

**核心功能**:
- ✅ 买卖订单深度图
- ✅ 流动性指标计算
- ✅ 市场操控检测
- ✅ 大单影响分析
- ✅ 市场情绪指标

**数据模型**:
```python
class MarketDepth(ValueObject):
    type_id: TypeId
    region_id: RegionId
    buy_orders: List[OrderLevel]
    sell_orders: List[OrderLevel]
    total_buy_volume: Quantity
    total_sell_volume: Quantity
    liquidity_score: float
```

### 2. 订单管理

#### 2.1 订单跟踪
**功能描述**: 全面管理用户的市场订单

**核心功能**:
- ✅ 活跃订单实时状态
- ✅ 订单历史记录
- ✅ 订单执行分析
- ✅ 订单性能统计
- ✅ 批量订单操作

**数据模型**:
```python
class MarketOrder(Entity):
    order_id: OrderId
    character_id: CharacterId
    type_id: TypeId
    location_id: LocationId
    order_type: OrderType  # buy/sell
    price: Money
    volume_total: Quantity
    volume_remain: Quantity
    min_volume: Quantity
    duration: int
    issued: datetime
    state: OrderState
```

#### 2.2 智能订单建议
**功能描述**: 基于市场分析的智能订单建议

**核心功能**:
- 🚀 最优价格建议
- 🚀 订单量优化建议
- 🚀 订单时机建议
- 🚀 竞争对手分析
- 🚀 订单策略推荐

### 3. 交易分析

#### 3.1 交易历史分析
**功能描述**: 分析用户的交易历史和表现

**核心功能**:
- ✅ 交易记录详细展示
- ✅ 盈亏统计分析
- ✅ 交易频率分析
- ✅ 商品偏好分析
- ✅ 交易效率评估

**数据模型**:
```python
class Transaction(Entity):
    transaction_id: TransactionId
    character_id: CharacterId
    type_id: TypeId
    location_id: LocationId
    transaction_type: TransactionType
    quantity: Quantity
    unit_price: Money
    total_price: Money
    client_id: CharacterId
    date: datetime
```

#### 3.2 盈利能力分析
**功能描述**: 深度分析交易的盈利能力

**核心功能**:
- 🚀 单品盈利分析
- 🚀 投资回报率计算
- 🚀 资金周转率分析
- 🚀 风险调整收益
- 🚀 基准比较分析

### 4. 套利机会识别

#### 4.1 区域套利
**功能描述**: 识别不同区域间的价格差异机会

**核心功能**:
- 🚀 跨区域价格对比
- 🚀 运输成本计算
- 🚀 净利润预估
- 🚀 风险评估
- 🚀 最优路线规划

**数据模型**:
```python
class ArbitrageOpportunity(ValueObject):
    type_id: TypeId
    buy_region: RegionId
    sell_region: RegionId
    buy_price: Money
    sell_price: Money
    profit_margin: float
    transport_cost: Money
    net_profit: Money
    risk_score: float
    volume_available: Quantity
```

#### 4.2 时间套利
**功能描述**: 识别基于时间的价格波动机会

**核心功能**:
- 🚀 价格周期性分析
- 🚀 季节性趋势识别
- 🚀 事件驱动机会
- 🚀 最佳买卖时机
- 🚀 持仓时间优化

### 5. 投资组合管理

#### 5.1 交易组合分析
**功能描述**: 管理和分析交易投资组合

**核心功能**:
- 🚀 组合构成分析
- 🚀 风险分散度评估
- 🚀 相关性分析
- 🚀 组合优化建议
- 🚀 再平衡策略

#### 5.2 风险管理
**功能描述**: 评估和管理交易风险

**核心功能**:
- 🚀 VaR (风险价值) 计算
- 🚀 最大回撤分析
- 🚀 波动率监控
- 🚀 集中度风险评估
- 🚀 止损建议

### 6. 市场情报

#### 6.1 竞争对手分析
**功能描述**: 分析市场中的竞争对手行为

**核心功能**:
- 🚀 主要交易者识别
- 🚀 交易模式分析
- 🚀 价格影响评估
- 🚀 市场份额分析
- 🚀 策略预测

#### 6.2 市场新闻和事件
**功能描述**: 整合市场相关新闻和事件信息

**核心功能**:
- 🚀 游戏更新影响分析
- 🚀 经济事件跟踪
- 🚀 价格异动解释
- 🚀 市场情绪分析
- 🚀 预警通知

## 🔧 技术实现

### 领域模型设计

#### 聚合根: TradingPortfolio
```python
class TradingPortfolio(AggregateRoot):
    def __init__(self, trader_id: CharacterId):
        super().__init__(trader_id)
        self._orders: Dict[OrderId, MarketOrder] = {}
        self._transactions: List[Transaction] = []
        self._positions: Dict[TypeId, Position] = {}
    
    def place_order(self, order: MarketOrder) -> None:
        """下单"""
        self._validate_order(order)
        self._orders[order.id] = order
        self._raise_event(OrderPlacedEvent(self.id, order))
    
    def update_order_status(self, order_id: OrderId, new_status: OrderState) -> None:
        """更新订单状态"""
        if order_id in self._orders:
            self._orders[order_id].update_status(new_status)
            self._raise_event(OrderStatusUpdatedEvent(self.id, order_id, new_status))
    
    def calculate_portfolio_value(self) -> Money:
        """计算组合价值"""
        return sum(position.market_value for position in self._positions.values())
```

#### 领域服务
```python
class MarketAnalysisService(DomainService):
    def analyze_price_trend(
        self, 
        type_id: TypeId, 
        region_id: RegionId, 
        period: TimePeriod
    ) -> PriceTrendAnalysis:
        """分析价格趋势"""
        
    def identify_arbitrage_opportunities(
        self, 
        type_ids: List[TypeId], 
        regions: List[RegionId]
    ) -> List[ArbitrageOpportunity]:
        """识别套利机会"""

class TradingStrategyService(DomainService):
    def suggest_optimal_price(
        self, 
        type_id: TypeId, 
        region_id: RegionId, 
        order_type: OrderType
    ) -> Money:
        """建议最优价格"""
        
    def calculate_risk_metrics(
        self, 
        portfolio: TradingPortfolio
    ) -> RiskMetrics:
        """计算风险指标"""
```

### 应用服务

#### MarketApplicationService
```python
class MarketApplicationService:
    async def get_market_overview(
        self, 
        region_id: RegionId
    ) -> MarketOverviewDTO:
        """获取市场概览"""
        
    async def search_arbitrage_opportunities(
        self, 
        search_criteria: ArbitrageSearchCriteria
    ) -> List[ArbitrageOpportunityDTO]:
        """搜索套利机会"""
        
    async def analyze_trading_performance(
        self, 
        character_id: CharacterId, 
        period: TimePeriod
    ) -> TradingPerformanceDTO:
        """分析交易表现"""
        
    async def get_order_suggestions(
        self, 
        character_id: CharacterId, 
        type_id: TypeId
    ) -> OrderSuggestionDTO:
        """获取订单建议"""
```

### 数据同步策略

#### 实时数据 (每1分钟)
- 活跃订单状态
- 价格变化监控

#### 高频数据 (每5分钟)
- 市场深度数据
- 交易量统计

#### 常规数据 (每30分钟)
- 历史价格数据
- 市场统计指标

#### 分析数据 (每小时)
- 套利机会计算
- 趋势分析更新

## 📊 用户界面设计

### 1. 市场概览页面
```
┌─────────────────────────────────────────────────────────────┐
│ 市场概览 - The Forge                                        │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│ │ 日交易量         │ │ 活跃商品         │ │ 平均价差         │ │
│ │ 2.5T ISK        │ │ 15,234 种       │ │ 3.2%            │ │
│ │ ↑ +5.2% (24h)   │ │                 │ │ ↓ -0.8% (24h)   │ │
│ └─────────────────┘ └─────────────────┘ └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 热门商品                                                     │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 商品名称              买价      卖价      价差    24h量   │ │
│ │ PLEX                 3.2M ISK  3.3M ISK  3.1%   125,000 │ │
│ │ Tritanium            5.2 ISK   5.3 ISK   1.9%   50.2M   │ │
│ │ Raven Navy Issue     450M ISK  465M ISK  3.3%   45      │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2. 订单管理页面
```
┌─────────────────────────────────────────────────────────────┐
│ 订单管理                                                     │
├─────────────────────────────────────────────────────────────┤
│ [活跃订单] [历史订单] [新建订单]                             │
├─────────────────────────────────────────────────────────────┤
│ 状态  商品名称           类型  价格      数量    剩余    位置 │
│ 🟢   PLEX               买单  3.15M ISK  100     85     Jita │
│ 🟡   Tritanium          卖单  5.3 ISK    1.2M    800K   Jita │
│ 🔴   Raven Navy Issue   买单  440M ISK   1       1      Jita │
│ ⏸️   Large Shield Ext.  卖单  2.5M ISK   50      50     Jita │
├─────────────────────────────────────────────────────────────┤
│ 订单统计: 活跃 15 | 今日成交 8 | 总价值 2.3B ISK            │
│ [批量修改价格] [批量取消] [导出数据] [性能分析]              │
└─────────────────────────────────────────────────────────────┘
```

### 3. 套利机会页面
```
┌─────────────────────────────────────────────────────────────┐
│ 套利机会                                                     │
├─────────────────────────────────────────────────────────────┤
│ [筛选条件] 最小利润: 10M ISK  风险等级: 中等  运输: 跳跃货船 │
├─────────────────────────────────────────────────────────────┤
│ 商品名称           买入地    卖出地    利润     风险  推荐度 │
│ Raven Navy Issue   Amarr     Jita     25M ISK   低    ★★★★★ │
│ Large Shield Ext.  Dodixie   Jita     5.2M ISK  中    ★★★★☆ │
│ Tritanium          Rens      Jita     2.1M ISK  低    ★★★☆☆ │
├─────────────────────────────────────────────────────────────┤
│ 详细分析: Raven Navy Issue                                   │
│ 买入价格: 425M ISK (Amarr)  卖出价格: 450M ISK (Jita)      │
│ 运输成本: 2M ISK  净利润: 23M ISK  投资回报率: 5.4%        │
│ 风险因素: 价格波动 (低), 运输风险 (低), 流动性 (高)        │
│ [添加到购物清单] [设置价格预警] [查看历史趋势]              │
└─────────────────────────────────────────────────────────────┘
```

## 🔍 测试策略

### 单元测试
- 价格计算算法测试
- 套利机会识别测试
- 风险指标计算测试

### 集成测试
- 市场数据API集成测试
- 订单状态同步测试
- 价格预警机制测试

### 性能测试
- 大量订单处理性能测试
- 实时数据更新性能测试
- 复杂查询响应时间测试

## 📈 成功指标

### 功能指标
- 价格数据准确率 > 99.9%
- 订单状态同步延迟 < 30秒
- 套利机会识别准确率 > 85%

### 用户体验指标
- 交易决策效率提升 > 40%
- 平均盈利率提升 > 25%
- 用户满意度评分 > 4.7/5.0

---

**文档版本**: v1.0  
**最后更新**: 2025-01-09  
**负责人**: 市场交易团队
