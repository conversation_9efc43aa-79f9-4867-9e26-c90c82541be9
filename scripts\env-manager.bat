@echo off
echo 🛠️ EVE Online Assistant - 环境管理器
echo ===================================

:menu
echo.
echo 请选择操作:
echo   1. 设置Conda环境 (推荐)
echo   2. 设置Python venv环境
echo   3. 检查当前环境
echo   4. 激活Conda环境
echo   5. 激活venv环境
echo   6. 清理环境
echo   0. 退出
echo.
set /p choice=请输入选择 (0-6): 

if "%choice%"=="1" goto setup_conda
if "%choice%"=="2" goto setup_venv
if "%choice%"=="3" goto check_env
if "%choice%"=="4" goto activate_conda
if "%choice%"=="5" goto activate_venv
if "%choice%"=="6" goto cleanup
if "%choice%"=="0" goto exit
echo 无效选择，请重试
goto menu

:setup_conda
echo.
echo 🚀 设置Conda环境...
call scripts\setup\setup-conda-env.bat
goto menu

:setup_venv
echo.
echo 🚀 设置Python venv环境...
call scripts\setup\setup-venv.bat
goto menu

:check_env
echo.
echo 🔍 检查当前环境...
echo ==================
echo.
echo 📍 当前Python:
where python
python --version
echo.
echo 📍 当前pip:
where pip
pip --version
echo.
echo 📍 Conda环境:
conda info --envs 2>nul || echo Conda未安装
echo.
echo 📍 虚拟环境:
if exist eve-assistant-env (
    echo ✅ venv环境存在: eve-assistant-env
) else (
    echo ❌ venv环境不存在
)
echo.
echo 📦 已安装的关键包:
pip list | findstr -i "fastapi uvicorn structlog" 2>nul || echo 未找到关键包
pause
goto menu

:activate_conda
echo.
echo 🔄 激活Conda环境...
conda activate eve-assistant
if %errorlevel% equ 0 (
    echo ✅ Conda环境已激活
    echo 💡 现在可以运行: python scripts/start.py
) else (
    echo ❌ Conda环境激活失败
    echo 💡 请先运行选项1创建环境
)
pause
goto menu

:activate_venv
echo.
echo 🔄 激活venv环境...
if exist eve-assistant-env\Scripts\activate.bat (
    call eve-assistant-env\Scripts\activate.bat
    echo ✅ venv环境已激活
    echo 💡 现在可以运行: python scripts/start.py
) else (
    echo ❌ venv环境不存在
    echo 💡 请先运行选项2创建环境
)
pause
goto menu

:cleanup
echo.
echo 🧹 清理环境...
echo ===============
echo.
echo 请选择要清理的环境:
echo   1. 删除Conda环境
echo   2. 删除venv环境
echo   3. 清理Python缓存
echo   0. 返回主菜单
echo.
set /p cleanup_choice=请输入选择 (0-3): 

if "%cleanup_choice%"=="1" (
    echo 删除Conda环境 'eve-assistant'...
    conda remove -n eve-assistant --all -y
    echo ✅ Conda环境已删除
)
if "%cleanup_choice%"=="2" (
    echo 删除venv环境 'eve-assistant-env'...
    if exist eve-assistant-env (
        rmdir /s /q eve-assistant-env
        echo ✅ venv环境已删除
    ) else (
        echo ❌ venv环境不存在
    )
)
if "%cleanup_choice%"=="3" (
    echo 清理Python缓存...
    for /d /r . %%d in (__pycache__) do @if exist "%%d" rd /s /q "%%d"
    echo ✅ Python缓存已清理
)
if "%cleanup_choice%"=="0" goto menu

pause
goto menu

:exit
echo.
echo 👋 再见！
exit /b 0
