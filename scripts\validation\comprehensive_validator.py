#!/usr/bin/env python3
"""
综合验证器
运行所有验证检查并生成综合报告
"""
import sys
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from scripts.validation.third_party_api_validator import ThirdPartyAPIValidator
from scripts.validation.config_sync_checker import ConfigSyncChecker
from src.infrastructure.config.unified_config import config_manager


class ComprehensiveValidator:
    """综合验证器"""
    
    def __init__(self):
        self.api_validator = ThirdPartyAPIValidator()
        self.sync_checker = ConfigSyncChecker()
        self.results = {}
    
    def run_all_validations(self) -> Dict[str, Any]:
        """运行所有验证检查"""
        print("🔍 开始综合验证检查")
        print("=" * 60)
        
        # 1. 第三方API验证
        print("\n📡 第三方API验证")
        print("-" * 30)
        api_report = self.api_validator.generate_validation_report()
        self.results['third_party_api'] = api_report
        self._print_validation_summary("第三方API", api_report['overall_status'])
        
        # 2. 配置同步检查
        print("\n🔄 配置同步检查")
        print("-" * 30)
        sync_report = self.sync_checker.generate_sync_report()
        self.results['config_sync'] = sync_report
        self._print_sync_summary("配置同步", sync_report['overall_status'])
        
        # 3. 统一配置验证
        print("\n⚙️ 统一配置验证")
        print("-" * 30)
        config_result = config_manager.validate_config_consistency()
        config_report = {
            'is_valid': config_result.is_valid,
            'errors': config_result.errors,
            'warnings': config_result.warnings,
            'suggestions': config_result.suggestions
        }
        self.results['unified_config'] = config_report
        self._print_config_summary("统一配置", config_report)
        
        # 4. 系统健康检查
        print("\n🏥 系统健康检查")
        print("-" * 30)
        health_report = self._run_system_health_check()
        self.results['system_health'] = health_report
        self._print_health_summary("系统健康", health_report)
        
        # 生成综合报告
        comprehensive_report = self._generate_comprehensive_report()
        
        return comprehensive_report
    
    def _run_system_health_check(self) -> Dict[str, Any]:
        """运行系统健康检查"""
        health_report = {
            'timestamp': datetime.now().isoformat(),
            'checks': {},
            'overall_healthy': True
        }
        
        # 检查数据库连接
        try:
            from src.infrastructure.persistence.database import db_manager
            db_healthy = db_manager.health_check()
            health_report['checks']['database'] = {
                'status': 'healthy' if db_healthy else 'unhealthy',
                'details': 'Database connection successful' if db_healthy else 'Database connection failed'
            }
            if not db_healthy:
                health_report['overall_healthy'] = False
            print(f"  数据库: {'✅ 正常' if db_healthy else '❌ 异常'}")
        except Exception as e:
            health_report['checks']['database'] = {
                'status': 'error',
                'details': str(e)
            }
            health_report['overall_healthy'] = False
            print(f"  数据库: ❌ 错误 - {str(e)}")
        
        # 检查日志目录
        try:
            logs_dir = project_root / "logs"
            logs_accessible = logs_dir.exists() and logs_dir.is_dir()
            health_report['checks']['logs'] = {
                'status': 'healthy' if logs_accessible else 'unhealthy',
                'details': f'Logs directory: {logs_dir}'
            }
            if not logs_accessible:
                health_report['overall_healthy'] = False
            print(f"  日志目录: {'✅ 正常' if logs_accessible else '❌ 异常'}")
        except Exception as e:
            health_report['checks']['logs'] = {
                'status': 'error',
                'details': str(e)
            }
            health_report['overall_healthy'] = False
            print(f"  日志目录: ❌ 错误 - {str(e)}")
        
        # 检查配置文件
        try:
            env_file = project_root / ".env"
            env_exists = env_file.exists()
            health_report['checks']['config_file'] = {
                'status': 'healthy' if env_exists else 'warning',
                'details': f'Environment file: {env_file}'
            }
            print(f"  配置文件: {'✅ 存在' if env_exists else '⚠️ 缺失'}")
        except Exception as e:
            health_report['checks']['config_file'] = {
                'status': 'error',
                'details': str(e)
            }
            print(f"  配置文件: ❌ 错误 - {str(e)}")
        
        return health_report
    
    def _print_validation_summary(self, name: str, status: Dict[str, Any]):
        """打印验证摘要"""
        is_valid = status.get('is_valid', False)
        errors = status.get('total_errors', 0)
        warnings = status.get('total_warnings', 0)
        
        status_icon = "✅" if is_valid else "❌"
        print(f"  {status_icon} {name}: {'通过' if is_valid else '失败'}")
        if errors > 0:
            print(f"    错误: {errors}")
        if warnings > 0:
            print(f"    警告: {warnings}")
    
    def _print_sync_summary(self, name: str, status: Dict[str, Any]):
        """打印同步摘要"""
        is_synced = status.get('is_synced', False)
        differences = status.get('total_differences', 0)
        warnings = status.get('total_warnings', 0)
        
        status_icon = "✅" if is_synced else "❌"
        print(f"  {status_icon} {name}: {'同步' if is_synced else '不同步'}")
        if differences > 0:
            print(f"    差异: {differences}")
        if warnings > 0:
            print(f"    警告: {warnings}")
    
    def _print_config_summary(self, name: str, report: Dict[str, Any]):
        """打印配置摘要"""
        is_valid = report.get('is_valid', False)
        errors = len(report.get('errors', []))
        warnings = len(report.get('warnings', []))
        
        status_icon = "✅" if is_valid else "❌"
        print(f"  {status_icon} {name}: {'有效' if is_valid else '无效'}")
        if errors > 0:
            print(f"    错误: {errors}")
        if warnings > 0:
            print(f"    警告: {warnings}")
    
    def _print_health_summary(self, name: str, report: Dict[str, Any]):
        """打印健康摘要"""
        is_healthy = report.get('overall_healthy', False)
        checks = report.get('checks', {})
        
        status_icon = "✅" if is_healthy else "❌"
        print(f"  {status_icon} {name}: {'健康' if is_healthy else '异常'}")
        
        unhealthy_checks = [
            check_name for check_name, check_data in checks.items()
            if check_data.get('status') != 'healthy'
        ]
        if unhealthy_checks:
            print(f"    异常检查: {', '.join(unhealthy_checks)}")
    
    def _generate_comprehensive_report(self) -> Dict[str, Any]:
        """生成综合报告"""
        # 计算总体状态
        overall_status = {
            'is_healthy': True,
            'total_errors': 0,
            'total_warnings': 0,
            'total_issues': 0
        }
        
        # 统计各项检查结果
        for check_name, check_result in self.results.items():
            if check_name == 'third_party_api':
                if not check_result['overall_status']['is_valid']:
                    overall_status['is_healthy'] = False
                overall_status['total_errors'] += check_result['overall_status']['total_errors']
                overall_status['total_warnings'] += check_result['overall_status']['total_warnings']
            
            elif check_name == 'config_sync':
                if not check_result['overall_status']['is_synced']:
                    overall_status['is_healthy'] = False
                overall_status['total_errors'] += check_result['overall_status']['total_differences']
                overall_status['total_warnings'] += check_result['overall_status']['total_warnings']
            
            elif check_name == 'unified_config':
                if not check_result['is_valid']:
                    overall_status['is_healthy'] = False
                overall_status['total_errors'] += len(check_result['errors'])
                overall_status['total_warnings'] += len(check_result['warnings'])
            
            elif check_name == 'system_health':
                if not check_result['overall_healthy']:
                    overall_status['is_healthy'] = False
                # 健康检查的问题计入警告
                unhealthy_count = sum(
                    1 for check in check_result['checks'].values()
                    if check.get('status') != 'healthy'
                )
                overall_status['total_warnings'] += unhealthy_count
        
        overall_status['total_issues'] = overall_status['total_errors'] + overall_status['total_warnings']
        
        # 生成建议
        suggestions = []
        if overall_status['total_errors'] > 0:
            suggestions.append("修复所有错误以确保系统正常运行")
        if overall_status['total_warnings'] > 0:
            suggestions.append("关注警告信息，可能影响系统稳定性")
        if overall_status['is_healthy']:
            suggestions.append("系统状态良好，建议定期运行验证检查")
        
        comprehensive_report = {
            'timestamp': datetime.now().isoformat(),
            'validator_version': '1.0.0',
            'overall_status': overall_status,
            'suggestions': suggestions,
            'detailed_results': self.results
        }
        
        return comprehensive_report
    
    def save_report(self, report: Dict[str, Any], filename: str = None) -> Path:
        """保存报告到文件"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"comprehensive_validation_report_{timestamp}.json"
        
        report_file = project_root / "logs" / filename
        report_file.parent.mkdir(exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        return report_file


def main():
    """主函数"""
    validator = ComprehensiveValidator()
    
    # 运行所有验证
    report = validator.run_all_validations()
    
    # 显示总体结果
    print("\n" + "=" * 60)
    print("📊 综合验证结果")
    print("=" * 60)
    
    overall = report['overall_status']
    status_icon = "🎉" if overall['is_healthy'] else "⚠️"
    status_text = "系统健康" if overall['is_healthy'] else "需要关注"
    
    print(f"\n{status_icon} 总体状态: {status_text}")
    print(f"   错误: {overall['total_errors']}")
    print(f"   警告: {overall['total_warnings']}")
    print(f"   总问题: {overall['total_issues']}")
    
    if report['suggestions']:
        print(f"\n💡 建议:")
        for suggestion in report['suggestions']:
            print(f"   - {suggestion}")
    
    # 保存报告
    report_file = validator.save_report(report)
    print(f"\n📄 详细报告已保存到: {report_file}")
    
    # 返回退出码
    return 0 if overall['is_healthy'] else 1


if __name__ == "__main__":
    sys.exit(main())
