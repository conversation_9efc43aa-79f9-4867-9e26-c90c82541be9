{"name": "@storybook/addon-measure", "version": "7.6.20", "description": "Inspect layouts by visualizing the box model", "keywords": ["storybook-addons", "essentials", "style", "CSS", "design"], "homepage": "https://github.com/storybookjs/storybook/tree/next/code/addons/measure", "bugs": {"url": "https://github.com/storybookjs/storybook/issues"}, "repository": {"type": "git", "url": "https://github.com/storybookjs/storybook.git", "directory": "code/addons/measure"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "license": "MIT", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "require": "./dist/index.js", "import": "./dist/index.mjs"}, "./manager": "./dist/manager.js", "./preview": "./dist/preview.js", "./register": "./dist/manager.js", "./package.json": "./package.json"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "typesVersions": {"*": {"*": ["dist/index.d.ts"], "manager": ["dist/manager.d.ts"], "preview": ["dist/preview.d.ts"]}}, "files": ["dist/**/*", "README.md", "*.js", "*.d.ts", "!src/**/*"], "scripts": {"check": "node --loader ../../../scripts/node_modules/esbuild-register/loader.js -r ../../../scripts/node_modules/esbuild-register/register.js ../../../scripts/prepare/check.ts", "prep": "node --loader ../../../scripts/node_modules/esbuild-register/loader.js -r ../../../scripts/node_modules/esbuild-register/register.js ../../../scripts/prepare/addon-bundle.ts"}, "dependencies": {"@storybook/global": "^5.0.0", "tiny-invariant": "^1.3.1"}, "devDependencies": {"@storybook/client-logger": "7.6.20", "@storybook/components": "7.6.20", "@storybook/core-events": "7.6.20", "@storybook/manager-api": "7.6.20", "@storybook/preview-api": "7.6.20", "@storybook/types": "7.6.20", "react": "^16.8.0", "react-dom": "^16.8.0", "typescript": "~4.9.3"}, "publishConfig": {"access": "public"}, "bundler": {"exportEntries": ["./src/index.ts"], "managerEntries": ["./src/manager.tsx"], "previewEntries": ["./src/preview.tsx"]}, "gitHead": "e6a7fd8a655c69780bc20b9749c2699e44beae17", "storybook": {"displayName": "Measure", "unsupportedFrameworks": ["react-native"], "icon": "https://user-images.githubusercontent.com/42671/119589951-dbcd9600-bda1-11eb-9227-078f3cfc1e74.png"}}