"""
监控相关路由
"""
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Dict, List, Any, Optional

from ....infrastructure.monitoring.error_monitor import error_monitor, ErrorSeverity, ErrorCategory
from ....infrastructure.config.logging import get_logger
from ..dependencies import get_current_user, require_admin_permission

logger = get_logger(__name__)
router = APIRouter(prefix="/monitoring", tags=["monitoring"])


@router.get("/errors/stats", response_model=Dict[str, Any])
async def get_error_statistics(
    current_user = Depends(get_current_user),
    _: None = Depends(require_admin_permission)
):
    """获取错误统计信息"""
    try:
        stats = error_monitor.get_error_statistics()
        
        # 添加额外的分析信息
        stats["analysis"] = {
            "error_rate_per_hour": stats["total_errors"] / max(stats["uptime_hours"], 1),
            "critical_error_count": stats["errors_by_severity"].get("critical", 0),
            "most_common_category": max(
                stats["errors_by_category"].items(), 
                key=lambda x: x[1], 
                default=("unknown", 0)
            )[0],
            "health_status": _calculate_health_status(stats)
        }
        
        return stats
        
    except Exception as e:
        logger.error("获取错误统计失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get error statistics")


@router.get("/errors/recent", response_model=List[Dict[str, Any]])
async def get_recent_errors(
    limit: int = Query(50, ge=1, le=200),
    severity: Optional[str] = Query(None),
    category: Optional[str] = Query(None),
    current_user = Depends(get_current_user),
    _: None = Depends(require_admin_permission)
):
    """获取最近的错误"""
    try:
        errors = error_monitor.get_recent_errors(limit)
        
        # 过滤条件
        if severity:
            errors = [e for e in errors if e.get("severity") == severity]
        
        if category:
            errors = [e for e in errors if e.get("category") == category]
        
        return errors
        
    except Exception as e:
        logger.error("获取最近错误失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get recent errors")


@router.get("/errors/by-date/{date}", response_model=List[Dict[str, Any]])
async def get_errors_by_date(
    date: str,
    current_user = Depends(get_current_user),
    _: None = Depends(require_admin_permission)
):
    """获取指定日期的错误"""
    try:
        # 验证日期格式
        try:
            datetime.strptime(date, '%Y-%m-%d')
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid date format. Use YYYY-MM-DD")
        
        errors = error_monitor.get_errors_by_date(date)
        return errors
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("获取指定日期错误失败", error=str(e), date=date)
        raise HTTPException(status_code=500, detail="Failed to get errors by date")


@router.post("/errors/{error_id}/resolve")
async def resolve_error(
    error_id: str,
    resolution_notes: str,
    current_user = Depends(get_current_user),
    _: None = Depends(require_admin_permission)
):
    """标记错误为已解决"""
    try:
        success = error_monitor.resolve_error(error_id, resolution_notes)
        
        if success:
            logger.info("错误已解决", error_id=error_id, resolved_by=current_user.id)
            return {"message": "Error resolved successfully", "error_id": error_id}
        else:
            raise HTTPException(status_code=404, detail="Error not found")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error("解决错误失败", error=str(e), error_id=error_id)
        raise HTTPException(status_code=500, detail="Failed to resolve error")


@router.post("/errors/cleanup")
async def cleanup_old_errors(
    days: int = Query(30, ge=1, le=365),
    current_user = Depends(get_current_user),
    _: None = Depends(require_admin_permission)
):
    """清理旧的错误日志"""
    try:
        cleaned_count = error_monitor.cleanup_old_errors(days)
        
        logger.info("清理旧错误日志", cleaned_count=cleaned_count, days=days, operator=current_user.id)
        
        return {
            "message": f"Cleaned up {cleaned_count} old error log files",
            "cleaned_count": cleaned_count,
            "days": days
        }
        
    except Exception as e:
        logger.error("清理错误日志失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to cleanup old errors")


@router.get("/health", response_model=Dict[str, Any])
async def get_system_health():
    """获取系统健康状态"""
    try:
        error_stats = error_monitor.get_error_statistics()
        
        # 计算健康指标
        health_metrics = {
            "overall_status": _calculate_health_status(error_stats),
            "error_rate": error_stats["total_errors"] / max(error_stats["uptime_hours"], 1),
            "critical_errors": error_stats["errors_by_severity"].get("critical", 0),
            "recent_error_trend": _calculate_error_trend(error_stats),
            "uptime_hours": error_stats["uptime_hours"],
            "timestamp": datetime.utcnow().isoformat()
        }
        
        return health_metrics
        
    except Exception as e:
        logger.error("获取系统健康状态失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get system health")


@router.get("/alerts", response_model=List[Dict[str, Any]])
async def get_active_alerts(
    current_user = Depends(get_current_user),
    _: None = Depends(require_admin_permission)
):
    """获取活跃的警报"""
    try:
        error_stats = error_monitor.get_error_statistics()
        alerts = []
        
        # 检查严重错误
        critical_count = error_stats["errors_by_severity"].get("critical", 0)
        if critical_count > 0:
            alerts.append({
                "type": "critical_errors",
                "severity": "critical",
                "message": f"发现 {critical_count} 个严重错误",
                "count": critical_count,
                "timestamp": datetime.utcnow().isoformat()
            })
        
        # 检查错误率
        error_rate = error_stats["total_errors"] / max(error_stats["uptime_hours"], 1)
        if error_rate > 10:  # 每小时超过10个错误
            alerts.append({
                "type": "high_error_rate",
                "severity": "high",
                "message": f"错误率过高: {error_rate:.2f} 错误/小时",
                "error_rate": error_rate,
                "timestamp": datetime.utcnow().isoformat()
            })
        
        # 检查最近错误趋势
        recent_errors = error_monitor.get_recent_errors(20)
        recent_critical = sum(1 for e in recent_errors if e.get("severity") == "critical")
        if recent_critical > 3:
            alerts.append({
                "type": "recent_critical_spike",
                "severity": "high",
                "message": f"最近20个错误中有 {recent_critical} 个严重错误",
                "count": recent_critical,
                "timestamp": datetime.utcnow().isoformat()
            })
        
        return alerts
        
    except Exception as e:
        logger.error("获取活跃警报失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get active alerts")


def _calculate_health_status(error_stats: Dict[str, Any]) -> str:
    """计算系统健康状态"""
    critical_count = error_stats["errors_by_severity"].get("critical", 0)
    error_rate = error_stats["total_errors"] / max(error_stats["uptime_hours"], 1)
    
    if critical_count > 0:
        return "critical"
    elif error_rate > 10:
        return "degraded"
    elif error_rate > 5:
        return "warning"
    else:
        return "healthy"


def _calculate_error_trend(error_stats: Dict[str, Any]) -> str:
    """计算错误趋势"""
    recent_errors = error_stats.get("recent_errors", [])
    
    if len(recent_errors) < 10:
        return "insufficient_data"
    
    # 简单的趋势分析：比较最近10个和之前10个错误的时间间隔
    recent_10 = recent_errors[-10:]
    previous_10 = recent_errors[-20:-10] if len(recent_errors) >= 20 else []
    
    if not previous_10:
        return "insufficient_data"
    
    try:
        recent_times = [datetime.fromisoformat(e["timestamp"]) for e in recent_10]
        previous_times = [datetime.fromisoformat(e["timestamp"]) for e in previous_10]
        
        recent_span = (recent_times[-1] - recent_times[0]).total_seconds()
        previous_span = (previous_times[-1] - previous_times[0]).total_seconds()
        
        if recent_span < previous_span * 0.5:
            return "increasing"
        elif recent_span > previous_span * 2:
            return "decreasing"
        else:
            return "stable"
            
    except Exception:
        return "unknown"
