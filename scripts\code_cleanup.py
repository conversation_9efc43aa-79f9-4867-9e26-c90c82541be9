#!/usr/bin/env python3
"""
自动化代码清理脚本
清理未使用的导入、变量和函数
"""
import re
import sys
from pathlib import Path
from typing import List, Dict, Set, Tuple

def analyze_typescript_file(file_path: Path) -> Dict[str, List[str]]:
    """分析TypeScript文件中的未使用项"""
    issues = {
        "unused_imports": [],
        "unused_variables": [],
        "unused_functions": [],
        "type_errors": []
    }
    
    try:
        content = file_path.read_text(encoding='utf-8')
        lines = content.split('\n')
        
        # 检查未使用的React导入
        for i, line in enumerate(lines, 1):
            if re.match(r'import React', line) and 'React' not in content.replace(line, ''):
                issues["unused_imports"].append(f"Line {i}: Unused React import")
            
            # 检查未使用的变量声明
            var_match = re.search(r'const\s+(\w+)\s*=', line)
            if var_match:
                var_name = var_match.group(1)
                # 简单检查：如果变量名在文件中只出现一次（即声明处），则可能未使用
                if content.count(var_name) == 1:
                    issues["unused_variables"].append(f"Line {i}: Potentially unused variable '{var_name}'")
        
    except Exception as e:
        issues["type_errors"].append(f"Error reading file: {e}")
    
    return issues

def fix_common_typescript_issues(file_path: Path) -> bool:
    """修复常见的TypeScript问题"""
    try:
        content = file_path.read_text(encoding='utf-8')
        original_content = content
        
        # 修复1: 移除未使用的React导入（当文件中没有JSX时）
        if 'import React from \'react\'' in content and '<' not in content:
            content = content.replace('import React from \'react\'', '')
            content = re.sub(r'\nimport React from \'react\'\n', '\n', content)
        
        # 修复2: 移除未使用的React导入（在导入列表中）
        content = re.sub(r'import React, \{', 'import {', content)
        content = re.sub(r'import \{ React, ', 'import { ', content)
        
        # 修复3: 清理空的导入行
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
        
        # 修复4: 移除行尾空格
        lines = content.split('\n')
        lines = [line.rstrip() for line in lines]
        content = '\n'.join(lines)
        
        if content != original_content:
            file_path.write_text(content, encoding='utf-8')
            return True
        
        return False
        
    except Exception as e:
        print(f"Error fixing {file_path}: {e}")
        return False

def clean_frontend_code():
    """清理前端代码"""
    print("🧹 清理前端代码")
    print("=" * 40)
    
    frontend_dir = Path("frontend/src")
    if not frontend_dir.exists():
        print("❌ 前端源码目录不存在")
        return False
    
    typescript_files = list(frontend_dir.rglob("*.tsx")) + list(frontend_dir.rglob("*.ts"))
    
    fixed_files = []
    issues_found = {}
    
    for file_path in typescript_files:
        # 跳过node_modules和其他不需要处理的目录
        if 'node_modules' in str(file_path) or '.d.ts' in str(file_path):
            continue
        
        try:
            rel_path = file_path.relative_to(Path.cwd())
        except ValueError:
            rel_path = file_path
        print(f"📄 分析文件: {rel_path}")
        
        # 分析问题
        issues = analyze_typescript_file(file_path)
        if any(issues.values()):
            issues_found[str(file_path)] = issues
        
        # 尝试自动修复
        if fix_common_typescript_issues(file_path):
            fixed_files.append(str(file_path))
            print(f"   ✅ 已修复")
        else:
            print(f"   ℹ️  无需修复")
    
    print(f"\n📊 清理结果:")
    print(f"   扫描文件: {len(typescript_files)}")
    print(f"   修复文件: {len(fixed_files)}")
    print(f"   发现问题: {len(issues_found)}")
    
    if fixed_files:
        print(f"\n✅ 已修复的文件:")
        for file_path in fixed_files:
            print(f"   - {file_path}")
    
    return True

def clean_python_imports():
    """清理Python导入"""
    print("\n🐍 清理Python导入")
    print("=" * 40)
    
    src_dir = Path("src")
    if not src_dir.exists():
        print("❌ Python源码目录不存在")
        return False
    
    python_files = list(src_dir.rglob("*.py"))
    
    issues_found = []
    
    for file_path in python_files:
        if '__pycache__' in str(file_path):
            continue
        
        try:
            content = file_path.read_text(encoding='utf-8')
            lines = content.split('\n')
            
            # 检查未使用的导入（简单检查）
            for i, line in enumerate(lines, 1):
                if line.strip().startswith('import ') or line.strip().startswith('from '):
                    # 提取导入的模块名
                    import_match = re.search(r'import\s+(\w+)', line)
                    if import_match:
                        module_name = import_match.group(1)
                        # 检查模块是否在文件中被使用
                        if content.count(module_name) == 1:  # 只在导入行出现
                            issues_found.append(f"{file_path}:{i} - Potentially unused import: {module_name}")
        
        except Exception as e:
            print(f"Error analyzing {file_path}: {e}")
    
    print(f"📊 Python导入分析:")
    print(f"   扫描文件: {len(python_files)}")
    print(f"   潜在问题: {len(issues_found)}")
    
    if issues_found:
        print(f"\n⚠️  发现的潜在问题 (需要手动检查):")
        for issue in issues_found[:10]:  # 只显示前10个
            print(f"   - {issue}")
        if len(issues_found) > 10:
            print(f"   ... 还有 {len(issues_found) - 10} 个问题")
    
    return True

def optimize_code_structure():
    """优化代码结构"""
    print("\n🏗️  优化代码结构")
    print("=" * 40)
    
    optimizations = []
    
    # 检查是否有重复的工具函数
    utils_files = list(Path(".").rglob("*utils*.py")) + list(Path(".").rglob("*helpers*.py"))
    if len(utils_files) > 3:
        optimizations.append(f"发现 {len(utils_files)} 个工具文件，建议合并重复功能")
    
    # 检查是否有过长的文件
    large_files = []
    for file_path in Path("src").rglob("*.py"):
        try:
            lines = len(file_path.read_text(encoding='utf-8').split('\n'))
            if lines > 500:
                large_files.append((file_path, lines))
        except:
            pass
    
    if large_files:
        optimizations.append(f"发现 {len(large_files)} 个超长文件 (>500行)，建议拆分")
    
    # 检查是否有循环导入的可能
    init_files = list(Path("src").rglob("__init__.py"))
    complex_inits = []
    for init_file in init_files:
        try:
            content = init_file.read_text(encoding='utf-8')
            if len(content.split('\n')) > 20:
                complex_inits.append(init_file)
        except:
            pass
    
    if complex_inits:
        optimizations.append(f"发现 {len(complex_inits)} 个复杂的__init__.py文件，可能存在循环导入风险")
    
    print(f"📊 结构优化建议:")
    if optimizations:
        for opt in optimizations:
            print(f"   💡 {opt}")
    else:
        print("   ✅ 代码结构良好，无需优化")
    
    return True

def main():
    """主函数"""
    print("🎯 代码清理优化")
    print("=" * 60)
    
    # 清理前端代码
    frontend_result = clean_frontend_code()
    
    # 清理Python导入
    python_result = clean_python_imports()
    
    # 优化代码结构
    structure_result = optimize_code_structure()
    
    print("\n" + "=" * 60)
    print("📊 清理结果总结:")
    print(f"   前端代码清理: {'✅ 完成' if frontend_result else '❌ 失败'}")
    print(f"   Python导入清理: {'✅ 完成' if python_result else '❌ 失败'}")
    print(f"   代码结构优化: {'✅ 完成' if structure_result else '❌ 失败'}")
    
    if frontend_result and python_result and structure_result:
        print("\n🎉 代码清理优化完成！")
        print("\n💡 清理内容:")
        print("   1. 移除了未使用的React导入")
        print("   2. 清理了空白行和行尾空格")
        print("   3. 分析了Python导入使用情况")
        print("   4. 提供了代码结构优化建议")
        print("   5. 提高了代码质量和可维护性")
        
        return 0
    else:
        print("\n❌ 代码清理优化需要进一步处理")
        return 1

if __name__ == "__main__":
    sys.exit(main())
