"""
ngrok管理路由
"""
from fastapi import APIRouter, Depends, HTTPException
from typing import Dict, Any, Optional

from ....infrastructure.ngrok.manager import ngrok_manager
from ....infrastructure.config import settings
from ....infrastructure.config.logging import get_logger
from ..dependencies import require_admin, get_current_user

logger = get_logger(__name__)

router = APIRouter()


@router.get("/status")
async def get_ngrok_status(
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取ngrok状态"""
    try:
        status = ngrok_manager.get_status()
        return {
            "success": True,
            "data": status
        }
    except Exception as e:
        logger.error("获取ngrok状态失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get ngrok status")


@router.post("/start")
async def start_ngrok_tunnel(
    port: Optional[int] = None,
    current_user: Dict[str, Any] = Depends(require_admin)
):
    """启动ngrok隧道"""
    try:
        if not ngrok_manager.is_available():
            raise HTTPException(
                status_code=400, 
                detail="pyngrok not available. Please install with: pip install pyngrok"
            )
        
        if not settings.ngrok_enabled:
            raise HTTPException(
                status_code=400,
                detail="ngrok is disabled. Set NGROK_ENABLED=true in .env file"
            )
        
        target_port = port or settings.server_port
        public_url = ngrok_manager.start_tunnel(target_port)
        
        if public_url:
            return {
                "success": True,
                "message": "ngrok tunnel started successfully",
                "data": {
                    "public_url": public_url,
                    "local_port": target_port,
                    "callback_url": f"{public_url}/auth/callback"
                }
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to start ngrok tunnel")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error("启动ngrok隧道失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to start ngrok tunnel: {str(e)}")


@router.post("/stop")
async def stop_ngrok_tunnel(
    current_user: Dict[str, Any] = Depends(require_admin)
):
    """停止ngrok隧道"""
    try:
        ngrok_manager.stop_tunnel()
        return {
            "success": True,
            "message": "ngrok tunnel stopped successfully"
        }
    except Exception as e:
        logger.error("停止ngrok隧道失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to stop ngrok tunnel")


@router.post("/restart")
async def restart_ngrok_tunnel(
    port: Optional[int] = None,
    current_user: Dict[str, Any] = Depends(require_admin)
):
    """重启ngrok隧道"""
    try:
        if not ngrok_manager.is_available():
            raise HTTPException(
                status_code=400,
                detail="pyngrok not available"
            )
        
        target_port = port or settings.server_port
        public_url = ngrok_manager.restart_tunnel(target_port)
        
        if public_url:
            return {
                "success": True,
                "message": "ngrok tunnel restarted successfully",
                "data": {
                    "public_url": public_url,
                    "local_port": target_port,
                    "callback_url": f"{public_url}/auth/callback"
                }
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to restart ngrok tunnel")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error("重启ngrok隧道失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to restart ngrok tunnel")


@router.get("/info")
async def get_tunnel_info(
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取隧道详细信息"""
    try:
        tunnel_info = ngrok_manager.get_tunnel_info()
        
        if tunnel_info:
            return {
                "success": True,
                "data": tunnel_info
            }
        else:
            return {
                "success": True,
                "data": None,
                "message": "No active tunnel"
            }
            
    except Exception as e:
        logger.error("获取隧道信息失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get tunnel info")


@router.post("/update-callback")
async def update_callback_url(
    current_user: Dict[str, Any] = Depends(require_admin)
):
    """更新EVE SSO回调URL"""
    try:
        if not ngrok_manager.public_url:
            raise HTTPException(
                status_code=400,
                detail="No active ngrok tunnel"
            )
        
        success = ngrok_manager.update_callback_url()
        
        if success:
            callback_url = f"{ngrok_manager.public_url}/auth/callback"
            return {
                "success": True,
                "message": "Callback URL updated successfully",
                "data": {
                    "callback_url": callback_url,
                    "reminder": "Please update the callback URL in EVE Developer Portal"
                }
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to update callback URL")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error("更新回调URL失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to update callback URL")


@router.get("/config")
async def get_ngrok_config(
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取ngrok配置"""
    try:
        config = {
            "enabled": settings.ngrok_enabled,
            "region": settings.ngrok_region,
            "subdomain": settings.ngrok_subdomain,
            "domain": settings.ngrok_domain,
            "auto_update_callback": settings.ngrok_auto_update_callback,
            "server_port": settings.server_port,
            "auth_token_configured": bool(settings.ngrok_auth_token)
        }
        
        return {
            "success": True,
            "data": config
        }
    except Exception as e:
        logger.error("获取ngrok配置失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get ngrok config")


@router.get("/health")
async def ngrok_health_check(
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """ngrok健康检查"""
    try:
        is_healthy = ngrok_manager.health_check()
        
        return {
            "success": True,
            "data": {
                "healthy": is_healthy,
                "available": ngrok_manager.is_available(),
                "tunnel_active": ngrok_manager.tunnel is not None,
                "public_url": ngrok_manager.public_url
            }
        }
    except Exception as e:
        logger.error("ngrok健康检查失败", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to perform ngrok health check")
