#!/usr/bin/env python3
"""
生产级EVE Online Assistant启动脚本
包含完整的依赖检查、安装和测试
"""
import sys
import subprocess
import os
import time

def print_header(title):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f"🚀 {title}")
    print("=" * 60)

def print_step(step, description):
    """打印步骤"""
    print(f"\n📋 步骤 {step}: {description}")
    print("-" * 40)

def install_package(package, python_path=None):
    """安装包到指定Python环境"""
    if python_path is None:
        python_path = sys.executable
    
    try:
        print(f"   正在安装 {package}...")
        result = subprocess.run([
            python_path, "-m", "pip", "install", package
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print(f"   ✅ {package} 安装成功")
            return True
        else:
            print(f"   ❌ {package} 安装失败:")
            print(f"      stderr: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print(f"   ❌ {package} 安装超时")
        return False
    except Exception as e:
        print(f"   ❌ {package} 安装异常: {e}")
        return False

def test_import(module_name, display_name=None):
    """测试模块导入"""
    if display_name is None:
        display_name = module_name
    
    try:
        __import__(module_name)
        print(f"   ✅ {display_name}")
        return True
    except ImportError as e:
        print(f"   ❌ {display_name} - {e}")
        return False

def test_application_import():
    """测试应用程序导入"""
    print_step(3, "测试应用程序模块导入")
    
    test_modules = [
        ("src.infrastructure.config", "配置模块"),
        ("src.infrastructure.esi", "ESI客户端"),
        ("src.presentation.api.main", "FastAPI应用"),
    ]
    
    all_passed = True
    for module, name in test_modules:
        try:
            __import__(module)
            print(f"   ✅ {name}")
        except Exception as e:
            print(f"   ❌ {name} - {e}")
            all_passed = False
    
    return all_passed

def start_server():
    """启动服务器"""
    print_step(4, "启动FastAPI服务器")
    
    try:
        print("   🌐 启动服务器...")
        print("   本地地址: http://127.0.0.1:8000")
        print("   API文档: http://127.0.0.1:8000/docs")
        print("   按 Ctrl+C 停止服务器")
        print()
        
        # 启动uvicorn服务器
        subprocess.run([
            sys.executable, "-m", "uvicorn",
            "src.presentation.api.main:app",
            "--reload",
            "--host", "127.0.0.1",
            "--port", "8000"
        ])
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print_header("EVE Online Assistant 生产级启动")
    
    # 显示Python信息
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    
    # 步骤1: 检查核心依赖
    print_step(1, "检查核心依赖")
    
    core_packages = [
        ("pydantic", "pydantic"),
        ("fastapi", "fastapi"),
        ("uvicorn", "uvicorn"),
        ("dotenv", "python-dotenv"),
        ("structlog", "structlog"),
        ("httpx", "httpx"),
        ("sqlalchemy", "sqlalchemy"),
        ("alembic", "alembic"),
    ]
    
    missing_packages = []
    for module, package in core_packages:
        if not test_import(module, package):
            missing_packages.append(package)
    
    # 步骤2: 安装缺失的依赖
    if missing_packages:
        print_step(2, "安装缺失的依赖")
        
        failed_packages = []
        for package in missing_packages:
            if not install_package(package):
                failed_packages.append(package)
        
        if failed_packages:
            print(f"\n❌ 以下包安装失败: {', '.join(failed_packages)}")
            print("请手动安装这些包后重试")
            return 1
        
        # 重新测试
        print("\n🔍 重新验证依赖...")
        for module, package in core_packages:
            test_import(module, package)
    else:
        print("   🎉 所有核心依赖都已安装！")
    
    # 步骤3: 测试应用程序导入
    if not test_application_import():
        print("\n❌ 应用程序模块导入失败，请检查代码")
        return 1
    
    print("\n🎉 所有测试通过！")
    
    # 步骤4: 启动服务器
    start_server()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
