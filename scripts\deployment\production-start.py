#!/usr/bin/env python3
"""
生产级EVE Online Assistant启动脚本
包含完整的依赖检查、安装和测试
"""
import sys
import subprocess
import os
import time
import json
import requests
import threading
from pathlib import Path

def print_header(title):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f"🚀 {title}")
    print("=" * 60)

def print_step(step, description):
    """打印步骤"""
    print(f"\n📋 步骤 {step}: {description}")
    print("-" * 40)

def check_ngrok_installed():
    """检查ngrok是否已安装"""
    try:
        result = subprocess.run(['ngrok', 'version'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"   ✅ ngrok已安装: {version}")
            return True
        else:
            print("   ❌ ngrok未安装或无法运行")
            return False
    except (FileNotFoundError, subprocess.TimeoutExpired):
        print("   ❌ ngrok未找到，请先安装ngrok")
        print("      下载地址: https://ngrok.com/download")
        return False
    except Exception as e:
        print(f"   ❌ 检查ngrok时出错: {e}")
        return False

def start_ngrok_tunnel(port=8000, timeout=30):
    """启动ngrok隧道"""
    print(f"   🚀 启动ngrok隧道，端口: {port}")

    try:
        # 启动ngrok进程
        ngrok_process = subprocess.Popen(
            ['ngrok', 'http', str(port), '--log=stdout'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )

        # 等待ngrok启动
        print("   ⏳ 等待ngrok启动...")
        time.sleep(5)

        # 获取ngrok隧道信息
        for attempt in range(timeout // 2):
            try:
                response = requests.get('http://localhost:4040/api/tunnels', timeout=2)
                if response.status_code == 200:
                    tunnels = response.json().get('tunnels', [])
                    if tunnels:
                        public_url = tunnels[0]['public_url']
                        print(f"   ✅ ngrok隧道已启动: {public_url}")
                        return ngrok_process, public_url
                    else:
                        print("   ⏳ 等待ngrok隧道建立...")
                        time.sleep(2)
                        continue
                else:
                    print(f"   ⚠️  ngrok API响应异常: {response.status_code}")
                    time.sleep(2)
                    continue
            except requests.exceptions.RequestException:
                print("   ⏳ 等待ngrok API可用...")
                time.sleep(2)
                continue

        print("   ❌ ngrok隧道启动超时")
        ngrok_process.terminate()
        return None, None

    except Exception as e:
        print(f"   ❌ 启动ngrok隧道失败: {e}")
        return None, None

def update_env_callback_url(public_url):
    """更新.env文件中的回调URL"""
    env_file = Path('.env')
    if not env_file.exists():
        print("   ❌ .env文件不存在")
        return False

    try:
        # 读取现有内容
        with open(env_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        # 更新回调URL
        callback_url = f"{public_url}/auth/callback"
        updated = False

        for i, line in enumerate(lines):
            if line.startswith('EVE_SSO_CALLBACK_URL='):
                old_url = line.strip().split('=', 1)[1].strip('"')
                lines[i] = f'EVE_SSO_CALLBACK_URL="{callback_url}"\n'
                updated = True
                print(f"   📝 更新回调URL:")
                print(f"      旧: {old_url}")
                print(f"      新: {callback_url}")
                break

        if updated:
            # 写回文件
            with open(env_file, 'w', encoding='utf-8') as f:
                f.writelines(lines)
            print("   ✅ .env文件已更新")
            return True
        else:
            print("   ❌ 未找到EVE_SSO_CALLBACK_URL配置")
            return False

    except Exception as e:
        print(f"   ❌ 更新.env文件失败: {e}")
        return False

def setup_ngrok():
    """设置ngrok隧道"""
    print_step("0", "设置ngrok隧道")

    # 检查ngrok是否安装
    if not check_ngrok_installed():
        print("\n⚠️  ngrok未安装，将跳过隧道设置")
        print("   如需EVE SSO功能，请安装ngrok后重新运行")
        return None, None

    # 启动ngrok隧道
    ngrok_process, public_url = start_ngrok_tunnel(8000)
    if not ngrok_process or not public_url:
        print("\n❌ ngrok隧道启动失败")
        return None, None

    # 更新环境变量
    if not update_env_callback_url(public_url):
        print("\n⚠️  环境变量更新失败，但ngrok隧道已启动")

    print(f"\n🎉 ngrok设置完成!")
    print(f"   公网地址: {public_url}")
    print(f"   本地地址: http://localhost:8000")
    print(f"   回调URL: {public_url}/auth/callback")

    return ngrok_process, public_url

def install_package(package, python_path=None):
    """安装包到指定Python环境"""
    if python_path is None:
        python_path = sys.executable
    
    try:
        print(f"   正在安装 {package}...")
        result = subprocess.run([
            python_path, "-m", "pip", "install", package
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print(f"   ✅ {package} 安装成功")
            return True
        else:
            print(f"   ❌ {package} 安装失败:")
            print(f"      stderr: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print(f"   ❌ {package} 安装超时")
        return False
    except Exception as e:
        print(f"   ❌ {package} 安装异常: {e}")
        return False

def test_import(module_name, display_name=None):
    """测试模块导入"""
    if display_name is None:
        display_name = module_name
    
    try:
        __import__(module_name)
        print(f"   ✅ {display_name}")
        return True
    except ImportError as e:
        print(f"   ❌ {display_name} - {e}")
        return False



def start_frontend_server():
    """启动前端服务器"""
    print("   🎨 启动前端服务器...")

    # 检查是否在frontend目录中有package.json
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("   ⚠️  frontend目录不存在，跳过前端启动")
        return None

    package_json = frontend_dir / "package.json"
    if not package_json.exists():
        print("   ⚠️  package.json不存在，跳过前端启动")
        return None

    try:
        # 检查node_modules是否存在
        node_modules = frontend_dir / "node_modules"
        if not node_modules.exists():
            print("   📦 安装前端依赖...")

            # 确保使用正确的npm路径
            npm_cmd = "npm"
            if os.name == 'nt':  # Windows系统
                npm_cmd = "npm.cmd"

            install_result = subprocess.run(
                [npm_cmd, "install"],
                cwd=frontend_dir,
                capture_output=True,
                text=True,
                timeout=120,
                shell=True  # 在Windows上使用shell
            )
            if install_result.returncode != 0:
                print(f"   ❌ 前端依赖安装失败: {install_result.stderr}")
                return None
            print("   ✅ 前端依赖安装成功")
        else:
            print("   ✅ 前端依赖已存在")

        # 启动前端开发服务器
        print("   🚀 启动Vite开发服务器...")

        # 确保使用正确的npm路径
        npm_cmd = "npm"
        if os.name == 'nt':  # Windows系统
            npm_cmd = "npm.cmd"

        frontend_process = subprocess.Popen(
            [npm_cmd, "run", "dev"],
            cwd=frontend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,  # 合并stderr到stdout
            text=True,
            bufsize=1,  # 行缓冲
            universal_newlines=True,
            shell=True  # 在Windows上使用shell
        )

        # 等待前端服务器启动并检查状态
        print("   ⏳ 等待前端服务器启动...")
        time.sleep(8)  # 增加等待时间

        # 检查进程是否还在运行
        if frontend_process.poll() is None:
            # 进程还在运行，尝试测试端口
            try:
                import urllib.request
                urllib.request.urlopen('http://localhost:3000', timeout=3)
                print("   ✅ 前端服务器启动成功")
                return frontend_process
            except:
                print("   ⚠️  前端服务器启动中，请稍候...")
                return frontend_process
        else:
            # 进程已退出，获取错误信息
            stdout, stderr = frontend_process.communicate()
            print(f"   ❌ 前端服务器启动失败")
            if stdout:
                print(f"      输出: {stdout}")
            if stderr:
                print(f"      错误: {stderr}")
            return None

    except FileNotFoundError as e:
        print(f"   ❌ npm未找到: {e}")
        print("      可能的解决方案:")
        print("      1. 确保Node.js已安装: https://nodejs.org/")
        print("      2. 重启命令行窗口以刷新PATH环境变量")
        print("      3. 手动启动前端: cd frontend && npm run dev")
        return None
    except subprocess.TimeoutExpired:
        print("   ❌ 前端依赖安装超时")
        return None
    except Exception as e:
        print(f"   ❌ 启动前端服务器失败: {e}")
        return None

def start_server(ngrok_process=None, public_url=None):
    """启动服务器"""
    print_step("5", "启动应用服务器")

    try:
        # 启动前端服务器
        frontend_process = start_frontend_server()

        print("   🌐 启动后端服务器...")
        print("   本地API地址: http://127.0.0.1:8000")
        print("   本地前端地址: http://127.0.0.1:3000")
        print("   API文档: http://127.0.0.1:8000/docs")

        if public_url:
            print(f"   公网API地址: {public_url}")
            print(f"   公网API文档: {public_url}/docs")
            print(f"   EVE回调URL: {public_url}/auth/callback")

        print("   按 Ctrl+C 停止所有服务")
        print()

        # 启动uvicorn服务器
        server_process = subprocess.Popen([
            sys.executable, "-m", "uvicorn",
            "src.presentation.api.main:app",
            "--reload",
            "--reload-dir", "src",
            "--host", "127.0.0.1",
            "--port", "8000"
        ])

        # 等待服务器启动
        time.sleep(3)
        print("   ✅ 后端服务器启动成功")

        if frontend_process:
            print("   ✅ 前端服务器启动成功")
            print(f"\n🎉 应用已启动！")
            print(f"   前端界面: http://localhost:3000")
            print(f"   后端API: http://localhost:8000")
            if public_url:
                print(f"   公网访问: {public_url}")
        else:
            print("   ⚠️  前端服务器启动失败，仅后端可用")

        # 等待用户中断
        try:
            server_process.wait()
        except KeyboardInterrupt:
            print("\n🛑 正在停止所有服务...")

            # 停止FastAPI服务器
            server_process.terminate()
            try:
                server_process.wait(timeout=5)
                print("   ✅ FastAPI服务器已停止")
            except subprocess.TimeoutExpired:
                server_process.kill()
                print("   ⚠️  强制终止FastAPI服务器")

            # 停止前端服务器
            if frontend_process:
                frontend_process.terminate()
                try:
                    frontend_process.wait(timeout=5)
                    print("   ✅ 前端服务器已停止")
                except subprocess.TimeoutExpired:
                    frontend_process.kill()
                    print("   ⚠️  强制终止前端服务器")

            # 停止ngrok隧道
            if ngrok_process:
                ngrok_process.terminate()
                try:
                    ngrok_process.wait(timeout=5)
                    print("   ✅ ngrok隧道已停止")
                except subprocess.TimeoutExpired:
                    ngrok_process.kill()
                    print("   ⚠️  强制终止ngrok隧道")

            print("   🎉 所有服务已停止")

    except Exception as e:
        print(f"❌ 启动失败: {e}")

        # 清理进程
        if ngrok_process:
            ngrok_process.terminate()

        return False

    return True

def main():
    """主函数"""
    print_header("EVE Online Assistant 生产级启动")

    # 确保工作目录正确
    script_dir = Path(__file__).parent.parent.parent  # 回到项目根目录
    os.chdir(script_dir)
    print(f"工作目录: {os.getcwd()}")

    # 添加项目根目录到Python路径
    if str(script_dir) not in sys.path:
        sys.path.insert(0, str(script_dir))

    # 显示Python信息
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")

    # 步骤0: 设置ngrok隧道
    ngrok_process, public_url = setup_ngrok()

    # 步骤1: 检查核心依赖
    print_step("1", "检查核心依赖")
    
    core_packages = [
        ("pydantic", "pydantic"),
        ("fastapi", "fastapi"),
        ("uvicorn", "uvicorn"),
        ("dotenv", "python-dotenv"),
        ("structlog", "structlog"),
        ("httpx", "httpx"),
        ("sqlalchemy", "sqlalchemy"),
        ("alembic", "alembic"),
    ]
    
    missing_packages = []
    for module, package in core_packages:
        if not test_import(module, package):
            missing_packages.append(package)
    
    # 步骤2: 安装缺失的依赖
    if missing_packages:
        print_step("2", "安装缺失的依赖")

        failed_packages = []
        for package in missing_packages:
            if not install_package(package):
                failed_packages.append(package)

        if failed_packages:
            print(f"\n❌ 以下包安装失败: {', '.join(failed_packages)}")
            print("请手动安装这些包后重试")
            # 清理ngrok进程
            if ngrok_process:
                ngrok_process.terminate()
            return 1

        # 重新测试
        print("\n🔍 重新验证依赖...")
        for module, package in core_packages:
            test_import(module, package)
    else:
        print("   🎉 所有核心依赖都已安装！")

    # 步骤3: 安装项目包
    print_step("3", "安装项目包")
    if not install_package("-e ."):
        print("\n⚠️  项目包安装失败，但继续启动...")
    else:
        print("   ✅ 项目包安装成功")

    # 步骤4: 测试应用程序导入
    print_step("4", "测试应用程序模块导入")

    test_modules = [
        ("src.infrastructure.config", "配置模块"),
        ("src.infrastructure.esi", "ESI客户端"),
        ("src.presentation.api.main", "FastAPI应用"),
        ("src.infrastructure.persistence.repositories", "仓储模块"),
    ]

    all_passed = True
    for module, name in test_modules:
        try:
            __import__(module)
            print(f"   ✅ {name}")
        except Exception as e:
            print(f"   ❌ {name} - {e}")
            all_passed = False

    if not all_passed:
        print("\n❌ 应用程序模块导入失败，请检查代码")
        # 清理ngrok进程
        if ngrok_process:
            ngrok_process.terminate()
        return 1

    print("\n🎉 所有测试通过！")

    # 显示启动信息
    if public_url:
        print(f"\n📋 服务信息:")
        print(f"   本地地址: http://localhost:8000")
        print(f"   公网地址: {public_url}")
        print(f"   EVE回调URL: {public_url}/auth/callback")
        print(f"   前端地址: http://localhost:3000")
        print(f"\n⚠️  请确保在EVE开发者门户中配置了正确的回调URL:")
        print(f"   {public_url}/auth/callback")

    # 步骤5: 启动服务器
    start_server(ngrok_process, public_url)

    return 0

if __name__ == "__main__":
    sys.exit(main())
