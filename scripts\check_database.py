#!/usr/bin/env python3
"""
检查数据库结构和数据
"""
import sys
import sqlite3
from datetime import datetime

sys.path.insert(0, '.')

def check_database():
    """检查数据库结构"""
    print("🗄️ 数据库结构检查")
    print("=" * 50)
    
    try:
        # 连接数据库
        conn = sqlite3.connect('eve_assistant.db')
        cursor = conn.cursor()
        
        # 1. 检查所有表
        print("\n1. 检查数据库表...")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"   数据库中共有 {len(tables)} 个表:")
        for table in tables:
            table_name = table[0]
            print(f"   - {table_name}")
            
            # 检查表结构
            cursor.execute(f"PRAGMA table_info({table_name});")
            columns = cursor.fetchall()
            print(f"     字段数: {len(columns)}")
            
            # 检查记录数
            cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
            count = cursor.fetchone()[0]
            print(f"     记录数: {count}")
        
        # 2. 检查关键表的详细结构
        key_tables = ['users', 'tokens', 'characters']
        
        for table_name in key_tables:
            print(f"\n2. 检查 {table_name} 表结构...")
            try:
                cursor.execute(f"PRAGMA table_info({table_name});")
                columns = cursor.fetchall()
                
                if columns:
                    print(f"   {table_name} 表字段:")
                    for col in columns:
                        col_id, name, type_name, not_null, default, pk = col
                        pk_str = " (主键)" if pk else ""
                        null_str = " NOT NULL" if not_null else ""
                        default_str = f" DEFAULT {default}" if default else ""
                        print(f"     - {name}: {type_name}{null_str}{default_str}{pk_str}")
                else:
                    print(f"   ❌ {table_name} 表不存在")
            except Exception as e:
                print(f"   ❌ 检查 {table_name} 表失败: {e}")
        
        # 3. 检查索引
        print(f"\n3. 检查数据库索引...")
        cursor.execute("SELECT name, tbl_name FROM sqlite_master WHERE type='index' AND name NOT LIKE 'sqlite_%';")
        indexes = cursor.fetchall()
        
        print(f"   数据库中共有 {len(indexes)} 个自定义索引:")
        for index in indexes:
            index_name, table_name = index
            print(f"   - {index_name} (表: {table_name})")
        
        # 4. 检查外键约束
        print(f"\n4. 检查外键约束...")
        cursor.execute("PRAGMA foreign_keys;")
        fk_enabled = cursor.fetchone()[0]
        print(f"   外键约束: {'启用' if fk_enabled else '禁用'}")
        
        # 5. 数据库统计
        print(f"\n5. 数据库统计...")
        cursor.execute("SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size();")
        db_size = cursor.fetchone()[0]
        print(f"   数据库大小: {db_size:,} 字节 ({db_size/1024:.1f} KB)")
        
        cursor.execute("PRAGMA integrity_check;")
        integrity = cursor.fetchone()[0]
        print(f"   完整性检查: {integrity}")
        
        conn.close()
        
        print(f"\n✅ 数据库检查完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return False

def check_auth_tables():
    """检查认证相关表的数据"""
    print(f"\n" + "=" * 50)
    print("🔐 认证表数据检查")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('eve_assistant.db')
        cursor = conn.cursor()
        
        # 检查用户表
        print(f"\n1. 用户表数据...")
        cursor.execute("SELECT COUNT(*) FROM users;")
        user_count = cursor.fetchone()[0]
        print(f"   用户总数: {user_count}")
        
        if user_count > 0:
            cursor.execute("SELECT id, username, email, is_active, main_character_id, created_at FROM users LIMIT 5;")
            users = cursor.fetchall()
            print(f"   最近用户:")
            for user in users:
                print(f"     ID:{user[0]} 用户名:{user[1]} 邮箱:{user[2]} 活跃:{user[3]} 主角色:{user[4]}")
        
        # 检查令牌表
        print(f"\n2. 令牌表数据...")
        cursor.execute("SELECT COUNT(*) FROM tokens;")
        token_count = cursor.fetchone()[0]
        print(f"   令牌总数: {token_count}")
        
        if token_count > 0:
            cursor.execute("SELECT COUNT(*) FROM tokens WHERE is_active = 1;")
            active_tokens = cursor.fetchone()[0]
            print(f"   活跃令牌: {active_tokens}")
            
            cursor.execute("SELECT COUNT(*) FROM tokens WHERE expires_at > datetime('now');")
            valid_tokens = cursor.fetchone()[0]
            print(f"   有效令牌: {valid_tokens}")
        
        # 检查角色表
        print(f"\n3. 角色表数据...")
        cursor.execute("SELECT COUNT(*) FROM characters;")
        char_count = cursor.fetchone()[0]
        print(f"   角色总数: {char_count}")
        
        if char_count > 0:
            cursor.execute("SELECT character_id, name, corporation_id, last_sync_at FROM characters LIMIT 5;")
            characters = cursor.fetchall()
            print(f"   角色列表:")
            for char in characters:
                print(f"     ID:{char[0]} 名称:{char[1]} 公司:{char[2]} 同步:{char[3]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 认证表检查失败: {e}")
        return False

if __name__ == "__main__":
    success1 = check_database()
    success2 = check_auth_tables()
    
    print(f"\n" + "=" * 50)
    if success1 and success2:
        print("🎉 数据库检查全部通过！")
        print("✅ 数据库结构完整")
        print("✅ 认证表正常")
    else:
        print("⚠️  数据库检查发现问题")
    print("=" * 50)
