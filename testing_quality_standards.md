# 测试质量标准

## 导入测试标准 (Import Testing Standards)

### 必须测试的导入类型
1. **核心应用导入**
   ```python
   from src.presentation.api.main import app
   ```

2. **路由模块导入**
   ```python
   from src.presentation.api.routers import monitoring, auth, characters
   ```

3. **依赖注入导入**
   ```python
   from src.presentation.api.dependencies import get_current_user, require_admin
   ```

4. **服务层导入**
   ```python
   from src.application.auth import AuthenticationService
   ```

### 导入测试模板
```python
def test_critical_imports():
    """测试关键模块导入"""
    import_tests = [
        ("FastAPI应用", "from src.presentation.api.main import app"),
        ("认证路由", "from src.presentation.api.routers.auth import router"),
        ("监控路由", "from src.presentation.api.routers.monitoring import router"),
        ("依赖注入", "from src.presentation.api.dependencies import get_current_user"),
    ]
    
    for name, import_stmt in import_tests:
        try:
            exec(import_stmt)
            print(f"✅ {name}")
        except ImportError as e:
            print(f"❌ {name}: {e}")
            return False
    return True
```

## 集成测试标准 (Integration Testing Standards)

### 测试层次结构
```
Level 1: 模块导入测试
├── 核心模块导入
├── 路由模块导入
└── 依赖注入导入

Level 2: 服务集成测试
├── 数据库连接测试
├── ESI服务测试
└── 认证服务测试

Level 3: API集成测试
├── 路由注册测试
├── 中间件集成测试
└── 错误处理测试

Level 4: 端到端测试
├── 应用启动测试
├── 健康检查测试
└── 用户流程测试
```

### 集成测试检查清单
- [ ] 所有路由正确注册
- [ ] 依赖注入配置正确
- [ ] 数据库连接正常
- [ ] 中间件正确加载
- [ ] 错误处理机制有效
- [ ] 日志系统正常工作

## 测试执行策略

### 开发阶段测试
```bash
# 1. 快速导入测试
python -c "from src.presentation.api.main import app; print('✅ 导入成功')"

# 2. 模块级测试
python -m pytest tests/unit/ -v

# 3. 集成测试
python -m pytest tests/integration/ -v

# 4. 完整应用测试
python scripts/test/full_application_test.py
```

### 预提交测试
```bash
# 静态分析
mypy src/
flake8 src/
black --check src/

# 导入验证
python scripts/test/import_validation.py

# 快速集成测试
python scripts/test/quick_integration_test.py
```

## 错误分类和处理

### ImportError 分类
1. **模块不存在** - 文件路径错误
2. **函数不存在** - 函数名错误或未定义
3. **循环导入** - 模块间相互依赖
4. **依赖缺失** - 第三方包未安装

### 排查工具
```python
# 检查模块内容
import importlib
import sys

def inspect_module(module_path):
    try:
        module = importlib.import_module(module_path)
        print(f"模块 {module_path} 包含:")
        for attr in dir(module):
            if not attr.startswith('_'):
                print(f"  - {attr}")
    except ImportError as e:
        print(f"无法导入 {module_path}: {e}")
```

## 测试自动化

### GitHub Actions 配置示例
```yaml
name: Test Suite
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.11
      
      - name: Install dependencies
        run: |
          pip install -r requirements.txt
          pip install -e .
      
      - name: Import validation
        run: python scripts/test/import_validation.py
      
      - name: Static analysis
        run: |
          mypy src/
          flake8 src/
      
      - name: Unit tests
        run: pytest tests/unit/ -v
      
      - name: Integration tests
        run: pytest tests/integration/ -v
```

### 本地开发钩子
```bash
# .git/hooks/pre-commit
#!/bin/bash
echo "运行预提交检查..."

# 导入验证
python scripts/test/import_validation.py
if [ $? -ne 0 ]; then
    echo "❌ 导入验证失败"
    exit 1
fi

# 静态分析
mypy src/
if [ $? -ne 0 ]; then
    echo "❌ 类型检查失败"
    exit 1
fi

echo "✅ 预提交检查通过"
```

## 质量指标

### 测试覆盖率目标
- **导入测试**: 100% (所有关键模块)
- **单元测试**: 80% (代码覆盖率)
- **集成测试**: 90% (关键路径)
- **端到端测试**: 100% (主要用户流程)

### 性能指标
- **导入测试执行时间**: < 10秒
- **集成测试执行时间**: < 2分钟
- **完整测试套件**: < 10分钟
- **应用启动时间**: < 30秒

## 最佳实践

### 测试组织
```
tests/
├── unit/           # 单元测试
├── integration/    # 集成测试
├── e2e/           # 端到端测试
├── fixtures/      # 测试数据
└── utils/         # 测试工具
```

### 测试命名规范
- `test_import_*` - 导入测试
- `test_integration_*` - 集成测试
- `test_e2e_*` - 端到端测试
- `test_unit_*` - 单元测试

### 错误处理测试
```python
def test_import_error_handling():
    """测试导入错误的处理"""
    with pytest.raises(ImportError):
        from non_existent_module import something
    
    # 测试优雅降级
    try:
        from optional_module import feature
        has_feature = True
    except ImportError:
        has_feature = False
        
    assert isinstance(has_feature, bool)
```

## 持续改进

### 测试反馈循环
1. **问题发现** → 2. **根因分析** → 3. **测试增强** → 4. **规范更新**

### 测试指标监控
- 测试通过率趋势
- 测试执行时间趋势
- 代码覆盖率变化
- 缺陷逃逸率

### 团队协作
- 定期测试策略回顾
- 测试用例同行评审
- 测试工具和框架升级
- 测试知识分享和培训
