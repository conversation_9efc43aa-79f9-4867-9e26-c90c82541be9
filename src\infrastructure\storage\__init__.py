"""
统一存储管理器
"""
from enum import Enum
from typing import Any, Optional, Dict, List
import asyncio

from .redis_cache import redis_cache, cache_manager
from .pickle_storage import pickle_storage, model_storage
from .json_storage import json_storage, config_manager
from .smart_cache import smart_cache
from .eve_storage_adapter import eve_storage
from .performance_monitor import performance_monitor, monitor_storage_operation
from ..config.logging import get_logger

logger = get_logger(__name__)


class StorageType(Enum):
    """存储类型枚举"""
    DATABASE = "database"      # SQLite/PostgreSQL - 结构化数据
    CACHE = "cache"           # Redis - 高频缓存
    PICKLE = "pickle"         # Pickle文件 - 复杂对象
    JSON = "json"            # JSON文件 - 配置数据
    FILE = "file"            # 文件系统 - 大文件


class UnifiedStorageManager:
    """统一存储管理器"""
    
    def __init__(self):
        self.redis_cache = redis_cache
        self.cache_manager = cache_manager
        self.pickle_storage = pickle_storage
        self.model_storage = model_storage
        self.json_storage = json_storage
        self.config_manager = config_manager
    
    async def initialize(self):
        """初始化所有存储组件"""
        try:
            # 初始化Redis连接
            await self.redis_cache.connect()

            # 启用性能监控
            performance_monitor.enable_monitoring()

            logger.info("存储管理器初始化完成")
        except Exception as e:
            logger.error("存储管理器初始化失败", error=str(e))
    
    async def cleanup(self):
        """清理资源"""
        try:
            await self.redis_cache.disconnect()
            logger.info("存储管理器清理完成")
        except Exception as e:
            logger.error("存储管理器清理失败", error=str(e))
    
    # ESI数据缓存
    async def cache_esi_data(self, endpoint: str, character_id: int, data: Any, ttl: int = 300):
        """缓存ESI数据"""
        await self.cache_manager.cache_esi_response(endpoint, character_id, data, ttl)
    
    async def get_esi_data(self, endpoint: str, character_id: int) -> Optional[Any]:
        """获取ESI数据缓存"""
        return await self.cache_manager.get_esi_response(endpoint, character_id)
    
    # 角色分析缓存
    async def cache_character_analysis(self, character_id: int, analysis: Any):
        """缓存角色分析结果"""
        # 同时使用Redis和Pickle存储
        await self.cache_manager.cache_character_analysis(character_id, analysis)
        self.model_storage.save_analysis_result(character_id, "full_analysis", analysis)
    
    async def get_character_analysis(self, character_id: int) -> Optional[Any]:
        """获取角色分析缓存"""
        # 优先从Redis获取
        result = await self.cache_manager.get_character_analysis(character_id)
        if result is None:
            # 从Pickle文件获取
            result = self.model_storage.load_analysis_result(character_id, "full_analysis")
        return result
    
    # 配置管理
    def get_user_config(self, user_id: int, config_key: str, default: Any = None) -> Any:
        """获取用户配置"""
        return self.config_manager.get_user_setting(user_id, config_key, default)
    
    def set_user_config(self, user_id: int, config_key: str, value: Any) -> bool:
        """设置用户配置"""
        return self.config_manager.set_user_setting(user_id, config_key, value)
    
    def get_system_config(self, config_name: str, default: Any = None) -> Any:
        """获取系统配置"""
        return self.config_manager.get_system_config(config_name, default)
    
    def set_system_config(self, config_name: str, config_data: Dict[str, Any]) -> bool:
        """设置系统配置"""
        return self.config_manager.set_system_config(config_name, config_data)
    
    # 模型存储
    def save_model(self, model_name: str, model: Any, version: str = "latest") -> bool:
        """保存模型"""
        return self.model_storage.save_model(model_name, model, version)
    
    def load_model(self, model_name: str, version: str = "latest") -> Optional[Any]:
        """加载模型"""
        return self.model_storage.load_model(model_name, version)
    
    # 数据导出
    def export_character_data(self, character_id: int, data: Dict[str, Any]) -> bool:
        """导出角色数据"""
        export_name = f"character_{character_id}_data"
        return self.json_storage.export_data(export_name, data)
    
    def export_market_data(self, region_id: int, data: List[Dict[str, Any]]) -> bool:
        """导出市场数据"""
        export_name = f"market_region_{region_id}_data"
        return self.json_storage.export_data(export_name, data)
    
    # 存储统计
    async def get_storage_stats(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        stats = {
            "redis": {
                "connected": self.redis_cache._client is not None,
                "status": "healthy" if self.redis_cache._client else "disconnected"
            },
            "pickle": self.pickle_storage.get_storage_stats(),
            "json": {
                "configs": len(self.json_storage.list_configs()),
                "user_settings": len(self.json_storage.list_user_settings()),
                "exports": len(self.json_storage.list_exports())
            },
            "performance": performance_monitor.get_stats(),
            "recent_performance": performance_monitor.get_recent_performance(),
            "alerts": performance_monitor.get_alerts(10)
        }
        return stats
    
    # 清理操作
    async def cleanup_expired_data(self) -> Dict[str, int]:
        """清理过期数据"""
        results = {}
        
        # 清理Pickle过期文件
        results["pickle_cleaned"] = self.pickle_storage.cleanup_expired()
        
        # 清理JSON备份文件
        results["json_backups_cleaned"] = self.json_storage.cleanup_old_backups()
        
        return results
    
    # 数据迁移和备份
    def backup_user_data(self, user_id: int) -> bool:
        """备份用户数据"""
        try:
            # 获取用户设置
            settings = self.json_storage.load_user_settings(user_id)
            if settings:
                backup_name = f"user_{user_id}_backup"
                return self.json_storage.export_data(backup_name, {
                    "user_id": user_id,
                    "settings": settings,
                    "backup_type": "user_data"
                })
            return False
        except Exception as e:
            logger.error("备份用户数据失败", user_id=user_id, error=str(e))
            return False


# 全局存储管理器实例
storage_manager = UnifiedStorageManager()


# 优化的存储策略配置
STORAGE_STRATEGIES = {
    # 实时数据 - Redis缓存，30秒TTL，匹配同步频率
    "esi_character_location": {"storage": StorageType.CACHE, "ttl": 30, "priority": "high"},
    "esi_character_online": {"storage": StorageType.CACHE, "ttl": 30, "priority": "high"},

    # 频繁数据 - Redis缓存，5分钟TTL
    "esi_market_orders": {"storage": StorageType.CACHE, "ttl": 300, "priority": "high"},
    "esi_character_mail": {"storage": StorageType.CACHE, "ttl": 300, "priority": "medium"},
    "esi_character_notifications": {"storage": StorageType.CACHE, "ttl": 300, "priority": "medium"},

    # 常规数据 - Redis + Pickle双层缓存，1小时TTL
    "esi_character_skills": {"storage": StorageType.CACHE, "ttl": 3600, "fallback": StorageType.PICKLE},
    "esi_character_assets": {"storage": StorageType.CACHE, "ttl": 3600, "fallback": StorageType.PICKLE},
    "esi_character_wallet": {"storage": StorageType.CACHE, "ttl": 1800, "priority": "medium"},
    "esi_industry_jobs": {"storage": StorageType.CACHE, "ttl": 1800, "fallback": StorageType.PICKLE},

    # 每日数据 - Pickle存储，24小时TTL
    "esi_character_info": {"storage": StorageType.PICKLE, "ttl": 86400, "priority": "low"},
    "esi_corporation_info": {"storage": StorageType.PICKLE, "ttl": 86400, "priority": "low"},
    "esi_universe_types": {"storage": StorageType.PICKLE, "ttl": 604800, "priority": "low"},  # 7天

    # 计算密集型结果 - Pickle存储，智能TTL
    "character_analysis": {"storage": StorageType.PICKLE, "ttl": 3600, "compress": True},
    "skill_training_plan": {"storage": StorageType.PICKLE, "ttl": 1800, "compress": True},
    "market_analysis": {"storage": StorageType.PICKLE, "ttl": 900, "compress": True},
    "profit_calculations": {"storage": StorageType.PICKLE, "ttl": 600, "compress": True},

    # 用户数据 - JSON存储，持久化
    "user_settings": {"storage": StorageType.JSON, "persistent": True, "backup": True},
    "user_preferences": {"storage": StorageType.JSON, "persistent": True, "backup": True},
    "user_dashboards": {"storage": StorageType.JSON, "persistent": True, "backup": True},

    # 系统配置 - JSON存储，版本控制
    "system_config": {"storage": StorageType.JSON, "persistent": True, "versioned": True},
    "sync_rules": {"storage": StorageType.JSON, "persistent": True, "versioned": True},
    "alert_rules": {"storage": StorageType.JSON, "persistent": True, "versioned": True},

    # 核心业务数据 - 数据库，持久化
    "character_data": {"storage": StorageType.DATABASE, "persistent": True, "indexed": True},
    "corporation_data": {"storage": StorageType.DATABASE, "persistent": True, "indexed": True},
    "market_history": {"storage": StorageType.DATABASE, "persistent": True, "partitioned": True},
    "user_accounts": {"storage": StorageType.DATABASE, "persistent": True, "encrypted": True},
    "auth_tokens": {"storage": StorageType.DATABASE, "persistent": True, "encrypted": True},

    # 大数据和历史数据 - 文件存储，压缩
    "market_history_bulk": {"storage": StorageType.FILE, "compress": True, "archive": True},
    "character_history": {"storage": StorageType.FILE, "compress": True, "archive": True},
    "audit_logs": {"storage": StorageType.FILE, "rotate": True, "compress": True},
}


def get_storage_strategy(data_type: str) -> Dict[str, Any]:
    """获取数据类型的存储策略"""
    return STORAGE_STRATEGIES.get(data_type, {
        "storage": StorageType.DATABASE,
        "persistent": True
    })


__all__ = [
    "StorageType",
    "UnifiedStorageManager", 
    "storage_manager",
    "get_storage_strategy",
    "redis_cache",
    "cache_manager",
    "pickle_storage", 
    "model_storage",
    "json_storage",
    "config_manager"
]
