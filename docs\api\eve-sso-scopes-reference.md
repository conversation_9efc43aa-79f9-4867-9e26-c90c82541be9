# EVE SSO权限范围参考指南

## 🎯 **当前配置的完整权限列表**

您的`.env`文件中现在包含了以下完整的EVE SSO权限范围：

### 📋 **权限详细说明**

#### **🧑 角色基础信息**
- `esi-characters.read_characters.v1` - 读取角色基本信息
- `esi-characters.read_corporation_roles.v1` - 读取角色在公司中的职位

#### **📍 位置和在线状态**
- `esi-location.read_location.v1` - 读取角色当前位置
- `esi-location.read_online.v1` - 读取角色在线状态

#### **🎓 技能相关**
- `esi-skills.read_skills.v1` - 读取角色技能
- `esi-skills.read_skillqueue.v1` - 读取技能训练队列

#### **💰 资产和钱包**
- `esi-assets.read_assets.v1` - 读取角色资产
- `esi-wallet.read_character_wallet.v1` - 读取角色钱包

#### **📧 通讯相关**
- `esi-mail.read_mail.v1` - 读取角色邮件
- `esi-clones.read_clones.v1` - 读取克隆体信息

#### **💼 市场和工业**
- `esi-markets.read_character_orders.v1` - 读取市场订单
- `esi-industry.read_character_jobs.v1` - 读取工业作业

#### **🏢 组织相关**
- `esi-corporations.read_corporation_membership.v1` - 读取公司成员信息
- `esi-alliances.read_contacts.v1` - 读取联盟联系人

#### **🌌 宇宙和搜索**
- `esi-universe.read_structures.v1` - 读取宇宙结构信息
- `esi-search.search_structures.v1` - 搜索结构

#### **📜 合同和其他**
- `esi-contracts.read_character_contracts.v1` - 读取角色合同
- `esi-killmails.read_killmails.v1` - 读取击杀邮件
- `esi-fittings.read_fittings.v1` - 读取装配方案
- `esi-calendar.read_calendar_events.v1` - 读取日历事件

## 🔧 **权限配置建议**

### ✅ **推荐配置（当前）**
当前配置包含了EVE Online Assistant所需的所有主要权限，适合：
- 完整的角色管理
- 资产和财务分析
- 市场数据监控
- 工业作业跟踪
- 组织关系管理

### 🎯 **精简配置（可选）**
如果您只需要基础功能，可以使用以下精简权限：
```
esi-characters.read_characters.v1 esi-location.read_location.v1 esi-skills.read_skills.v1 esi-assets.read_assets.v1 esi-wallet.read_character_wallet.v1
```

### 🚀 **扩展配置（未来）**
如果需要更多功能，可以考虑添加：
- `esi-bookmarks.read_character_bookmarks.v1` - 书签管理
- `esi-planets.manage_planets.v1` - 行星管理
- `esi-fleets.read_fleet.v1` - 舰队信息

## ⚠️ **重要注意事项**

### 🔐 **权限安全**
1. **最小权限原则** - 只申请应用实际需要的权限
2. **用户同意** - 用户在登录时会看到所有权限列表
3. **权限审查** - 定期审查是否需要所有权限

### 🔄 **权限更新**
1. **EVE Developer Portal** - 在应用设置中更新权限范围
2. **用户重新授权** - 权限变更后用户需要重新登录授权
3. **向后兼容** - 移除权限可能影响现有功能

### 📊 **权限使用监控**
- 监控哪些权限被实际使用
- 识别未使用的权限并考虑移除
- 跟踪权限相关的API调用频率

## 🛠️ **配置验证**

### 验证当前配置
```bash
python tools/validators/eve-sso-validator.py
```

### 测试权限范围
```bash
python -c "
from src.infrastructure.config import settings
print('当前权限范围:')
for scope in settings.eve_sso_scopes.split():
    print(f'  ✅ {scope}')
"
```

## 📈 **权限使用统计**

当前配置的权限将支持以下功能模块：

| 功能模块 | 相关权限数量 | 覆盖率 |
|----------|-------------|--------|
| 角色管理 | 4个 | 100% |
| 资产管理 | 3个 | 100% |
| 市场分析 | 2个 | 100% |
| 工业监控 | 2个 | 100% |
| 组织关系 | 3个 | 100% |
| 通讯管理 | 2个 | 100% |
| 其他功能 | 4个 | 100% |

**总计：20个权限，覆盖EVE Online Assistant的所有核心功能！** 🎉

## 🔗 **相关文档**

- [EVE ESI文档](https://esi.evetech.net/ui/)
- [EVE Developer Portal](https://developers.eveonline.com/)
- [权限配置指南](eve-sso-setup.md)
