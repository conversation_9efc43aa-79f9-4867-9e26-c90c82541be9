"""
ngrok隧道管理器
自动管理ngrok隧道，动态更新回调URL
"""
import os
import time
import asyncio
from typing import Optional, Dict, Any
from pathlib import Path
import json

try:
    from pyngrok import ngrok, conf
    from pyngrok.exception import PyngrokNgrokError
    PYNGROK_AVAILABLE = True
except ImportError:
    PYNGROK_AVAILABLE = False

from ..config import settings
from ..config.logging import get_logger

logger = get_logger(__name__)


class NgrokManager:
    """ngrok隧道管理器"""
    
    def __init__(self):
        self.tunnel = None
        self.public_url = None
        self.tunnel_info_file = Path("data/ngrok_tunnel.json")
        self.tunnel_info_file.parent.mkdir(exist_ok=True)
        
        # ngrok配置
        self.ngrok_auth_token = getattr(settings, 'ngrok_auth_token', None)
        self.ngrok_region = getattr(settings, 'ngrok_region', 'us')
        self.ngrok_subdomain = getattr(settings, 'ngrok_subdomain', None)
        self.ngrok_domain = getattr(settings, 'ngrok_domain', None)
        self.local_port = getattr(settings, 'server_port', 8000)
        self.auto_update_callback = getattr(settings, 'ngrok_auto_update_callback', True)
    
    def is_available(self) -> bool:
        """检查ngrok是否可用"""
        return PYNGROK_AVAILABLE
    
    def setup_ngrok_config(self):
        """设置ngrok配置"""
        if not self.is_available():
            logger.error("pyngrok未安装，请运行: pip install pyngrok")
            return False
        
        try:
            # 设置ngrok配置
            if self.ngrok_auth_token:
                conf.get_default().auth_token = self.ngrok_auth_token
                logger.info("ngrok认证令牌已设置")
            
            if self.ngrok_region:
                conf.get_default().region = self.ngrok_region
                logger.info(f"ngrok区域设置为: {self.ngrok_region}")
            
            return True
            
        except Exception as e:
            logger.error("ngrok配置设置失败", error=str(e))
            return False
    
    def start_tunnel(self, port: Optional[int] = None) -> Optional[str]:
        """启动ngrok隧道"""
        if not self.is_available():
            logger.error("ngrok不可用")
            return None
        
        try:
            # 设置配置
            if not self.setup_ngrok_config():
                return None
            
            # 使用指定端口或默认端口
            target_port = port or self.local_port
            
            # 构建隧道选项
            tunnel_options = {
                "addr": target_port,
                "proto": "http"
            }
            
            # 添加可选配置
            if self.ngrok_subdomain:
                tunnel_options["subdomain"] = self.ngrok_subdomain
            
            if self.ngrok_domain:
                tunnel_options["hostname"] = self.ngrok_domain
            
            logger.info(f"启动ngrok隧道，端口: {target_port}")
            
            # 创建隧道
            self.tunnel = ngrok.connect(**tunnel_options)
            self.public_url = self.tunnel.public_url
            
            logger.info(f"ngrok隧道已启动: {self.public_url}")
            
            # 保存隧道信息
            self.save_tunnel_info()
            
            # 自动更新回调URL
            if self.auto_update_callback:
                self.update_callback_url()
            
            return self.public_url
            
        except PyngrokNgrokError as e:
            logger.error("ngrok隧道启动失败", error=str(e))
            return None
        except Exception as e:
            logger.error("启动ngrok隧道时发生未知错误", error=str(e))
            return None
    
    def stop_tunnel(self):
        """停止ngrok隧道"""
        try:
            if self.tunnel:
                ngrok.disconnect(self.tunnel.public_url)
                logger.info(f"ngrok隧道已停止: {self.tunnel.public_url}")
                self.tunnel = None
                self.public_url = None
                
                # 清理隧道信息文件
                if self.tunnel_info_file.exists():
                    self.tunnel_info_file.unlink()
            
        except Exception as e:
            logger.error("停止ngrok隧道失败", error=str(e))
    
    def restart_tunnel(self, port: Optional[int] = None) -> Optional[str]:
        """重启ngrok隧道"""
        logger.info("重启ngrok隧道")
        self.stop_tunnel()
        time.sleep(1)  # 等待清理完成
        return self.start_tunnel(port)
    
    def get_tunnel_info(self) -> Optional[Dict[str, Any]]:
        """获取隧道信息"""
        if not self.tunnel:
            return None
        
        try:
            tunnels = ngrok.get_tunnels()
            for tunnel in tunnels:
                if tunnel.public_url == self.public_url:
                    return {
                        "public_url": tunnel.public_url,
                        "proto": tunnel.proto,
                        "config": tunnel.config,
                        "metrics": tunnel.metrics if hasattr(tunnel, 'metrics') else None
                    }
        except Exception as e:
            logger.error("获取隧道信息失败", error=str(e))
        
        return None
    
    def save_tunnel_info(self):
        """保存隧道信息到文件"""
        if not self.tunnel:
            return
        
        try:
            tunnel_data = {
                "public_url": self.public_url,
                "local_port": self.local_port,
                "created_at": time.time(),
                "tunnel_name": getattr(self.tunnel, 'name', 'default')
            }
            
            with open(self.tunnel_info_file, 'w') as f:
                json.dump(tunnel_data, f, indent=2)
            
            logger.debug("隧道信息已保存", file=str(self.tunnel_info_file))
            
        except Exception as e:
            logger.error("保存隧道信息失败", error=str(e))
    
    def load_tunnel_info(self) -> Optional[Dict[str, Any]]:
        """从文件加载隧道信息"""
        try:
            if self.tunnel_info_file.exists():
                with open(self.tunnel_info_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            logger.error("加载隧道信息失败", error=str(e))
        
        return None
    
    def update_callback_url(self):
        """更新EVE SSO回调URL"""
        if not self.public_url:
            logger.warning("无法更新回调URL：隧道未启动")
            return False
        
        try:
            # 构建新的回调URL
            new_callback_url = f"{self.public_url}/auth/callback"
            
            # 更新环境变量文件
            env_file = Path(".env")
            if env_file.exists():
                # 读取现有内容
                with open(env_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 更新回调URL
                lines = content.split('\n')
                updated = False
                
                for i, line in enumerate(lines):
                    if line.startswith('EVE_SSO_CALLBACK_URL='):
                        lines[i] = f'EVE_SSO_CALLBACK_URL="{new_callback_url}"'
                        updated = True
                        break
                
                # 如果没找到，添加新行
                if not updated:
                    lines.append(f'EVE_SSO_CALLBACK_URL="{new_callback_url}"')
                
                # 写回文件
                with open(env_file, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(lines))
                
                logger.info(f"回调URL已更新: {new_callback_url}")
                
                # 提醒用户更新EVE Developer Portal
                logger.warning("⚠️  请记得在EVE Developer Portal中更新回调URL!")
                logger.warning(f"   新的回调URL: {new_callback_url}")
                
                return True
            
        except Exception as e:
            logger.error("更新回调URL失败", error=str(e))
        
        return False
    
    def get_status(self) -> Dict[str, Any]:
        """获取ngrok状态"""
        status = {
            "available": self.is_available(),
            "tunnel_active": self.tunnel is not None,
            "public_url": self.public_url,
            "local_port": self.local_port
        }
        
        if self.tunnel:
            tunnel_info = self.get_tunnel_info()
            if tunnel_info:
                status.update(tunnel_info)
        
        return status
    
    def health_check(self) -> bool:
        """健康检查"""
        if not self.is_available():
            return False
        
        if not self.tunnel:
            return False
        
        try:
            # 检查隧道是否仍然活跃
            tunnels = ngrok.get_tunnels()
            for tunnel in tunnels:
                if tunnel.public_url == self.public_url:
                    return True
            return False
            
        except Exception as e:
            logger.error("ngrok健康检查失败", error=str(e))
            return False
    
    def cleanup(self):
        """清理资源"""
        try:
            self.stop_tunnel()
            ngrok.kill()
            logger.info("ngrok进程已清理")
        except Exception as e:
            logger.error("ngrok清理失败", error=str(e))


# 全局ngrok管理器实例
ngrok_manager = NgrokManager()


class NgrokContextManager:
    """ngrok上下文管理器"""
    
    def __init__(self, port: Optional[int] = None, auto_start: bool = True):
        self.port = port
        self.auto_start = auto_start
        self.manager = ngrok_manager
    
    def __enter__(self):
        if self.auto_start:
            self.manager.start_tunnel(self.port)
        return self.manager
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.auto_start:
            self.manager.stop_tunnel()


def start_ngrok_tunnel(port: Optional[int] = None) -> Optional[str]:
    """便捷函数：启动ngrok隧道"""
    return ngrok_manager.start_tunnel(port)


def stop_ngrok_tunnel():
    """便捷函数：停止ngrok隧道"""
    ngrok_manager.stop_tunnel()


def get_ngrok_url() -> Optional[str]:
    """便捷函数：获取ngrok URL"""
    return ngrok_manager.public_url
