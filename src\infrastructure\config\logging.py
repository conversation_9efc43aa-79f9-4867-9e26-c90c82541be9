"""
日志配置
"""
import logging
import sys
from typing import Any, Dict

try:
    import structlog
    from structlog.types import Processor
    HAS_STRUCTLOG = True
except ImportError:
    HAS_STRUCTLOG = False
    # 创建structlog的模拟类
    class MockStructlog:
        @staticmethod
        def get_logger(name=None):
            return logging.getLogger(name or __name__)

    structlog = MockStructlog()

from .settings import settings


def configure_logging() -> None:
    """配置应用日志"""

    # 配置标准库日志
    logging.basicConfig(
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        stream=sys.stdout,
        level=getattr(logging, settings.log_level),
    )

    # 配置 structlog（如果可用）
    if HAS_STRUCTLOG:
        try:
            processors = [
                structlog.contextvars.merge_contextvars,
                structlog.processors.add_log_level,
                structlog.processors.StackInfoRenderer(),
                structlog.dev.set_exc_info,
            ]

            if settings.log_format == "json":
                processors.extend([
                    structlog.processors.TimeStamper(fmt="iso"),
                    structlog.processors.JSONRenderer()
                ])
            else:
                processors.extend([
                    structlog.processors.TimeStamper(fmt="%Y-%m-%d %H:%M:%S"),
                    structlog.dev.ConsoleRenderer(colors=True)
                ])

            structlog.configure(
                processors=processors,
                wrapper_class=structlog.make_filtering_bound_logger(
                    getattr(logging, settings.log_level)
                ),
                logger_factory=structlog.PrintLoggerFactory(),
                cache_logger_on_first_use=True,
            )
        except Exception as e:
            # 如果structlog配置失败，回退到标准日志
            logging.getLogger().warning(f"Structlog配置失败，使用标准日志: {e}")
    else:
        logging.getLogger().info("使用标准日志系统（structlog不可用）")


def get_logger(name: str):
    """获取日志记录器"""
    if HAS_STRUCTLOG:
        try:
            return structlog.get_logger(name)
        except Exception:
            return logging.getLogger(name)
    else:
        return logging.getLogger(name)


# 配置日志
configure_logging()

# 创建默认日志记录器
logger = get_logger(__name__)
