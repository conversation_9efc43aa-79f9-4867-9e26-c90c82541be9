@echo off
echo 🚀 EVE Online Assistant - Python venv环境设置
echo =============================================

echo 📋 检查Python安装...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python未安装或未添加到PATH
    echo 💡 请安装Python 3.9+: https://www.python.org/
    pause
    exit /b 1
)

echo ✅ Python已安装
python --version

echo.
echo 🔧 创建虚拟环境 'eve-assistant-env'...
if exist eve-assistant-env (
    echo ⚠️  环境已存在，删除旧环境...
    rmdir /s /q eve-assistant-env
)

python -m venv eve-assistant-env

if %errorlevel% neq 0 (
    echo ❌ 虚拟环境创建失败
    pause
    exit /b 1
)

echo.
echo 🔄 激活虚拟环境...
call eve-assistant-env\Scripts\activate.bat

echo.
echo 📦 升级pip...
python -m pip install --upgrade pip

echo.
echo 📦 安装项目依赖...
pip install -e .

if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败，尝试手动安装...
    pip install fastapi uvicorn python-dotenv pydantic pydantic-settings
    pip install structlog httpx sqlalchemy alembic pyjwt python-jose[cryptography] redis
)

echo.
echo 📦 安装开发依赖...
pip install pytest pytest-asyncio pytest-cov black isort mypy

echo.
echo 📄 生成requirements.txt...
pip freeze > requirements.txt

echo.
echo ✅ 环境设置完成！
echo.
echo 📋 使用说明:
echo   1. 激活环境: eve-assistant-env\Scripts\activate.bat
echo   2. 启动应用: python scripts/start.py
echo   3. 退出环境: deactivate
echo   4. 删除环境: rmdir /s /q eve-assistant-env
echo.
echo 📦 已安装的包:
pip list | findstr -i "fastapi uvicorn structlog"
echo.
echo 🎉 现在可以安全地在隔离环境中运行EVE Assistant了！
pause
