import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { ConfigProvider, theme } from 'antd'

import { ThemeMode, ThemeConfig } from '@/types'

interface ThemeContextType {
  themeMode: ThemeMode
  themeConfig: ThemeConfig
  setThemeMode: (mode: ThemeMode) => void
  setThemeConfig: (config: Partial<ThemeConfig>) => void
  isDark: boolean
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

interface ThemeProviderProps {
  children: ReactNode
}

const THEME_STORAGE_KEY = 'eve_assistant_theme'

const defaultThemeConfig: ThemeConfig = {
  mode: 'auto',
  primaryColor: '#1890ff',
  borderRadius: 6,
  compactMode: false,
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const [themeConfig, setThemeConfigState] = useState<ThemeConfig>(() => {
    try {
      const saved = localStorage.getItem(THEME_STORAGE_KEY)
      return saved ? { ...defaultThemeConfig, ...JSON.parse(saved) } : defaultThemeConfig
    } catch {
      return defaultThemeConfig
    }
  })

  const [systemTheme, setSystemTheme] = useState<'light' | 'dark'>(() => {
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
  })

  // 计算实际主题模式
  const actualThemeMode = themeConfig.mode === 'auto' ? systemTheme : themeConfig.mode
  const isDark = actualThemeMode === 'dark'

  // 监听系统主题变化
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    
    const handleChange = (e: MediaQueryListEvent) => {
      setSystemTheme(e.matches ? 'dark' : 'light')
    }

    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  // 应用主题到document
  useEffect(() => {
    const root = document.documentElement
    
    if (isDark) {
      root.classList.add('dark')
    } else {
      root.classList.remove('dark')
    }

    // 设置CSS变量
    root.style.setProperty('--primary-color', themeConfig.primaryColor)
    root.style.setProperty('--border-radius', `${themeConfig.borderRadius}px`)
  }, [isDark, themeConfig])

  // 保存主题配置到localStorage
  useEffect(() => {
    try {
      localStorage.setItem(THEME_STORAGE_KEY, JSON.stringify(themeConfig))
    } catch (error) {
      console.error('Failed to save theme config:', error)
    }
  }, [themeConfig])

  const setThemeMode = (mode: ThemeMode) => {
    setThemeConfigState(prev => ({ ...prev, mode }))
  }

  const setThemeConfig = (config: Partial<ThemeConfig>) => {
    setThemeConfigState(prev => ({ ...prev, ...config }))
  }

  // Ant Design主题配置
  const antdTheme = {
    algorithm: isDark ? theme.darkAlgorithm : theme.defaultAlgorithm,
    token: {
      colorPrimary: themeConfig.primaryColor,
      borderRadius: themeConfig.borderRadius,
      fontSize: themeConfig.compactMode ? 13 : 14,
      sizeStep: themeConfig.compactMode ? 3 : 4,
      sizeUnit: themeConfig.compactMode ? 3 : 4,
      // EVE主题色彩
      colorSuccess: '#52c41a',
      colorWarning: '#faad14',
      colorError: '#f5222d',
      colorInfo: themeConfig.primaryColor,
      // 暗色主题特定配置
      ...(isDark && {
        colorBgContainer: '#1f2937',
        colorBgElevated: '#374151',
        colorBgLayout: '#111827',
        colorBorder: '#4b5563',
        colorBorderSecondary: '#6b7280',
        colorText: '#f9fafb',
        colorTextSecondary: '#d1d5db',
        colorTextTertiary: '#9ca3af',
        colorFill: '#374151',
        colorFillSecondary: '#4b5563',
        colorFillTertiary: '#6b7280',
        colorFillQuaternary: '#9ca3af',
      }),
    },
    components: {
      Layout: {
        colorBgHeader: isDark ? '#1f2937' : '#001529',
        colorBgBody: isDark ? '#111827' : '#f0f2f5',
        colorBgTrigger: isDark ? '#374151' : '#002140',
      },
      Menu: {
        colorBgContainer: isDark ? '#1f2937' : '#001529',
        colorText: isDark ? '#f9fafb' : 'rgba(255, 255, 255, 0.65)',
        colorTextSelected: isDark ? '#ffffff' : '#ffffff',
        colorItemBg: 'transparent',
        colorItemBgSelected: isDark ? '#374151' : '#1890ff',
        colorItemBgHover: isDark ? '#374151' : 'rgba(255, 255, 255, 0.1)',
      },
      Card: {
        colorBgContainer: isDark ? '#1f2937' : '#ffffff',
        colorBorderSecondary: isDark ? '#4b5563' : '#f0f0f0',
      },
      Table: {
        colorBgContainer: isDark ? '#1f2937' : '#ffffff',
        colorFillAlter: isDark ? '#374151' : '#fafafa',
      },
      Input: {
        colorBgContainer: isDark ? '#374151' : '#ffffff',
        colorBorder: isDark ? '#6b7280' : '#d9d9d9',
      },
      Button: {
        borderRadius: themeConfig.borderRadius,
        controlHeight: themeConfig.compactMode ? 28 : 32,
      },
      Tabs: {
        colorBgContainer: isDark ? '#1f2937' : '#ffffff',
      },
      Modal: {
        colorBgElevated: isDark ? '#1f2937' : '#ffffff',
      },
      Drawer: {
        colorBgElevated: isDark ? '#1f2937' : '#ffffff',
      },
      Dropdown: {
        colorBgElevated: isDark ? '#1f2937' : '#ffffff',
      },
      Popover: {
        colorBgElevated: isDark ? '#1f2937' : '#ffffff',
      },
      Tooltip: {
        colorBgSpotlight: isDark ? '#374151' : 'rgba(0, 0, 0, 0.85)',
      },
    },
  }

  const contextValue: ThemeContextType = {
    themeMode: themeConfig.mode,
    themeConfig,
    setThemeMode,
    setThemeConfig,
    isDark,
  }

  return (
    <ThemeContext.Provider value={contextValue}>
      <ConfigProvider theme={antdTheme}>
        {children}
      </ConfigProvider>
    </ThemeContext.Provider>
  )
}

export function useTheme(): ThemeContextType {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}

// 主题切换Hook
export function useThemeToggle() {
  const { themeMode, setThemeMode, isDark } = useTheme()

  const toggleTheme = () => {
    if (themeMode === 'auto') {
      setThemeMode(isDark ? 'light' : 'dark')
    } else {
      setThemeMode(themeMode === 'light' ? 'dark' : 'light')
    }
  }

  const setLightTheme = () => setThemeMode('light')
  const setDarkTheme = () => setThemeMode('dark')
  const setAutoTheme = () => setThemeMode('auto')

  return {
    toggleTheme,
    setLightTheme,
    setDarkTheme,
    setAutoTheme,
    currentMode: themeMode,
    isDark,
  }
}
