{"name": "@storybook/addon-highlight", "version": "7.6.20", "description": "Highlight DOM nodes within your stories", "keywords": ["storybook-addons", "essentials", "style", "appearance"], "homepage": "https://github.com/storybookjs/storybook/tree/next/code/addons/highlight", "bugs": {"url": "https://github.com/storybookjs/storybook/issues"}, "repository": {"type": "git", "url": "https://github.com/storybookjs/storybook.git", "directory": "code/addons/highlight"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "license": "MIT", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "require": "./dist/index.js", "import": "./dist/index.mjs"}, "./preview": "./dist/preview.js", "./package.json": "./package.json"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist/**/*", "README.md", "*.js", "*.d.ts", "!src/**/*"], "scripts": {"check": "node --loader ../../../scripts/node_modules/esbuild-register/loader.js -r ../../../scripts/node_modules/esbuild-register/register.js ../../../scripts/prepare/check.ts", "prep": "node --loader ../../../scripts/node_modules/esbuild-register/loader.js -r ../../../scripts/node_modules/esbuild-register/register.js ../../../scripts/prepare/addon-bundle.ts"}, "dependencies": {"@storybook/global": "^5.0.0"}, "devDependencies": {"@storybook/core-events": "7.6.20", "@storybook/preview-api": "7.6.20", "@types/webpack-env": "^1.16.0", "typescript": "~4.9.3"}, "publishConfig": {"access": "public"}, "bundler": {"exportEntries": ["./src/index.ts"], "previewEntries": ["./src/preview.ts"]}, "gitHead": "e6a7fd8a655c69780bc20b9749c2699e44beae17", "sbmodern": "dist/modern/index.js", "storybook": {"displayName": "Highlight", "unsupportedFrameworks": ["react-native"], "icon": "https://user-images.githubusercontent.com/42671/162045505-9d06fe2e-8fcb-4c41-9486-e0553bce10cc.png"}}