"""
数据同步相关的数据传输对象
"""
from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional


class SyncStatusInfo(BaseModel):
    """同步状态信息"""
    status: str = Field(..., description="同步状态")
    start_time: Optional[str] = Field(None, description="开始时间")
    end_time: Optional[str] = Field(None, description="结束时间")
    last_error: Optional[str] = Field(None, description="最后错误")


class SyncStatusResponse(BaseModel):
    """同步状态响应"""
    sync_status: Dict[str, SyncStatusInfo] = Field(..., description="同步状态")
    running_syncs: List[str] = Field(..., description="运行中的同步")
    sync_features: Dict[str, bool] = Field(..., description="同步功能开关")
    sync_intervals: Dict[str, int] = Field(..., description="同步间隔")


class SyncRequest(BaseModel):
    """同步请求"""
    sync_type: str = Field(..., description="同步类型")
    force: bool = Field(False, description="强制同步")


class SyncResponse(BaseModel):
    """同步响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")


class CharacterSyncRequest(BaseModel):
    """角色同步请求"""
    sync_types: Optional[List[str]] = Field(None, description="同步类型列表")
    force_refresh: bool = Field(False, description="强制刷新")
    background: bool = Field(False, description="后台执行")


class CharacterSyncResponse(BaseModel):
    """角色同步响应"""
    character_id: int = Field(..., description="角色ID")
    sync_types: List[str] = Field(..., description="同步类型")
    status: str = Field(..., description="同步状态")
    results: Optional[Dict[str, Any]] = Field(None, description="同步结果")
    sync_time: Optional[str] = Field(None, description="同步时间")
    message: str = Field(..., description="响应消息")


class SyncHistoryItem(BaseModel):
    """同步历史项"""
    id: int = Field(..., description="历史ID")
    sync_type: str = Field(..., description="同步类型")
    character_id: Optional[int] = Field(None, description="角色ID")
    status: str = Field(..., description="同步状态")
    start_time: str = Field(..., description="开始时间")
    end_time: Optional[str] = Field(None, description="结束时间")
    error_message: Optional[str] = Field(None, description="错误消息")
    results: Optional[Dict[str, Any]] = Field(None, description="同步结果")


class SyncHistoryResponse(BaseModel):
    """同步历史响应"""
    history: List[SyncHistoryItem] = Field(..., description="同步历史")
    total: int = Field(..., description="总数量")
    limit: int = Field(..., description="限制数量")
    offset: int = Field(..., description="偏移量")


class SyncConfigResponse(BaseModel):
    """同步配置响应"""
    sync_intervals: Dict[str, int] = Field(..., description="同步间隔")
    sync_features: Dict[str, bool] = Field(..., description="同步功能开关")
    available_sync_types: List[str] = Field(..., description="可用同步类型")


class SyncConfigUpdateRequest(BaseModel):
    """同步配置更新请求"""
    sync_intervals: Optional[Dict[str, int]] = Field(None, description="同步间隔")
    sync_features: Optional[Dict[str, bool]] = Field(None, description="同步功能开关")


class SyncMetrics(BaseModel):
    """同步指标"""
    total_syncs: int = Field(..., description="总同步次数")
    successful_syncs: int = Field(..., description="成功同步次数")
    failed_syncs: int = Field(..., description="失败同步次数")
    average_sync_time: float = Field(..., description="平均同步时间")
    last_sync_time: Optional[str] = Field(None, description="最后同步时间")


class SyncMetricsResponse(BaseModel):
    """同步指标响应"""
    overall: SyncMetrics = Field(..., description="总体指标")
    by_type: Dict[str, SyncMetrics] = Field(..., description="按类型分组的指标")
    by_character: Dict[str, SyncMetrics] = Field(..., description="按角色分组的指标")
