#!/usr/bin/env python3
"""
测试执行脚本
"""
import sys
import subprocess
import argparse
import json
from pathlib import Path
from datetime import datetime


def run_command(cmd, capture_output=True):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            capture_output=capture_output, 
            text=True,
            check=False
        )
        return result
    except Exception as e:
        print(f"❌ 命令执行失败: {cmd}")
        print(f"   错误: {e}")
        return None


def run_unit_tests():
    """运行单元测试"""
    print("🧪 运行单元测试...")
    result = run_command("pytest tests/unit -v --tb=short")
    
    if result and result.returncode == 0:
        print("✅ 单元测试通过")
        return True
    else:
        print("❌ 单元测试失败")
        if result:
            print(result.stdout)
            print(result.stderr)
        return False


def run_integration_tests():
    """运行集成测试"""
    print("🔗 运行集成测试...")
    result = run_command("pytest tests/integration -v --tb=short")
    
    if result and result.returncode == 0:
        print("✅ 集成测试通过")
        return True
    else:
        print("❌ 集成测试失败")
        if result:
            print(result.stdout)
            print(result.stderr)
        return False


def run_import_tests():
    """运行模块导入测试"""
    print("📦 运行模块导入测试...")
    result = run_command("pytest tests/test_module_imports.py -v --tb=short")
    
    if result and result.returncode == 0:
        print("✅ 模块导入测试通过")
        return True
    else:
        print("❌ 模块导入测试失败")
        if result:
            print(result.stdout)
            print(result.stderr)
        return False


def run_coverage_tests():
    """运行覆盖率测试"""
    print("📊 运行覆盖率测试...")
    result = run_command("pytest --cov=src --cov-report=term-missing --cov-report=html")
    
    if result and result.returncode == 0:
        print("✅ 覆盖率测试完成")
        print("   HTML报告: htmlcov/index.html")
        return True
    else:
        print("❌ 覆盖率测试失败")
        if result:
            print(result.stdout)
            print(result.stderr)
        return False


def run_all_tests():
    """运行所有测试"""
    print("🚀 运行所有测试...")
    result = run_command("pytest -v --tb=short")
    
    if result and result.returncode == 0:
        print("✅ 所有测试通过")
        return True
    else:
        print("❌ 部分测试失败")
        if result:
            print(result.stdout)
            print(result.stderr)
        return False


def check_test_environment():
    """检查测试环境"""
    print("🔍 检查测试环境...")
    
    # 检查pytest是否安装
    result = run_command("python -m pytest --version")
    if not result or result.returncode != 0:
        print("❌ pytest未安装")
        return False
    
    print("✅ pytest已安装")
    
    # 检查测试目录是否存在
    test_dir = Path("tests")
    if not test_dir.exists():
        print("❌ tests目录不存在")
        return False
    
    print("✅ tests目录存在")
    
    # 检查关键测试文件
    required_files = [
        "tests/conftest.py",
        "tests/unit/test_auth.py",
        "tests/integration/test_api_auth.py",
        "tests/test_module_imports.py"
    ]
    
    for file_path in required_files:
        if not Path(file_path).exists():
            print(f"⚠️  {file_path} 不存在")
        else:
            print(f"✅ {file_path} 存在")
    
    return True


def generate_test_report():
    """生成测试报告"""
    print("📋 生成测试报告...")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"test_report_{timestamp}.json"
    
    result = run_command(f"pytest --json-report --json-report-file={report_file}")
    
    if result and result.returncode == 0:
        print(f"✅ 测试报告已生成: {report_file}")
        
        # 读取并显示报告摘要
        try:
            with open(report_file) as f:
                report = json.load(f)
            
            summary = report.get("summary", {})
            print(f"   测试总数: {summary.get('total', 0)}")
            print(f"   通过: {summary.get('passed', 0)}")
            print(f"   失败: {summary.get('failed', 0)}")
            print(f"   跳过: {summary.get('skipped', 0)}")
            print(f"   错误: {summary.get('error', 0)}")
            
        except Exception as e:
            print(f"⚠️  读取报告失败: {e}")
        
        return True
    else:
        print("❌ 测试报告生成失败")
        return False


def install_test_dependencies():
    """安装测试依赖"""
    print("📦 安装测试依赖...")
    
    dependencies = [
        "pytest>=7.0.0",
        "pytest-asyncio>=0.21.0",
        "pytest-cov>=4.0.0",
        "pytest-json-report>=1.5.0",
        "httpx>=0.24.0",
    ]
    
    for dep in dependencies:
        print(f"   安装 {dep}...")
        result = run_command(f"pip install {dep}")
        if not result or result.returncode != 0:
            print(f"❌ 安装 {dep} 失败")
            return False
    
    print("✅ 测试依赖安装完成")
    return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="测试执行脚本")
    parser.add_argument("--unit", action="store_true", help="只运行单元测试")
    parser.add_argument("--integration", action="store_true", help="只运行集成测试")
    parser.add_argument("--imports", action="store_true", help="只运行导入测试")
    parser.add_argument("--coverage", action="store_true", help="运行覆盖率测试")
    parser.add_argument("--report", action="store_true", help="生成测试报告")
    parser.add_argument("--install-deps", action="store_true", help="安装测试依赖")
    parser.add_argument("--check-env", action="store_true", help="检查测试环境")
    
    args = parser.parse_args()
    
    print("🧪 EVE Online Assistant 测试执行器")
    print("=" * 50)
    
    success = True
    
    if args.install_deps:
        success &= install_test_dependencies()
    
    if args.check_env:
        success &= check_test_environment()
    
    if args.unit:
        success &= run_unit_tests()
    elif args.integration:
        success &= run_integration_tests()
    elif args.imports:
        success &= run_import_tests()
    elif args.coverage:
        success &= run_coverage_tests()
    elif args.report:
        success &= generate_test_report()
    else:
        # 默认运行所有测试
        success &= check_test_environment()
        success &= run_import_tests()
        success &= run_unit_tests()
        success &= run_integration_tests()
        success &= run_coverage_tests()
    
    print("=" * 50)
    if success:
        print("🎉 测试执行完成，所有测试通过！")
        return 0
    else:
        print("❌ 测试执行完成，部分测试失败！")
        return 1


if __name__ == "__main__":
    sys.exit(main())
