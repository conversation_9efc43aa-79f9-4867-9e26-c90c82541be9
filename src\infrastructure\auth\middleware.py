"""
认证中间件
"""
from typing import Optional, List, Callable, Any
from functools import wraps

from fastapi import HTT<PERSON>Ex<PERSON>, status, Depends, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from .jwt_service import jwt_service, TokenType
from ..config.logging import get_logger
from ...domain.shared.value_objects import UserId, CharacterId
from ...domain.shared.exceptions import AuthenticationError, AuthorizationError

logger = get_logger(__name__)

# HTTP Bearer认证方案
security = HTTPBearer(auto_error=False)


class AuthenticationContext:
    """认证上下文"""
    
    def __init__(self, 
                 user_id: UserId,
                 character_id: Optional[CharacterId] = None,
                 scopes: List[str] = None,
                 token_type: str = "access",
                 jti: str = None):
        self.user_id = user_id
        self.character_id = character_id
        self.scopes = scopes or []
        self.token_type = token_type
        self.jti = jti
    
    def has_scope(self, required_scope: str) -> bool:
        """检查是否具有指定权限"""
        return required_scope in self.scopes
    
    def has_scopes(self, required_scopes: List[str]) -> bool:
        """检查是否具有所有指定权限"""
        return all(scope in self.scopes for scope in required_scopes)
    
    def has_any_scope(self, required_scopes: List[str]) -> bool:
        """检查是否具有任意一个指定权限"""
        return any(scope in self.scopes for scope in required_scopes)


async def get_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Optional[AuthenticationContext]:
    """获取当前用户（可选认证）"""
    if not credentials:
        return None
    
    try:
        payload = jwt_service.verify_token(credentials.credentials)
        
        user_id = UserId(payload["user_id"])
        character_id = CharacterId(payload["character_id"]) if payload.get("character_id") else None
        scopes = payload.get("scopes", [])
        token_type = payload.get("type", "access")
        jti = payload.get("jti")
        
        return AuthenticationContext(
            user_id=user_id,
            character_id=character_id,
            scopes=scopes,
            token_type=token_type,
            jti=jti
        )
        
    except Exception as e:
        logger.warning("令牌验证失败", error=str(e))
        return None


async def get_authenticated_user(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> AuthenticationContext:
    """获取已认证用户（必需认证）"""
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Missing authentication token",
            headers={"WWW-Authenticate": "Bearer"}
        )
    
    try:
        payload = jwt_service.verify_token(credentials.credentials)
        
        user_id = UserId(payload["user_id"])
        character_id = CharacterId(payload["character_id"]) if payload.get("character_id") else None
        scopes = payload.get("scopes", [])
        token_type = payload.get("type", "access")
        jti = payload.get("jti")
        
        return AuthenticationContext(
            user_id=user_id,
            character_id=character_id,
            scopes=scopes,
            token_type=token_type,
            jti=jti
        )
        
    except AuthenticationError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e),
            headers={"WWW-Authenticate": "Bearer"}
        )
    except Exception as e:
        logger.error("认证过程中发生错误", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication failed",
            headers={"WWW-Authenticate": "Bearer"}
        )


def require_scopes(required_scopes: List[str], require_all: bool = True):
    """权限检查装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取认证上下文
            auth_context = None
            for key, value in kwargs.items():
                if isinstance(value, AuthenticationContext):
                    auth_context = value
                    break
            
            if not auth_context:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            # 检查权限
            if require_all:
                has_permission = auth_context.has_scopes(required_scopes)
            else:
                has_permission = auth_context.has_any_scope(required_scopes)
            
            if not has_permission:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Insufficient permissions. Required: {required_scopes}"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def require_character_access(character_id_param: str = "character_id"):
    """角色访问权限检查装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 获取认证上下文
            auth_context = None
            for key, value in kwargs.items():
                if isinstance(value, AuthenticationContext):
                    auth_context = value
                    break
            
            if not auth_context:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            # 获取请求的角色ID
            requested_character_id = kwargs.get(character_id_param)
            if not requested_character_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Missing parameter: {character_id_param}"
                )
            
            # 检查是否有权限访问该角色
            if (auth_context.character_id and 
                auth_context.character_id.value != requested_character_id):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access denied to this character"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


class AuthenticationMiddleware:
    """认证中间件类"""
    
    def __init__(self, app):
        self.app = app
    
    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return
        
        request = Request(scope, receive)
        
        # 提取认证信息
        auth_header = request.headers.get("authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token = auth_header[7:]  # 移除 "Bearer " 前缀
            
            try:
                payload = jwt_service.verify_token(token)
                
                # 将认证信息添加到请求状态
                request.state.user_id = payload["user_id"]
                request.state.character_id = payload.get("character_id")
                request.state.scopes = payload.get("scopes", [])
                request.state.token_type = payload.get("type", "access")
                request.state.jti = payload.get("jti")
                request.state.authenticated = True
                
            except Exception as e:
                logger.debug("令牌验证失败", error=str(e))
                request.state.authenticated = False
        else:
            request.state.authenticated = False
        
        await self.app(scope, receive, send)


def create_api_key_dependency(required_scopes: List[str] = None):
    """创建API密钥依赖"""
    async def api_key_auth(
        credentials: HTTPAuthorizationCredentials = Depends(security)
    ) -> AuthenticationContext:
        if not credentials:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="API key required",
                headers={"WWW-Authenticate": "Bearer"}
            )
        
        try:
            payload = jwt_service.verify_token(
                credentials.credentials, 
                TokenType.API_KEY
            )
            
            user_id = UserId(payload["user_id"])
            scopes = payload.get("scopes", [])
            
            # 检查权限
            if required_scopes:
                missing_scopes = set(required_scopes) - set(scopes)
                if missing_scopes:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail=f"Missing required scopes: {list(missing_scopes)}"
                    )
            
            return AuthenticationContext(
                user_id=user_id,
                scopes=scopes,
                token_type="api_key",
                jti=payload.get("jti")
            )
            
        except AuthenticationError as e:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=str(e),
                headers={"WWW-Authenticate": "Bearer"}
            )
    
    return api_key_auth


# 常用的依赖项
RequireAuth = Depends(get_authenticated_user)
OptionalAuth = Depends(get_current_user)

# 常用的权限检查
def RequireScopes(scopes: List[str], require_all: bool = True):
    """权限检查依赖工厂"""
    async def check_scopes(
        auth: AuthenticationContext = RequireAuth
    ) -> AuthenticationContext:
        if require_all:
            has_permission = auth.has_scopes(scopes)
        else:
            has_permission = auth.has_any_scope(scopes)
        
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient permissions. Required: {scopes}"
            )
        
        return auth
    
    return Depends(check_scopes)


def RequireCharacterAccess(character_id_param: str = "character_id"):
    """角色访问权限检查依赖工厂"""
    async def check_character_access(
        request: Request,
        auth: AuthenticationContext = RequireAuth
    ) -> AuthenticationContext:
        # 从路径参数中获取角色ID
        character_id = request.path_params.get(character_id_param)
        
        if not character_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Missing parameter: {character_id_param}"
            )
        
        # 检查权限
        if (auth.character_id and 
            auth.character_id.value != int(character_id)):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to this character"
            )
        
        return auth
    
    return Depends(check_character_access)
