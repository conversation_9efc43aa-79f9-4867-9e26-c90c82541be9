#!/usr/bin/env python3
"""
最小的测试服务器
"""
from fastapi import FastAPI
import uvicorn

print("Importing FastAPI...")
app = FastAPI()

@app.get("/health")
async def health():
    print("Health endpoint called")
    return {"status": "ok"}

@app.post("/auth/login")
async def auth_login():
    print("Auth login endpoint called")
    return {
        "success": True,
        "data": {
            "login_url": "https://login.eveonline.com/v2/oauth/authorize?response_type=code&redirect_uri=http://localhost:3000/auth/callback&client_id=demo&scope=esi-characters.read_characters.v1&state=demo_state_123",
            "state": "demo_state_123",
            "expires_in": 300,
            "scopes": ["esi-characters.read_characters.v1"]
        }
    }

if __name__ == "__main__":
    print("Starting minimal server...")
    try:
        uvicorn.run(app, host="127.0.0.1", port=8000, log_level="debug")
    except Exception as e:
        print(f"Server startup failed: {e}")
        import traceback
        traceback.print_exc()
