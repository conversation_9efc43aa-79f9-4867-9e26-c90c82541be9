# EVE Online Assistant

🚀 基于DDD架构的EVE Online角色管理和数据分析工具

## 📋 项目概述

EVE Online Assistant是一个企业级的EVE Online数据管理平台，采用领域驱动设计(DDD)架构，提供角色管理、数据同步、技能分析等功能。

## 🏗️ 架构特点

- ✅ **DDD架构** - 完整的领域驱动设计实现
- ✅ **分层架构** - 清晰的职责分离
- ✅ **微服务就绪** - 模块化设计
- ✅ **异步支持** - 高性能数据处理
- ✅ **企业级** - 生产环境就绪

## 🚀 快速开始

### 安装依赖
```bash
pip install -r requirements.txt
```

### 启动应用
```bash
python scripts/start.py
```

### 访问应用
- 本地地址: http://127.0.0.1:8000
- API文档: http://127.0.0.1:8000/docs

## 📚 文档

- [架构设计](docs/architecture/) - DDD架构和系统设计
- [API文档](docs/api/) - REST API接口文档
- [部署指南](docs/deployment/) - 生产环境部署
- [开发指南](docs/development/) - 开发环境配置
- [知识库](docs/knowledge-base/) - 领域知识和最佳实践

## 🛠️ 开发工具

- [脚本集合](scripts/) - 启动、部署、维护脚本
- [工具集合](tools/) - 辅助工具和验证器
- [配置模板](config/) - 环境配置和示例

## 📊 项目结构

```
eve-online-assistant/
├── src/                    # 源代码
│   ├── domain/            # 领域层
│   ├── application/       # 应用层
│   ├── infrastructure/    # 基础设施层
│   └── presentation/      # 表示层
├── docs/                  # 文档
├── scripts/               # 脚本
├── tools/                 # 工具
├── config/                # 配置
└── tests/                 # 测试
```

## 🎯 主要功能

- 🔐 **EVE SSO认证** - 安全的OAuth2登录
- 👤 **角色管理** - 多角色数据同步
- 📊 **数据分析** - 技能、资产、市场分析
- 🔄 **自动同步** - 智能数据更新策略
- 📈 **性能监控** - 实时性能指标

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License
