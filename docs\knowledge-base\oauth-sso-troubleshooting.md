# OAuth/SSO登录问题排查知识库

## 常见问题模式

### 1. "Client could not be found" 错误

**问题特征**：
- 用户点击第三方登录按钮后出现客户端未找到错误
- 跳转URL中包含错误的client_id或redirect_uri参数

**排查步骤**：
1. **检查实际跳转URL**：查看浏览器地址栏或开发者工具中的实际请求URL
2. **验证URL参数**：确认client_id、redirect_uri、scope等参数是否正确
3. **检查前端配置**：查看前端是否使用了硬编码的demo/测试配置
4. **验证后端API**：直接测试后端登录接口是否返回正确配置
5. **检查fallback机制**：确认前端是否有错误处理的fallback逻辑

**常见原因**：
- 前端使用了硬编码的测试配置
- 后端API调用失败触发了错误的fallback机制
- 环境变量配置错误
- 第三方平台应用配置问题

### 2. 前后端配置不一致

**问题特征**：
- 后端API返回正确配置，但前端使用了不同的参数
- 存在多套配置（开发/测试/生产）混用

**解决原则**：
- 统一配置源，避免前后端重复配置
- 前端应从后端API获取登录URL，而非自行构造
- 移除硬编码的fallback配置
- 建立配置验证机制

### 3. 错误处理最佳实践

**避免的做法**：
- 静默使用fallback配置掩盖真实错误
- 使用demo/测试配置作为生产fallback
- 缺乏详细的错误日志

**推荐做法**：
- 显示具体错误信息给开发者
- 记录详细的API调用日志
- 在开发环境中快速失败，暴露问题
- 建立配置验证和健康检查机制

## 排查工具和方法

### 1. 前端调试
```javascript
// 在浏览器控制台检查API调用
console.log('API Request:', config.url, config.data)
console.log('API Response:', response.data)
```

### 2. 后端API测试
```bash
# 直接测试登录接口
curl -X POST http://localhost:8000/auth/login \
  -H "Content-Type: application/json" \
  -d '{"scopes":["scope1","scope2"]}'
```

### 3. URL参数验证
- 使用浏览器开发者工具查看Network标签
- 检查实际发送的请求参数
- 对比期望的配置参数

## 预防措施

### 1. 配置管理
- 使用环境变量统一管理配置
- 建立配置验证机制
- 避免硬编码配置参数

### 2. 错误处理
- 建立详细的错误日志系统
- 在开发环境中快速失败
- 提供有意义的错误信息

### 3. 测试覆盖
- 包含OAuth流程的端到端测试
- 验证不同环境下的配置正确性
- 测试错误场景的处理逻辑

## 相关文档
- [EVE SSO官方文档](https://docs.esi.evetech.net/docs/sso/)
- [OAuth 2.0 RFC](https://tools.ietf.org/html/rfc6749)
- [前端错误处理最佳实践](./frontend-error-handling.md)
