# EVE SSO 配置指南

## 🚀 快速配置步骤

### 1. 获取公网地址
运行应用后，ngrok会提供一个公网地址，例如：
```
https://abc123.ngrok.io
```

### 2. 在EVE Developer Portal配置

#### 访问EVE Developer Portal
1. 打开 [EVE Developer Portal](https://developers.eveonline.com/)
2. 使用您的EVE Online账号登录

#### 创建或编辑应用
1. 点击 "Manage Applications"
2. 选择现有应用或点击 "Create New Application"

#### 配置应用设置
```
Application Name: EVE Online Assistant
Description: EVE角色管理和数据分析工具
Connection Type: Authentication & API Access
Permissions: 选择需要的权限范围（见下方权限列表）
Callback URL: https://your-ngrok-url.ngrok.io/auth/callback
```

**重要**: 将 `your-ngrok-url` 替换为实际的ngrok地址！

### 3. 权限范围配置

推荐的权限范围：
```
✅ esi-characters.read_characters.v1
✅ esi-characters.read_corporation_roles.v1
✅ esi-characters.read_titles.v1
✅ esi-clones.read_clones.v1
✅ esi-clones.read_implants.v1
✅ esi-location.read_location.v1
✅ esi-location.read_online.v1
✅ esi-mail.read_mail.v1
✅ esi-skills.read_skills.v1
✅ esi-skills.read_skillqueue.v1
✅ esi-assets.read_assets.v1
✅ esi-wallet.read_character_wallet.v1
✅ esi-markets.read_character_orders.v1
✅ esi-industry.read_character_jobs.v1
```

### 4. 获取客户端凭据
配置完成后，您将获得：
- **Client ID**: 复制此ID
- **Secret Key**: 复制此密钥（仅显示一次）

### 5. 更新应用配置

#### 方法1: 环境变量（推荐）
创建 `.env` 文件：
```env
EVE_SSO_CLIENT_ID=your_client_id_here
EVE_SSO_CLIENT_SECRET=your_secret_key_here
EVE_SSO_CALLBACK_URL=https://your-ngrok-url.ngrok.io/auth/callback
```

#### 方法2: 直接修改配置文件
编辑 `src/infrastructure/config/settings.py`：
```python
eve_sso_client_id: str = "your_client_id_here"
eve_sso_client_secret: str = "your_secret_key_here"
eve_sso_callback_url: str = "https://your-ngrok-url.ngrok.io/auth/callback"
```

## 🧪 测试配置

### 1. 重启应用
```bash
python start.py
```

### 2. 测试EVE SSO登录
访问：`https://your-ngrok-url.ngrok.io/auth/login`

### 3. 验证回调
成功登录后应该重定向回应用并显示角色信息

## 🔧 故障排除

### 常见问题

#### 1. 回调URL不匹配
**错误**: `invalid_request: The redirect URI provided is invalid`
**解决**: 确保EVE Developer Portal中的回调URL与ngrok地址完全匹配

#### 2. 客户端ID无效
**错误**: `invalid_client`
**解决**: 检查Client ID是否正确复制，没有多余空格

#### 3. ngrok隧道断开
**错误**: 无法访问公网地址
**解决**: 重新启动ngrok，更新EVE Developer Portal中的回调URL

#### 4. 权限不足
**错误**: `insufficient_scope`
**解决**: 在EVE Developer Portal中添加所需的权限范围

### 调试命令

检查配置：
```bash
python -c "
from src.infrastructure.config import settings
print(f'Client ID: {settings.eve_sso_client_id}')
print(f'Callback URL: {settings.eve_sso_callback_url}')
"
```

测试ESI连接：
```bash
python scripts/test_esi_connection.py
```

## 📱 使用指南

### 登录流程
1. 访问 `/auth/login`
2. 点击 "Login with EVE Online"
3. 在EVE SSO页面选择角色并授权
4. 自动重定向回应用
5. 开始使用EVE Online Assistant

### API端点
- **健康检查**: `/health/`
- **EVE SSO登录**: `/auth/login`
- **角色列表**: `/characters/`
- **角色详情**: `/characters/{character_id}`
- **数据同步**: `/sync/character/{character_id}`

## 🎉 配置完成

配置完成后，您就可以：
- ✅ 使用EVE SSO登录
- ✅ 管理多个EVE角色
- ✅ 同步角色数据
- ✅ 查看技能、资产、邮件等信息
- ✅ 进行数据分析和优化建议

享受您的EVE Online Assistant！
