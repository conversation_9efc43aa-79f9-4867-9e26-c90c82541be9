#!/usr/bin/env python3
"""
EVE Online Assistant 启动脚本
"""
import sys
import subprocess
import os

def install_package(package):
    """安装包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def check_and_install_deps():
    """检查并安装依赖"""
    print("🔍 检查依赖...")

    required_packages = ["fastapi", "uvicorn", "python-dotenv"]
    missing_packages = []

    for package in required_packages:
        try:
            if package == "python-dotenv":
                import dotenv
            else:
                __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - 需要安装")
            missing_packages.append(package)

    if missing_packages:
        print(f"\n📦 安装缺失的包...")
        for package in missing_packages:
            print(f"   安装 {package}...")
            if install_package(package):
                print(f"   ✅ {package} 安装成功")
            else:
                print(f"   ❌ {package} 安装失败")
                return False

    return True

def get_isolated_python_path():
    """获取隔离环境Python路径"""
    possible_paths = [
        r"C:\Users\<USER>\anaconda3\envs\eve-assistant\python.exe",
        r"C:\Users\<USER>\anaconda3\envs\eve-assistant\Scripts\python.exe",
        "./eve-assistant-env/Scripts/python.exe",
        "./eve-assistant-env/python.exe"
    ]

    for path in possible_paths:
        if os.path.exists(path):
            return path
    return None

def auto_switch_to_isolated_env():
    """自动切换到隔离环境"""
    isolated_python = get_isolated_python_path()

    if isolated_python and isolated_python != sys.executable:
        print("🔄 检测到隔离环境，自动切换...")
        print(f"   从: {sys.executable}")
        print(f"   到: {isolated_python}")

        # 重新启动脚本使用隔离环境
        try:
            subprocess.run([isolated_python] + sys.argv)
            return True
        except Exception as e:
            print(f"❌ 自动切换失败: {e}")
            return False

    return False

def check_environment():
    """检查当前环境"""
    print("🔍 检查Python环境...")
    print(f"🐍 Python版本: {sys.version}")
    print(f"📍 Python路径: {sys.executable}")

    # 检查是否在conda环境中
    conda_env = os.environ.get('CONDA_DEFAULT_ENV')
    if conda_env:
        print(f"✅ Conda环境: {conda_env}")
        if conda_env == 'eve-assistant':
            print("✅ 正在使用推荐的隔离环境")
            return True
        else:
            print("⚠️  建议使用 'eve-assistant' 环境")
    else:
        print("⚠️  未检测到conda环境")

    # 检查是否在虚拟环境中
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ 在虚拟环境中")
        return True
    else:
        # 检查是否是隔离环境的Python
        if "eve-assistant" in sys.executable:
            print("✅ 使用隔离环境Python")
            return True

        print("⚠️  在系统Python环境中（不推荐）")
        print("💡 尝试自动切换到隔离环境...")

        # 尝试自动切换
        if auto_switch_to_isolated_env():
            return True

        print("💡 手动激活方法: conda activate eve-assistant")

    return False

def main():
    """主函数"""
    print("🚀 EVE Online Assistant 启动器")
    print("=" * 40)

    # 检查环境
    env_ok = check_environment()
    if not env_ok:
        print("\n💡 环境建议:")
        print("   1. 激活隔离环境: conda activate eve-assistant")
        print("   2. 或使用快速启动: quick-start.bat")
        print("   3. 或使用环境管理器: scripts\\env-manager.bat")
        print("\n⚠️  继续使用当前环境可能存在依赖冲突风险")

        # 询问是否继续
        try:
            choice = input("\n是否继续启动? (y/N): ").lower().strip()
            if choice not in ['y', 'yes']:
                print("👋 启动已取消")
                return 1
        except KeyboardInterrupt:
            print("\n👋 启动已取消")
            return 1

    # 检查依赖
    if not check_and_install_deps():
        print("❌ 依赖安装失败")
        print("请手动运行: pip install fastapi uvicorn python-dotenv")
        return 1

    print("\n🌐 启动FastAPI服务器...")
    print("   本地地址: http://127.0.0.1:8000")
    print("   API文档: http://127.0.0.1:8000/docs")
    print("   按 Ctrl+C 停止服务器")
    print()

    try:
        # 启动uvicorn服务器
        subprocess.run([
            sys.executable, "-m", "uvicorn",
            "src.presentation.api.main:app",
            "--reload",
            "--host", "127.0.0.1",
            "--port", "8000"
        ])
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except FileNotFoundError:
        print("❌ uvicorn未找到，请安装: pip install uvicorn")
        return 1
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())
