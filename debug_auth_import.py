#!/usr/bin/env python3
"""
调试认证模块导入问题
"""
import sys
sys.path.insert(0, '.')

print("🔍 调试认证模块导入...")

try:
    print("1. 测试基础导入...")
    from src.infrastructure.config import settings
    print("✅ 配置模块导入成功")
    
    print("2. 测试EVE SSO客户端导入...")
    from src.infrastructure.esi import EVESSOClient
    print("✅ EVE SSO客户端导入成功")
    
    print("3. 测试认证服务导入...")
    from src.application.auth import AuthenticationService
    print("✅ 认证服务导入成功")
    
    print("4. 测试依赖注入...")
    from src.presentation.api.dependencies import get_sso_client, get_auth_service
    print("✅ 依赖注入导入成功")
    
    print("5. 测试创建SSO客户端...")
    sso_client = get_sso_client()
    print(f"✅ SSO客户端创建成功: {type(sso_client)}")
    
    print("6. 测试创建认证服务...")
    auth_service = get_auth_service()
    print(f"✅ 认证服务创建成功: {type(auth_service)}")
    
    print("7. 测试认证路由导入...")
    from src.presentation.api.routers import auth
    print("✅ 认证路由导入成功")
    
    print("8. 测试主应用导入...")
    from src.presentation.api.main import app
    print("✅ 主应用导入成功")
    
    print("\n🎉 所有模块导入测试通过！")
    
except Exception as e:
    print(f"❌ 导入失败: {e}")
    import traceback
    traceback.print_exc()
