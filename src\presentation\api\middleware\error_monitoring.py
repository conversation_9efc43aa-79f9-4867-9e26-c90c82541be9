"""
错误监控中间件
"""
import uuid
from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from typing import Callable

from ....infrastructure.monitoring.error_monitor import error_monitor, ErrorSeverity, ErrorCategory
from ....infrastructure.config.logging import get_logger

logger = get_logger(__name__)


class ErrorMonitoringMiddleware(BaseHTTPMiddleware):
    """错误监控中间件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 生成请求ID
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        try:
            # 处理请求
            response = await call_next(request)
            
            # 检查响应状态码
            if response.status_code >= 500:
                await self._report_http_error(request, response, request_id)
            
            return response
            
        except Exception as e:
            # 捕获未处理的异常
            error_id = await self._report_unhandled_exception(request, e, request_id)
            
            # 返回错误响应
            return JSONResponse(
                status_code=500,
                content={
                    "error": "Internal Server Error",
                    "error_id": error_id,
                    "request_id": request_id,
                    "message": "An unexpected error occurred. Please contact support if the problem persists."
                }
            )
    
    async def _report_http_error(self, request: Request, response: Response, request_id: str) -> None:
        """报告HTTP错误"""
        try:
            # 确定错误严重程度
            severity = ErrorSeverity.HIGH if response.status_code >= 500 else ErrorSeverity.MEDIUM
            
            # 确定错误分类
            category = self._categorize_error_by_path(request.url.path)
            
            # 创建模拟异常
            class HTTPError(Exception):
                pass
            
            exception = HTTPError(f"HTTP {response.status_code} error on {request.method} {request.url.path}")
            
            # 收集上下文信息
            context = await self._collect_request_context(request)
            context["response_status"] = response.status_code
            
            # 报告错误
            error_monitor.report_error(
                exception=exception,
                severity=severity,
                category=category,
                context=context,
                user_id=getattr(request.state, 'user_id', None),
                character_id=getattr(request.state, 'character_id', None),
                request_id=request_id
            )
            
        except Exception as e:
            logger.error(f"报告HTTP错误失败: {e}")
    
    async def _report_unhandled_exception(self, request: Request, exception: Exception, request_id: str) -> str:
        """报告未处理的异常"""
        try:
            # 确定错误严重程度
            severity = self._determine_severity(exception)
            
            # 确定错误分类
            category = self._categorize_error(exception, request.url.path)
            
            # 收集上下文信息
            context = await self._collect_request_context(request)
            
            # 报告错误
            error_id = error_monitor.report_error(
                exception=exception,
                severity=severity,
                category=category,
                context=context,
                user_id=getattr(request.state, 'user_id', None),
                character_id=getattr(request.state, 'character_id', None),
                request_id=request_id
            )
            
            return error_id
            
        except Exception as e:
            logger.error(f"报告未处理异常失败: {e}")
            return "unknown_error"
    
    async def _collect_request_context(self, request: Request) -> dict:
        """收集请求上下文信息"""
        context = {
            "method": request.method,
            "url": str(request.url),
            "path": request.url.path,
            "query_params": dict(request.query_params),
            "headers": dict(request.headers),
            "client_host": request.client.host if request.client else None,
            "user_agent": request.headers.get("user-agent"),
        }
        
        # 移除敏感信息
        if "authorization" in context["headers"]:
            context["headers"]["authorization"] = "[REDACTED]"
        
        if "cookie" in context["headers"]:
            context["headers"]["cookie"] = "[REDACTED]"
        
        # 尝试获取请求体（对于POST/PUT请求）
        if request.method in ["POST", "PUT", "PATCH"]:
            try:
                # 注意：这里需要小心处理，因为请求体可能已经被读取
                # 在实际实现中，可能需要在更早的阶段保存请求体
                context["has_body"] = True
            except Exception:
                context["has_body"] = False
        
        return context
    
    def _determine_severity(self, exception: Exception) -> ErrorSeverity:
        """确定错误严重程度"""
        exception_type = type(exception).__name__
        
        # 严重错误
        if exception_type in ["DatabaseError", "ConnectionError", "SystemExit"]:
            return ErrorSeverity.CRITICAL
        
        # 高严重程度错误
        if exception_type in ["ValueError", "TypeError", "AttributeError"]:
            return ErrorSeverity.HIGH
        
        # 中等严重程度错误
        if exception_type in ["HTTPException", "ValidationError"]:
            return ErrorSeverity.MEDIUM
        
        # 默认为中等严重程度
        return ErrorSeverity.MEDIUM
    
    def _categorize_error(self, exception: Exception, path: str) -> ErrorCategory:
        """根据异常类型和路径分类错误"""
        exception_type = type(exception).__name__
        
        # 根据异常类型分类
        if exception_type in ["DatabaseError", "SQLAlchemyError"]:
            return ErrorCategory.DATABASE
        
        if exception_type in ["HTTPException", "RequestException"]:
            return ErrorCategory.API
        
        if exception_type in ["AuthenticationError", "PermissionError"]:
            return ErrorCategory.AUTHENTICATION
        
        if exception_type in ["ValidationError", "ValueError"]:
            return ErrorCategory.VALIDATION
        
        if exception_type in ["ConnectionError", "TimeoutError"]:
            return ErrorCategory.NETWORK
        
        # 根据路径分类
        return self._categorize_error_by_path(path)
    
    def _categorize_error_by_path(self, path: str) -> ErrorCategory:
        """根据请求路径分类错误"""
        if "/auth/" in path or "/login" in path or "/callback" in path:
            return ErrorCategory.AUTHENTICATION
        
        if "/sync/" in path:
            return ErrorCategory.SYNC
        
        if "/api/" in path:
            return ErrorCategory.API
        
        return ErrorCategory.UNKNOWN


def setup_error_monitoring_middleware(app):
    """设置错误监控中间件"""
    app.add_middleware(ErrorMonitoringMiddleware)
    logger.info("错误监控中间件已启用")
