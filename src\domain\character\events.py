"""
角色领域事件
"""
from datetime import datetime
from typing import Any, Dict, Optional

from ..shared.domain_events import DomainEvent


class CharacterCreatedEvent(DomainEvent):
    """角色创建事件"""
    
    def __init__(self, 
                 character_id: int,
                 name: str,
                 corporation_id: int,
                 occurred_at: Optional[datetime] = None):
        event_data = {
            "name": name,
            "corporation_id": corporation_id
        }
        super().__init__(
            aggregate_id=character_id,
            event_data=event_data,
            occurred_at=occurred_at
        )


class CharacterUpdatedEvent(DomainEvent):
    """角色信息更新事件"""
    
    def __init__(self,
                 character_id: int,
                 old_corporation_id: Optional[int] = None,
                 new_corporation_id: Optional[int] = None,
                 old_alliance_id: Optional[int] = None,
                 new_alliance_id: Optional[int] = None,
                 occurred_at: Optional[datetime] = None):
        event_data = {
            "old_corporation_id": old_corporation_id,
            "new_corporation_id": new_corporation_id,
            "old_alliance_id": old_alliance_id,
            "new_alliance_id": new_alliance_id
        }
        super().__init__(
            aggregate_id=character_id,
            event_data=event_data,
            occurred_at=occurred_at
        )


class CharacterLocationChangedEvent(DomainEvent):
    """角色位置变更事件"""
    
    def __init__(self,
                 character_id: int,
                 old_system_id: Optional[int] = None,
                 new_system_id: Optional[int] = None,
                 old_station_id: Optional[int] = None,
                 new_station_id: Optional[int] = None,
                 old_structure_id: Optional[int] = None,
                 new_structure_id: Optional[int] = None,
                 occurred_at: Optional[datetime] = None):
        event_data = {
            "old_system_id": old_system_id,
            "new_system_id": new_system_id,
            "old_station_id": old_station_id,
            "new_station_id": new_station_id,
            "old_structure_id": old_structure_id,
            "new_structure_id": new_structure_id
        }
        super().__init__(
            aggregate_id=character_id,
            event_data=event_data,
            occurred_at=occurred_at
        )


class CharacterOnlineStatusChangedEvent(DomainEvent):
    """角色在线状态变更事件"""
    
    def __init__(self,
                 character_id: int,
                 is_online: bool,
                 login_time: Optional[datetime] = None,
                 logout_time: Optional[datetime] = None,
                 occurred_at: Optional[datetime] = None):
        event_data = {
            "is_online": is_online,
            "login_time": login_time.isoformat() if login_time else None,
            "logout_time": logout_time.isoformat() if logout_time else None
        }
        super().__init__(
            aggregate_id=character_id,
            event_data=event_data,
            occurred_at=occurred_at
        )


class SkillTrainingStartedEvent(DomainEvent):
    """技能训练开始事件"""
    
    def __init__(self,
                 character_id: int,
                 skill_id: int,
                 target_level: int,
                 start_time: datetime,
                 finish_time: datetime,
                 occurred_at: Optional[datetime] = None):
        event_data = {
            "skill_id": skill_id,
            "target_level": target_level,
            "start_time": start_time.isoformat(),
            "finish_time": finish_time.isoformat()
        }
        super().__init__(
            aggregate_id=character_id,
            event_data=event_data,
            occurred_at=occurred_at
        )


class SkillTrainingCompletedEvent(DomainEvent):
    """技能训练完成事件"""
    
    def __init__(self,
                 character_id: int,
                 skill_id: int,
                 new_level: int,
                 skill_points_gained: int,
                 occurred_at: Optional[datetime] = None):
        event_data = {
            "skill_id": skill_id,
            "new_level": new_level,
            "skill_points_gained": skill_points_gained
        }
        super().__init__(
            aggregate_id=character_id,
            event_data=event_data,
            occurred_at=occurred_at
        )


class SkillTrainingPausedEvent(DomainEvent):
    """技能训练暂停事件"""
    
    def __init__(self,
                 character_id: int,
                 skill_id: int,
                 paused_at: datetime,
                 occurred_at: Optional[datetime] = None):
        event_data = {
            "skill_id": skill_id,
            "paused_at": paused_at.isoformat()
        }
        super().__init__(
            aggregate_id=character_id,
            event_data=event_data,
            occurred_at=occurred_at
        )


class CharacterAttributesUpdatedEvent(DomainEvent):
    """角色属性更新事件"""
    
    def __init__(self,
                 character_id: int,
                 old_attributes: Dict[str, int],
                 new_attributes: Dict[str, int],
                 occurred_at: Optional[datetime] = None):
        event_data = {
            "old_attributes": old_attributes,
            "new_attributes": new_attributes
        }
        super().__init__(
            aggregate_id=character_id,
            event_data=event_data,
            occurred_at=occurred_at
        )


class CharacterWalletBalanceChangedEvent(DomainEvent):
    """角色钱包余额变更事件"""
    
    def __init__(self,
                 character_id: int,
                 old_balance: float,
                 new_balance: float,
                 change_amount: float,
                 transaction_type: str,
                 occurred_at: Optional[datetime] = None):
        event_data = {
            "old_balance": old_balance,
            "new_balance": new_balance,
            "change_amount": change_amount,
            "transaction_type": transaction_type
        }
        super().__init__(
            aggregate_id=character_id,
            event_data=event_data,
            occurred_at=occurred_at
        )


class CharacterJoinedCorporationEvent(DomainEvent):
    """角色加入公司事件"""
    
    def __init__(self,
                 character_id: int,
                 old_corporation_id: int,
                 new_corporation_id: int,
                 join_date: datetime,
                 occurred_at: Optional[datetime] = None):
        event_data = {
            "old_corporation_id": old_corporation_id,
            "new_corporation_id": new_corporation_id,
            "join_date": join_date.isoformat()
        }
        super().__init__(
            aggregate_id=character_id,
            event_data=event_data,
            occurred_at=occurred_at
        )


class CharacterLeftCorporationEvent(DomainEvent):
    """角色离开公司事件"""
    
    def __init__(self,
                 character_id: int,
                 corporation_id: int,
                 leave_date: datetime,
                 reason: Optional[str] = None,
                 occurred_at: Optional[datetime] = None):
        event_data = {
            "corporation_id": corporation_id,
            "leave_date": leave_date.isoformat(),
            "reason": reason
        }
        super().__init__(
            aggregate_id=character_id,
            event_data=event_data,
            occurred_at=occurred_at
        )


class CharacterCloneJumpedEvent(DomainEvent):
    """角色克隆跳跃事件"""
    
    def __init__(self,
                 character_id: int,
                 from_location_id: int,
                 to_location_id: int,
                 clone_id: int,
                 jump_time: datetime,
                 occurred_at: Optional[datetime] = None):
        event_data = {
            "from_location_id": from_location_id,
            "to_location_id": to_location_id,
            "clone_id": clone_id,
            "jump_time": jump_time.isoformat()
        }
        super().__init__(
            aggregate_id=character_id,
            event_data=event_data,
            occurred_at=occurred_at
        )
