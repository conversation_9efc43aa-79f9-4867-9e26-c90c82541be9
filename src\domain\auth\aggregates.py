"""
认证领域聚合根
"""
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set
from uuid import UUID, uuid4

from ..shared.base_entity import AggregateRoot
from ..shared.value_objects import UserId, CharacterId
from .value_objects import (
    AccessToken, RefreshToken, Scope, SessionToken, CharacterBinding,
    AuthenticationRequest, PKCEChallenge
)
from .events import (
    UserRegisteredEvent, UserAuthenticatedEvent, CharacterBoundEvent,
    TokenRefreshedEvent, SessionCreatedEvent, SessionExpiredEvent,
    UserLoggedOutEvent
)
from .exceptions import (
    CharacterNotBoundError, InvalidTokenError, SessionExpiredError,
    DuplicateCharacterBindingError, MaxCharacterBindingsExceededError
)


@dataclass
class UserAuthentication(AggregateRoot):
    """用户认证聚合根"""
    user_id: UserId
    email: str
    username: str
    password_hash: str
    is_active: bool = True
    is_verified: bool = False
    created_at: datetime = field(default_factory=datetime.utcnow)
    last_login_at: Optional[datetime] = None
    failed_login_attempts: int = 0
    locked_until: Optional[datetime] = None
    
    # 角色绑定
    character_bindings: Dict[CharacterId, CharacterBinding] = field(default_factory=dict)
    main_character_id: Optional[CharacterId] = None
    max_character_bindings: int = 10
    
    # 会话管理
    active_sessions: Dict[UUID, SessionToken] = field(default_factory=dict)
    max_active_sessions: int = 5
    
    # 认证请求
    pending_auth_requests: Dict[str, AuthenticationRequest] = field(default_factory=dict)
    
    def __post_init__(self):
        super().__post_init__()
        self.id = self.user_id
    
    @classmethod
    def create(cls, user_id: UserId, email: str, username: str, 
               password_hash: str) -> 'UserAuthentication':
        """创建新用户"""
        user_auth = cls(
            user_id=user_id,
            email=email,
            username=username,
            password_hash=password_hash
        )
        
        user_auth.add_domain_event(UserRegisteredEvent(
            user_id=user_id,
            email=email,
            username=username,
            created_at=user_auth.created_at
        ))
        
        return user_auth
    
    def authenticate(self, password_hash: str) -> bool:
        """验证用户密码"""
        if self.is_locked():
            return False
        
        if not self.is_active:
            return False
        
        if self.password_hash == password_hash:
            # 认证成功
            self.failed_login_attempts = 0
            self.last_login_at = datetime.utcnow()
            self.locked_until = None
            
            self.add_domain_event(UserAuthenticatedEvent(
                user_id=self.user_id,
                authenticated_at=self.last_login_at
            ))
            
            return True
        else:
            # 认证失败
            self.failed_login_attempts += 1
            
            # 如果失败次数过多，锁定账户
            if self.failed_login_attempts >= 5:
                self.locked_until = datetime.utcnow() + timedelta(minutes=30)
            
            return False
    
    def is_locked(self) -> bool:
        """检查账户是否被锁定"""
        if self.locked_until is None:
            return False
        
        if datetime.utcnow() >= self.locked_until:
            # 锁定时间已过，自动解锁
            self.locked_until = None
            self.failed_login_attempts = 0
            return False
        
        return True
    
    def bind_character(self, character_id: CharacterId, access_token: AccessToken,
                      refresh_token: RefreshToken, scopes: List[Scope]) -> None:
        """绑定EVE角色"""
        # 检查是否已经绑定了该角色
        if character_id in self.character_bindings:
            raise DuplicateCharacterBindingError(character_id)
        
        # 检查绑定数量限制
        if len(self.character_bindings) >= self.max_character_bindings:
            raise MaxCharacterBindingsExceededError(self.max_character_bindings)
        
        # 创建角色绑定
        binding = CharacterBinding(
            character_id=character_id,
            access_token=access_token,
            refresh_token=refresh_token,
            scopes=scopes,
            bound_at=datetime.utcnow()
        )
        
        self.character_bindings[character_id] = binding
        
        # 如果是第一个角色，设为主角色
        if self.main_character_id is None:
            self.main_character_id = character_id
        
        self.add_domain_event(CharacterBoundEvent(
            user_id=self.user_id,
            character_id=character_id,
            scopes=[scope.name for scope in scopes],
            bound_at=binding.bound_at
        ))
    
    def unbind_character(self, character_id: CharacterId) -> None:
        """解绑EVE角色"""
        if character_id not in self.character_bindings:
            raise CharacterNotBoundError(character_id)
        
        # 移除绑定
        del self.character_bindings[character_id]
        
        # 如果解绑的是主角色，选择新的主角色
        if self.main_character_id == character_id:
            if self.character_bindings:
                # 选择最早绑定的角色作为新的主角色
                self.main_character_id = min(
                    self.character_bindings.keys(),
                    key=lambda cid: self.character_bindings[cid].bound_at
                )
            else:
                self.main_character_id = None
    
    def refresh_character_token(self, character_id: CharacterId,
                               new_access_token: AccessToken,
                               new_refresh_token: Optional[RefreshToken] = None) -> None:
        """刷新角色令牌"""
        if character_id not in self.character_bindings:
            raise CharacterNotBoundError(character_id)
        
        old_binding = self.character_bindings[character_id]
        new_binding = old_binding.refresh_token(new_access_token, new_refresh_token)
        
        self.character_bindings[character_id] = new_binding
        
        self.add_domain_event(TokenRefreshedEvent(
            user_id=self.user_id,
            character_id=character_id,
            refreshed_at=datetime.utcnow()
        ))
    
    def create_session(self, character_id: Optional[CharacterId] = None,
                      expires_in: timedelta = timedelta(hours=24),
                      session_data: Dict = None) -> SessionToken:
        """创建用户会话"""
        # 使用主角色或指定角色
        session_character_id = character_id or self.main_character_id
        
        if session_character_id and session_character_id not in self.character_bindings:
            raise CharacterNotBoundError(session_character_id)
        
        # 检查活跃会话数量限制
        self._cleanup_expired_sessions()
        
        if len(self.active_sessions) >= self.max_active_sessions:
            # 移除最旧的会话
            oldest_session_id = min(
                self.active_sessions.keys(),
                key=lambda sid: self.active_sessions[sid].created_at
            )
            del self.active_sessions[oldest_session_id]
        
        # 创建新会话
        session = SessionToken.create(
            user_id=self.user_id,
            character_id=session_character_id or CharacterId(0),  # 使用0表示无角色会话
            expires_in=expires_in
        )
        
        self.active_sessions[session.token_id] = session
        
        self.add_domain_event(SessionCreatedEvent(
            user_id=self.user_id,
            session_id=session.token_id,
            character_id=session_character_id,
            created_at=session.created_at,
            expires_at=session.expires_at
        ))
        
        return session
    
    def invalidate_session(self, session_id: UUID) -> None:
        """使会话失效"""
        if session_id in self.active_sessions:
            session = self.active_sessions[session_id]
            del self.active_sessions[session_id]
            
            self.add_domain_event(SessionExpiredEvent(
                user_id=self.user_id,
                session_id=session_id,
                expired_at=datetime.utcnow()
            ))
    
    def logout_all_sessions(self) -> None:
        """登出所有会话"""
        session_ids = list(self.active_sessions.keys())
        
        for session_id in session_ids:
            self.invalidate_session(session_id)
        
        self.add_domain_event(UserLoggedOutEvent(
            user_id=self.user_id,
            logged_out_at=datetime.utcnow(),
            all_sessions=True
        ))
    
    def create_auth_request(self, scopes: List[Scope], 
                           redirect_url: Optional[str] = None,
                           use_pkce: bool = True) -> AuthenticationRequest:
        """创建认证请求"""
        # 清理过期的认证请求
        self._cleanup_expired_auth_requests()
        
        auth_request = AuthenticationRequest.create(
            scopes=scopes,
            redirect_url=redirect_url,
            use_pkce=use_pkce
        )
        
        self.pending_auth_requests[auth_request.state] = auth_request
        
        return auth_request
    
    def complete_auth_request(self, state: str, character_id: CharacterId,
                             access_token: AccessToken, refresh_token: RefreshToken,
                             scopes: List[Scope]) -> AuthenticationRequest:
        """完成认证请求"""
        if state not in self.pending_auth_requests:
            raise InvalidTokenError("Invalid authentication state")
        
        auth_request = self.pending_auth_requests[state]
        
        if not auth_request.is_valid():
            del self.pending_auth_requests[state]
            raise InvalidTokenError("Authentication request expired or invalid")
        
        # 绑定角色
        self.bind_character(character_id, access_token, refresh_token, scopes)
        
        # 清理认证请求
        del self.pending_auth_requests[state]
        
        return auth_request
    
    def _cleanup_expired_sessions(self) -> None:
        """清理过期的会话"""
        expired_session_ids = [
            session_id for session_id, session in self.active_sessions.items()
            if session.is_expired()
        ]
        
        for session_id in expired_session_ids:
            self.invalidate_session(session_id)
    
    def _cleanup_expired_auth_requests(self) -> None:
        """清理过期的认证请求"""
        expired_states = [
            state for state, request in self.pending_auth_requests.items()
            if request.is_expired()
        ]
        
        for state in expired_states:
            del self.pending_auth_requests[state]
    
    def get_character_binding(self, character_id: CharacterId) -> CharacterBinding:
        """获取角色绑定"""
        if character_id not in self.character_bindings:
            raise CharacterNotBoundError(character_id)
        
        return self.character_bindings[character_id]
    
    def has_valid_character_token(self, character_id: CharacterId) -> bool:
        """检查角色是否有有效令牌"""
        try:
            binding = self.get_character_binding(character_id)
            return binding.is_token_valid()
        except CharacterNotBoundError:
            return False
    
    def get_character_scopes(self, character_id: CharacterId) -> List[str]:
        """获取角色权限范围"""
        binding = self.get_character_binding(character_id)
        return [scope.name for scope in binding.scopes]
    
    def verify_session(self, session_token: str) -> Optional[SessionToken]:
        """验证会话令牌"""
        for session in self.active_sessions.values():
            if session.token == session_token and session.is_valid():
                return session
        
        return None
