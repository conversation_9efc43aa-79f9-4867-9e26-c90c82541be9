# Python模块导入问题排查知识库

## 问题分类

### 1. ModuleNotFoundError 问题

#### 1.1 包结构不完整导致的导入错误

**问题描述**：
- 错误信息：`ModuleNotFoundError: No module named 'src.infrastructure.persistence.repositories.asset'`
- 根本原因：`__init__.py` 文件中导入了不存在的模块

**解决方案**：
1. 检查目录结构，确认实际存在的文件
2. 修改 `__init__.py` 文件，只导入存在的模块
3. 移除 `__all__` 中不存在模块的引用

**预防措施**：
- 创建新模块时，同步更新 `__init__.py`
- 删除模块时，同步清理 `__init__.py` 中的导入
- 使用IDE的重构功能进行模块重命名

#### 1.2 Python路径问题

**问题描述**：
- 错误信息：`No module named 'src.presentation'`
- 根本原因：Python无法找到项目根目录

**解决方案**：
1. 使用 `pip install -e .` 安装项目包
2. 确保 `pyproject.toml` 配置正确
3. 设置 `PYTHONPATH` 环境变量

**验证方法**：
```python
import sys
sys.path.append('.')
from src.presentation.api.main import app
print('导入成功')
```

### 2. 服务器启动问题

#### 2.1 uvicorn文件监控问题

**问题描述**：
- 服务器启动后立即崩溃
- 错误信息涉及 `fsevents` 或文件监控

**解决方案**：
1. 使用 `--reload-dir src` 限制监控范围
2. 避免监控 `node_modules` 等大型目录
3. 使用 `--reload-exclude` 排除特定目录

**最佳实践**：
```bash
uvicorn src.presentation.api.main:app --reload --reload-dir src --host 127.0.0.1 --port 8000
```

#### 2.2 依赖注入问题

**问题描述**：
- API调用时出现500错误
- 错误发生在依赖解析阶段

**解决方案**：
1. 检查依赖注入链中的所有模块
2. 确保所有被注入的服务类都能正确导入
3. 使用简化的依赖注入进行测试

## 排查流程

### 快速诊断步骤

1. **检查导入**：
   ```python
   python -c "from src.presentation.api.main import app; print('成功')"
   ```

2. **检查包安装**：
   ```bash
   pip list | grep eve-online-assistant
   ```

3. **检查文件结构**：
   ```bash
   find src -name "*.py" | head -10
   ```

4. **检查 __init__.py 文件**：
   - 确保所有导入的模块都存在
   - 检查循环导入问题

### 系统性修复方法

1. **模块清理脚本**：
   - 扫描所有 `__init__.py` 文件
   - 验证导入的模块是否存在
   - 自动清理无效导入

2. **依赖验证**：
   - 检查所有服务类的依赖链
   - 确保依赖注入配置正确
   - 使用mock对象进行隔离测试

## 预防措施

### 开发规范

1. **模块创建规范**：
   - 创建新模块时同步更新 `__init__.py`
   - 使用IDE的模板功能确保一致性
   - 遵循项目的命名约定

2. **重构规范**：
   - 使用IDE的重构功能而非手动修改
   - 重构后运行完整的导入测试
   - 更新相关的文档和测试

3. **测试规范**：
   - 每次修改后运行导入测试
   - 使用CI/CD自动化测试
   - 包含模块导入的单元测试

### 监控和告警

1. **启动时检查**：
   - 服务器启动时验证所有关键模块
   - 记录导入失败的详细信息
   - 提供友好的错误提示

2. **健康检查**：
   - 定期检查模块导入状态
   - 监控依赖注入的性能
   - 记录异常模式

## 工具和脚本

### 导入验证脚本

```python
#!/usr/bin/env python3
"""验证项目模块导入"""
import importlib
import sys
from pathlib import Path

def verify_imports():
    """验证关键模块导入"""
    modules = [
        'src.presentation.api.main',
        'src.application.services.auth',
        'src.infrastructure.persistence.repositories',
    ]
    
    for module in modules:
        try:
            importlib.import_module(module)
            print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module}: {e}")
            return False
    return True

if __name__ == "__main__":
    success = verify_imports()
    sys.exit(0 if success else 1)
```

### __init__.py 清理脚本

```python
#!/usr/bin/env python3
"""清理无效的__init__.py导入"""
import ast
import os
from pathlib import Path

def clean_init_files():
    """清理所有__init__.py文件中的无效导入"""
    for init_file in Path('src').rglob('__init__.py'):
        clean_single_init(init_file)

def clean_single_init(init_file):
    """清理单个__init__.py文件"""
    # 实现逻辑...
    pass
```

## 经验总结

### 常见陷阱

1. **循环导入**：避免模块间的循环依赖
2. **路径问题**：确保Python能找到项目根目录
3. **大小写敏感**：注意文件名的大小写
4. **隐藏字符**：检查文件名中的特殊字符

### 最佳实践

1. **渐进式开发**：逐步添加模块，及时测试
2. **自动化测试**：每次修改后自动验证导入
3. **文档同步**：保持代码和文档的一致性
4. **团队协作**：建立统一的开发规范

---

*最后更新：2025-08-10*
*相关问题：EVE登录功能模块导入错误修复*
