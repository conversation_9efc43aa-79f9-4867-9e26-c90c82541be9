"""
统一配置管理中心
提供单一的配置源和管理机制
"""
import os
import json
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, asdict
from pathlib import Path
from abc import ABC, abstractmethod
from datetime import datetime

from .settings import settings
from ..monitoring.error_monitor import error_monitor


@dataclass
class OAuthConfig:
    """OAuth配置"""
    client_id: str
    client_secret: str
    callback_url: str
    scopes: List[str]
    authorization_url: str
    token_url: str
    verify_url: Optional[str] = None


@dataclass
class ValidationResult:
    """配置验证结果"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    suggestions: List[str]


class ConfigSource(ABC):
    """配置源抽象基类"""
    
    @abstractmethod
    def get_config(self, key: str) -> Any:
        """获取配置值"""
        pass
    
    @abstractmethod
    def set_config(self, key: str, value: Any) -> bool:
        """设置配置值"""
        pass
    
    @abstractmethod
    def has_config(self, key: str) -> bool:
        """检查配置是否存在"""
        pass


class EnvironmentConfigSource(ConfigSource):
    """环境变量配置源"""
    
    def get_config(self, key: str) -> Any:
        return os.getenv(key)
    
    def set_config(self, key: str, value: Any) -> bool:
        os.environ[key] = str(value)
        return True
    
    def has_config(self, key: str) -> bool:
        return key in os.environ


class FileConfigSource(ConfigSource):
    """文件配置源"""
    
    def __init__(self, config_file: str = ".env"):
        self.config_file = Path(config_file)
        self._config_cache = {}
        self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        if self.config_file.exists():
            with open(self.config_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        self._config_cache[key.strip()] = value.strip().strip('"\'')
    
    def get_config(self, key: str) -> Any:
        return self._config_cache.get(key)
    
    def set_config(self, key: str, value: Any) -> bool:
        self._config_cache[key] = str(value)
        return self._save_config()
    
    def has_config(self, key: str) -> bool:
        return key in self._config_cache
    
    def _save_config(self) -> bool:
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                for key, value in self._config_cache.items():
                    f.write(f'{key}="{value}"\n')
            return True
        except Exception as e:
            error_monitor.report_error(
                exception=e,
                context={"config_file": str(self.config_file)}
            )
            return False


class DatabaseConfigSource(ConfigSource):
    """数据库配置源（预留）"""
    
    def get_config(self, key: str) -> Any:
        # TODO: 实现数据库配置读取
        return None
    
    def set_config(self, key: str, value: Any) -> bool:
        # TODO: 实现数据库配置写入
        return False
    
    def has_config(self, key: str) -> bool:
        # TODO: 实现数据库配置检查
        return False


class UnifiedConfigManager:
    """统一配置管理器"""
    
    def __init__(self):
        self.config_sources = [
            EnvironmentConfigSource(),
            FileConfigSource(),
            DatabaseConfigSource()
        ]
        self._config_cache = {}
        self._cache_timestamp = None
    
    def get_oauth_config(self, provider: str) -> Optional[OAuthConfig]:
        """获取OAuth配置"""
        if provider.lower() == 'eve':
            return self._get_eve_oauth_config()
        else:
            return None
    
    def _get_eve_oauth_config(self) -> Optional[OAuthConfig]:
        """获取EVE OAuth配置"""
        try:
            client_id = self._get_config_value('EVE_CLIENT_ID')
            client_secret = self._get_config_value('EVE_CLIENT_SECRET')
            callback_url = self._get_config_value('EVE_CALLBACK_URL')
            scopes_str = self._get_config_value('EVE_SSO_SCOPES', '')
            
            if not all([client_id, client_secret, callback_url]):
                return None
            
            scopes = scopes_str.split() if scopes_str else []
            
            return OAuthConfig(
                client_id=client_id,
                client_secret=client_secret,
                callback_url=callback_url,
                scopes=scopes,
                authorization_url='https://login.eveonline.com/v2/oauth/authorize',
                token_url='https://login.eveonline.com/v2/oauth/token',
                verify_url='https://login.eveonline.com/oauth/verify'
            )
        except Exception as e:
            error_monitor.report_error(
                exception=e,
                context={"provider": "eve", "action": "get_oauth_config"}
            )
            return None
    
    def _get_config_value(self, key: str, default: Any = None) -> Any:
        """从配置源获取配置值"""
        for source in self.config_sources:
            if source.has_config(key):
                value = source.get_config(key)
                if value is not None:
                    return value
        return default
    
    def validate_config_consistency(self) -> ValidationResult:
        """验证配置一致性"""
        errors = []
        warnings = []
        suggestions = []
        
        # 检查EVE OAuth配置
        eve_config = self.get_oauth_config('eve')
        if not eve_config:
            errors.append("EVE OAuth配置不完整")
            suggestions.append("检查EVE_CLIENT_ID、EVE_CLIENT_SECRET、EVE_CALLBACK_URL配置")
        else:
            # 验证scopes
            invalid_scopes = self._validate_eve_scopes(eve_config.scopes)
            if invalid_scopes:
                errors.extend([f"无效的EVE scope: {scope}" for scope in invalid_scopes])
                suggestions.append("移除无效的scope，参考EVE ESI官方文档")
        
        # 检查配置源一致性
        consistency_issues = self._check_config_source_consistency()
        warnings.extend(consistency_issues)
        
        is_valid = len(errors) == 0
        
        return ValidationResult(
            is_valid=is_valid,
            errors=errors,
            warnings=warnings,
            suggestions=suggestions
        )
    
    def _validate_eve_scopes(self, scopes: List[str]) -> List[str]:
        """验证EVE scopes的有效性"""
        # 已知的无效scopes
        invalid_scopes = [
            'esi-characters.read_characters.v1',
            'esi-character.read_character.v1'
        ]
        
        found_invalid = []
        for scope in scopes:
            if scope in invalid_scopes:
                found_invalid.append(scope)
        
        return found_invalid
    
    def _check_config_source_consistency(self) -> List[str]:
        """检查配置源之间的一致性"""
        issues = []
        
        # 检查关键配置在不同源中的一致性
        key_configs = ['EVE_CLIENT_ID', 'EVE_CALLBACK_URL', 'EVE_SSO_SCOPES']
        
        for key in key_configs:
            values = []
            sources = []
            
            for source in self.config_sources:
                if source.has_config(key):
                    value = source.get_config(key)
                    if value:
                        values.append(value)
                        sources.append(type(source).__name__)
            
            # 如果有多个不同的值，报告不一致
            if len(set(values)) > 1:
                issues.append(f"配置 {key} 在不同源中不一致: {dict(zip(sources, values))}")
        
        return issues
    
    def sync_frontend_config(self) -> Dict[str, Any]:
        """同步前端配置"""
        frontend_config = {
            'api_base_url': self._get_config_value('API_BASE_URL', 'http://localhost:8000'),
            'eve_sso': {
                'scopes': self._get_config_value('EVE_SSO_SCOPES', '').split(),
                'callback_url': self._get_config_value('EVE_CALLBACK_URL')
            },
            'features': {
                'monitoring_enabled': self._get_config_value('MONITORING_ENABLED', 'true').lower() == 'true',
                'debug_mode': self._get_config_value('DEBUG', 'false').lower() == 'true'
            }
        }
        
        return frontend_config
    
    def update_config(self, key: str, value: Any, source_type: str = 'environment') -> bool:
        """更新配置"""
        try:
            if source_type == 'environment':
                source = self.config_sources[0]  # EnvironmentConfigSource
            elif source_type == 'file':
                source = self.config_sources[1]  # FileConfigSource
            else:
                return False
            
            success = source.set_config(key, value)
            
            if success:
                # 清除缓存
                self._config_cache.clear()
                self._cache_timestamp = None
                
                # 记录配置变更
                self._log_config_change(key, value, source_type)
            
            return success
            
        except Exception as e:
            error_monitor.report_error(
                exception=e,
                context={"key": key, "value": str(value), "source_type": source_type}
            )
            return False
    
    def _log_config_change(self, key: str, value: Any, source_type: str):
        """记录配置变更"""
        change_log = {
            'timestamp': datetime.utcnow().isoformat(),
            'key': key,
            'value': str(value),
            'source_type': source_type,
            'user': 'system'  # TODO: 获取实际用户信息
        }
        
        # 保存到变更日志文件
        log_file = Path('logs/config_changes.jsonl')
        log_file.parent.mkdir(exist_ok=True)
        
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(json.dumps(change_log) + '\n')
    
    def get_config_history(self, key: str) -> List[Dict[str, Any]]:
        """获取配置变更历史"""
        history = []
        log_file = Path('logs/config_changes.jsonl')
        
        if log_file.exists():
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        change = json.loads(line.strip())
                        if change.get('key') == key:
                            history.append(change)
                    except json.JSONDecodeError:
                        continue
        
        return sorted(history, key=lambda x: x['timestamp'], reverse=True)
    
    def export_config(self, format: str = 'json') -> str:
        """导出配置"""
        config_data = {}
        
        # 收集所有配置
        all_keys = set()
        for source in self.config_sources:
            if hasattr(source, '_config_cache'):
                all_keys.update(source._config_cache.keys())
        
        for key in all_keys:
            value = self._get_config_value(key)
            if value is not None:
                config_data[key] = value
        
        if format == 'json':
            return json.dumps(config_data, indent=2)
        elif format == 'env':
            lines = []
            for key, value in config_data.items():
                lines.append(f'{key}="{value}"')
            return '\n'.join(lines)
        else:
            raise ValueError(f"不支持的导出格式: {format}")


# 全局配置管理器实例
config_manager = UnifiedConfigManager()
