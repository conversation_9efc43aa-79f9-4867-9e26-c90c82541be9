#!/usr/bin/env python3
"""
EVE SSO Scope权限管理工具
提供权限的分类查看、启用/禁用、批量操作等功能
"""
import sys
import re
from pathlib import Path
from typing import Dict, List, Set
from dataclasses import dataclass


@dataclass
class ScopeInfo:
    """权限信息"""
    name: str
    category: str
    description: str
    enabled: bool = True


class ScopeManager:
    """权限管理器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.env_file = self.project_root / ".env"
        self.scopes = {}
        self.categories = {
            "基础权限": {
                "icon": "🔓",
                "scopes": ["publicData"],
                "description": "基础的角色信息访问权限"
            },
            "位置状态": {
                "icon": "📍", 
                "scopes": [
                    "esi-location.read_location.v1",
                    "esi-location.read_online.v1", 
                    "esi-location.read_ship_type.v1"
                ],
                "description": "角色位置和在线状态追踪"
            },
            "技能系统": {
                "icon": "🎓",
                "scopes": [
                    "esi-skills.read_skills.v1",
                    "esi-skills.read_skillqueue.v1"
                ],
                "description": "技能信息和训练队列管理"
            },
            "克隆体": {
                "icon": "🧬",
                "scopes": [
                    "esi-clones.read_clones.v1",
                    "esi-clones.read_implants.v1"
                ],
                "description": "克隆体和植入体信息"
            },
            "资产财务": {
                "icon": "💰",
                "scopes": [
                    "esi-assets.read_assets.v1",
                    "esi-assets.read_corporation_assets.v1",
                    "esi-wallet.read_character_wallet.v1",
                    "esi-wallet.read_corporation_wallets.v1"
                ],
                "description": "个人和公司资产、钱包管理"
            },
            "角色信息": {
                "icon": "🧑",
                "scopes": [
                    "esi-characters.read_contacts.v1",
                    "esi-characters.write_contacts.v1",
                    "esi-characters.read_loyalty.v1",
                    "esi-characters.read_medals.v1",
                    "esi-characters.read_standings.v1",
                    "esi-characters.read_agents_research.v1",
                    "esi-characters.read_blueprints.v1",
                    "esi-characters.read_corporation_roles.v1",
                    "esi-characters.read_fatigue.v1",
                    "esi-characters.read_notifications.v1",
                    "esi-characters.read_titles.v1",
                    "esi-characters.read_fw_stats.v1"
                ],
                "description": "角色详细信息和社交关系"
            },
            "公司管理": {
                "icon": "🏢",
                "scopes": [
                    "esi-corporations.read_corporation_membership.v1",
                    "esi-corporations.read_structures.v1",
                    "esi-corporations.track_members.v1",
                    "esi-corporations.read_divisions.v1",
                    "esi-corporations.read_contacts.v1",
                    "esi-corporations.read_titles.v1",
                    "esi-corporations.read_blueprints.v1",
                    "esi-corporations.read_standings.v1",
                    "esi-corporations.read_starbases.v1",
                    "esi-corporations.read_container_logs.v1",
                    "esi-corporations.read_facilities.v1",
                    "esi-corporations.read_medals.v1",
                    "esi-corporations.read_fw_stats.v1"
                ],
                "description": "公司管理和成员信息"
            },
            "联盟管理": {
                "icon": "🤝",
                "scopes": [
                    "esi-alliances.read_contacts.v1"
                ],
                "description": "联盟联系人管理"
            },
            "通讯系统": {
                "icon": "📧",
                "scopes": [
                    "esi-mail.organize_mail.v1",
                    "esi-mail.read_mail.v1",
                    "esi-mail.send_mail.v1",
                    "esi-calendar.respond_calendar_events.v1",
                    "esi-calendar.read_calendar_events.v1"
                ],
                "description": "邮件和日历管理"
            },
            "合同系统": {
                "icon": "📋",
                "scopes": [
                    "esi-contracts.read_character_contracts.v1",
                    "esi-contracts.read_corporation_contracts.v1"
                ],
                "description": "个人和公司合同管理"
            },
            "工业系统": {
                "icon": "🏭",
                "scopes": [
                    "esi-industry.read_character_jobs.v1",
                    "esi-industry.read_corporation_jobs.v1",
                    "esi-industry.read_character_mining.v1",
                    "esi-industry.read_corporation_mining.v1"
                ],
                "description": "工业作业和挖矿记录"
            },
            "市场系统": {
                "icon": "💹",
                "scopes": [
                    "esi-markets.read_character_orders.v1",
                    "esi-markets.read_corporation_orders.v1",
                    "esi-markets.structure_markets.v1"
                ],
                "description": "市场订单和交易管理"
            },
            "战斗记录": {
                "icon": "💀",
                "scopes": [
                    "esi-killmails.read_killmails.v1",
                    "esi-killmails.read_corporation_killmails.v1"
                ],
                "description": "击杀邮件和战斗记录"
            },
            "舰队管理": {
                "icon": "⚓",
                "scopes": [
                    "esi-fleets.read_fleet.v1",
                    "esi-fleets.write_fleet.v1",
                    "esi-fittings.read_fittings.v1",
                    "esi-fittings.write_fittings.v1"
                ],
                "description": "舰队和装配管理"
            },
            "行星管理": {
                "icon": "🌍",
                "scopes": [
                    "esi-planets.manage_planets.v1",
                    "esi-planets.read_customs_offices.v1"
                ],
                "description": "行星工业和海关管理"
            },
            "宇宙数据": {
                "icon": "🌌",
                "scopes": [
                    "esi-search.search_structures.v1",
                    "esi-universe.read_structures.v1"
                ],
                "description": "宇宙结构和搜索功能"
            },
            "用户界面": {
                "icon": "🎮",
                "scopes": [
                    "esi-ui.open_window.v1",
                    "esi-ui.write_waypoint.v1"
                ],
                "description": "游戏内UI控制和导航"
            }
        }
        
        self.load_current_scopes()
    
    def load_current_scopes(self):
        """从.env文件加载当前权限配置"""
        if not self.env_file.exists():
            print(f"❌ 配置文件不存在: {self.env_file}")
            return
        
        with open(self.env_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取EVE_SSO_SCOPES配置
        match = re.search(r'EVE_SSO_SCOPES="([^"]*)"', content, re.DOTALL)
        if not match:
            print("❌ 未找到EVE_SSO_SCOPES配置")
            return
        
        scopes_content = match.group(1)
        
        # 解析权限列表
        for line in scopes_content.split('\n'):
            line = line.strip()
            if line and not line.startswith('#'):
                scope_name = line.strip()
                if scope_name:
                    category = self.get_scope_category(scope_name)
                    description = self.get_scope_description(scope_name)
                    self.scopes[scope_name] = ScopeInfo(
                        name=scope_name,
                        category=category,
                        description=description,
                        enabled=True
                    )
    
    def get_scope_category(self, scope_name: str) -> str:
        """获取权限所属分类"""
        for category, info in self.categories.items():
            if scope_name in info["scopes"]:
                return category
        return "其他"
    
    def get_scope_description(self, scope_name: str) -> str:
        """获取权限描述"""
        descriptions = {
            "publicData": "角色基本信息（ID、姓名、公司等）",
            "esi-location.read_location.v1": "角色当前位置（星系、空间站等）",
            "esi-location.read_online.v1": "角色在线状态",
            "esi-location.read_ship_type.v1": "当前飞船类型和名称",
            "esi-skills.read_skills.v1": "技能信息和等级",
            "esi-skills.read_skillqueue.v1": "技能训练队列",
            "esi-clones.read_clones.v1": "克隆体信息和跳跃克隆",
            "esi-clones.read_implants.v1": "植入体信息",
            "esi-assets.read_assets.v1": "个人资产清单",
            "esi-assets.read_corporation_assets.v1": "公司资产清单",
            "esi-wallet.read_character_wallet.v1": "个人钱包余额和交易记录",
            "esi-wallet.read_corporation_wallets.v1": "公司钱包信息",
            "esi-characters.read_contacts.v1": "角色联系人列表",
            "esi-characters.write_contacts.v1": "编辑角色联系人",
            "esi-mail.read_mail.v1": "读取游戏内邮件",
            "esi-mail.send_mail.v1": "发送游戏内邮件",
            "esi-industry.read_character_jobs.v1": "个人工业作业状态",
            "esi-markets.read_character_orders.v1": "个人市场订单",
            "esi-killmails.read_killmails.v1": "击杀邮件记录"
        }
        return descriptions.get(scope_name, "权限描述")
    
    def show_summary(self):
        """显示权限概览"""
        print("🌀 EVE SSO权限管理工具")
        print("=" * 60)
        
        total_scopes = len(self.scopes)
        enabled_scopes = sum(1 for s in self.scopes.values() if s.enabled)
        
        print(f"📊 权限统计:")
        print(f"   总权限数: {total_scopes}")
        print(f"   已启用: {enabled_scopes}")
        print(f"   已禁用: {total_scopes - enabled_scopes}")
        print(f"   覆盖率: {(enabled_scopes/total_scopes)*100:.1f}%")
        
        print(f"\n📋 分类统计:")
        for category, info in self.categories.items():
            category_scopes = [s for s in info["scopes"] if s in self.scopes]
            enabled_count = sum(1 for s in category_scopes if self.scopes[s].enabled)
            
            print(f"   {info['icon']} {category}: {enabled_count}/{len(category_scopes)}")
    
    def show_category_details(self, category_name: str = None):
        """显示分类详情"""
        if category_name and category_name not in self.categories:
            print(f"❌ 未找到分类: {category_name}")
            return
        
        categories_to_show = [category_name] if category_name else list(self.categories.keys())
        
        for category in categories_to_show:
            info = self.categories[category]
            print(f"\n{info['icon']} {category}")
            print(f"   描述: {info['description']}")
            print(f"   权限列表:")
            
            for scope in info["scopes"]:
                if scope in self.scopes:
                    status = "✅" if self.scopes[scope].enabled else "❌"
                    print(f"     {status} {scope}")
                    print(f"        {self.scopes[scope].description}")
    
    def toggle_category(self, category_name: str, enable: bool = True):
        """启用/禁用整个分类"""
        if category_name not in self.categories:
            print(f"❌ 未找到分类: {category_name}")
            return
        
        info = self.categories[category_name]
        action = "启用" if enable else "禁用"
        
        print(f"🔧 {action}分类: {info['icon']} {category_name}")
        
        for scope in info["scopes"]:
            if scope in self.scopes:
                self.scopes[scope].enabled = enable
                status = "✅" if enable else "❌"
                print(f"   {status} {scope}")
        
        print(f"✅ 分类 {category_name} 已{action}")
    
    def save_changes(self):
        """保存权限变更到.env文件"""
        if not self.env_file.exists():
            print(f"❌ 配置文件不存在: {self.env_file}")
            return False
        
        # 读取原文件
        with open(self.env_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 生成新的权限配置
        new_scopes = []
        for category, info in self.categories.items():
            if any(scope in self.scopes and self.scopes[scope].enabled for scope in info["scopes"]):
                new_scopes.append(f"    # {info['icon']} {category}")
                for scope in info["scopes"]:
                    if scope in self.scopes:
                        if self.scopes[scope].enabled:
                            new_scopes.append(f"    {scope}")
                        else:
                            new_scopes.append(f"    # {scope}")
                new_scopes.append("")
        
        new_scopes_str = '\n'.join(new_scopes)
        new_config = f'EVE_SSO_SCOPES="\n{new_scopes_str}"'
        
        # 替换配置
        new_content = re.sub(
            r'EVE_SSO_SCOPES="[^"]*"',
            new_config,
            content,
            flags=re.DOTALL
        )
        
        # 保存文件
        with open(self.env_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("✅ 权限配置已保存到 .env 文件")
        return True


def main():
    """主函数"""
    manager = ScopeManager()
    
    if len(sys.argv) == 1:
        # 显示概览
        manager.show_summary()
        print("\n🔧 使用方法:")
        print("   python tools/scope_manager.py summary          # 显示概览")
        print("   python tools/scope_manager.py list [category]  # 显示分类详情")
        print("   python tools/scope_manager.py enable category  # 启用分类")
        print("   python tools/scope_manager.py disable category # 禁用分类")
        print("   python tools/scope_manager.py save             # 保存变更")
        
    elif sys.argv[1] == "summary":
        manager.show_summary()
        
    elif sys.argv[1] == "list":
        category = sys.argv[2] if len(sys.argv) > 2 else None
        manager.show_category_details(category)
        
    elif sys.argv[1] == "enable" and len(sys.argv) > 2:
        manager.toggle_category(sys.argv[2], True)
        
    elif sys.argv[1] == "disable" and len(sys.argv) > 2:
        manager.toggle_category(sys.argv[2], False)
        
    elif sys.argv[1] == "save":
        manager.save_changes()
        
    else:
        print("❌ 未知命令")


if __name__ == "__main__":
    main()
