#!/usr/bin/env python3
"""
ESI配置验证脚本
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.infrastructure.config import settings
from src.infrastructure.esi import ESIClient, EVESSOClient, ESI_SCOPES


async def validate_basic_config():
    """验证基础配置"""
    print("=== 验证基础配置 ===")
    
    # 检查必需的配置项
    required_configs = {
        'eve_sso_client_id': settings.eve_sso_client_id,
        'eve_sso_client_secret': settings.eve_sso_client_secret,
        'eve_sso_callback_url': settings.eve_sso_callback_url,
        'esi_base_url': settings.esi_base_url,
        'esi_user_agent': settings.esi_user_agent
    }
    
    missing_configs = []
    for config_name, config_value in required_configs.items():
        if not config_value or config_value == "your-client-id" or config_value == "your-client-secret":
            missing_configs.append(config_name.upper())
        else:
            print(f"✅ {config_name}: 已配置")
    
    if missing_configs:
        print(f"❌ 缺少或未正确配置: {', '.join(missing_configs)}")
        print("\n请在 .env 文件中配置以下变量:")
        for config in missing_configs:
            print(f"  {config}=your_value_here")
        return False
    
    print("✅ 基础配置验证通过")
    return True


async def validate_esi_connection():
    """验证ESI API连接"""
    print("\n=== 验证ESI API连接 ===")
    
    try:
        async with ESIClient() as client:
            # 测试ESI状态端点
            status_data = await client.get("/v1/status/")
            print(f"✅ ESI API连接正常")
            print(f"   服务器状态: {status_data.get('server_version', 'Unknown')}")
            print(f"   在线玩家: {status_data.get('players', 'Unknown')}")
            
            # 测试公开数据端点
            try:
                # 获取Jita星系信息作为测试
                jita_info = await client.get("/v4/universe/systems/30000142/")
                print(f"✅ 数据端点测试成功: {jita_info.get('name', 'Jita')}")
            except Exception as e:
                print(f"⚠️  数据端点测试失败: {e}")
            
            return True
            
    except Exception as e:
        print(f"❌ ESI API连接失败: {e}")
        print("   请检查网络连接和ESI_BASE_URL配置")
        return False


async def validate_sso_config():
    """验证SSO配置"""
    print("\n=== 验证EVE SSO配置 ===")
    
    try:
        sso_client = EVESSOClient()
        
        # 生成测试登录URL
        test_scopes = [
            ESI_SCOPES["character_info"],
            ESI_SCOPES["character_location"],
            ESI_SCOPES["character_skills"]
        ]
        
        login_url, state = sso_client.generate_login_url(test_scopes)
        
        print("✅ EVE SSO配置正常")
        print(f"   Client ID: {settings.eve_sso_client_id[:8]}...")
        print(f"   回调URL: {settings.eve_sso_callback_url}")
        print(f"   测试登录URL已生成 (长度: {len(login_url)} 字符)")
        
        # 验证JWKS端点
        try:
            jwks = await sso_client._get_jwks()
            print(f"✅ JWKS端点正常 (包含 {len(jwks.get('keys', []))} 个密钥)")
        except Exception as e:
            print(f"⚠️  JWKS端点测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ EVE SSO配置失败: {e}")
        return False


def validate_sync_config():
    """验证同步配置"""
    print("\n=== 验证数据同步配置 ===")
    
    sync_configs = {
        "实时同步间隔": settings.sync_interval_realtime,
        "频繁同步间隔": settings.sync_interval_frequent,
        "常规同步间隔": settings.sync_interval_regular,
        "每日同步间隔": settings.sync_interval_daily,
    }
    
    for config_name, config_value in sync_configs.items():
        print(f"✅ {config_name}: {config_value}秒")
    
    # 检查同步功能开关
    sync_features = {
        "角色位置同步": settings.sync_character_location,
        "在线状态同步": settings.sync_character_online,
        "技能同步": settings.sync_character_skills,
        "资产同步": settings.sync_character_assets,
        "钱包同步": settings.sync_character_wallet,
        "市场订单同步": settings.sync_market_orders,
        "工业任务同步": settings.sync_industry_jobs,
        "公司信息同步": settings.sync_corporation_info,
    }
    
    enabled_features = [name for name, enabled in sync_features.items() if enabled]
    disabled_features = [name for name, enabled in sync_features.items() if not enabled]
    
    print(f"\n已启用功能 ({len(enabled_features)}):")
    for feature in enabled_features:
        print(f"  ✅ {feature}")
    
    if disabled_features:
        print(f"\n已禁用功能 ({len(disabled_features)}):")
        for feature in disabled_features:
            print(f"  ❌ {feature}")
    
    return True


def validate_cache_config():
    """验证缓存配置"""
    print("\n=== 验证缓存配置 ===")
    
    cache_configs = {
        "默认缓存": settings.cache_ttl_default,
        "角色缓存": settings.cache_ttl_character,
        "市场缓存": settings.cache_ttl_market,
        "资产缓存": settings.cache_ttl_assets,
        "技能缓存": settings.cache_ttl_skills,
        "公司缓存": settings.cache_ttl_corporation,
        "工业缓存": settings.cache_ttl_industry,
    }
    
    for config_name, ttl in cache_configs.items():
        hours = ttl / 3600
        if hours >= 1:
            print(f"✅ {config_name}: {ttl}秒 ({hours:.1f}小时)")
        else:
            minutes = ttl / 60
            print(f"✅ {config_name}: {ttl}秒 ({minutes:.1f}分钟)")
    
    return True


def validate_rate_limit_config():
    """验证限流配置"""
    print("\n=== 验证API限流配置 ===")
    
    print(f"✅ 每秒请求限制: {settings.rate_limit_requests_per_second}")
    print(f"✅ 突发请求限制: {settings.rate_limit_burst}")
    print(f"✅ API超时时间: {settings.esi_timeout}秒")
    print(f"✅ 最大重试次数: {settings.esi_max_retries}")
    print(f"✅ 重试延迟: {settings.esi_retry_delay}秒")
    print(f"✅ 退避因子: {settings.esi_backoff_factor}")
    
    # 检查配置合理性
    warnings = []
    
    if settings.rate_limit_requests_per_second > 20:
        warnings.append("每秒请求数过高，可能触发ESI限流")
    
    if settings.esi_timeout < 10:
        warnings.append("API超时时间过短，可能导致请求失败")
    
    if settings.esi_max_retries > 5:
        warnings.append("重试次数过多，可能影响性能")
    
    if warnings:
        print("\n⚠️  配置建议:")
        for warning in warnings:
            print(f"   - {warning}")
    
    return True


async def main():
    """主函数"""
    print("EVE Online Assistant - ESI配置验证")
    print("=" * 50)
    
    validation_results = []
    
    try:
        # 基础配置验证
        result = await validate_basic_config()
        validation_results.append(("基础配置", result))
        
        if not result:
            print("\n❌ 基础配置验证失败，请先配置必需的环境变量")
            return False
        
        # ESI连接验证
        result = await validate_esi_connection()
        validation_results.append(("ESI连接", result))
        
        # SSO配置验证
        result = await validate_sso_config()
        validation_results.append(("SSO配置", result))
        
        # 同步配置验证
        result = validate_sync_config()
        validation_results.append(("同步配置", result))
        
        # 缓存配置验证
        result = validate_cache_config()
        validation_results.append(("缓存配置", result))
        
        # 限流配置验证
        result = validate_rate_limit_config()
        validation_results.append(("限流配置", result))
        
        # 总结
        print("\n" + "=" * 50)
        print("验证结果总结:")
        
        all_passed = True
        for config_name, passed in validation_results:
            status = "✅ 通过" if passed else "❌ 失败"
            print(f"  {config_name}: {status}")
            if not passed:
                all_passed = False
        
        if all_passed:
            print("\n🎉 所有配置验证通过！EVE Online Assistant已准备就绪。")
            print("\n下一步:")
            print("1. 启动应用: python -m uvicorn src.presentation.api.main:app --reload")
            print("2. 访问: http://localhost:8000/docs")
            print("3. 进行EVE SSO认证测试")
        else:
            print("\n❌ 部分配置验证失败，请检查上述错误信息并修复配置。")
        
        return all_passed
        
    except Exception as e:
        print(f"\n💥 验证过程中发生未预期错误: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
