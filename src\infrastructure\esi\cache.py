"""
ESI API缓存管理
"""
import json
import hashlib
from datetime import datetime, timedelta
from typing import Any, Dict, Optional, Union, List
from dataclasses import dataclass, asdict

from ..config import settings
from ..config.logging import get_logger

logger = get_logger(__name__)


@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    data: Any
    expires_at: datetime
    etag: Optional[str] = None
    last_modified: Optional[str] = None
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.utcnow()
    
    @property
    def is_expired(self) -> bool:
        """检查是否过期"""
        return datetime.utcnow() > self.expires_at
    
    @property
    def ttl(self) -> int:
        """剩余生存时间（秒）"""
        if self.is_expired:
            return 0
        return int((self.expires_at - datetime.utcnow()).total_seconds())
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'key': self.key,
            'data': self.data,
            'expires_at': self.expires_at.isoformat(),
            'etag': self.etag,
            'last_modified': self.last_modified,
            'created_at': self.created_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CacheEntry':
        """从字典创建"""
        return cls(
            key=data['key'],
            data=data['data'],
            expires_at=datetime.fromisoformat(data['expires_at']),
            etag=data.get('etag'),
            last_modified=data.get('last_modified'),
            created_at=datetime.fromisoformat(data['created_at'])
        )


class ESICache:
    """ESI API缓存"""
    
    def __init__(self, max_size: int = 10000):
        self.max_size = max_size
        self._cache: Dict[str, CacheEntry] = {}
        self._access_order: List[str] = []  # LRU跟踪
    
    def _generate_key(self, endpoint: str, params: Optional[Dict[str, Any]] = None,
                     character_id: Optional[int] = None) -> str:
        """生成缓存键"""
        key_parts = [endpoint]
        
        if character_id:
            key_parts.append(f"char_{character_id}")
        
        if params:
            # 对参数进行排序以确保一致性
            sorted_params = sorted(params.items())
            params_str = "&".join(f"{k}={v}" for k, v in sorted_params)
            key_parts.append(params_str)
        
        key_string = "|".join(key_parts)
        return hashlib.md5(key_string.encode()).hexdigest()
    
    def get(self, endpoint: str, params: Optional[Dict[str, Any]] = None,
            character_id: Optional[int] = None) -> Optional[CacheEntry]:
        """获取缓存条目"""
        key = self._generate_key(endpoint, params, character_id)
        
        if key not in self._cache:
            return None
        
        entry = self._cache[key]
        
        # 检查是否过期
        if entry.is_expired:
            self._remove(key)
            return None
        
        # 更新访问顺序
        self._update_access_order(key)
        
        logger.debug(f"缓存命中: {endpoint}", key=key, ttl=entry.ttl)
        return entry
    
    def set(self, endpoint: str, data: Any, expires_in: int,
            params: Optional[Dict[str, Any]] = None,
            character_id: Optional[int] = None,
            etag: Optional[str] = None,
            last_modified: Optional[str] = None) -> None:
        """设置缓存条目"""
        key = self._generate_key(endpoint, params, character_id)
        expires_at = datetime.utcnow() + timedelta(seconds=expires_in)
        
        entry = CacheEntry(
            key=key,
            data=data,
            expires_at=expires_at,
            etag=etag,
            last_modified=last_modified
        )
        
        self._cache[key] = entry
        self._update_access_order(key)
        
        # 检查缓存大小限制
        self._enforce_size_limit()
        
        logger.debug(f"缓存设置: {endpoint}", key=key, expires_in=expires_in)
    
    def delete(self, endpoint: str, params: Optional[Dict[str, Any]] = None,
               character_id: Optional[int] = None) -> bool:
        """删除缓存条目"""
        key = self._generate_key(endpoint, params, character_id)
        return self._remove(key)
    
    def clear(self) -> None:
        """清空缓存"""
        self._cache.clear()
        self._access_order.clear()
        logger.info("缓存已清空")
    
    def cleanup_expired(self) -> int:
        """清理过期条目"""
        expired_keys = []
        
        for key, entry in self._cache.items():
            if entry.is_expired:
                expired_keys.append(key)
        
        for key in expired_keys:
            self._remove(key)
        
        logger.debug(f"清理了 {len(expired_keys)} 个过期缓存条目")
        return len(expired_keys)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total_entries = len(self._cache)
        expired_entries = sum(1 for entry in self._cache.values() if entry.is_expired)
        
        return {
            'total_entries': total_entries,
            'expired_entries': expired_entries,
            'active_entries': total_entries - expired_entries,
            'max_size': self.max_size,
            'usage_percentage': (total_entries / self.max_size) * 100 if self.max_size > 0 else 0
        }
    
    def _update_access_order(self, key: str) -> None:
        """更新访问顺序"""
        if key in self._access_order:
            self._access_order.remove(key)
        self._access_order.append(key)
    
    def _remove(self, key: str) -> bool:
        """移除缓存条目"""
        if key in self._cache:
            del self._cache[key]
            if key in self._access_order:
                self._access_order.remove(key)
            return True
        return False
    
    def _enforce_size_limit(self) -> None:
        """强制执行大小限制"""
        while len(self._cache) > self.max_size:
            # 移除最久未访问的条目
            if self._access_order:
                oldest_key = self._access_order[0]
                self._remove(oldest_key)
                logger.debug(f"缓存已满，移除最久未访问的条目: {oldest_key}")


class CacheManager:
    """缓存管理器"""
    
    def __init__(self):
        self.character_cache = ESICache(max_size=settings.esi_cache_character_max_size)
        self.universe_cache = ESICache(max_size=settings.esi_cache_universe_max_size)
        self.market_cache = ESICache(max_size=settings.esi_cache_market_max_size)
        self.public_cache = ESICache(max_size=settings.esi_cache_public_max_size)
        
        # 缓存TTL配置
        self.cache_ttl = {
            # 角色相关数据
            'character_info': 3600,  # 1小时
            'character_skills': 1800,  # 30分钟
            'character_assets': 300,  # 5分钟
            'character_wallet': 300,  # 5分钟
            'character_orders': 300,  # 5分钟
            'character_industry': 300,  # 5分钟
            'character_location': 60,  # 1分钟
            'skill_queue': 300,  # 5分钟
            
            # 公司/联盟信息
            'corporation_info': 3600,  # 1小时
            'alliance_info': 3600,  # 1小时
            
            # 宇宙数据（相对静态）
            'universe_types': 86400,  # 24小时
            'universe_groups': 86400,  # 24小时
            'universe_categories': 86400,  # 24小时
            'solar_systems': 86400,  # 24小时
            'regions': 86400,  # 24小时
            'stations': 86400,  # 24小时
            
            # 市场数据
            'market_orders': 300,  # 5分钟
            'market_history': 3600,  # 1小时
            'market_prices': 3600,  # 1小时
            
            # 默认TTL
            'default': 300  # 5分钟
        }
    
    def get_cache_for_endpoint(self, endpoint: str) -> ESICache:
        """根据端点获取对应的缓存"""
        if any(x in endpoint for x in ['character', 'skills', 'assets', 'wallet', 'orders', 'industry', 'location']):
            return self.character_cache
        elif any(x in endpoint for x in ['universe', 'types', 'groups', 'categories', 'systems', 'regions']):
            return self.universe_cache
        elif any(x in endpoint for x in ['markets', 'prices']):
            return self.market_cache
        else:
            return self.public_cache
    
    def get_ttl_for_endpoint(self, endpoint: str) -> int:
        """根据端点获取TTL"""
        for key, ttl in self.cache_ttl.items():
            if key in endpoint:
                return ttl
        return self.cache_ttl['default']
    
    def get(self, endpoint: str, params: Optional[Dict[str, Any]] = None,
            character_id: Optional[int] = None) -> Optional[CacheEntry]:
        """获取缓存"""
        cache = self.get_cache_for_endpoint(endpoint)
        return cache.get(endpoint, params, character_id)
    
    def set(self, endpoint: str, data: Any, params: Optional[Dict[str, Any]] = None,
            character_id: Optional[int] = None, etag: Optional[str] = None,
            last_modified: Optional[str] = None, custom_ttl: Optional[int] = None) -> None:
        """设置缓存"""
        cache = self.get_cache_for_endpoint(endpoint)
        ttl = custom_ttl or self.get_ttl_for_endpoint(endpoint)
        
        cache.set(endpoint, data, ttl, params, character_id, etag, last_modified)
    
    def delete(self, endpoint: str, params: Optional[Dict[str, Any]] = None,
               character_id: Optional[int] = None) -> bool:
        """删除缓存"""
        cache = self.get_cache_for_endpoint(endpoint)
        return cache.delete(endpoint, params, character_id)
    
    def clear_character_cache(self, character_id: int) -> None:
        """清除指定角色的缓存"""
        # 这里需要实现更精确的清除逻辑
        # 暂时清除整个角色缓存
        logger.info(f"清除角色 {character_id} 的缓存")
        # TODO: 实现按角色ID清除的逻辑
    
    def cleanup_all_expired(self) -> Dict[str, int]:
        """清理所有过期缓存"""
        results = {
            'character': self.character_cache.cleanup_expired(),
            'universe': self.universe_cache.cleanup_expired(),
            'market': self.market_cache.cleanup_expired(),
            'public': self.public_cache.cleanup_expired()
        }
        
        total_cleaned = sum(results.values())
        logger.info(f"清理了总计 {total_cleaned} 个过期缓存条目", details=results)
        
        return results
    
    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取所有缓存统计"""
        return {
            'character': self.character_cache.get_stats(),
            'universe': self.universe_cache.get_stats(),
            'market': self.market_cache.get_stats(),
            'public': self.public_cache.get_stats()
        }


# 全局缓存管理器实例
cache_manager = CacheManager()
