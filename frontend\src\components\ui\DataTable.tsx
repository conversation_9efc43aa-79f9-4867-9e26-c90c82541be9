import { useState } from 'react'
import { Table, Input, Button, Space, Tooltip, Tag } from 'antd'
import {
  SearchOutlined,
  ReloadOutlined,
  DownloadOutlined,
  FilterOutlined,
  SettingOutlined
} from '@ant-design/icons'
import { motion } from 'framer-motion'
import clsx from 'clsx'

import { TableProps, TableColumn } from '@/types'

interface DataTableProps<T = any> extends TableProps<T> {
  title?: string
  searchable?: boolean
  searchPlaceholder?: string
  refreshable?: boolean
  exportable?: boolean
  onRefresh?: () => void
  onExport?: () => void
  onSearch?: (value: string) => void
  toolbar?: React.ReactNode
  className?: string
}

export function DataTable<T = any>({
  title,
  data,
  columns,
  loading = false,
  pagination,
  searchable = true,
  searchPlaceholder = '搜索...',
  refreshable = true,
  exportable = false,
  onRefresh,
  onExport,
  onSearch,
  toolbar,
  className = '',
  ...tableProps
}: DataTableProps<T>) {
  const [searchValue, setSearchValue] = useState('')

  const handleSearch = (value: string) => {
    setSearchValue(value)
    onSearch?.(value)
  }

  const formatCellValue = (value: any, column: TableColumn<T>) => {
    if (value === null || value === undefined) {
      return <span className="text-gray-400">-</span>
    }

    // 根据列的数据类型格式化值
    if (column.dataIndex && typeof value === 'number') {
      // 检查是否是货币字段
      if (column.dataIndex.toString().includes('price') ||
          column.dataIndex.toString().includes('value') ||
          column.dataIndex.toString().includes('cost')) {
        return formatCurrency(value)
      }

      // 检查是否是数量字段
      if (column.dataIndex.toString().includes('quantity') ||
          column.dataIndex.toString().includes('amount') ||
          column.dataIndex.toString().includes('count')) {
        return formatNumber(value)
      }
    }

    // 检查是否是布尔值
    if (typeof value === 'boolean') {
      return (
        <Tag color={value ? 'green' : 'default'}>
          {value ? '是' : '否'}
        </Tag>
      )
    }

    // 检查是否是日期
    if (value instanceof Date || (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}/.test(value))) {
      return formatDate(value)
    }

    return value
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('zh-CN').format(num)
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'decimal',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount) + ' ISK'
  }

  const formatDate = (date: Date | string) => {
    const d = new Date(date)
    return d.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  // 增强列配置
  const enhancedColumns = columns.map(column => ({
    ...column,
    render: column.render || ((value: any, record: T, index: number) =>
      formatCellValue(value, column)
    ),
    sorter: column.sorter === true ?
      (a: T, b: T) => {
        const aVal = column.dataIndex ? (a as any)[column.dataIndex] : ''
        const bVal = column.dataIndex ? (b as any)[column.dataIndex] : ''

        if (typeof aVal === 'number' && typeof bVal === 'number') {
          return aVal - bVal
        }

        return String(aVal).localeCompare(String(bVal))
      } : column.sorter,
  }))

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={clsx('data-table', className)}
    >
      {/* 表格头部 */}
      {(title || searchable || refreshable || exportable || toolbar) && (
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
          <div>
            {title && (
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {title}
              </h3>
            )}
          </div>

          <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2">
            {/* 搜索框 */}
            {searchable && (
              <Input
                placeholder={searchPlaceholder}
                prefix={<SearchOutlined className="text-gray-400" />}
                value={searchValue}
                onChange={(e) => handleSearch(e.target.value)}
                className="w-full sm:w-64"
                allowClear
              />
            )}

            {/* 工具栏 */}
            <Space>
              {toolbar}

              {refreshable && (
                <Tooltip title="刷新数据">
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={onRefresh}
                    loading={loading}
                  />
                </Tooltip>
              )}

              {exportable && (
                <Tooltip title="导出数据">
                  <Button
                    icon={<DownloadOutlined />}
                    onClick={onExport}
                  />
                </Tooltip>
              )}

              <Tooltip title="表格设置">
                <Button icon={<SettingOutlined />} />
              </Tooltip>
            </Space>
          </div>
        </div>
      )}

      {/* 数据表格 */}
      <Table
        {...tableProps}
        columns={enhancedColumns}
        dataSource={data}
        loading={loading}
        pagination={pagination ? {
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `显示 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          ...pagination,
        } : false}
        scroll={{ x: 'max-content' }}
        className="bg-white dark:bg-dark-800 rounded-lg"
      />
    </motion.div>
  )
}

// 预设的表格组件
export function AssetTable({
  assets,
  loading,
  onRefresh
}: {
  assets: any[]
  loading?: boolean
  onRefresh?: () => void
}) {
  const columns: TableColumn[] = [
    {
      title: '物品',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: any) => (
        <div className="flex items-center space-x-2">
          <img
            src={record.iconUrl}
            alt={name}
            className="w-8 h-8 rounded"
            onError={(e) => {
              e.currentTarget.style.display = 'none'
            }}
          />
          <div>
            <div className="font-medium">{name}</div>
            <div className="text-xs text-gray-500">{record.group}</div>
          </div>
        </div>
      ),
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      sorter: true,
      align: 'right',
    },
    {
      title: '单价',
      dataIndex: 'price',
      key: 'price',
      sorter: true,
      align: 'right',
    },
    {
      title: '总价值',
      dataIndex: 'totalValue',
      key: 'totalValue',
      sorter: true,
      align: 'right',
    },
    {
      title: '位置',
      dataIndex: 'location',
      key: 'location',
      ellipsis: true,
    },
  ]

  return (
    <DataTable
      title="资产列表"
      data={assets}
      columns={columns}
      loading={loading}
      onRefresh={onRefresh}
      searchable
      refreshable
      exportable
      searchPlaceholder="搜索物品名称..."
    />
  )
}

export function MarketOrderTable({
  orders,
  loading,
  onRefresh
}: {
  orders: any[]
  loading?: boolean
  onRefresh?: () => void
}) {
  const columns: TableColumn[] = [
    {
      title: '类型',
      dataIndex: 'isBuyOrder',
      key: 'type',
      render: (isBuyOrder: boolean) => (
        <Tag color={isBuyOrder ? 'green' : 'red'}>
          {isBuyOrder ? '买单' : '卖单'}
        </Tag>
      ),
      filters: [
        { text: '买单', value: true },
        { text: '卖单', value: false },
      ],
    },
    {
      title: '物品',
      dataIndex: 'typeName',
      key: 'typeName',
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      sorter: true,
      align: 'right',
    },
    {
      title: '剩余数量',
      dataIndex: 'volumeRemain',
      key: 'volumeRemain',
      sorter: true,
      align: 'right',
    },
    {
      title: '总数量',
      dataIndex: 'volumeTotal',
      key: 'volumeTotal',
      sorter: true,
      align: 'right',
    },
    {
      title: '位置',
      dataIndex: 'locationName',
      key: 'locationName',
      ellipsis: true,
    },
    {
      title: '发布时间',
      dataIndex: 'issued',
      key: 'issued',
      sorter: true,
    },
  ]

  return (
    <DataTable
      title="市场订单"
      data={orders}
      columns={columns}
      loading={loading}
      onRefresh={onRefresh}
      searchable
      refreshable
      exportable
      searchPlaceholder="搜索物品名称..."
    />
  )
}
