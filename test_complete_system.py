#!/usr/bin/env python3
"""
测试完整系统
"""
import subprocess
import time
import urllib.request
import os
from pathlib import Path

def test_backend():
    """测试后端"""
    print("🔧 测试后端服务器...")
    try:
        response = urllib.request.urlopen('http://localhost:8000/health', timeout=5)
        if response.getcode() == 200:
            print("   ✅ 后端服务器正常")
            return True
        else:
            print(f"   ❌ 后端服务器异常: {response.getcode()}")
            return False
    except Exception as e:
        print(f"   ❌ 后端服务器不可用: {e}")
        return False

def test_ngrok():
    """测试ngrok"""
    print("\n🌐 测试ngrok隧道...")
    try:
        response = urllib.request.urlopen('https://diverse-monitor-model.ngrok-free.app/health', timeout=10)
        if response.getcode() == 200:
            print("   ✅ ngrok隧道正常")
            return True
        else:
            print(f"   ❌ ngrok隧道异常: {response.getcode()}")
            return False
    except Exception as e:
        print(f"   ❌ ngrok隧道不可用: {e}")
        return False

def test_eve_login_api():
    """测试EVE登录API"""
    print("\n🎮 测试EVE登录API...")
    try:
        import json
        
        # 测试数据
        data = json.dumps({"scopes": ["esi-characters.read_characters.v1"]}).encode('utf-8')
        
        # 创建请求
        req = urllib.request.Request(
            'http://localhost:8000/auth/login',
            data=data,
            headers={'Content-Type': 'application/json'}
        )
        
        response = urllib.request.urlopen(req, timeout=10)
        
        if response.getcode() == 200:
            result = json.loads(response.read().decode())
            if result.get('success') and 'login_url' in result.get('data', {}):
                print("   ✅ EVE登录API正常")
                login_url = result['data']['login_url']
                if 'diverse-monitor-model.ngrok-free.app' in login_url:
                    print("   ✅ 使用固定域名回调")
                else:
                    print("   ⚠️  回调域名可能不正确")
                return True
            else:
                print(f"   ❌ EVE登录API响应格式错误: {result}")
                return False
        else:
            print(f"   ❌ EVE登录API异常: {response.getcode()}")
            return False
            
    except Exception as e:
        print(f"   ❌ EVE登录API测试失败: {e}")
        return False

def start_frontend_manual():
    """手动启动前端"""
    print("\n🎨 手动启动前端服务器...")
    
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("   ❌ frontend目录不存在")
        return False
    
    try:
        # 使用完整路径启动npm
        npm_path = r"C:\Program Files\nodejs\npm.cmd"
        
        print(f"   🔧 使用npm路径: {npm_path}")
        print(f"   📁 工作目录: {frontend_dir.absolute()}")
        
        # 启动前端
        process = subprocess.Popen(
            [npm_path, "run", "dev"],
            cwd=frontend_dir,
            shell=False,  # 不使用shell
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1
        )
        
        print("   ⏳ 等待前端启动...")
        
        # 等待并检查输出
        timeout = 30
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            # 检查进程状态
            if process.poll() is not None:
                # 进程已退出
                output = process.stdout.read()
                print(f"   ❌ 前端进程已退出")
                print(f"   📋 输出: {output}")
                return False
            
            # 尝试连接
            try:
                response = urllib.request.urlopen('http://localhost:3000', timeout=2)
                if response.getcode() == 200:
                    print("   ✅ 前端服务器启动成功！")
                    print("   📝 访问地址: http://localhost:3000")
                    return process
            except:
                pass
            
            time.sleep(1)
        
        print("   ❌ 前端启动超时")
        process.terminate()
        return False
        
    except Exception as e:
        print(f"   ❌ 启动前端失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 完整系统测试")
    print("=" * 60)
    
    # 测试后端
    backend_ok = test_backend()
    
    # 测试ngrok
    ngrok_ok = test_ngrok()
    
    # 测试EVE登录API
    eve_api_ok = test_eve_login_api()
    
    print("\n" + "=" * 60)
    print("📊 测试结果:")
    print(f"   后端服务器: {'✅' if backend_ok else '❌'}")
    print(f"   ngrok隧道: {'✅' if ngrok_ok else '❌'}")
    print(f"   EVE登录API: {'✅' if eve_api_ok else '❌'}")
    
    if backend_ok and ngrok_ok and eve_api_ok:
        print("\n🎉 核心功能全部正常！")
        print("\n🎯 你现在可以:")
        print("   1. 更新EVE开发者门户回调URL:")
        print("      https://diverse-monitor-model.ngrok-free.app/auth/callback")
        print("   2. 使用API文档测试EVE登录:")
        print("      https://diverse-monitor-model.ngrok-free.app/docs")
        print("   3. 直接调用登录API进行测试")
        
        # 尝试启动前端
        print("\n🎨 尝试启动前端界面...")
        frontend_process = start_frontend_manual()
        
        if frontend_process:
            print("\n🎉 前端也启动成功！完整系统可用")
            return 0
        else:
            print("\n⚠️  前端启动失败，但核心功能可用")
            print("💡 可以通过API文档进行所有操作")
            return 0
    else:
        print("\n❌ 核心功能有问题，需要修复")
        return 1

if __name__ == "__main__":
    sys.exit(main())
