# 测试指南

## 概述

本项目建立了完整的测试体系，包括单元测试、集成测试和端到端测试，确保代码质量和功能稳定性。

## 测试架构

### 测试金字塔

```
    /\
   /  \     E2E Tests (10%)
  /____\    Integration Tests (20%)
 /______\   Unit Tests (70%)
```

### 测试类型

- **单元测试 (Unit Tests)**: 测试单个函数、类或模块
- **集成测试 (Integration Tests)**: 测试模块间的交互
- **端到端测试 (E2E Tests)**: 测试完整的用户流程
- **模块导入测试**: 验证所有模块能正确导入

## 快速开始

### 安装测试依赖

```bash
# 安装开发依赖（包含测试工具）
pip install -e .[dev]
```

### 运行测试

```bash
# 运行所有测试
pytest

# 运行特定类型的测试
pytest -m unit          # 单元测试
pytest -m integration   # 集成测试
pytest -m e2e           # 端到端测试

# 运行特定文件
pytest tests/unit/test_auth.py
pytest tests/test_module_imports.py

# 生成覆盖率报告
pytest --cov=src --cov-report=html
```

### 使用测试脚本

```bash
# 使用自定义测试脚本
python scripts/run_tests.py                    # 运行所有测试
python scripts/run_tests.py --unit            # 只运行单元测试
python scripts/run_tests.py --integration     # 只运行集成测试
python scripts/run_tests.py --imports         # 只运行导入测试
python scripts/run_tests.py --coverage        # 运行覆盖率测试
python scripts/run_tests.py --report          # 生成测试报告

# 质量监控
python scripts/test_quality_monitor.py        # 完整质量报告
python scripts/test_quality_monitor.py --imports-only  # 只检查导入
```

## 测试结构

```
tests/
├── conftest.py                 # pytest配置和fixtures
├── unit/                       # 单元测试
│   ├── test_auth.py           # 认证服务测试
│   └── test_models.py         # 数据模型测试
├── integration/                # 集成测试
│   ├── test_api_auth.py       # 认证API测试
│   └── test_database.py       # 数据库集成测试
├── e2e/                        # 端到端测试
│   └── test_login_flow.py     # 登录流程测试
└── test_module_imports.py     # 模块导入测试
```

## 编写测试

### 单元测试示例

```python
import pytest
from src.application.services.auth import AuthenticationService

@pytest.mark.unit
class TestAuthenticationService:
    
    @pytest.fixture
    def auth_service(self):
        return AuthenticationService()
    
    def test_validate_eve_scopes_valid(self, auth_service):
        scopes = ["esi-characters.read_characters.v1"]
        result = auth_service.validate_eve_scopes(scopes)
        assert result is True
    
    def test_validate_eve_scopes_invalid(self, auth_service):
        scopes = ["invalid-scope"]
        result = auth_service.validate_eve_scopes(scopes)
        assert result is False
```

### 集成测试示例

```python
import pytest
from httpx import AsyncClient

@pytest.mark.integration
@pytest.mark.asyncio
class TestAuthAPI:
    
    async def test_eve_login_endpoint(self, client: AsyncClient):
        response = await client.post(
            "/auth/login",
            json={"scopes": ["esi-characters.read_characters.v1"]}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "login_url" in data["data"]
```

### 测试标记

使用pytest标记来分类测试：

```python
@pytest.mark.unit          # 单元测试
@pytest.mark.integration   # 集成测试
@pytest.mark.e2e          # 端到端测试
@pytest.mark.slow         # 慢速测试
@pytest.mark.external     # 需要外部服务的测试
```

## 测试配置

### pytest配置 (pyproject.toml)

```toml
[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=src",
    "--cov-report=term-missing",
    "--cov-report=html",
]
markers = [
    "unit: Unit tests",
    "integration: Integration tests", 
    "e2e: End-to-end tests",
    "slow: Slow running tests",
]
```

### 测试环境变量

测试时会自动设置以下环境变量：

- `TESTING=true`
- `DATABASE_URL=sqlite+aiosqlite:///:memory:`
- `EVE_CLIENT_ID=test_client_id`
- `EVE_CLIENT_SECRET=test_client_secret`

## 持续集成

### GitHub Actions

项目配置了以下CI/CD工作流：

- **test.yml**: 主要测试流水线
  - 多Python版本测试 (3.11, 3.12)
  - 代码质量检查 (flake8, black, isort, mypy)
  - 单元测试和集成测试
  - 覆盖率报告

- **quality.yml**: 代码质量检查
  - 综合测试报告
  - 代码复杂度分析
  - 安全扫描
  - 依赖审计

- **scheduled-review.yml**: 定期审查
  - 每周全面审查
  - 每日快速检查
  - 自动创建审查issue

### Pre-commit钩子

安装pre-commit钩子：

```bash
pip install pre-commit
pre-commit install
```

每次提交时会自动运行：
- 代码格式化 (black, isort)
- 代码质量检查 (flake8, mypy)
- 安全检查 (bandit)
- 模块导入验证
- __init__.py一致性检查

## 测试最佳实践

### 1. 测试命名

- 测试文件: `test_*.py` 或 `*_test.py`
- 测试类: `Test*`
- 测试方法: `test_*`
- 描述性命名: `test_should_return_error_when_invalid_scope`

### 2. 测试结构 (AAA模式)

```python
def test_validate_eve_scopes_invalid():
    # Arrange (准备)
    auth_service = AuthenticationService()
    invalid_scopes = ["invalid-scope"]
    
    # Act (执行)
    result = auth_service.validate_eve_scopes(invalid_scopes)
    
    # Assert (断言)
    assert result is False
```

### 3. 测试隔离

- 每个测试独立运行
- 使用fixtures提供测试数据
- 避免测试间的依赖
- 使用事务回滚清理数据

### 4. Mock使用

- 只mock外部依赖
- 避免过度mock
- 验证mock的调用
- 使用真实数据进行集成测试

## 覆盖率要求

- **总体覆盖率**: ≥80%
- **关键模块**: ≥90%
- **新增代码**: ≥95%

查看覆盖率报告：

```bash
# 生成HTML报告
pytest --cov=src --cov-report=html
open htmlcov/index.html

# 终端报告
pytest --cov=src --cov-report=term-missing
```

## 故障排除

### 常见问题

1. **模块导入失败**
   ```bash
   # 确保项目包已安装
   pip install -e .
   
   # 检查模块导入
   python scripts/test_quality_monitor.py --imports-only
   ```

2. **测试数据库问题**
   ```bash
   # 清理测试缓存
   pytest --cache-clear
   
   # 重新创建测试数据库
   rm -rf .pytest_cache
   ```

3. **覆盖率数据不准确**
   ```bash
   # 清理覆盖率数据
   coverage erase
   pytest --cov=src
   ```

### 调试测试

```bash
# 详细输出
pytest -v -s

# 只运行失败的测试
pytest --lf

# 进入调试器
pytest --pdb

# 显示最慢的测试
pytest --durations=10
```

## 性能测试

对于性能敏感的代码，添加性能测试：

```python
import time
import pytest

@pytest.mark.slow
def test_performance_critical_function():
    start_time = time.time()
    
    # 执行被测试的函数
    result = some_performance_critical_function()
    
    end_time = time.time()
    execution_time = end_time - start_time
    
    # 断言执行时间在可接受范围内
    assert execution_time < 1.0  # 1秒内完成
    assert result is not None
```

## 测试报告

### 生成报告

```bash
# JSON报告
pytest --json-report --json-report-file=test-report.json

# JUnit XML报告 (用于CI)
pytest --junitxml=test-results.xml

# HTML报告
pytest --html=test-report.html --self-contained-html
```

### 质量监控

使用质量监控脚本定期检查：

```bash
# 生成完整质量报告
python scripts/test_quality_monitor.py

# 查看历史趋势
ls reports/quality_report_*.json
```

---

更多信息请参考：
- [pytest文档](https://docs.pytest.org/)
- [测试体系框架知识库](knowledge-base/testing-framework.md)
- [模块导入问题排查](knowledge-base/module-import-troubleshooting.md)
