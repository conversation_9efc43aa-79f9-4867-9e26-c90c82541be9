# EVE Online ESI API 知识库

## 概述

EVE Swagger Interface (ESI) 是EVE Online官方提供的RESTful API，用于第三方开发。它允许开发者查询游戏数据，包括角色、公司、联盟等信息。

## 基本信息

- **API基础URL**: `https://esi.evetech.net`
- **文档地址**: https://developers.eveonline.com/docs/
- **API Explorer**: https://esi.evetech.net/ui/
- **Swagger规范**: https://esi.evetech.net/_latest/swagger.json
- **数据格式**: JSON
- **认证方式**: OAuth 2.0 (EVE SSO)

## API特性

### 版本控制
- 使用 `X-Compatibility-Date` 头部或 `compatibility_date` 查询参数
- 格式: `YYYY-MM-DD`
- API变更时间: 每日11:00 UTC

### 缓存机制
- 支持ETag缓存
- 不同端点有不同的缓存时间
- 使用 `If-None-Match` 头部进行条件请求

### 限流
- 错误限制状态码: 420
- 需要合理控制请求频率

## 主要API分类

### 1. 角色 (Character)
- **公开信息**: `/v5/characters/{character_id}/`
- **角色属性**: `/v1/characters/{character_id}/attributes/`
- **技能信息**: 需要 `esi-skills.read_skills.v1` 权限
- **钱包日志**: `/v5/characters/{character_id}/wallet/journal/`
- **资产信息**: 需要 `esi-assets.read_assets.v1` 权限
- **合同信息**: 需要 `esi-contracts.read_character_contracts.v1` 权限
- **工业任务**: 需要 `esi-industry.read_character_jobs.v1` 权限
- **邮件系统**: 需要 `esi-mail.read_mail.v1` 权限
- **日历事件**: 需要 `esi-calendar.read_calendar_events.v1` 权限

### 2. 公司 (Corporation)
- **公司信息**: `/v5/corporations/{corporation_id}/`
- **成员列表**: 需要 `esi-corporations.read_corporation_membership.v1` 权限
- **公司结构**: 需要 `esi-corporations.read_structures.v1` 权限
- **公司资产**: 需要 `esi-assets.read_corporation_assets.v1` 权限
- **公司合同**: 需要 `esi-contracts.read_corporation_contracts.v1` 权限

### 3. 联盟 (Alliance)
- **联盟列表**: `/v1/alliances/`
- **联盟信息**: `/v4/alliances/{alliance_id}/`
- **联盟公司**: `/v1/alliances/{alliance_id}/corporations/`
- **联盟图标**: `/v1/alliances/{alliance_id}/icons/`

### 4. 市场 (Market)
- **市场订单**: 需要相应权限
- **市场历史**: 公开数据
- **结构市场**: 需要 `esi-markets.structure_markets.v1` 权限

### 5. 宇宙 (Universe)
- **星系信息**: `/v4/universe/systems/{system_id}/`
- **物品信息**: 公开数据
- **结构信息**: 部分需要权限

### 6. 工业 (Industry)
- **工业任务**: 需要相应权限
- **蓝图信息**: 需要相应权限

## 认证与权限

### SSO权限范围 (Scopes)
常用权限包括：
- `esi-skills.read_skills.v1`: 读取技能信息
- `esi-wallet.read_character_wallet.v1`: 读取钱包信息
- `esi-assets.read_assets.v1`: 读取资产信息
- `esi-contracts.read_character_contracts.v1`: 读取合同信息
- `esi-industry.read_character_jobs.v1`: 读取工业任务
- `esi-mail.read_mail.v1`: 读取邮件
- `esi-location.read_location.v1`: 读取位置信息
- `esi-markets.read_character_orders.v1`: 读取市场订单

### 认证流程
1. 创建SSO应用
2. 获取授权码
3. 交换访问令牌
4. 使用令牌访问API
5. 刷新令牌

## 数据模型示例

### 角色信息
```json
{
  "alliance_id": 434243723,
  "birthday": "2015-03-24T11:37:00Z",
  "bloodline_id": 3,
  "corporation_id": 109299958,
  "description": "",
  "gender": "male",
  "name": "角色名称",
  "race_id": 2,
  "security_status": -1.5
}
```

### 钱包日志
```json
{
  "amount": -100000,
  "balance": 500000.43,
  "context_id": 4,
  "context_id_type": "contract_id",
  "date": "2018-02-23T14:31:32Z",
  "description": "Contract Deposit",
  "first_party_id": 2112625428,
  "id": 89,
  "ref_type": "contract_deposit",
  "second_party_id": 1000132
}
```

## 开发建议

### 最佳实践
1. 使用ETag缓存减少请求
2. 合理设置请求间隔避免限流
3. 处理各种HTTP状态码
4. 使用批量API减少请求次数
5. 实现令牌自动刷新机制

### 错误处理
- 400: 请求错误
- 401: 未授权
- 403: 禁止访问
- 404: 资源不存在
- 420: 请求限制
- 500: 服务器错误
- 503: 服务不可用
- 504: 网关超时

## DDD架构建议

### 领域划分
1. **角色管理域** (Character Domain)
2. **公司管理域** (Corporation Domain)
3. **市场交易域** (Market Domain)
4. **工业生产域** (Industry Domain)
5. **资产管理域** (Asset Domain)
6. **通信域** (Communication Domain)

### 聚合根设计
- Character (角色)
- Corporation (公司)
- Alliance (联盟)
- MarketOrder (市场订单)
- IndustryJob (工业任务)
- Contract (合同)

## 详细API端点列表

### 角色相关端点
| 端点 | 方法 | 权限要求 | 缓存时间 | 描述 |
|------|------|----------|----------|------|
| `/v5/characters/{character_id}/` | GET | 无 | 604800s | 角色公开信息 |
| `/v1/characters/{character_id}/attributes/` | GET | `esi-skills.read_skills.v1` | 120s | 角色属性 |
| `/v5/characters/{character_id}/wallet/journal/` | GET | `esi-wallet.read_character_wallet.v1` | 3600s | 钱包日志 |
| `/v5/characters/{character_id}/assets/` | GET | `esi-assets.read_assets.v1` | 3600s | 角色资产 |
| `/v1/characters/{character_id}/contracts/` | GET | `esi-contracts.read_character_contracts.v1` | 300s | 角色合同 |
| `/v1/characters/{character_id}/industry/jobs/` | GET | `esi-industry.read_character_jobs.v1` | 300s | 工业任务 |
| `/v1/characters/{character_id}/mail/` | GET | `esi-mail.read_mail.v1` | 30s | 邮件列表 |
| `/v1/characters/{character_id}/skills/` | GET | `esi-skills.read_skills.v1` | 120s | 技能信息 |
| `/v1/characters/{character_id}/skillqueue/` | GET | `esi-skills.read_skillqueue.v1` | 120s | 技能队列 |
| `/v1/characters/{character_id}/location/` | GET | `esi-location.read_location.v1` | 5s | 当前位置 |
| `/v1/characters/{character_id}/online/` | GET | `esi-location.read_online.v1` | 60s | 在线状态 |
| `/v1/characters/{character_id}/ship/` | GET | `esi-location.read_ship_type.v1` | 5s | 当前飞船 |

### 公司相关端点
| 端点 | 方法 | 权限要求 | 缓存时间 | 描述 |
|------|------|----------|----------|------|
| `/v5/corporations/{corporation_id}/` | GET | 无 | 3600s | 公司公开信息 |
| `/v4/corporations/{corporation_id}/members/` | GET | `esi-corporations.read_corporation_membership.v1` | 3600s | 公司成员 |
| `/v4/corporations/{corporation_id}/structures/` | GET | `esi-corporations.read_structures.v1` | 3600s | 公司结构 |
| `/v5/corporations/{corporation_id}/assets/` | GET | `esi-assets.read_corporation_assets.v1` | 3600s | 公司资产 |
| `/v1/corporations/{corporation_id}/contracts/` | GET | `esi-contracts.read_corporation_contracts.v1` | 300s | 公司合同 |
| `/v1/corporations/{corporation_id}/industry/jobs/` | GET | `esi-industry.read_corporation_jobs.v1` | 300s | 公司工业任务 |

### 市场相关端点
| 端点 | 方法 | 权限要求 | 缓存时间 | 描述 |
|------|------|----------|----------|------|
| `/v1/markets/{region_id}/orders/` | GET | 无 | 300s | 区域市场订单 |
| `/v1/markets/{region_id}/history/` | GET | 无 | 23小时 | 市场历史数据 |
| `/v1/markets/structures/{structure_id}/` | GET | `esi-markets.structure_markets.v1` | 300s | 结构市场订单 |
| `/v1/characters/{character_id}/orders/` | GET | `esi-markets.read_character_orders.v1` | 1200s | 角色市场订单 |
| `/v3/corporations/{corporation_id}/orders/` | GET | `esi-markets.read_corporation_orders.v1` | 1200s | 公司市场订单 |

### 宇宙相关端点
| 端点 | 方法 | 权限要求 | 缓存时间 | 描述 |
|------|------|----------|----------|------|
| `/v4/universe/systems/{system_id}/` | GET | 无 | 每日11:05过期 | 星系信息 |
| `/v1/universe/types/{type_id}/` | GET | 无 | 每日11:05过期 | 物品类型信息 |
| `/v2/universe/structures/{structure_id}/` | GET | `esi-universe.read_structures.v1` | 3600s | 结构信息 |
| `/v1/universe/regions/` | GET | 无 | 每日11:05过期 | 区域列表 |
| `/v1/universe/constellations/` | GET | 无 | 每日11:05过期 | 星座列表 |

## 常见业务场景

### 1. 角色资产管理
```
1. 获取角色基本信息 -> /v5/characters/{character_id}/
2. 获取角色资产列表 -> /v5/characters/{character_id}/assets/
3. 获取资产名称 -> /v1/characters/{character_id}/assets/names/
4. 获取资产位置信息 -> /v4/universe/systems/{system_id}/
```

### 2. 市场分析
```
1. 获取区域市场订单 -> /v1/markets/{region_id}/orders/
2. 获取市场历史数据 -> /v1/markets/{region_id}/history/
3. 获取物品类型信息 -> /v1/universe/types/{type_id}/
4. 计算利润和趋势分析
```

### 3. 工业生产管理
```
1. 获取工业任务 -> /v1/characters/{character_id}/industry/jobs/
2. 获取蓝图信息 -> /v2/characters/{character_id}/blueprints/
3. 获取制造设施 -> /v2/corporations/{corporation_id}/facilities/
4. 计算生产成本和利润
```

### 4. 公司管理
```
1. 获取公司信息 -> /v5/corporations/{corporation_id}/
2. 获取成员列表 -> /v4/corporations/{corporation_id}/members/
3. 获取公司结构 -> /v4/corporations/{corporation_id}/structures/
4. 获取公司资产 -> /v5/corporations/{corporation_id}/assets/
```

## 技术实现建议

### 1. 数据同步策略
- **实时数据**: 位置、在线状态 (5-60秒缓存)
- **准实时数据**: 市场订单、邮件 (5-30分钟缓存)
- **定期更新**: 技能、资产 (1-2小时缓存)
- **静态数据**: 物品信息、星系信息 (每日更新)

### 2. 数据存储设计
```sql
-- 角色表
CREATE TABLE characters (
    character_id BIGINT PRIMARY KEY,
    name VARCHAR(255),
    corporation_id BIGINT,
    alliance_id BIGINT,
    security_status DECIMAL(10,2),
    updated_at TIMESTAMP
);

-- 资产表
CREATE TABLE assets (
    item_id BIGINT PRIMARY KEY,
    character_id BIGINT,
    type_id INT,
    quantity BIGINT,
    location_id BIGINT,
    location_type VARCHAR(50),
    updated_at TIMESTAMP
);

-- 市场订单表
CREATE TABLE market_orders (
    order_id BIGINT PRIMARY KEY,
    type_id INT,
    region_id INT,
    system_id INT,
    price DECIMAL(15,2),
    volume_remain INT,
    is_buy_order BOOLEAN,
    issued TIMESTAMP,
    updated_at TIMESTAMP
);
```

### 3. API客户端设计模式
```python
class ESIClient:
    def __init__(self, base_url, user_agent):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({'User-Agent': user_agent})

    def get_character_info(self, character_id):
        # 实现角色信息获取
        pass

    def get_character_assets(self, character_id, token):
        # 实现资产信息获取
        pass
```

## 相关资源

- [官方开发者博客](https://developers.eveonline.com/blog)
- [ESI问题追踪](https://github.com/esi/esi-issues)
- [社区Slack频道](https://www.fuzzwork.co.uk/tweetfleet-slack-invites/)
- [静态数据导出(SDE)](https://developers.eveonline.com/docs/sde/)
- [图像服务器](https://developers.eveonline.com/docs/image-server/)
