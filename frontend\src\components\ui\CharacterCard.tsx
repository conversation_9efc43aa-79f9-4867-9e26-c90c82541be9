import React from 'react'
import { Card, Avatar, Tag, Button, Tooltip, Progress } from 'antd'
import { 
  UserOutlined, 
  WalletOutlined, 
  TrophyOutlined,
  EnvironmentOutlined,
  RocketOutlined,
  ClockCircleOutlined
} from '@ant-design/icons'
import { motion } from 'framer-motion'
import clsx from 'clsx'

import { Character } from '@/types'

interface CharacterCardProps {
  character: Character
  onClick?: () => void
  actions?: React.ReactNode[]
  size?: 'small' | 'default' | 'large'
  showDetails?: boolean
  className?: string
}

export function CharacterCard({
  character,
  onClick,
  actions,
  size = 'default',
  showDetails = true,
  className = '',
}: CharacterCardProps) {
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('zh-CN').format(num)
  }

  const formatISK = (amount: number) => {
    if (amount >= 1e9) {
      return `${(amount / 1e9).toFixed(1)}B`
    } else if (amount >= 1e6) {
      return `${(amount / 1e6).toFixed(1)}M`
    } else if (amount >= 1e3) {
      return `${(amount / 1e3).toFixed(1)}K`
    }
    return amount.toString()
  }

  const getSecurityStatusColor = (status: number) => {
    if (status >= 5) return '#52c41a'
    if (status >= 0) return '#faad14'
    return '#f5222d'
  }

  const cardSize = size === 'small' ? 'small' : 'default'

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
      className={className}
    >
      <Card
        hoverable={!!onClick}
        onClick={onClick}
        actions={actions}
        size={cardSize}
        className={clsx(
          'character-card transition-all duration-200',
          onClick && 'cursor-pointer hover:shadow-lg hover:border-primary-300'
        )}
        cover={
          <div className="relative">
            <div className={clsx(
              'flex items-center justify-center bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20',
              size === 'small' ? 'h-32' : size === 'large' ? 'h-48' : 'h-40'
            )}>
              <Avatar
                size={size === 'small' ? 64 : size === 'large' ? 128 : 96}
                src={character.portraitUrl}
                icon={<UserOutlined />}
                className="border-4 border-white shadow-lg"
              />
            </div>
            
            {/* 在线状态指示器 */}
            <div className="absolute top-2 right-2">
              <Tag
                color={character.isOnline ? 'green' : 'default'}
                className="text-xs"
              >
                {character.isOnline ? '在线' : '离线'}
              </Tag>
            </div>

            {/* 安全等级 */}
            <div className="absolute top-2 left-2">
              <Tooltip title={`安全等级: ${character.securityStatus.toFixed(1)}`}>
                <Tag
                  color={character.securityStatus >= 0 ? 'green' : 'red'}
                  className="text-xs"
                >
                  {character.securityStatus >= 0 ? '守法' : '罪犯'}
                </Tag>
              </Tooltip>
            </div>
          </div>
        }
      >
        <Card.Meta
          title={
            <div className="space-y-1">
              <div className={clsx(
                'font-bold text-gray-900 dark:text-gray-100',
                size === 'small' ? 'text-sm' : 'text-base'
              )}>
                {character.name}
              </div>
              <div className={clsx(
                'text-gray-600 dark:text-gray-400',
                size === 'small' ? 'text-xs' : 'text-sm'
              )}>
                {character.corporation}
                {character.alliance && (
                  <span className="text-purple-600 dark:text-purple-400">
                    {' • '}{character.alliance}
                  </span>
                )}
              </div>
            </div>
          }
          description={
            showDetails && (
              <div className="space-y-3 mt-3">
                {/* 基础信息 */}
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div className="flex items-center space-x-1">
                    <WalletOutlined className="text-green-500" />
                    <span className="text-gray-600 dark:text-gray-400">
                      {formatISK(character.wallet)} ISK
                    </span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <TrophyOutlined className="text-blue-500" />
                    <span className="text-gray-600 dark:text-gray-400">
                      {formatNumber(character.totalSkillPoints)} SP
                    </span>
                  </div>
                </div>

                {/* 位置和飞船 */}
                {(character.location || character.ship) && (
                  <div className="space-y-1 text-xs">
                    {character.location && (
                      <div className="flex items-center space-x-1">
                        <EnvironmentOutlined className="text-orange-500" />
                        <span className="text-gray-600 dark:text-gray-400 truncate">
                          {character.location}
                        </span>
                      </div>
                    )}
                    {character.ship && (
                      <div className="flex items-center space-x-1">
                        <RocketOutlined className="text-purple-500" />
                        <span className="text-gray-600 dark:text-gray-400 truncate">
                          {character.ship}
                        </span>
                      </div>
                    )}
                  </div>
                )}

                {/* 最后登录时间 */}
                {character.lastLogin && (
                  <div className="flex items-center space-x-1 text-xs">
                    <ClockCircleOutlined className="text-gray-400" />
                    <span className="text-gray-500 dark:text-gray-500">
                      最后登录: {new Date(character.lastLogin).toLocaleDateString()}
                    </span>
                  </div>
                )}

                {/* 未分配技能点 */}
                {character.unallocatedSkillPoints > 0 && (
                  <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded p-2 border border-yellow-200 dark:border-yellow-800">
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-yellow-700 dark:text-yellow-300">
                        未分配技能点
                      </span>
                      <span className="font-semibold text-yellow-800 dark:text-yellow-200">
                        {formatNumber(character.unallocatedSkillPoints)}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            )
          }
        />
      </Card>
    </motion.div>
  )
}

// 简化版角色卡片
export function SimpleCharacterCard({
  character,
  onClick,
  className = '',
}: {
  character: Character
  onClick?: () => void
  className?: string
}) {
  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.3 }}
      className={className}
    >
      <div
        className={clsx(
          'flex items-center space-x-3 p-3 rounded-lg border border-gray-200 dark:border-dark-700 bg-white dark:bg-dark-800',
          onClick && 'cursor-pointer hover:bg-gray-50 dark:hover:bg-dark-700 hover:border-primary-300'
        )}
        onClick={onClick}
      >
        <Avatar
          size={48}
          src={character.portraitUrl}
          icon={<UserOutlined />}
        />
        <div className="flex-1 min-w-0">
          <div className="font-medium text-gray-900 dark:text-gray-100 truncate">
            {character.name}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400 truncate">
            {character.corporation}
          </div>
        </div>
        <div className="flex flex-col items-end space-y-1">
          <Tag
            color={character.isOnline ? 'green' : 'default'}
            size="small"
          >
            {character.isOnline ? '在线' : '离线'}
          </Tag>
          <span className="text-xs text-gray-500">
            {new Intl.NumberFormat('zh-CN', { notation: 'compact' }).format(character.totalSkillPoints)} SP
          </span>
        </div>
      </div>
    </motion.div>
  )
}

// 角色选择器组件
export function CharacterSelector({
  characters,
  selectedCharacter,
  onSelect,
  className = '',
}: {
  characters: Character[]
  selectedCharacter?: Character
  onSelect: (character: Character) => void
  className?: string
}) {
  return (
    <div className={clsx('space-y-2', className)}>
      <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
        选择角色
      </div>
      <div className="space-y-2">
        {characters.map((character) => (
          <SimpleCharacterCard
            key={character.id}
            character={character}
            onClick={() => onSelect(character)}
            className={clsx(
              selectedCharacter?.id === character.id && 
              'ring-2 ring-primary-500 border-primary-500'
            )}
          />
        ))}
      </div>
    </div>
  )
}
