"""
资产相关数据库模型
"""
from sqlalchemy import (
    Column, Integer, String, BigInteger, Float, Boolean,
    ForeignKey, Index, DateTime
)
from sqlalchemy.orm import relationship

from .base import BaseModel


class AssetModel(BaseModel):
    """资产模型"""
    __tablename__ = "assets"
    
    # 资产基本信息
    item_id = Column(BigInteger, primary_key=True)  # EVE物品ID作为主键
    character_id = Column(BigInteger, ForeignKey('characters.character_id'), nullable=True, index=True)
    corporation_id = Column(BigInteger, nullable=True, index=True)  # 公司资产
    
    # 物品信息
    type_id = Column(Integer, nullable=False, index=True)
    quantity = Column(BigInteger, nullable=False, default=1)
    
    # 位置信息
    location_id = Column(BigInteger, nullable=False, index=True)
    location_type = Column(String(50), nullable=False)  # station, solar_system, other
    location_flag = Column(String(50), nullable=False)  # Hangar, CargoHold, etc.
    
    # 物品属性
    is_singleton = Column(Boolean, nullable=False, default=False)
    is_blueprint_copy = Column(Boolean, nullable=True)
    
    # 估值信息
    estimated_value = Column(Float, nullable=True)
    last_price_update = Column(DateTime, nullable=True)
    
    # 数据同步
    last_sync_at = Column(DateTime, nullable=True)
    
    # 关联关系
    character = relationship("CharacterModel", back_populates="assets")
    
    # 索引
    __table_args__ = (
        Index('idx_asset_owner_type', 'character_id', 'type_id'),
        Index('idx_asset_location', 'location_id', 'location_type'),
        Index('idx_asset_value', 'estimated_value'),
        Index('idx_asset_sync', 'last_sync_at'),
    )


class AssetLocationModel(BaseModel):
    """资产位置缓存模型"""
    __tablename__ = "asset_locations"
    
    location_id = Column(BigInteger, primary_key=True)
    location_name = Column(String(255), nullable=True)
    location_type = Column(String(50), nullable=False)  # station, structure, system
    system_id = Column(Integer, nullable=True, index=True)
    system_name = Column(String(255), nullable=True)
    region_id = Column(Integer, nullable=True, index=True)
    region_name = Column(String(255), nullable=True)
    
    # 缓存时间
    cached_at = Column(DateTime, nullable=False)
    
    # 索引
    __table_args__ = (
        Index('idx_location_system', 'system_id', 'location_type'),
        Index('idx_location_region', 'region_id'),
    )


class AssetValueHistoryModel(BaseModel):
    """资产价值历史模型"""
    __tablename__ = "asset_value_history"
    
    character_id = Column(BigInteger, ForeignKey('characters.character_id'), nullable=False, index=True)
    date = Column(DateTime, nullable=False, index=True)
    
    # 价值统计
    total_value = Column(Float, nullable=False)
    liquid_value = Column(Float, nullable=False)  # 流动资产价值
    ship_value = Column(Float, nullable=False)    # 舰船价值
    module_value = Column(Float, nullable=False)  # 装备价值
    commodity_value = Column(Float, nullable=False)  # 商品价值
    
    # 数量统计
    total_items = Column(Integer, nullable=False)
    unique_types = Column(Integer, nullable=False)
    locations = Column(Integer, nullable=False)
    
    # 关联关系
    character = relationship("CharacterModel")
    
    # 索引
    __table_args__ = (
        Index('idx_value_history_date', 'character_id', 'date'),
    )
