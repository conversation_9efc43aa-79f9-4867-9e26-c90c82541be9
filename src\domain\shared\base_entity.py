"""
领域实体基类
"""
from abc import ABC
from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID, uuid4

from .domain_events import DomainEvent


class Entity(ABC):
    """实体基类"""
    
    def __init__(self, entity_id: Optional[Any] = None):
        self._id = entity_id
        self._domain_events: List[DomainEvent] = []
        self._created_at = datetime.utcnow()
        self._updated_at = datetime.utcnow()
    
    @property
    def id(self) -> Any:
        """实体ID"""
        return self._id
    
    @property
    def created_at(self) -> datetime:
        """创建时间"""
        return self._created_at
    
    @property
    def updated_at(self) -> datetime:
        """更新时间"""
        return self._updated_at
    
    @property
    def domain_events(self) -> List[DomainEvent]:
        """领域事件列表"""
        return self._domain_events.copy()
    
    def add_domain_event(self, event: DomainEvent) -> None:
        """添加领域事件"""
        self._domain_events.append(event)
    
    def clear_domain_events(self) -> None:
        """清除领域事件"""
        self._domain_events.clear()
    
    def mark_as_updated(self) -> None:
        """标记为已更新"""
        self._updated_at = datetime.utcnow()
    
    def __eq__(self, other: object) -> bool:
        """实体相等性比较"""
        if not isinstance(other, Entity):
            return False
        return self._id == other._id
    
    def __hash__(self) -> int:
        """实体哈希值"""
        return hash(self._id)
    
    def __repr__(self) -> str:
        """实体字符串表示"""
        return f"{self.__class__.__name__}(id={self._id})"


class AggregateRoot(Entity):
    """聚合根基类"""
    
    def __init__(self, entity_id: Optional[Any] = None):
        super().__init__(entity_id)
        self._version = 0
    
    @property
    def version(self) -> int:
        """版本号（用于乐观锁）"""
        return self._version
    
    def increment_version(self) -> None:
        """增加版本号"""
        self._version += 1
        self.mark_as_updated()


class ValueObject(ABC):
    """值对象基类"""
    
    def __eq__(self, other: object) -> bool:
        """值对象相等性比较"""
        if not isinstance(other, self.__class__):
            return False
        return self.__dict__ == other.__dict__
    
    def __hash__(self) -> int:
        """值对象哈希值"""
        return hash(tuple(sorted(self.__dict__.items())))
    
    def __repr__(self) -> str:
        """值对象字符串表示"""
        attrs = ", ".join(f"{k}={v!r}" for k, v in self.__dict__.items())
        return f"{self.__class__.__name__}({attrs})"


class DomainService(ABC):
    """领域服务基类"""
    pass


class Specification(ABC):
    """规约模式基类"""
    
    def is_satisfied_by(self, candidate: Any) -> bool:
        """检查候选对象是否满足规约"""
        raise NotImplementedError
    
    def and_(self, other: 'Specification') -> 'AndSpecification':
        """与操作"""
        return AndSpecification(self, other)
    
    def or_(self, other: 'Specification') -> 'OrSpecification':
        """或操作"""
        return OrSpecification(self, other)
    
    def not_(self) -> 'NotSpecification':
        """非操作"""
        return NotSpecification(self)


class AndSpecification(Specification):
    """与规约"""
    
    def __init__(self, left: Specification, right: Specification):
        self.left = left
        self.right = right
    
    def is_satisfied_by(self, candidate: Any) -> bool:
        return self.left.is_satisfied_by(candidate) and self.right.is_satisfied_by(candidate)


class OrSpecification(Specification):
    """或规约"""
    
    def __init__(self, left: Specification, right: Specification):
        self.left = left
        self.right = right
    
    def is_satisfied_by(self, candidate: Any) -> bool:
        return self.left.is_satisfied_by(candidate) or self.right.is_satisfied_by(candidate)


class NotSpecification(Specification):
    """非规约"""
    
    def __init__(self, spec: Specification):
        self.spec = spec
    
    def is_satisfied_by(self, candidate: Any) -> bool:
        return not self.spec.is_satisfied_by(candidate)
