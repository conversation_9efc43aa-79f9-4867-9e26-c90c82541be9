{"name": "@storybook/addon-interactions", "version": "7.6.20", "description": "Automate, test and debug user interactions", "keywords": ["storybook-addons", "data-state", "test"], "homepage": "https://github.com/storybookjs/storybook/tree/next/code/addons/interactions", "bugs": {"url": "https://github.com/storybookjs/storybook/issues"}, "repository": {"type": "git", "url": "https://github.com/storybookjs/storybook.git", "directory": "code/addons/interactions"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "license": "MIT", "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "require": "./dist/index.js", "import": "./dist/index.mjs"}, "./manager": "./dist/manager.js", "./preview": "./dist/preview.js", "./register.js": "./dist/manager.js", "./package.json": "./package.json"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist/**/*", "README.md", "*.js", "*.d.ts", "!src/**/*"], "scripts": {"check": "node --loader ../../../scripts/node_modules/esbuild-register/loader.js -r ../../../scripts/node_modules/esbuild-register/register.js ../../../scripts/prepare/check.ts", "prep": "node --loader ../../../scripts/node_modules/esbuild-register/loader.js -r ../../../scripts/node_modules/esbuild-register/register.js ../../../scripts/prepare/addon-bundle.ts"}, "dependencies": {"@storybook/global": "^5.0.0", "@storybook/types": "7.6.20", "jest-mock": "^27.0.6", "polished": "^4.2.2", "ts-dedent": "^2.2.0"}, "devDependencies": {"@devtools-ds/object-inspector": "^1.1.2", "@storybook/client-logger": "7.6.20", "@storybook/components": "7.6.20", "@storybook/core-common": "7.6.20", "@storybook/core-events": "7.6.20", "@storybook/instrumenter": "7.6.20", "@storybook/jest": "next", "@storybook/manager-api": "7.6.20", "@storybook/preview-api": "7.6.20", "@storybook/testing-library": "next", "@storybook/theming": "7.6.20", "@types/node": "^18.0.0", "formik": "^2.2.9", "react": "^16.8.0", "react-dom": "^16.8.0", "typescript": "~4.9.3"}, "publishConfig": {"access": "public"}, "bundler": {"exportEntries": ["./src/index.ts"], "managerEntries": ["./src/manager.tsx"], "previewEntries": ["./src/preview.ts"], "nodeEntries": ["./src/preset.ts"]}, "gitHead": "e6a7fd8a655c69780bc20b9749c2699e44beae17", "storybook": {"displayName": "Interactions", "unsupportedFrameworks": ["react-native"], "icon": "https://user-images.githubusercontent.com/263385/101991666-479cc600-3c7c-11eb-837b-be4e5ffa1bb8.png"}}