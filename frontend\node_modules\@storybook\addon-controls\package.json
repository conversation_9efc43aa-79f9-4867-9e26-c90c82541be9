{"name": "@storybook/addon-controls", "version": "7.6.20", "description": "Interact with component inputs dynamically in the Storybook UI", "keywords": ["addon", "storybook", "knobs", "controls", "properties", "essentials", "data-state"], "homepage": "https://github.com/storybookjs/storybook/tree/next/code/addons/controls", "bugs": {"url": "https://github.com/storybookjs/storybook/issues"}, "repository": {"type": "git", "url": "https://github.com/storybookjs/storybook.git", "directory": "code/addons/controls"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "license": "MIT", "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "require": "./dist/index.js", "import": "./dist/index.mjs"}, "./manager": "./dist/manager.js", "./register": "./dist/manager.js", "./package.json": "./package.json"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist/**/*", "README.md", "*.js", "*.d.ts", "!src/**/*"], "scripts": {"check": "node --loader ../../../scripts/node_modules/esbuild-register/loader.js -r ../../../scripts/node_modules/esbuild-register/register.js ../../../scripts/prepare/check.ts", "prep": "node --loader ../../../scripts/node_modules/esbuild-register/loader.js -r ../../../scripts/node_modules/esbuild-register/register.js ../../../scripts/prepare/addon-bundle.ts"}, "dependencies": {"@storybook/blocks": "7.6.20", "lodash": "^4.17.21", "ts-dedent": "^2.0.0"}, "devDependencies": {"@storybook/client-logger": "7.6.20", "@storybook/components": "7.6.20", "@storybook/core-common": "7.6.20", "@storybook/manager-api": "7.6.20", "@storybook/node-logger": "7.6.20", "@storybook/preview-api": "7.6.20", "@storybook/theming": "7.6.20", "@storybook/types": "7.6.20", "react": "^16.8.0", "react-dom": "^16.8.0"}, "publishConfig": {"access": "public"}, "bundler": {"exportEntries": ["./src/index.ts"], "managerEntries": ["./src/manager.tsx"]}, "gitHead": "e6a7fd8a655c69780bc20b9749c2699e44beae17", "storybook": {"displayName": "Controls", "icon": "https://user-images.githubusercontent.com/263385/101991669-479cc600-3c7c-11eb-93d9-38b67e8371f2.png", "supportedFrameworks": ["react", "vue", "angular", "web-components", "ember"]}}