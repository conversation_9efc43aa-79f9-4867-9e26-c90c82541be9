"""
CORS中间件
"""
from typing import List, Optional, Set
from fastapi import Request, Response
from fastapi.middleware.cors import CORSMiddleware as FastAPICORSMiddleware
from starlette.middleware.base import BaseHTTPMiddleware

from ..config import settings
from ..config.logging import get_logger

logger = get_logger(__name__)


class CORSMiddleware(BaseHTTPMiddleware):
    """增强的CORS中间件"""
    
    def __init__(self, app, 
                 allow_origins: List[str] = None,
                 allow_credentials: bool = True,
                 allow_methods: List[str] = None,
                 allow_headers: List[str] = None,
                 expose_headers: List[str] = None,
                 max_age: int = 600):
        super().__init__(app)
        
        self.allow_origins = allow_origins or settings.get_cors_origins_list()
        self.allow_credentials = allow_credentials
        self.allow_methods = allow_methods or ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"]
        self.allow_headers = allow_headers or [
            "Accept",
            "Accept-Language", 
            "Content-Language",
            "Content-Type",
            "Authorization",
            "X-Requested-With",
            "X-CSRF-Token",
            "X-API-Key"
        ]
        self.expose_headers = expose_headers or [
            "X-Total-Count",
            "X-Page-Count", 
            "X-Rate-Limit-Remaining",
            "X-Rate-Limit-Reset"
        ]
        self.max_age = max_age
        
        # 预编译允许的源
        self.allow_origin_set = set(self.allow_origins) if "*" not in self.allow_origins else None
    
    async def dispatch(self, request: Request, call_next):
        origin = request.headers.get("origin")
        
        # 处理预检请求
        if request.method == "OPTIONS":
            return self._handle_preflight(request, origin)
        
        # 处理实际请求
        response = await call_next(request)
        
        # 添加CORS头
        self._add_cors_headers(response, origin)
        
        return response
    
    def _handle_preflight(self, request: Request, origin: str) -> Response:
        """处理预检请求"""
        response = Response()
        
        # 检查源
        if not self._is_origin_allowed(origin):
            logger.warning(f"CORS预检请求被拒绝，源: {origin}")
            return Response(status_code=403)
        
        # 检查请求方法
        requested_method = request.headers.get("access-control-request-method")
        if requested_method and requested_method not in self.allow_methods:
            logger.warning(f"CORS预检请求被拒绝，方法: {requested_method}")
            return Response(status_code=403)
        
        # 检查请求头
        requested_headers = request.headers.get("access-control-request-headers")
        if requested_headers:
            requested_headers_list = [h.strip() for h in requested_headers.split(",")]
            for header in requested_headers_list:
                if header.lower() not in [h.lower() for h in self.allow_headers]:
                    logger.warning(f"CORS预检请求被拒绝，头部: {header}")
                    return Response(status_code=403)
        
        # 添加CORS头
        self._add_cors_headers(response, origin)
        response.headers["Access-Control-Allow-Methods"] = ", ".join(self.allow_methods)
        response.headers["Access-Control-Allow-Headers"] = ", ".join(self.allow_headers)
        response.headers["Access-Control-Max-Age"] = str(self.max_age)
        
        logger.debug(f"CORS预检请求通过，源: {origin}")
        return response
    
    def _add_cors_headers(self, response: Response, origin: str) -> None:
        """添加CORS头"""
        if self._is_origin_allowed(origin):
            if "*" in self.allow_origins:
                response.headers["Access-Control-Allow-Origin"] = "*"
            else:
                response.headers["Access-Control-Allow-Origin"] = origin
            
            if self.allow_credentials:
                response.headers["Access-Control-Allow-Credentials"] = "true"
            
            if self.expose_headers:
                response.headers["Access-Control-Expose-Headers"] = ", ".join(self.expose_headers)
    
    def _is_origin_allowed(self, origin: str) -> bool:
        """检查源是否被允许"""
        if not origin:
            return False
        
        if "*" in self.allow_origins:
            return True
        
        if self.allow_origin_set:
            return origin in self.allow_origin_set
        
        # 支持通配符匹配
        for allowed_origin in self.allow_origins:
            if self._match_origin(origin, allowed_origin):
                return True
        
        return False
    
    def _match_origin(self, origin: str, pattern: str) -> bool:
        """匹配源模式"""
        if pattern == origin:
            return True
        
        # 简单的通配符支持
        if "*" in pattern:
            import re
            regex_pattern = pattern.replace("*", ".*")
            return bool(re.match(f"^{regex_pattern}$", origin))
        
        return False


def setup_cors_middleware(app):
    """设置CORS中间件"""
    app.add_middleware(
        FastAPICORSMiddleware,
        allow_origins=settings.get_cors_origins_list(),
        allow_credentials=settings.cors_allow_credentials,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    logger.info(
        "CORS中间件已配置",
        origins=settings.get_cors_origins_list(),
        allow_credentials=settings.cors_allow_credentials
    )
