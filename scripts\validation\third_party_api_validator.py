#!/usr/bin/env python3
"""
第三方API验证器
用于验证第三方服务配置与官方文档的一致性
"""
import sys
import json
import requests
import re
from pathlib import Path
from typing import List, Dict, Any, Optional, NamedTuple
from dataclasses import dataclass
from datetime import datetime
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.infrastructure.config import settings


@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    suggestions: List[str]
    details: Dict[str, Any]


@dataclass
class ScopeInfo:
    """Scope信息"""
    name: str
    description: str
    is_valid: bool
    category: str
    deprecated: bool = False


class EVEESIScopeValidator:
    """EVE ESI Scope验证器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.esi_base_url = "https://esi.evetech.net"
        self.valid_scopes_cache = None
        self.cache_timestamp = None
        
    def get_official_scopes(self) -> Dict[str, ScopeInfo]:
        """从EVE ESI官方文档获取有效的scope列表"""
        try:
            # 尝试从ESI swagger文档获取scope信息
            swagger_url = f"{self.esi_base_url}/latest/swagger.json"
            response = requests.get(swagger_url, timeout=10)
            response.raise_for_status()
            
            swagger_data = response.json()
            scopes = {}
            
            # 从swagger文档中提取scope信息
            if 'securityDefinitions' in swagger_data:
                oauth_def = swagger_data['securityDefinitions'].get('evesso', {})
                if 'scopes' in oauth_def:
                    for scope_name, description in oauth_def['scopes'].items():
                        scopes[scope_name] = ScopeInfo(
                            name=scope_name,
                            description=description,
                            is_valid=True,
                            category=self._categorize_scope(scope_name)
                        )
            
            self.valid_scopes_cache = scopes
            self.cache_timestamp = datetime.now()
            return scopes
            
        except Exception as e:
            self.logger.error(f"无法获取官方scope列表: {e}")
            # 返回已知的有效scope作为fallback
            return self._get_fallback_scopes()
    
    def _get_fallback_scopes(self) -> Dict[str, ScopeInfo]:
        """获取已知有效的scope作为fallback"""
        known_valid_scopes = {
            # 位置相关
            'esi-location.read_location.v1': ScopeInfo(
                'esi-location.read_location.v1', '读取角色当前位置', True, 'location'
            ),
            'esi-location.read_online.v1': ScopeInfo(
                'esi-location.read_online.v1', '读取角色在线状态', True, 'location'
            ),
            'esi-location.read_ship_type.v1': ScopeInfo(
                'esi-location.read_ship_type.v1', '读取当前飞船类型', True, 'location'
            ),
            
            # 技能相关
            'esi-skills.read_skills.v1': ScopeInfo(
                'esi-skills.read_skills.v1', '读取角色技能', True, 'skills'
            ),
            'esi-skills.read_skillqueue.v1': ScopeInfo(
                'esi-skills.read_skillqueue.v1', '读取技能队列', True, 'skills'
            ),
            
            # 资产相关
            'esi-assets.read_assets.v1': ScopeInfo(
                'esi-assets.read_assets.v1', '读取角色资产', True, 'assets'
            ),
            
            # 钱包相关
            'esi-wallet.read_character_wallet.v1': ScopeInfo(
                'esi-wallet.read_character_wallet.v1', '读取角色钱包', True, 'wallet'
            ),
            
            # 其他
            'esi-clones.read_clones.v1': ScopeInfo(
                'esi-clones.read_clones.v1', '读取克隆体信息', True, 'clones'
            ),
            'esi-mail.read_mail.v1': ScopeInfo(
                'esi-mail.read_mail.v1', '读取邮件', True, 'mail'
            ),
        }
        return known_valid_scopes
    
    def _categorize_scope(self, scope_name: str) -> str:
        """根据scope名称分类"""
        if 'location' in scope_name:
            return 'location'
        elif 'skills' in scope_name:
            return 'skills'
        elif 'assets' in scope_name:
            return 'assets'
        elif 'wallet' in scope_name:
            return 'wallet'
        elif 'clones' in scope_name:
            return 'clones'
        elif 'mail' in scope_name:
            return 'mail'
        elif 'characters' in scope_name:
            return 'characters'
        else:
            return 'other'
    
    def validate_scopes(self, scopes: List[str]) -> ValidationResult:
        """验证scope列表的有效性"""
        official_scopes = self.get_official_scopes()
        
        errors = []
        warnings = []
        suggestions = []
        details = {
            'total_scopes': len(scopes),
            'valid_scopes': [],
            'invalid_scopes': [],
            'scope_categories': {}
        }
        
        for scope in scopes:
            if scope in official_scopes:
                details['valid_scopes'].append(scope)
                scope_info = official_scopes[scope]
                category = scope_info.category
                if category not in details['scope_categories']:
                    details['scope_categories'][category] = []
                details['scope_categories'][category].append(scope)
                
                if scope_info.deprecated:
                    warnings.append(f"Scope '{scope}' 已废弃，建议使用替代方案")
            else:
                details['invalid_scopes'].append(scope)
                errors.append(f"无效的scope: '{scope}'")
                
                # 提供建议
                similar_scopes = self._find_similar_scopes(scope, official_scopes)
                if similar_scopes:
                    suggestions.append(f"对于 '{scope}'，建议使用: {', '.join(similar_scopes)}")
        
        # 特殊检查：检查是否使用了不存在的角色信息scope
        invalid_character_scopes = [
            'esi-characters.read_characters.v1',
            'esi-character.read_character.v1'
        ]
        
        for invalid_scope in invalid_character_scopes:
            if invalid_scope in scopes:
                errors.append(f"无效的scope: '{invalid_scope}' - 基本角色信息在OAuth过程中自动提供，无需特殊scope")
                suggestions.append("移除无效的角色信息scope，基本信息会自动在JWT令牌中提供")
        
        is_valid = len(errors) == 0
        
        return ValidationResult(
            is_valid=is_valid,
            errors=errors,
            warnings=warnings,
            suggestions=suggestions,
            details=details
        )
    
    def _find_similar_scopes(self, invalid_scope: str, official_scopes: Dict[str, ScopeInfo]) -> List[str]:
        """查找相似的有效scope"""
        similar = []
        invalid_lower = invalid_scope.lower()
        
        for scope_name in official_scopes.keys():
            scope_lower = scope_name.lower()
            
            # 检查是否包含相同的关键词
            invalid_parts = invalid_lower.split('.')
            scope_parts = scope_lower.split('.')
            
            common_parts = set(invalid_parts) & set(scope_parts)
            if len(common_parts) >= 2:  # 至少有2个共同部分
                similar.append(scope_name)
        
        return similar[:3]  # 最多返回3个建议


class ThirdPartyAPIValidator:
    """第三方API验证器主类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.eve_validator = EVEESIScopeValidator()
    
    def validate_eve_esi_config(self) -> ValidationResult:
        """验证EVE ESI配置"""
        try:
            # 获取当前配置的scopes
            configured_scopes = settings.eve_sso_scopes.split()
            
            # 验证scopes
            result = self.eve_validator.validate_scopes(configured_scopes)
            
            # 添加配置特定的检查
            if not settings.eve_client_id:
                result.errors.append("EVE_CLIENT_ID 未配置")
            
            if not settings.eve_client_secret:
                result.errors.append("EVE_CLIENT_SECRET 未配置")
            
            if not settings.eve_callback_url:
                result.errors.append("EVE_CALLBACK_URL 未配置")
            
            result.details['config_source'] = 'environment_variables'
            result.details['client_id_configured'] = bool(settings.eve_client_id)
            result.details['callback_url'] = settings.eve_callback_url
            
            return result
            
        except Exception as e:
            return ValidationResult(
                is_valid=False,
                errors=[f"配置验证失败: {str(e)}"],
                warnings=[],
                suggestions=["检查环境变量配置是否正确"],
                details={'error': str(e)}
            )
    
    def validate_oauth_endpoints(self, provider: str = "eve") -> ValidationResult:
        """验证OAuth端点的可访问性"""
        if provider == "eve":
            return self._validate_eve_oauth_endpoints()
        else:
            return ValidationResult(
                is_valid=False,
                errors=[f"不支持的OAuth提供商: {provider}"],
                warnings=[],
                suggestions=[],
                details={}
            )
    
    def _validate_eve_oauth_endpoints(self) -> ValidationResult:
        """验证EVE OAuth端点"""
        endpoints = {
            'authorization': 'https://login.eveonline.com/v2/oauth/authorize',
            'token': 'https://login.eveonline.com/v2/oauth/token',
            'verify': 'https://login.eveonline.com/oauth/verify',
            'esi_base': 'https://esi.evetech.net'
        }
        
        errors = []
        warnings = []
        details = {'endpoint_status': {}}
        
        for name, url in endpoints.items():
            try:
                response = requests.head(url, timeout=5)
                status_code = response.status_code
                details['endpoint_status'][name] = {
                    'url': url,
                    'status_code': status_code,
                    'accessible': status_code < 500
                }
                
                if status_code >= 500:
                    errors.append(f"端点 {name} ({url}) 返回服务器错误: {status_code}")
                elif status_code >= 400:
                    # 对于OAuth端点，4xx可能是正常的（需要参数）
                    if name in ['authorization', 'token']:
                        details['endpoint_status'][name]['accessible'] = True
                    else:
                        warnings.append(f"端点 {name} ({url}) 返回客户端错误: {status_code}")
                        
            except requests.RequestException as e:
                errors.append(f"无法访问端点 {name} ({url}): {str(e)}")
                details['endpoint_status'][name] = {
                    'url': url,
                    'error': str(e),
                    'accessible': False
                }
        
        is_valid = len(errors) == 0
        
        return ValidationResult(
            is_valid=is_valid,
            errors=errors,
            warnings=warnings,
            suggestions=["检查网络连接和防火墙设置"] if errors else [],
            details=details
        )
    
    def generate_validation_report(self) -> Dict[str, Any]:
        """生成完整的验证报告"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'validator_version': '1.0.0',
            'validations': {}
        }
        
        # EVE ESI配置验证
        eve_config_result = self.validate_eve_esi_config()
        report['validations']['eve_esi_config'] = {
            'is_valid': eve_config_result.is_valid,
            'errors': eve_config_result.errors,
            'warnings': eve_config_result.warnings,
            'suggestions': eve_config_result.suggestions,
            'details': eve_config_result.details
        }
        
        # OAuth端点验证
        oauth_result = self.validate_oauth_endpoints('eve')
        report['validations']['oauth_endpoints'] = {
            'is_valid': oauth_result.is_valid,
            'errors': oauth_result.errors,
            'warnings': oauth_result.warnings,
            'suggestions': oauth_result.suggestions,
            'details': oauth_result.details
        }
        
        # 总体状态
        report['overall_status'] = {
            'is_valid': all(v['is_valid'] for v in report['validations'].values()),
            'total_errors': sum(len(v['errors']) for v in report['validations'].values()),
            'total_warnings': sum(len(v['warnings']) for v in report['validations'].values())
        }
        
        return report


def main():
    """主函数"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    print("🔍 第三方API配置验证")
    print("=" * 50)
    
    validator = ThirdPartyAPIValidator()
    
    # 生成验证报告
    report = validator.generate_validation_report()
    
    # 显示结果
    print(f"\n📊 验证报告 - {report['timestamp']}")
    print("-" * 50)
    
    for validation_name, result in report['validations'].items():
        status = "✅ 通过" if result['is_valid'] else "❌ 失败"
        print(f"\n{validation_name}: {status}")
        
        if result['errors']:
            print("  错误:")
            for error in result['errors']:
                print(f"    - {error}")
        
        if result['warnings']:
            print("  警告:")
            for warning in result['warnings']:
                print(f"    - {warning}")
        
        if result['suggestions']:
            print("  建议:")
            for suggestion in result['suggestions']:
                print(f"    - {suggestion}")
    
    # 总体状态
    overall = report['overall_status']
    status_icon = "🎉" if overall['is_valid'] else "⚠️"
    print(f"\n{status_icon} 总体状态: {'通过' if overall['is_valid'] else '需要修复'}")
    print(f"   错误: {overall['total_errors']}")
    print(f"   警告: {overall['total_warnings']}")
    
    # 保存报告
    report_file = project_root / "logs" / f"api_validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    report_file.parent.mkdir(exist_ok=True)
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 详细报告已保存到: {report_file}")
    
    return 0 if overall['is_valid'] else 1


if __name__ == "__main__":
    sys.exit(main())
