#!/usr/bin/env python3
"""
测试规约模式实现
"""
import sys
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_specification_implementations():
    """测试规约模式实现"""
    print("🔧 测试规约模式实现")
    print("=" * 50)
    
    try:
        # 导入规约类
        from src.domain.shared.specifications import (
            AndSpecification, OrSpecification, NotSpecification,
            AttributeEqualsSpecification, AttributeInRangeSpecification,
            IsActiveSpecification, TokenValiditySpecification,
            CommonSpecifications, and_spec, or_spec, not_spec
        )
        
        print("✅ 规约类导入成功")
        
        # 创建测试对象
        class TestUser:
            def __init__(self, name, age, is_active, is_verified):
                self.name = name
                self.age = age
                self.is_active = is_active
                self.is_verified = is_verified
        
        class TestToken:
            def __init__(self, access_token, is_active, expires_at):
                self.access_token = access_token
                self.is_active = is_active
                self.expires_at = expires_at
        
        # 测试数据
        active_user = TestUser("Alice", 25, True, True)
        inactive_user = TestUser("Bob", 30, False, True)
        
        future_time = datetime.utcnow() + timedelta(hours=1)
        past_time = datetime.utcnow() - timedelta(hours=1)
        
        valid_token = TestToken("valid_token", True, future_time)
        expired_token = TestToken("expired_token", True, past_time)
        
        # 测试基础规约
        print("\n📋 测试基础规约:")
        
        # 属性等于规约
        name_spec = AttributeEqualsSpecification("name", "Alice")
        assert name_spec.is_satisfied_by(active_user) == True
        assert name_spec.is_satisfied_by(inactive_user) == False
        print("✅ AttributeEqualsSpecification")
        
        # 属性范围规约
        age_spec = AttributeInRangeSpecification("age", 20, 30)
        assert age_spec.is_satisfied_by(active_user) == True
        assert age_spec.is_satisfied_by(inactive_user) == True
        print("✅ AttributeInRangeSpecification")
        
        # 活跃状态规约
        active_spec = IsActiveSpecification()
        assert active_spec.is_satisfied_by(active_user) == True
        assert active_spec.is_satisfied_by(inactive_user) == False
        print("✅ IsActiveSpecification")
        
        # 令牌有效性规约
        token_spec = TokenValiditySpecification()
        assert token_spec.is_satisfied_by(valid_token) == True
        assert token_spec.is_satisfied_by(expired_token) == False
        print("✅ TokenValiditySpecification")
        
        # 测试组合规约
        print("\n📋 测试组合规约:")
        
        # AND规约
        and_spec_test = and_spec(name_spec, active_spec)
        assert and_spec_test.is_satisfied_by(active_user) == True
        assert and_spec_test.is_satisfied_by(inactive_user) == False
        print("✅ AndSpecification")
        
        # OR规约
        or_spec_test = or_spec(
            AttributeEqualsSpecification("name", "Alice"),
            AttributeEqualsSpecification("name", "Bob")
        )
        assert or_spec_test.is_satisfied_by(active_user) == True
        assert or_spec_test.is_satisfied_by(inactive_user) == True
        print("✅ OrSpecification")
        
        # NOT规约
        not_spec_test = not_spec(active_spec)
        assert not_spec_test.is_satisfied_by(active_user) == False
        assert not_spec_test.is_satisfied_by(inactive_user) == True
        print("✅ NotSpecification")
        
        # 测试常用规约
        print("\n📋 测试常用规约:")
        
        # 活跃用户规约
        active_user_spec = CommonSpecifications.active_user()
        assert active_user_spec.is_satisfied_by(active_user) == True
        assert active_user_spec.is_satisfied_by(inactive_user) == False
        print("✅ CommonSpecifications.active_user")
        
        # 有效令牌规约
        valid_token_spec = CommonSpecifications.valid_token()
        assert valid_token_spec.is_satisfied_by(valid_token) == True
        assert valid_token_spec.is_satisfied_by(expired_token) == False
        print("✅ CommonSpecifications.valid_token")
        
        print("\n🎯 规约模式测试结果:")
        print("✅ 所有基础规约测试通过")
        print("✅ 所有组合规约测试通过")
        print("✅ 所有常用规约测试通过")
        print("✅ 规约模式实现完整且功能正常")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except AssertionError as e:
        print(f"❌ 断言失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_specification_integration():
    """测试规约模式集成"""
    print("\n🔍 测试规约模式集成")
    print("-" * 30)
    
    try:
        # 测试从共享模块导入
        from src.domain.shared import (
            Specification, AndSpecification, CommonSpecifications
        )
        
        print("✅ 从共享模块导入成功")
        
        # 测试基类方法
        spec1 = CommonSpecifications.active_user()
        spec2 = CommonSpecifications.valid_token()
        
        # 测试链式调用
        combined_spec = spec1.and_(spec2)
        print("✅ 规约链式调用成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 规约模式实现测试")
    print("=" * 60)
    
    # 测试规约实现
    impl_result = test_specification_implementations()
    
    # 测试集成
    integration_result = test_specification_integration()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"   规约实现测试: {'✅ 通过' if impl_result else '❌ 失败'}")
    print(f"   集成测试: {'✅ 通过' if integration_result else '❌ 失败'}")
    
    if impl_result and integration_result:
        print("\n🎉 规约模式实现完成！")
        print("\n💡 实现内容:")
        print("   1. 创建了完整的规约模式基础设施")
        print("   2. 实现了常用的具体规约类")
        print("   3. 提供了便利的组合方法")
        print("   4. 创建了预定义的常用规约")
        print("   5. 完善了模块导入和集成")
        print("   6. 移除了抽象基类的NotImplementedError")
        
        return 0
    else:
        print("\n❌ 规约模式实现需要进一步调试")
        return 1

if __name__ == "__main__":
    sys.exit(main())
