"""
数据库配置和连接管理
"""
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from sqlalchemy import create_engine, event
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Session, sessionmaker
from sqlalchemy.pool import StaticPool

from ..config import settings
from ..config.logging import get_logger

logger = get_logger(__name__)

# 创建基础模型类
Base = declarative_base()

# 同步数据库引擎和会话
engine = create_engine(
    settings.database_url,
    echo=settings.database_echo,
    pool_pre_ping=True,
    # SQLite 特殊配置
    poolclass=StaticPool if settings.database_url.startswith("sqlite") else None,
    connect_args={"check_same_thread": False} if settings.database_url.startswith("sqlite") else {},
)

SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine,
)

# 异步数据库引擎和会话 (如果需要)
async_engine = None
AsyncSessionLocal = None

if not settings.database_url.startswith("sqlite"):
    # 将同步数据库URL转换为异步URL
    async_database_url = settings.database_url.replace("postgresql://", "postgresql+asyncpg://")
    
    async_engine = create_async_engine(
        async_database_url,
        echo=settings.database_echo,
        pool_pre_ping=True,
    )
    
    AsyncSessionLocal = async_sessionmaker(
        async_engine,
        class_=AsyncSession,
        expire_on_commit=False,
    )


def get_db() -> Session:
    """获取数据库会话 (同步)"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


@asynccontextmanager
async def get_async_db() -> AsyncGenerator[AsyncSession, None]:
    """获取数据库会话 (异步)"""
    if AsyncSessionLocal is None:
        raise RuntimeError("异步数据库会话未配置")
    
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()


def create_tables() -> None:
    """创建所有数据库表"""
    logger.info("创建数据库表")
    Base.metadata.create_all(bind=engine)


def drop_tables() -> None:
    """删除所有数据库表"""
    logger.warning("删除数据库表")
    Base.metadata.drop_all(bind=engine)


async def create_async_tables() -> None:
    """创建所有数据库表 (异步)"""
    if async_engine is None:
        raise RuntimeError("异步数据库引擎未配置")
    
    logger.info("创建数据库表 (异步)")
    async with async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)


async def drop_async_tables() -> None:
    """删除所有数据库表 (异步)"""
    if async_engine is None:
        raise RuntimeError("异步数据库引擎未配置")
    
    logger.warning("删除数据库表 (异步)")
    async with async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)


# 数据库事件监听器
@event.listens_for(engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    """为SQLite设置PRAGMA"""
    if settings.database_url.startswith("sqlite"):
        cursor = dbapi_connection.cursor()
        cursor.execute("PRAGMA foreign_keys=ON")
        cursor.close()


@event.listens_for(engine, "before_cursor_execute")
def receive_before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    """记录SQL执行前的日志"""
    if settings.debug and settings.database_echo:
        logger.debug("执行SQL", statement=statement, parameters=parameters)


@event.listens_for(engine, "after_cursor_execute")
def receive_after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    """记录SQL执行后的日志"""
    if settings.debug and settings.database_echo:
        logger.debug("SQL执行完成", statement=statement, rowcount=cursor.rowcount)


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.engine = engine
        self.async_engine = async_engine
        self.session_local = SessionLocal
        self.async_session_local = AsyncSessionLocal
    
    def get_session(self) -> Session:
        """获取同步会话"""
        return self.session_local()
    
    async def get_async_session(self) -> AsyncSession:
        """获取异步会话"""
        if self.async_session_local is None:
            raise RuntimeError("异步数据库会话未配置")
        return self.async_session_local()
    
    def health_check(self) -> bool:
        """数据库健康检查"""
        try:
            with self.get_session() as session:
                from sqlalchemy import text
                session.execute(text("SELECT 1"))
                return True
        except Exception as e:
            logger.error(f"数据库健康检查失败: {str(e)}")
            return False
    
    async def async_health_check(self) -> bool:
        """异步数据库健康检查"""
        if self.async_engine is None:
            return self.health_check()
        
        try:
            async with self.get_async_session() as session:
                from sqlalchemy import text
                await session.execute(text("SELECT 1"))
                return True
        except Exception as e:
            logger.error(f"异步数据库健康检查失败: {str(e)}")
            return False


# 创建数据库管理器实例
db_manager = DatabaseManager()
