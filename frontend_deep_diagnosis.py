#!/usr/bin/env python3
"""
前端深度诊断和修复工具
"""
import subprocess
import time
import os
import json
import sys
from pathlib import Path
import urllib.request

def check_node_environment():
    """检查Node.js环境"""
    print("🔍 Node.js环境检查")
    print("-" * 40)
    
    # 检查Node.js版本
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            node_version = result.stdout.strip()
            print(f"   ✅ Node.js版本: {node_version}")
            
            # 检查版本兼容性
            version_num = node_version.replace('v', '').split('.')[0]
            if int(version_num) >= 16:
                print("   ✅ Node.js版本兼容")
            else:
                print(f"   ⚠️  Node.js版本可能过低，建议使用v16+")
        else:
            print("   ❌ Node.js未安装或不可用")
            return False
    except Exception as e:
        print(f"   ❌ Node.js检查失败: {e}")
        return False
    
    # 检查npm版本和路径
    npm_commands = ['npm', 'npm.cmd', r'C:\Program Files\nodejs\npm.cmd']
    working_npm = None
    
    for npm_cmd in npm_commands:
        try:
            result = subprocess.run([npm_cmd, '--version'], capture_output=True, text=True, timeout=10, shell=True)
            if result.returncode == 0:
                npm_version = result.stdout.strip()
                print(f"   ✅ {npm_cmd}: {npm_version}")
                if not working_npm:
                    working_npm = npm_cmd
            else:
                print(f"   ❌ {npm_cmd}: 不可用")
        except Exception as e:
            print(f"   ❌ {npm_cmd}: {e}")
    
    if working_npm:
        print(f"   📋 推荐使用: {working_npm}")
        return working_npm
    else:
        print("   ❌ 没有找到可用的npm命令")
        return False

def check_frontend_structure():
    """检查前端项目结构"""
    print("\n📁 前端项目结构检查")
    print("-" * 40)
    
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("   ❌ frontend目录不存在")
        return False
    
    # 检查关键文件
    critical_files = [
        "package.json",
        "vite.config.ts",
        "tsconfig.json",
        "src/main.tsx",
        "src/App.tsx"
    ]
    
    for file_path in critical_files:
        file_full_path = frontend_dir / file_path
        if file_full_path.exists():
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} 缺失")
    
    # 检查node_modules
    node_modules = frontend_dir / "node_modules"
    if node_modules.exists():
        print("   ✅ node_modules存在")
        
        # 检查关键依赖
        key_deps = ["react", "vite", "@vitejs/plugin-react", "typescript"]
        for dep in key_deps:
            dep_path = node_modules / dep
            if dep_path.exists():
                print(f"   ✅ 依赖 {dep}")
            else:
                print(f"   ❌ 依赖 {dep} 缺失")
    else:
        print("   ❌ node_modules不存在，需要运行npm install")
        return False
    
    return True

def analyze_package_json():
    """分析package.json配置"""
    print("\n📄 package.json分析")
    print("-" * 40)
    
    package_json_path = Path("frontend/package.json")
    if not package_json_path.exists():
        print("   ❌ package.json不存在")
        return False
    
    try:
        with open(package_json_path, 'r', encoding='utf-8') as f:
            pkg = json.load(f)
        
        # 检查scripts
        scripts = pkg.get('scripts', {})
        print(f"   📋 可用脚本: {list(scripts.keys())}")
        
        if 'dev' in scripts:
            print(f"   ✅ dev脚本: {scripts['dev']}")
        else:
            print("   ❌ 缺少dev脚本")
            return False
        
        # 检查依赖数量
        deps = pkg.get('dependencies', {})
        dev_deps = pkg.get('devDependencies', {})
        print(f"   📋 生产依赖: {len(deps)}个")
        print(f"   📋 开发依赖: {len(dev_deps)}个")
        
        # 检查关键依赖版本
        key_deps = {
            'react': deps.get('react'),
            'vite': dev_deps.get('vite'),
            'typescript': dev_deps.get('typescript')
        }
        
        for dep, version in key_deps.items():
            if version:
                print(f"   ✅ {dep}: {version}")
            else:
                print(f"   ⚠️  {dep}: 未找到")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 解析package.json失败: {e}")
        return False

def test_frontend_build():
    """测试前端构建"""
    print("\n🔨 前端构建测试")
    print("-" * 40)
    
    frontend_dir = Path("frontend")
    working_npm = check_node_environment()
    
    if not working_npm:
        print("   ❌ npm不可用，跳过构建测试")
        return False
    
    try:
        # 尝试类型检查
        print("   🔍 TypeScript类型检查...")
        result = subprocess.run(
            [working_npm, 'run', 'type-check'],
            cwd=frontend_dir,
            capture_output=True,
            text=True,
            timeout=60,
            shell=True
        )
        
        if result.returncode == 0:
            print("   ✅ TypeScript类型检查通过")
        else:
            print("   ❌ TypeScript类型检查失败")
            print(f"   📋 错误信息: {result.stderr[:200]}...")
            return False
        
        return True
        
    except subprocess.TimeoutExpired:
        print("   ❌ 构建测试超时")
        return False
    except Exception as e:
        print(f"   ❌ 构建测试失败: {e}")
        return False

def start_frontend_with_detailed_output():
    """启动前端并显示详细输出"""
    print("\n🚀 启动前端服务器（详细模式）")
    print("-" * 40)
    
    frontend_dir = Path("frontend")
    working_npm = check_node_environment()
    
    if not working_npm:
        print("   ❌ npm不可用")
        return False
    
    try:
        print(f"   📋 使用命令: {working_npm} run dev")
        print(f"   📁 工作目录: {frontend_dir.absolute()}")
        print("   ⏳ 启动中...")
        
        # 启动前端进程
        process = subprocess.Popen(
            [working_npm, 'run', 'dev'],
            cwd=frontend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            shell=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # 实时读取输出
        startup_lines = []
        timeout = 30
        start_time = time.time()
        
        print("   📋 启动日志:")
        
        while time.time() - start_time < timeout:
            # 检查进程状态
            if process.poll() is not None:
                # 进程已退出
                remaining_output = process.stdout.read()
                if remaining_output:
                    print(f"   📋 剩余输出: {remaining_output}")
                
                print(f"   ❌ 前端进程已退出，返回码: {process.returncode}")
                return False
            
            # 读取一行输出
            try:
                line = process.stdout.readline()
                if line:
                    line = line.strip()
                    print(f"   📋 {line}")
                    startup_lines.append(line)
                    
                    # 检查启动成功标志
                    if "Local:" in line and "3000" in line:
                        print("\n   🎉 前端服务器启动成功！")
                        
                        # 测试连接
                        time.sleep(2)
                        try:
                            response = urllib.request.urlopen('http://localhost:3000', timeout=5)
                            if response.getcode() == 200:
                                print("   ✅ 前端服务器响应正常")
                                return process
                            else:
                                print(f"   ⚠️  前端服务器响应异常: {response.getcode()}")
                        except Exception as e:
                            print(f"   ⚠️  前端服务器连接测试失败: {e}")
                        
                        return process
                    
                    # 检查错误信息
                    if any(keyword in line.lower() for keyword in ['error', 'failed', 'cannot', 'enoent']):
                        print(f"   ❌ 检测到错误: {line}")
                        
            except Exception as e:
                print(f"   ⚠️  读取输出异常: {e}")
                break
            
            time.sleep(0.1)
        
        # 超时处理
        print("   ❌ 前端启动超时")
        print("   📋 收集到的启动日志:")
        for line in startup_lines[-10:]:  # 显示最后10行
            print(f"      {line}")
        
        process.terminate()
        return False
        
    except Exception as e:
        print(f"   ❌ 启动前端失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 前端深度诊断工具")
    print("=" * 60)
    
    # 1. 检查Node.js环境
    node_ok = check_node_environment()
    
    # 2. 检查项目结构
    structure_ok = check_frontend_structure()
    
    # 3. 分析package.json
    package_ok = analyze_package_json()
    
    # 4. 测试构建
    build_ok = test_frontend_build() if node_ok and structure_ok and package_ok else False
    
    print("\n" + "=" * 60)
    print("📊 诊断结果:")
    print(f"   🔧 Node.js环境: {'✅' if node_ok else '❌'}")
    print(f"   📁 项目结构: {'✅' if structure_ok else '❌'}")
    print(f"   📄 package.json: {'✅' if package_ok else '❌'}")
    print(f"   🔨 构建测试: {'✅' if build_ok else '❌'}")
    
    if all([node_ok, structure_ok, package_ok, build_ok]):
        print("\n🎉 前端环境检查全部通过！")
        print("\n🚀 尝试启动前端服务器...")
        
        frontend_process = start_frontend_with_detailed_output()
        
        if frontend_process:
            print("\n✅ 前端启动成功！")
            print("📝 访问地址: http://localhost:3000")
            
            try:
                print("\n⏳ 前端服务器运行中...")
                print("   按 Ctrl+C 停止服务器")
                frontend_process.wait()
            except KeyboardInterrupt:
                print("\n🛑 停止前端服务器...")
                frontend_process.terminate()
                try:
                    frontend_process.wait(timeout=5)
                    print("✅ 前端服务器已停止")
                except subprocess.TimeoutExpired:
                    frontend_process.kill()
                    print("⚠️  强制终止前端服务器")
            
            return 0
        else:
            print("\n❌ 前端启动失败")
            return 1
    else:
        print("\n❌ 前端环境有问题，需要修复")
        
        # 提供修复建议
        print("\n💡 修复建议:")
        if not node_ok:
            print("   1. 安装或更新Node.js: https://nodejs.org/")
        if not structure_ok:
            print("   2. 检查前端项目文件完整性")
            print("   3. 运行: cd frontend && npm install")
        if not package_ok:
            print("   4. 检查package.json配置")
        if not build_ok:
            print("   5. 修复TypeScript类型错误")
        
        return 1

if __name__ == "__main__":
    sys.exit(main())
