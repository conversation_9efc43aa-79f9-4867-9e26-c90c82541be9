"""
智能缓存管理器 - 根据数据访问模式自动优化存储策略
"""
import asyncio
from datetime import datetime, timedelta
from typing import Any, Optional, Dict, List, Tuple
from enum import Enum
import hashlib
import json

from ..config import settings
from ..config.logging import get_logger
from .redis_cache import redis_cache
from .pickle_storage import pickle_storage
from .json_storage import json_storage

logger = get_logger(__name__)


class AccessPattern(Enum):
    """数据访问模式"""
    HOT = "hot"          # 热数据：高频访问，低延迟要求
    WARM = "warm"        # 温数据：中频访问，中等延迟
    COLD = "cold"        # 冷数据：低频访问，高延迟可接受
    ARCHIVE = "archive"  # 归档数据：很少访问，主要用于备份


class DataCategory(Enum):
    """数据分类"""
    REALTIME = "realtime"        # 实时数据：位置、在线状态
    FREQUENT = "frequent"        # 频繁数据：市场、邮件
    REGULAR = "regular"          # 常规数据：技能、资产
    DAILY = "daily"             # 每日数据：静态信息
    COMPUTED = "computed"        # 计算结果：分析、规划
    CONFIG = "config"           # 配置数据：设置、规则
    PERSISTENT = "persistent"    # 持久数据：核心业务


class SmartCacheManager:
    """智能缓存管理器"""
    
    def __init__(self):
        self.redis = redis_cache
        self.pickle = pickle_storage
        self.json = json_storage
        
        # 访问统计
        self.access_stats = {}
        self.last_cleanup = datetime.utcnow()
        
        # 缓存策略配置
        self.strategies = {
            DataCategory.REALTIME: {
                "primary": "redis",
                "ttl": 30,
                "max_size": 1000,
                "eviction": "lru"
            },
            DataCategory.FREQUENT: {
                "primary": "redis",
                "secondary": "pickle",
                "ttl": 300,
                "max_size": 5000,
                "eviction": "lru"
            },
            DataCategory.REGULAR: {
                "primary": "redis",
                "secondary": "pickle",
                "ttl": 3600,
                "max_size": 10000,
                "eviction": "lfu"
            },
            DataCategory.DAILY: {
                "primary": "pickle",
                "ttl": 86400,
                "compress": True,
                "max_size": 50000
            },
            DataCategory.COMPUTED: {
                "primary": "pickle",
                "secondary": "redis",
                "ttl": 1800,
                "compress": True,
                "max_size": 20000
            },
            DataCategory.CONFIG: {
                "primary": "json",
                "backup": True,
                "versioned": True,
                "persistent": True
            },
            DataCategory.PERSISTENT: {
                "primary": "database",
                "backup": "json",
                "persistent": True
            }
        }
    
    async def get(self, key: str, category: DataCategory, 
                  character_id: Optional[int] = None) -> Optional[Any]:
        """智能获取数据"""
        try:
            # 记录访问统计
            self._record_access(key, category)
            
            # 获取缓存策略
            strategy = self.strategies.get(category, {})
            
            # 构建完整键名
            full_key = self._build_key(key, category, character_id)
            
            # 按优先级尝试获取数据
            data = None
            
            # 主存储
            primary = strategy.get("primary", "redis")
            if primary == "redis":
                data = await self.redis.get_json(full_key)
            elif primary == "pickle":
                data = self.pickle.load("cache", full_key, strategy.get("compress", False))
            elif primary == "json":
                data = self.json.load_config(full_key)
            
            if data is not None:
                logger.debug("从主存储获取数据", key=key, storage=primary)
                return data
            
            # 备用存储
            secondary = strategy.get("secondary")
            if secondary:
                if secondary == "redis":
                    data = await self.redis.get_json(full_key)
                elif secondary == "pickle":
                    data = self.pickle.load("cache", full_key, strategy.get("compress", False))
                
                if data is not None:
                    logger.debug("从备用存储获取数据", key=key, storage=secondary)
                    # 回写到主存储
                    await self._write_to_primary(full_key, data, strategy)
                    return data
            
            return None
            
        except Exception as e:
            logger.error("智能缓存获取失败", key=key, category=category.value, error=str(e))
            return None
    
    async def set(self, key: str, value: Any, category: DataCategory,
                  character_id: Optional[int] = None, ttl: Optional[int] = None) -> bool:
        """智能存储数据"""
        try:
            # 获取缓存策略
            strategy = self.strategies.get(category, {})
            
            # 构建完整键名
            full_key = self._build_key(key, category, character_id)
            
            # 确定TTL
            effective_ttl = ttl or strategy.get("ttl")
            
            # 主存储
            primary = strategy.get("primary", "redis")
            success = False
            
            if primary == "redis":
                success = await self.redis.set_json(full_key, value, effective_ttl)
            elif primary == "pickle":
                success = self.pickle.save("cache", full_key, value, 
                                         strategy.get("compress", False), effective_ttl)
            elif primary == "json":
                success = self.json.save_config(full_key, value, strategy.get("backup", False))
            
            # 备用存储
            secondary = strategy.get("secondary")
            if success and secondary:
                if secondary == "redis":
                    await self.redis.set_json(full_key, value, effective_ttl)
                elif secondary == "pickle":
                    self.pickle.save("cache", full_key, value, 
                                   strategy.get("compress", False), effective_ttl)
            
            if success:
                logger.debug("智能缓存存储成功", key=key, storage=primary)
            
            return success
            
        except Exception as e:
            logger.error("智能缓存存储失败", key=key, category=category.value, error=str(e))
            return False
    
    async def delete(self, key: str, category: DataCategory,
                     character_id: Optional[int] = None) -> bool:
        """删除缓存数据"""
        try:
            strategy = self.strategies.get(category, {})
            full_key = self._build_key(key, category, character_id)
            
            success = True
            
            # 删除主存储
            primary = strategy.get("primary", "redis")
            if primary == "redis":
                success &= await self.redis.delete(full_key)
            elif primary == "pickle":
                success &= self.pickle.delete("cache", full_key)
            
            # 删除备用存储
            secondary = strategy.get("secondary")
            if secondary:
                if secondary == "redis":
                    await self.redis.delete(full_key)
                elif secondary == "pickle":
                    self.pickle.delete("cache", full_key)
            
            return success
            
        except Exception as e:
            logger.error("智能缓存删除失败", key=key, category=category.value, error=str(e))
            return False
    
    async def invalidate_character_cache(self, character_id: int):
        """清除角色相关缓存"""
        try:
            patterns = [
                f"realtime:*:{character_id}",
                f"frequent:*:{character_id}",
                f"regular:*:{character_id}",
                f"computed:*:{character_id}"
            ]
            
            for pattern in patterns:
                await self.redis.clear_pattern(pattern)
            
            logger.info("角色缓存清除完成", character_id=character_id)
            
        except Exception as e:
            logger.error("清除角色缓存失败", character_id=character_id, error=str(e))
    
    async def optimize_cache(self):
        """缓存优化"""
        try:
            current_time = datetime.utcnow()
            
            # 每小时执行一次优化
            if (current_time - self.last_cleanup).total_seconds() < 3600:
                return
            
            logger.info("开始缓存优化")
            
            # 分析访问模式
            hot_keys, warm_keys, cold_keys = self._analyze_access_patterns()
            
            # 调整存储策略
            await self._adjust_storage_strategy(hot_keys, warm_keys, cold_keys)
            
            # 清理过期数据
            await self._cleanup_expired_data()
            
            # 压缩冷数据
            await self._compress_cold_data(cold_keys)
            
            self.last_cleanup = current_time
            logger.info("缓存优化完成")
            
        except Exception as e:
            logger.error("缓存优化失败", error=str(e))
    
    def _build_key(self, key: str, category: DataCategory, 
                   character_id: Optional[int] = None) -> str:
        """构建缓存键"""
        if character_id:
            return f"{category.value}:{key}:{character_id}"
        else:
            return f"{category.value}:{key}"
    
    def _record_access(self, key: str, category: DataCategory):
        """记录访问统计"""
        full_key = f"{category.value}:{key}"
        current_time = datetime.utcnow()
        
        if full_key not in self.access_stats:
            self.access_stats[full_key] = {
                "count": 0,
                "last_access": current_time,
                "first_access": current_time,
                "category": category
            }
        
        stats = self.access_stats[full_key]
        stats["count"] += 1
        stats["last_access"] = current_time
    
    def _analyze_access_patterns(self) -> Tuple[List[str], List[str], List[str]]:
        """分析访问模式"""
        current_time = datetime.utcnow()
        hot_keys = []
        warm_keys = []
        cold_keys = []
        
        for key, stats in self.access_stats.items():
            # 计算访问频率
            time_span = (current_time - stats["first_access"]).total_seconds()
            if time_span > 0:
                frequency = stats["count"] / time_span
                
                # 计算最近访问时间
                last_access_hours = (current_time - stats["last_access"]).total_seconds() / 3600
                
                # 分类数据
                if frequency > 0.01 and last_access_hours < 1:  # 高频且最近访问
                    hot_keys.append(key)
                elif frequency > 0.001 and last_access_hours < 24:  # 中频且一天内访问
                    warm_keys.append(key)
                else:
                    cold_keys.append(key)
        
        return hot_keys, warm_keys, cold_keys
    
    async def _adjust_storage_strategy(self, hot_keys: List[str], 
                                     warm_keys: List[str], cold_keys: List[str]):
        """调整存储策略"""
        # 热数据：确保在Redis中
        for key in hot_keys:
            if not await self.redis.exists(key):
                # 从其他存储迁移到Redis
                data = self.pickle.load("cache", key)
                if data:
                    await self.redis.set_json(key, data, 300)
        
        # 冷数据：迁移到Pickle存储
        for key in cold_keys:
            if await self.redis.exists(key):
                data = await self.redis.get_json(key)
                if data:
                    self.pickle.save("cache", key, data, True, 86400)
                    await self.redis.delete(key)
    
    async def _cleanup_expired_data(self):
        """清理过期数据"""
        # 清理Pickle过期文件
        self.pickle.cleanup_expired()
        
        # 清理JSON备份文件
        self.json.cleanup_old_backups(30)
    
    async def _compress_cold_data(self, cold_keys: List[str]):
        """压缩冷数据"""
        for key in cold_keys:
            data = self.pickle.load("cache", key, False)
            if data:
                # 重新保存为压缩格式
                self.pickle.save("cache", key, data, True)
                # 删除未压缩版本
                self.pickle.delete("cache", key, False)
    
    async def _write_to_primary(self, key: str, data: Any, strategy: Dict[str, Any]):
        """回写数据到主存储"""
        primary = strategy.get("primary", "redis")
        ttl = strategy.get("ttl")
        
        if primary == "redis":
            await self.redis.set_json(key, data, ttl)
        elif primary == "pickle":
            self.pickle.save("cache", key, data, strategy.get("compress", False), ttl)


# 全局智能缓存管理器
smart_cache = SmartCacheManager()
