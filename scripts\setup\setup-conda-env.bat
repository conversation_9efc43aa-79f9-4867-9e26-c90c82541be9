@echo off
echo 🚀 EVE Online Assistant - Conda环境设置
echo ========================================

echo 📋 检查conda安装...
conda --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Conda未安装或未添加到PATH
    echo 💡 请安装Anaconda或Miniconda: https://www.anaconda.com/
    pause
    exit /b 1
)

echo ✅ Conda已安装
conda --version

echo.
echo 🔧 创建专用conda环境 'eve-assistant'...
conda create -n eve-assistant python=3.11 -y

if %errorlevel% neq 0 (
    echo ❌ 环境创建失败
    pause
    exit /b 1
)

echo.
echo 🔄 激活环境...
call conda activate eve-assistant

echo.
echo 📦 安装项目依赖...
pip install -e .

if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败，尝试手动安装...
    pip install fastapi uvicorn python-dotenv pydantic pydantic-settings
    pip install structlog httpx sqlalchemy alembic pyjwt python-jose[cryptography] redis
)

echo.
echo 📦 安装开发依赖...
pip install pytest pytest-asyncio pytest-cov black isort mypy

echo.
echo ✅ 环境设置完成！
echo.
echo 📋 使用说明:
echo   1. 激活环境: conda activate eve-assistant
echo   2. 启动应用: python scripts/start.py
echo   3. 退出环境: conda deactivate
echo   4. 删除环境: conda remove -n eve-assistant --all
echo.
echo 🎯 环境信息:
conda info --envs
echo.
echo 📦 已安装的包:
pip list | findstr -i "fastapi uvicorn structlog"
echo.
echo 🎉 现在可以安全地在隔离环境中运行EVE Assistant了！
pause
