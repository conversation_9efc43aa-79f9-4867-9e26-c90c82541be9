(function(e){if(typeof exports=="object"&&typeof module=="object")module.exports=e();else if(typeof define=="function"&&define.amd)define(e);else{var i=typeof globalThis<"u"?globalThis:typeof global<"u"?global:typeof self<"u"?self:this||{};i.prettierPlugins=i.prettierPlugins||{},i.prettierPlugins.graphql=e()}})(function(){"use strict";var oe=(a,d)=>()=>(d||a((d={exports:{}}).exports,d),d.exports);var be=oe((Ce,ae)=>{var H=Object.getOwnPropertyNames,se=(a,d)=>function(){return a&&(d=(0,a[H(a)[0]])(a=0)),d},L=(a,d)=>function(){return d||(0,a[H(a)[0]])((d={exports:{}}).exports,d),d.exports},K=se({"<define:process>"(){}}),ce=L({"src/common/parser-create-error.js"(a,d){"use strict";K();function i(c,r){let _=new SyntaxError(c+" ("+r.start.line+":"+r.start.column+")");return _.loc=r,_}d.exports=i}}),ue=L({"src/utils/try-combinations.js"(a,d){"use strict";K();function i(){let c;for(var r=arguments.length,_=new Array(r),E=0;E<r;E++)_[E]=arguments[E];for(let[k,O]of _.entries())try{return{result:O()}}catch(A){k===0&&(c=A)}return{error:c}}d.exports=i}}),le=L({"src/language-graphql/pragma.js"(a,d){"use strict";K();function i(r){return/^\s*#[^\S\n]*@(?:format|prettier)\s*(?:\n|$)/.test(r)}function c(r){return`# @format

`+r}d.exports={hasPragma:i,insertPragma:c}}}),pe=L({"src/language-graphql/loc.js"(a,d){"use strict";K();function i(r){return typeof r.start=="number"?r.start:r.loc&&r.loc.start}function c(r){return typeof r.end=="number"?r.end:r.loc&&r.loc.end}d.exports={locStart:i,locEnd:c}}}),fe=L({"node_modules/graphql/jsutils/isObjectLike.js"(a){"use strict";K(),Object.defineProperty(a,"__esModule",{value:!0}),a.default=i;function d(c){return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?d=function(_){return typeof _}:d=function(_){return _&&typeof Symbol=="function"&&_.constructor===Symbol&&_!==Symbol.prototype?"symbol":typeof _},d(c)}function i(c){return d(c)=="object"&&c!==null}}}),z=L({"node_modules/graphql/polyfills/symbols.js"(a){"use strict";K(),Object.defineProperty(a,"__esModule",{value:!0}),a.SYMBOL_TO_STRING_TAG=a.SYMBOL_ASYNC_ITERATOR=a.SYMBOL_ITERATOR=void 0;var d=typeof Symbol=="function"&&Symbol.iterator!=null?Symbol.iterator:"@@iterator";a.SYMBOL_ITERATOR=d;var i=typeof Symbol=="function"&&Symbol.asyncIterator!=null?Symbol.asyncIterator:"@@asyncIterator";a.SYMBOL_ASYNC_ITERATOR=i;var c=typeof Symbol=="function"&&Symbol.toStringTag!=null?Symbol.toStringTag:"@@toStringTag";a.SYMBOL_TO_STRING_TAG=c}}),$=L({"node_modules/graphql/language/location.js"(a){"use strict";K(),Object.defineProperty(a,"__esModule",{value:!0}),a.getLocation=d;function d(i,c){for(var r=/\r\n|[\n\r]/g,_=1,E=c+1,k;(k=r.exec(i.body))&&k.index<c;)_+=1,E=c+1-(k.index+k[0].length);return{line:_,column:E}}}}),de=L({"node_modules/graphql/language/printLocation.js"(a){"use strict";K(),Object.defineProperty(a,"__esModule",{value:!0}),a.printLocation=i,a.printSourceLocation=c;var d=$();function i(k){return c(k.source,(0,d.getLocation)(k.source,k.start))}function c(k,O){var A=k.locationOffset.column-1,N=_(A)+k.body,g=O.line-1,D=k.locationOffset.line-1,v=O.line+D,I=O.line===1?A:0,s=O.column+I,p="".concat(k.name,":").concat(v,":").concat(s,`
`),e=N.split(/\r\n|[\n\r]/g),n=e[g];if(n.length>120){for(var t=Math.floor(s/80),u=s%80,y=[],f=0;f<n.length;f+=80)y.push(n.slice(f,f+80));return p+r([["".concat(v),y[0]]].concat(y.slice(1,t+1).map(function(m){return["",m]}),[[" ",_(u-1)+"^"],["",y[t+1]]]))}return p+r([["".concat(v-1),e[g-1]],["".concat(v),n],["",_(s-1)+"^"],["".concat(v+1),e[g+1]]])}function r(k){var O=k.filter(function(N){var g=N[0],D=N[1];return D!==void 0}),A=Math.max.apply(Math,O.map(function(N){var g=N[0];return g.length}));return O.map(function(N){var g=N[0],D=N[1];return E(A,g)+(D?" | "+D:" |")}).join(`
`)}function _(k){return Array(k+1).join(" ")}function E(k,O){return _(k-O.length)+O}}}),W=L({"node_modules/graphql/error/GraphQLError.js"(a){"use strict";K();function d(f){return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?d=function(o){return typeof o}:d=function(o){return o&&typeof Symbol=="function"&&o.constructor===Symbol&&o!==Symbol.prototype?"symbol":typeof o},d(f)}Object.defineProperty(a,"__esModule",{value:!0}),a.printError=y,a.GraphQLError=void 0;var i=E(fe()),c=z(),r=$(),_=de();function E(f){return f&&f.__esModule?f:{default:f}}function k(f,m){if(!(f instanceof m))throw new TypeError("Cannot call a class as a function")}function O(f,m){for(var o=0;o<m.length;o++){var h=m[o];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(f,h.key,h)}}function A(f,m,o){return m&&O(f.prototype,m),o&&O(f,o),f}function N(f,m){if(typeof m!="function"&&m!==null)throw new TypeError("Super expression must either be null or a function");f.prototype=Object.create(m&&m.prototype,{constructor:{value:f,writable:!0,configurable:!0}}),m&&n(f,m)}function g(f){var m=p();return function(){var h=t(f),l;if(m){var T=t(this).constructor;l=Reflect.construct(h,arguments,T)}else l=h.apply(this,arguments);return D(this,l)}}function D(f,m){return m&&(d(m)==="object"||typeof m=="function")?m:v(f)}function v(f){if(f===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return f}function I(f){var m=typeof Map=="function"?new Map:void 0;return I=function(h){if(h===null||!e(h))return h;if(typeof h!="function")throw new TypeError("Super expression must either be null or a function");if(typeof m<"u"){if(m.has(h))return m.get(h);m.set(h,l)}function l(){return s(h,arguments,t(this).constructor)}return l.prototype=Object.create(h.prototype,{constructor:{value:l,enumerable:!1,writable:!0,configurable:!0}}),n(l,h)},I(f)}function s(f,m,o){return p()?s=Reflect.construct:s=function(l,T,S){var x=[null];x.push.apply(x,T);var b=Function.bind.apply(l,x),M=new b;return S&&n(M,S.prototype),M},s.apply(null,arguments)}function p(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}function e(f){return Function.toString.call(f).indexOf("[native code]")!==-1}function n(f,m){return n=Object.setPrototypeOf||function(h,l){return h.__proto__=l,h},n(f,m)}function t(f){return t=Object.setPrototypeOf?Object.getPrototypeOf:function(o){return o.__proto__||Object.getPrototypeOf(o)},t(f)}var u=function(f){N(o,f);var m=g(o);function o(h,l,T,S,x,b,M){var U,V,q,G,C;k(this,o),C=m.call(this,h);var R=Array.isArray(l)?l.length!==0?l:void 0:l?[l]:void 0,Y=T;if(!Y&&R){var J;Y=(J=R[0].loc)===null||J===void 0?void 0:J.source}var F=S;!F&&R&&(F=R.reduce(function(w,P){return P.loc&&w.push(P.loc.start),w},[])),F&&F.length===0&&(F=void 0);var B;S&&T?B=S.map(function(w){return(0,r.getLocation)(T,w)}):R&&(B=R.reduce(function(w,P){return P.loc&&w.push((0,r.getLocation)(P.loc.source,P.loc.start)),w},[]));var j=M;if(j==null&&b!=null){var Q=b.extensions;(0,i.default)(Q)&&(j=Q)}return Object.defineProperties(v(C),{name:{value:"GraphQLError"},message:{value:h,enumerable:!0,writable:!0},locations:{value:(U=B)!==null&&U!==void 0?U:void 0,enumerable:B!=null},path:{value:x!=null?x:void 0,enumerable:x!=null},nodes:{value:R!=null?R:void 0},source:{value:(V=Y)!==null&&V!==void 0?V:void 0},positions:{value:(q=F)!==null&&q!==void 0?q:void 0},originalError:{value:b},extensions:{value:(G=j)!==null&&G!==void 0?G:void 0,enumerable:j!=null}}),b!=null&&b.stack?(Object.defineProperty(v(C),"stack",{value:b.stack,writable:!0,configurable:!0}),D(C)):(Error.captureStackTrace?Error.captureStackTrace(v(C),o):Object.defineProperty(v(C),"stack",{value:Error().stack,writable:!0,configurable:!0}),C)}return A(o,[{key:"toString",value:function(){return y(this)}},{key:c.SYMBOL_TO_STRING_TAG,get:function(){return"Object"}}]),o}(I(Error));a.GraphQLError=u;function y(f){var m=f.message;if(f.nodes)for(var o=0,h=f.nodes;o<h.length;o++){var l=h[o];l.loc&&(m+=`

`+(0,_.printLocation)(l.loc))}else if(f.source&&f.locations)for(var T=0,S=f.locations;T<S.length;T++){var x=S[T];m+=`

`+(0,_.printSourceLocation)(f.source,x)}return m}}}),Z=L({"node_modules/graphql/error/syntaxError.js"(a){"use strict";K(),Object.defineProperty(a,"__esModule",{value:!0}),a.syntaxError=i;var d=W();function i(c,r,_){return new d.GraphQLError("Syntax Error: ".concat(_),void 0,c,[r])}}}),he=L({"node_modules/graphql/language/kinds.js"(a){"use strict";K(),Object.defineProperty(a,"__esModule",{value:!0}),a.Kind=void 0;var d=Object.freeze({NAME:"Name",DOCUMENT:"Document",OPERATION_DEFINITION:"OperationDefinition",VARIABLE_DEFINITION:"VariableDefinition",SELECTION_SET:"SelectionSet",FIELD:"Field",ARGUMENT:"Argument",FRAGMENT_SPREAD:"FragmentSpread",INLINE_FRAGMENT:"InlineFragment",FRAGMENT_DEFINITION:"FragmentDefinition",VARIABLE:"Variable",INT:"IntValue",FLOAT:"FloatValue",STRING:"StringValue",BOOLEAN:"BooleanValue",NULL:"NullValue",ENUM:"EnumValue",LIST:"ListValue",OBJECT:"ObjectValue",OBJECT_FIELD:"ObjectField",DIRECTIVE:"Directive",NAMED_TYPE:"NamedType",LIST_TYPE:"ListType",NON_NULL_TYPE:"NonNullType",SCHEMA_DEFINITION:"SchemaDefinition",OPERATION_TYPE_DEFINITION:"OperationTypeDefinition",SCALAR_TYPE_DEFINITION:"ScalarTypeDefinition",OBJECT_TYPE_DEFINITION:"ObjectTypeDefinition",FIELD_DEFINITION:"FieldDefinition",INPUT_VALUE_DEFINITION:"InputValueDefinition",INTERFACE_TYPE_DEFINITION:"InterfaceTypeDefinition",UNION_TYPE_DEFINITION:"UnionTypeDefinition",ENUM_TYPE_DEFINITION:"EnumTypeDefinition",ENUM_VALUE_DEFINITION:"EnumValueDefinition",INPUT_OBJECT_TYPE_DEFINITION:"InputObjectTypeDefinition",DIRECTIVE_DEFINITION:"DirectiveDefinition",SCHEMA_EXTENSION:"SchemaExtension",SCALAR_TYPE_EXTENSION:"ScalarTypeExtension",OBJECT_TYPE_EXTENSION:"ObjectTypeExtension",INTERFACE_TYPE_EXTENSION:"InterfaceTypeExtension",UNION_TYPE_EXTENSION:"UnionTypeExtension",ENUM_TYPE_EXTENSION:"EnumTypeExtension",INPUT_OBJECT_TYPE_EXTENSION:"InputObjectTypeExtension"});a.Kind=d}}),ve=L({"node_modules/graphql/jsutils/invariant.js"(a){"use strict";K(),Object.defineProperty(a,"__esModule",{value:!0}),a.default=d;function d(i,c){var r=Boolean(i);if(!r)throw new Error(c!=null?c:"Unexpected invariant triggered.")}}}),ee=L({"node_modules/graphql/jsutils/nodejsCustomInspectSymbol.js"(a){"use strict";K(),Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var d=typeof Symbol=="function"&&typeof Symbol.for=="function"?Symbol.for("nodejs.util.inspect.custom"):void 0,i=d;a.default=i}}),Te=L({"node_modules/graphql/jsutils/defineInspect.js"(a){"use strict";K(),Object.defineProperty(a,"__esModule",{value:!0}),a.default=r;var d=c(ve()),i=c(ee());function c(_){return _&&_.__esModule?_:{default:_}}function r(_){var E=_.prototype.toJSON;typeof E=="function"||(0,d.default)(0),_.prototype.inspect=E,i.default&&(_.prototype[i.default]=E)}}}),te=L({"node_modules/graphql/language/ast.js"(a){"use strict";K(),Object.defineProperty(a,"__esModule",{value:!0}),a.isNode=_,a.Token=a.Location=void 0;var d=i(Te());function i(E){return E&&E.__esModule?E:{default:E}}var c=function(){function E(O,A,N){this.start=O.start,this.end=A.end,this.startToken=O,this.endToken=A,this.source=N}var k=E.prototype;return k.toJSON=function(){return{start:this.start,end:this.end}},E}();a.Location=c,(0,d.default)(c);var r=function(){function E(O,A,N,g,D,v,I){this.kind=O,this.start=A,this.end=N,this.line=g,this.column=D,this.value=I,this.prev=v,this.next=null}var k=E.prototype;return k.toJSON=function(){return{kind:this.kind,value:this.value,line:this.line,column:this.column}},E}();a.Token=r,(0,d.default)(r);function _(E){return E!=null&&typeof E.kind=="string"}}}),ne=L({"node_modules/graphql/language/tokenKind.js"(a){"use strict";K(),Object.defineProperty(a,"__esModule",{value:!0}),a.TokenKind=void 0;var d=Object.freeze({SOF:"<SOF>",EOF:"<EOF>",BANG:"!",DOLLAR:"$",AMP:"&",PAREN_L:"(",PAREN_R:")",SPREAD:"...",COLON:":",EQUALS:"=",AT:"@",BRACKET_L:"[",BRACKET_R:"]",BRACE_L:"{",PIPE:"|",BRACE_R:"}",NAME:"Name",INT:"Int",FLOAT:"Float",STRING:"String",BLOCK_STRING:"BlockString",COMMENT:"Comment"});a.TokenKind=d}}),re=L({"node_modules/graphql/jsutils/inspect.js"(a){"use strict";K(),Object.defineProperty(a,"__esModule",{value:!0}),a.default=E;var d=i(ee());function i(v){return v&&v.__esModule?v:{default:v}}function c(v){return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?c=function(s){return typeof s}:c=function(s){return s&&typeof Symbol=="function"&&s.constructor===Symbol&&s!==Symbol.prototype?"symbol":typeof s},c(v)}var r=10,_=2;function E(v){return k(v,[])}function k(v,I){switch(c(v)){case"string":return JSON.stringify(v);case"function":return v.name?"[function ".concat(v.name,"]"):"[function]";case"object":return v===null?"null":O(v,I);default:return String(v)}}function O(v,I){if(I.indexOf(v)!==-1)return"[Circular]";var s=[].concat(I,[v]),p=g(v);if(p!==void 0){var e=p.call(v);if(e!==v)return typeof e=="string"?e:k(e,s)}else if(Array.isArray(v))return N(v,s);return A(v,s)}function A(v,I){var s=Object.keys(v);if(s.length===0)return"{}";if(I.length>_)return"["+D(v)+"]";var p=s.map(function(e){var n=k(v[e],I);return e+": "+n});return"{ "+p.join(", ")+" }"}function N(v,I){if(v.length===0)return"[]";if(I.length>_)return"[Array]";for(var s=Math.min(r,v.length),p=v.length-s,e=[],n=0;n<s;++n)e.push(k(v[n],I));return p===1?e.push("... 1 more item"):p>1&&e.push("... ".concat(p," more items")),"["+e.join(", ")+"]"}function g(v){var I=v[String(d.default)];if(typeof I=="function")return I;if(typeof v.inspect=="function")return v.inspect}function D(v){var I=Object.prototype.toString.call(v).replace(/^\[object /,"").replace(/]$/,"");if(I==="Object"&&typeof v.constructor=="function"){var s=v.constructor.name;if(typeof s=="string"&&s!=="")return s}return I}}}),_e=L({"node_modules/graphql/jsutils/devAssert.js"(a){"use strict";K(),Object.defineProperty(a,"__esModule",{value:!0}),a.default=d;function d(i,c){var r=Boolean(i);if(!r)throw new Error(c)}}}),Ee=L({"node_modules/graphql/jsutils/instanceOf.js"(a){"use strict";K(),Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var d=i(re());function i(r){return r&&r.__esModule?r:{default:r}}var c=function(_,E){return _ instanceof E};a.default=c}}),me=L({"node_modules/graphql/language/source.js"(a){"use strict";K(),Object.defineProperty(a,"__esModule",{value:!0}),a.isSource=A,a.Source=void 0;var d=z(),i=_(re()),c=_(_e()),r=_(Ee());function _(N){return N&&N.__esModule?N:{default:N}}function E(N,g){for(var D=0;D<g.length;D++){var v=g[D];v.enumerable=v.enumerable||!1,v.configurable=!0,"value"in v&&(v.writable=!0),Object.defineProperty(N,v.key,v)}}function k(N,g,D){return g&&E(N.prototype,g),D&&E(N,D),N}var O=function(){function N(g){var D=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"GraphQL request",v=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{line:1,column:1};typeof g=="string"||(0,c.default)(0,"Body must be a string. Received: ".concat((0,i.default)(g),".")),this.body=g,this.name=D,this.locationOffset=v,this.locationOffset.line>0||(0,c.default)(0,"line in locationOffset is 1-indexed and must be positive."),this.locationOffset.column>0||(0,c.default)(0,"column in locationOffset is 1-indexed and must be positive.")}return k(N,[{key:d.SYMBOL_TO_STRING_TAG,get:function(){return"Source"}}]),N}();a.Source=O;function A(N){return(0,r.default)(N,O)}}}),ye=L({"node_modules/graphql/language/directiveLocation.js"(a){"use strict";K(),Object.defineProperty(a,"__esModule",{value:!0}),a.DirectiveLocation=void 0;var d=Object.freeze({QUERY:"QUERY",MUTATION:"MUTATION",SUBSCRIPTION:"SUBSCRIPTION",FIELD:"FIELD",FRAGMENT_DEFINITION:"FRAGMENT_DEFINITION",FRAGMENT_SPREAD:"FRAGMENT_SPREAD",INLINE_FRAGMENT:"INLINE_FRAGMENT",VARIABLE_DEFINITION:"VARIABLE_DEFINITION",SCHEMA:"SCHEMA",SCALAR:"SCALAR",OBJECT:"OBJECT",FIELD_DEFINITION:"FIELD_DEFINITION",ARGUMENT_DEFINITION:"ARGUMENT_DEFINITION",INTERFACE:"INTERFACE",UNION:"UNION",ENUM:"ENUM",ENUM_VALUE:"ENUM_VALUE",INPUT_OBJECT:"INPUT_OBJECT",INPUT_FIELD_DEFINITION:"INPUT_FIELD_DEFINITION"});a.DirectiveLocation=d}}),ke=L({"node_modules/graphql/language/blockString.js"(a){"use strict";K(),Object.defineProperty(a,"__esModule",{value:!0}),a.dedentBlockStringValue=d,a.getBlockStringIndentation=c,a.printBlockString=r;function d(_){var E=_.split(/\r\n|[\n\r]/g),k=c(_);if(k!==0)for(var O=1;O<E.length;O++)E[O]=E[O].slice(k);for(var A=0;A<E.length&&i(E[A]);)++A;for(var N=E.length;N>A&&i(E[N-1]);)--N;return E.slice(A,N).join(`
`)}function i(_){for(var E=0;E<_.length;++E)if(_[E]!==" "&&_[E]!=="	")return!1;return!0}function c(_){for(var E,k=!0,O=!0,A=0,N=null,g=0;g<_.length;++g)switch(_.charCodeAt(g)){case 13:_.charCodeAt(g+1)===10&&++g;case 10:k=!1,O=!0,A=0;break;case 9:case 32:++A;break;default:O&&!k&&(N===null||A<N)&&(N=A),O=!1}return(E=N)!==null&&E!==void 0?E:0}function r(_){var E=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",k=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,O=_.indexOf(`
`)===-1,A=_[0]===" "||_[0]==="	",N=_[_.length-1]==='"',g=_[_.length-1]==="\\",D=!O||N||g||k,v="";return D&&!(O&&A)&&(v+=`
`+E),v+=E?_.replace(/\n/g,`
`+E):_,D&&(v+=`
`),'"""'+v.replace(/"""/g,'\\"""')+'"""'}}}),Ne=L({"node_modules/graphql/language/lexer.js"(a){"use strict";K(),Object.defineProperty(a,"__esModule",{value:!0}),a.isPunctuatorTokenKind=E,a.Lexer=void 0;var d=Z(),i=te(),c=ne(),r=ke(),_=function(){function t(y){var f=new i.Token(c.TokenKind.SOF,0,0,0,0,null);this.source=y,this.lastToken=f,this.token=f,this.line=1,this.lineStart=0}var u=t.prototype;return u.advance=function(){this.lastToken=this.token;var f=this.token=this.lookahead();return f},u.lookahead=function(){var f=this.token;if(f.kind!==c.TokenKind.EOF)do{var m;f=(m=f.next)!==null&&m!==void 0?m:f.next=O(this,f)}while(f.kind===c.TokenKind.COMMENT);return f},t}();a.Lexer=_;function E(t){return t===c.TokenKind.BANG||t===c.TokenKind.DOLLAR||t===c.TokenKind.AMP||t===c.TokenKind.PAREN_L||t===c.TokenKind.PAREN_R||t===c.TokenKind.SPREAD||t===c.TokenKind.COLON||t===c.TokenKind.EQUALS||t===c.TokenKind.AT||t===c.TokenKind.BRACKET_L||t===c.TokenKind.BRACKET_R||t===c.TokenKind.BRACE_L||t===c.TokenKind.PIPE||t===c.TokenKind.BRACE_R}function k(t){return isNaN(t)?c.TokenKind.EOF:t<127?JSON.stringify(String.fromCharCode(t)):'"\\u'.concat(("00"+t.toString(16).toUpperCase()).slice(-4),'"')}function O(t,u){for(var y=t.source,f=y.body,m=f.length,o=u.end;o<m;){var h=f.charCodeAt(o),l=t.line,T=1+o-t.lineStart;switch(h){case 65279:case 9:case 32:case 44:++o;continue;case 10:++o,++t.line,t.lineStart=o;continue;case 13:f.charCodeAt(o+1)===10?o+=2:++o,++t.line,t.lineStart=o;continue;case 33:return new i.Token(c.TokenKind.BANG,o,o+1,l,T,u);case 35:return N(y,o,l,T,u);case 36:return new i.Token(c.TokenKind.DOLLAR,o,o+1,l,T,u);case 38:return new i.Token(c.TokenKind.AMP,o,o+1,l,T,u);case 40:return new i.Token(c.TokenKind.PAREN_L,o,o+1,l,T,u);case 41:return new i.Token(c.TokenKind.PAREN_R,o,o+1,l,T,u);case 46:if(f.charCodeAt(o+1)===46&&f.charCodeAt(o+2)===46)return new i.Token(c.TokenKind.SPREAD,o,o+3,l,T,u);break;case 58:return new i.Token(c.TokenKind.COLON,o,o+1,l,T,u);case 61:return new i.Token(c.TokenKind.EQUALS,o,o+1,l,T,u);case 64:return new i.Token(c.TokenKind.AT,o,o+1,l,T,u);case 91:return new i.Token(c.TokenKind.BRACKET_L,o,o+1,l,T,u);case 93:return new i.Token(c.TokenKind.BRACKET_R,o,o+1,l,T,u);case 123:return new i.Token(c.TokenKind.BRACE_L,o,o+1,l,T,u);case 124:return new i.Token(c.TokenKind.PIPE,o,o+1,l,T,u);case 125:return new i.Token(c.TokenKind.BRACE_R,o,o+1,l,T,u);case 34:return f.charCodeAt(o+1)===34&&f.charCodeAt(o+2)===34?I(y,o,l,T,u,t):v(y,o,l,T,u);case 45:case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return g(y,o,h,l,T,u);case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 95:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:return e(y,o,l,T,u)}throw(0,d.syntaxError)(y,o,A(h))}var S=t.line,x=1+o-t.lineStart;return new i.Token(c.TokenKind.EOF,m,m,S,x,u)}function A(t){return t<32&&t!==9&&t!==10&&t!==13?"Cannot contain the invalid character ".concat(k(t),"."):t===39?`Unexpected single quote character ('), did you mean to use a double quote (")?`:"Cannot parse the unexpected character ".concat(k(t),".")}function N(t,u,y,f,m){var o=t.body,h,l=u;do h=o.charCodeAt(++l);while(!isNaN(h)&&(h>31||h===9));return new i.Token(c.TokenKind.COMMENT,u,l,y,f,m,o.slice(u+1,l))}function g(t,u,y,f,m,o){var h=t.body,l=y,T=u,S=!1;if(l===45&&(l=h.charCodeAt(++T)),l===48){if(l=h.charCodeAt(++T),l>=48&&l<=57)throw(0,d.syntaxError)(t,T,"Invalid number, unexpected digit after 0: ".concat(k(l),"."))}else T=D(t,T,l),l=h.charCodeAt(T);if(l===46&&(S=!0,l=h.charCodeAt(++T),T=D(t,T,l),l=h.charCodeAt(T)),(l===69||l===101)&&(S=!0,l=h.charCodeAt(++T),(l===43||l===45)&&(l=h.charCodeAt(++T)),T=D(t,T,l),l=h.charCodeAt(T)),l===46||n(l))throw(0,d.syntaxError)(t,T,"Invalid number, expected digit but got: ".concat(k(l),"."));return new i.Token(S?c.TokenKind.FLOAT:c.TokenKind.INT,u,T,f,m,o,h.slice(u,T))}function D(t,u,y){var f=t.body,m=u,o=y;if(o>=48&&o<=57){do o=f.charCodeAt(++m);while(o>=48&&o<=57);return m}throw(0,d.syntaxError)(t,m,"Invalid number, expected digit but got: ".concat(k(o),"."))}function v(t,u,y,f,m){for(var o=t.body,h=u+1,l=h,T=0,S="";h<o.length&&!isNaN(T=o.charCodeAt(h))&&T!==10&&T!==13;){if(T===34)return S+=o.slice(l,h),new i.Token(c.TokenKind.STRING,u,h+1,y,f,m,S);if(T<32&&T!==9)throw(0,d.syntaxError)(t,h,"Invalid character within String: ".concat(k(T),"."));if(++h,T===92){switch(S+=o.slice(l,h-1),T=o.charCodeAt(h),T){case 34:S+='"';break;case 47:S+="/";break;case 92:S+="\\";break;case 98:S+="\b";break;case 102:S+="\f";break;case 110:S+=`
`;break;case 114:S+="\r";break;case 116:S+="	";break;case 117:{var x=s(o.charCodeAt(h+1),o.charCodeAt(h+2),o.charCodeAt(h+3),o.charCodeAt(h+4));if(x<0){var b=o.slice(h+1,h+5);throw(0,d.syntaxError)(t,h,"Invalid character escape sequence: \\u".concat(b,"."))}S+=String.fromCharCode(x),h+=4;break}default:throw(0,d.syntaxError)(t,h,"Invalid character escape sequence: \\".concat(String.fromCharCode(T),"."))}++h,l=h}}throw(0,d.syntaxError)(t,h,"Unterminated string.")}function I(t,u,y,f,m,o){for(var h=t.body,l=u+3,T=l,S=0,x="";l<h.length&&!isNaN(S=h.charCodeAt(l));){if(S===34&&h.charCodeAt(l+1)===34&&h.charCodeAt(l+2)===34)return x+=h.slice(T,l),new i.Token(c.TokenKind.BLOCK_STRING,u,l+3,y,f,m,(0,r.dedentBlockStringValue)(x));if(S<32&&S!==9&&S!==10&&S!==13)throw(0,d.syntaxError)(t,l,"Invalid character within String: ".concat(k(S),"."));S===10?(++l,++o.line,o.lineStart=l):S===13?(h.charCodeAt(l+1)===10?l+=2:++l,++o.line,o.lineStart=l):S===92&&h.charCodeAt(l+1)===34&&h.charCodeAt(l+2)===34&&h.charCodeAt(l+3)===34?(x+=h.slice(T,l)+'"""',l+=4,T=l):++l}throw(0,d.syntaxError)(t,l,"Unterminated string.")}function s(t,u,y,f){return p(t)<<12|p(u)<<8|p(y)<<4|p(f)}function p(t){return t>=48&&t<=57?t-48:t>=65&&t<=70?t-55:t>=97&&t<=102?t-87:-1}function e(t,u,y,f,m){for(var o=t.body,h=o.length,l=u+1,T=0;l!==h&&!isNaN(T=o.charCodeAt(l))&&(T===95||T>=48&&T<=57||T>=65&&T<=90||T>=97&&T<=122);)++l;return new i.Token(c.TokenKind.NAME,u,l,y,f,m,o.slice(u,l))}function n(t){return t===95||t>=65&&t<=90||t>=97&&t<=122}}}),Oe=L({"node_modules/graphql/language/parser.js"(a){"use strict";K(),Object.defineProperty(a,"__esModule",{value:!0}),a.parse=O,a.parseValue=A,a.parseType=N,a.Parser=void 0;var d=Z(),i=he(),c=te(),r=ne(),_=me(),E=ye(),k=Ne();function O(I,s){var p=new g(I,s);return p.parseDocument()}function A(I,s){var p=new g(I,s);p.expectToken(r.TokenKind.SOF);var e=p.parseValueLiteral(!1);return p.expectToken(r.TokenKind.EOF),e}function N(I,s){var p=new g(I,s);p.expectToken(r.TokenKind.SOF);var e=p.parseTypeReference();return p.expectToken(r.TokenKind.EOF),e}var g=function(){function I(p,e){var n=(0,_.isSource)(p)?p:new _.Source(p);this._lexer=new k.Lexer(n),this._options=e}var s=I.prototype;return s.parseName=function(){var e=this.expectToken(r.TokenKind.NAME);return{kind:i.Kind.NAME,value:e.value,loc:this.loc(e)}},s.parseDocument=function(){var e=this._lexer.token;return{kind:i.Kind.DOCUMENT,definitions:this.many(r.TokenKind.SOF,this.parseDefinition,r.TokenKind.EOF),loc:this.loc(e)}},s.parseDefinition=function(){if(this.peek(r.TokenKind.NAME))switch(this._lexer.token.value){case"query":case"mutation":case"subscription":return this.parseOperationDefinition();case"fragment":return this.parseFragmentDefinition();case"schema":case"scalar":case"type":case"interface":case"union":case"enum":case"input":case"directive":return this.parseTypeSystemDefinition();case"extend":return this.parseTypeSystemExtension()}else{if(this.peek(r.TokenKind.BRACE_L))return this.parseOperationDefinition();if(this.peekDescription())return this.parseTypeSystemDefinition()}throw this.unexpected()},s.parseOperationDefinition=function(){var e=this._lexer.token;if(this.peek(r.TokenKind.BRACE_L))return{kind:i.Kind.OPERATION_DEFINITION,operation:"query",name:void 0,variableDefinitions:[],directives:[],selectionSet:this.parseSelectionSet(),loc:this.loc(e)};var n=this.parseOperationType(),t;return this.peek(r.TokenKind.NAME)&&(t=this.parseName()),{kind:i.Kind.OPERATION_DEFINITION,operation:n,name:t,variableDefinitions:this.parseVariableDefinitions(),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet(),loc:this.loc(e)}},s.parseOperationType=function(){var e=this.expectToken(r.TokenKind.NAME);switch(e.value){case"query":return"query";case"mutation":return"mutation";case"subscription":return"subscription"}throw this.unexpected(e)},s.parseVariableDefinitions=function(){return this.optionalMany(r.TokenKind.PAREN_L,this.parseVariableDefinition,r.TokenKind.PAREN_R)},s.parseVariableDefinition=function(){var e=this._lexer.token;return{kind:i.Kind.VARIABLE_DEFINITION,variable:this.parseVariable(),type:(this.expectToken(r.TokenKind.COLON),this.parseTypeReference()),defaultValue:this.expectOptionalToken(r.TokenKind.EQUALS)?this.parseValueLiteral(!0):void 0,directives:this.parseDirectives(!0),loc:this.loc(e)}},s.parseVariable=function(){var e=this._lexer.token;return this.expectToken(r.TokenKind.DOLLAR),{kind:i.Kind.VARIABLE,name:this.parseName(),loc:this.loc(e)}},s.parseSelectionSet=function(){var e=this._lexer.token;return{kind:i.Kind.SELECTION_SET,selections:this.many(r.TokenKind.BRACE_L,this.parseSelection,r.TokenKind.BRACE_R),loc:this.loc(e)}},s.parseSelection=function(){return this.peek(r.TokenKind.SPREAD)?this.parseFragment():this.parseField()},s.parseField=function(){var e=this._lexer.token,n=this.parseName(),t,u;return this.expectOptionalToken(r.TokenKind.COLON)?(t=n,u=this.parseName()):u=n,{kind:i.Kind.FIELD,alias:t,name:u,arguments:this.parseArguments(!1),directives:this.parseDirectives(!1),selectionSet:this.peek(r.TokenKind.BRACE_L)?this.parseSelectionSet():void 0,loc:this.loc(e)}},s.parseArguments=function(e){var n=e?this.parseConstArgument:this.parseArgument;return this.optionalMany(r.TokenKind.PAREN_L,n,r.TokenKind.PAREN_R)},s.parseArgument=function(){var e=this._lexer.token,n=this.parseName();return this.expectToken(r.TokenKind.COLON),{kind:i.Kind.ARGUMENT,name:n,value:this.parseValueLiteral(!1),loc:this.loc(e)}},s.parseConstArgument=function(){var e=this._lexer.token;return{kind:i.Kind.ARGUMENT,name:this.parseName(),value:(this.expectToken(r.TokenKind.COLON),this.parseValueLiteral(!0)),loc:this.loc(e)}},s.parseFragment=function(){var e=this._lexer.token;this.expectToken(r.TokenKind.SPREAD);var n=this.expectOptionalKeyword("on");return!n&&this.peek(r.TokenKind.NAME)?{kind:i.Kind.FRAGMENT_SPREAD,name:this.parseFragmentName(),directives:this.parseDirectives(!1),loc:this.loc(e)}:{kind:i.Kind.INLINE_FRAGMENT,typeCondition:n?this.parseNamedType():void 0,directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet(),loc:this.loc(e)}},s.parseFragmentDefinition=function(){var e,n=this._lexer.token;return this.expectKeyword("fragment"),((e=this._options)===null||e===void 0?void 0:e.experimentalFragmentVariables)===!0?{kind:i.Kind.FRAGMENT_DEFINITION,name:this.parseFragmentName(),variableDefinitions:this.parseVariableDefinitions(),typeCondition:(this.expectKeyword("on"),this.parseNamedType()),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet(),loc:this.loc(n)}:{kind:i.Kind.FRAGMENT_DEFINITION,name:this.parseFragmentName(),typeCondition:(this.expectKeyword("on"),this.parseNamedType()),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet(),loc:this.loc(n)}},s.parseFragmentName=function(){if(this._lexer.token.value==="on")throw this.unexpected();return this.parseName()},s.parseValueLiteral=function(e){var n=this._lexer.token;switch(n.kind){case r.TokenKind.BRACKET_L:return this.parseList(e);case r.TokenKind.BRACE_L:return this.parseObject(e);case r.TokenKind.INT:return this._lexer.advance(),{kind:i.Kind.INT,value:n.value,loc:this.loc(n)};case r.TokenKind.FLOAT:return this._lexer.advance(),{kind:i.Kind.FLOAT,value:n.value,loc:this.loc(n)};case r.TokenKind.STRING:case r.TokenKind.BLOCK_STRING:return this.parseStringLiteral();case r.TokenKind.NAME:switch(this._lexer.advance(),n.value){case"true":return{kind:i.Kind.BOOLEAN,value:!0,loc:this.loc(n)};case"false":return{kind:i.Kind.BOOLEAN,value:!1,loc:this.loc(n)};case"null":return{kind:i.Kind.NULL,loc:this.loc(n)};default:return{kind:i.Kind.ENUM,value:n.value,loc:this.loc(n)}}case r.TokenKind.DOLLAR:if(!e)return this.parseVariable();break}throw this.unexpected()},s.parseStringLiteral=function(){var e=this._lexer.token;return this._lexer.advance(),{kind:i.Kind.STRING,value:e.value,block:e.kind===r.TokenKind.BLOCK_STRING,loc:this.loc(e)}},s.parseList=function(e){var n=this,t=this._lexer.token,u=function(){return n.parseValueLiteral(e)};return{kind:i.Kind.LIST,values:this.any(r.TokenKind.BRACKET_L,u,r.TokenKind.BRACKET_R),loc:this.loc(t)}},s.parseObject=function(e){var n=this,t=this._lexer.token,u=function(){return n.parseObjectField(e)};return{kind:i.Kind.OBJECT,fields:this.any(r.TokenKind.BRACE_L,u,r.TokenKind.BRACE_R),loc:this.loc(t)}},s.parseObjectField=function(e){var n=this._lexer.token,t=this.parseName();return this.expectToken(r.TokenKind.COLON),{kind:i.Kind.OBJECT_FIELD,name:t,value:this.parseValueLiteral(e),loc:this.loc(n)}},s.parseDirectives=function(e){for(var n=[];this.peek(r.TokenKind.AT);)n.push(this.parseDirective(e));return n},s.parseDirective=function(e){var n=this._lexer.token;return this.expectToken(r.TokenKind.AT),{kind:i.Kind.DIRECTIVE,name:this.parseName(),arguments:this.parseArguments(e),loc:this.loc(n)}},s.parseTypeReference=function(){var e=this._lexer.token,n;return this.expectOptionalToken(r.TokenKind.BRACKET_L)?(n=this.parseTypeReference(),this.expectToken(r.TokenKind.BRACKET_R),n={kind:i.Kind.LIST_TYPE,type:n,loc:this.loc(e)}):n=this.parseNamedType(),this.expectOptionalToken(r.TokenKind.BANG)?{kind:i.Kind.NON_NULL_TYPE,type:n,loc:this.loc(e)}:n},s.parseNamedType=function(){var e=this._lexer.token;return{kind:i.Kind.NAMED_TYPE,name:this.parseName(),loc:this.loc(e)}},s.parseTypeSystemDefinition=function(){var e=this.peekDescription()?this._lexer.lookahead():this._lexer.token;if(e.kind===r.TokenKind.NAME)switch(e.value){case"schema":return this.parseSchemaDefinition();case"scalar":return this.parseScalarTypeDefinition();case"type":return this.parseObjectTypeDefinition();case"interface":return this.parseInterfaceTypeDefinition();case"union":return this.parseUnionTypeDefinition();case"enum":return this.parseEnumTypeDefinition();case"input":return this.parseInputObjectTypeDefinition();case"directive":return this.parseDirectiveDefinition()}throw this.unexpected(e)},s.peekDescription=function(){return this.peek(r.TokenKind.STRING)||this.peek(r.TokenKind.BLOCK_STRING)},s.parseDescription=function(){if(this.peekDescription())return this.parseStringLiteral()},s.parseSchemaDefinition=function(){var e=this._lexer.token,n=this.parseDescription();this.expectKeyword("schema");var t=this.parseDirectives(!0),u=this.many(r.TokenKind.BRACE_L,this.parseOperationTypeDefinition,r.TokenKind.BRACE_R);return{kind:i.Kind.SCHEMA_DEFINITION,description:n,directives:t,operationTypes:u,loc:this.loc(e)}},s.parseOperationTypeDefinition=function(){var e=this._lexer.token,n=this.parseOperationType();this.expectToken(r.TokenKind.COLON);var t=this.parseNamedType();return{kind:i.Kind.OPERATION_TYPE_DEFINITION,operation:n,type:t,loc:this.loc(e)}},s.parseScalarTypeDefinition=function(){var e=this._lexer.token,n=this.parseDescription();this.expectKeyword("scalar");var t=this.parseName(),u=this.parseDirectives(!0);return{kind:i.Kind.SCALAR_TYPE_DEFINITION,description:n,name:t,directives:u,loc:this.loc(e)}},s.parseObjectTypeDefinition=function(){var e=this._lexer.token,n=this.parseDescription();this.expectKeyword("type");var t=this.parseName(),u=this.parseImplementsInterfaces(),y=this.parseDirectives(!0),f=this.parseFieldsDefinition();return{kind:i.Kind.OBJECT_TYPE_DEFINITION,description:n,name:t,interfaces:u,directives:y,fields:f,loc:this.loc(e)}},s.parseImplementsInterfaces=function(){var e;if(!this.expectOptionalKeyword("implements"))return[];if(((e=this._options)===null||e===void 0?void 0:e.allowLegacySDLImplementsInterfaces)===!0){var n=[];this.expectOptionalToken(r.TokenKind.AMP);do n.push(this.parseNamedType());while(this.expectOptionalToken(r.TokenKind.AMP)||this.peek(r.TokenKind.NAME));return n}return this.delimitedMany(r.TokenKind.AMP,this.parseNamedType)},s.parseFieldsDefinition=function(){var e;return((e=this._options)===null||e===void 0?void 0:e.allowLegacySDLEmptyFields)===!0&&this.peek(r.TokenKind.BRACE_L)&&this._lexer.lookahead().kind===r.TokenKind.BRACE_R?(this._lexer.advance(),this._lexer.advance(),[]):this.optionalMany(r.TokenKind.BRACE_L,this.parseFieldDefinition,r.TokenKind.BRACE_R)},s.parseFieldDefinition=function(){var e=this._lexer.token,n=this.parseDescription(),t=this.parseName(),u=this.parseArgumentDefs();this.expectToken(r.TokenKind.COLON);var y=this.parseTypeReference(),f=this.parseDirectives(!0);return{kind:i.Kind.FIELD_DEFINITION,description:n,name:t,arguments:u,type:y,directives:f,loc:this.loc(e)}},s.parseArgumentDefs=function(){return this.optionalMany(r.TokenKind.PAREN_L,this.parseInputValueDef,r.TokenKind.PAREN_R)},s.parseInputValueDef=function(){var e=this._lexer.token,n=this.parseDescription(),t=this.parseName();this.expectToken(r.TokenKind.COLON);var u=this.parseTypeReference(),y;this.expectOptionalToken(r.TokenKind.EQUALS)&&(y=this.parseValueLiteral(!0));var f=this.parseDirectives(!0);return{kind:i.Kind.INPUT_VALUE_DEFINITION,description:n,name:t,type:u,defaultValue:y,directives:f,loc:this.loc(e)}},s.parseInterfaceTypeDefinition=function(){var e=this._lexer.token,n=this.parseDescription();this.expectKeyword("interface");var t=this.parseName(),u=this.parseImplementsInterfaces(),y=this.parseDirectives(!0),f=this.parseFieldsDefinition();return{kind:i.Kind.INTERFACE_TYPE_DEFINITION,description:n,name:t,interfaces:u,directives:y,fields:f,loc:this.loc(e)}},s.parseUnionTypeDefinition=function(){var e=this._lexer.token,n=this.parseDescription();this.expectKeyword("union");var t=this.parseName(),u=this.parseDirectives(!0),y=this.parseUnionMemberTypes();return{kind:i.Kind.UNION_TYPE_DEFINITION,description:n,name:t,directives:u,types:y,loc:this.loc(e)}},s.parseUnionMemberTypes=function(){return this.expectOptionalToken(r.TokenKind.EQUALS)?this.delimitedMany(r.TokenKind.PIPE,this.parseNamedType):[]},s.parseEnumTypeDefinition=function(){var e=this._lexer.token,n=this.parseDescription();this.expectKeyword("enum");var t=this.parseName(),u=this.parseDirectives(!0),y=this.parseEnumValuesDefinition();return{kind:i.Kind.ENUM_TYPE_DEFINITION,description:n,name:t,directives:u,values:y,loc:this.loc(e)}},s.parseEnumValuesDefinition=function(){return this.optionalMany(r.TokenKind.BRACE_L,this.parseEnumValueDefinition,r.TokenKind.BRACE_R)},s.parseEnumValueDefinition=function(){var e=this._lexer.token,n=this.parseDescription(),t=this.parseName(),u=this.parseDirectives(!0);return{kind:i.Kind.ENUM_VALUE_DEFINITION,description:n,name:t,directives:u,loc:this.loc(e)}},s.parseInputObjectTypeDefinition=function(){var e=this._lexer.token,n=this.parseDescription();this.expectKeyword("input");var t=this.parseName(),u=this.parseDirectives(!0),y=this.parseInputFieldsDefinition();return{kind:i.Kind.INPUT_OBJECT_TYPE_DEFINITION,description:n,name:t,directives:u,fields:y,loc:this.loc(e)}},s.parseInputFieldsDefinition=function(){return this.optionalMany(r.TokenKind.BRACE_L,this.parseInputValueDef,r.TokenKind.BRACE_R)},s.parseTypeSystemExtension=function(){var e=this._lexer.lookahead();if(e.kind===r.TokenKind.NAME)switch(e.value){case"schema":return this.parseSchemaExtension();case"scalar":return this.parseScalarTypeExtension();case"type":return this.parseObjectTypeExtension();case"interface":return this.parseInterfaceTypeExtension();case"union":return this.parseUnionTypeExtension();case"enum":return this.parseEnumTypeExtension();case"input":return this.parseInputObjectTypeExtension()}throw this.unexpected(e)},s.parseSchemaExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("schema");var n=this.parseDirectives(!0),t=this.optionalMany(r.TokenKind.BRACE_L,this.parseOperationTypeDefinition,r.TokenKind.BRACE_R);if(n.length===0&&t.length===0)throw this.unexpected();return{kind:i.Kind.SCHEMA_EXTENSION,directives:n,operationTypes:t,loc:this.loc(e)}},s.parseScalarTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("scalar");var n=this.parseName(),t=this.parseDirectives(!0);if(t.length===0)throw this.unexpected();return{kind:i.Kind.SCALAR_TYPE_EXTENSION,name:n,directives:t,loc:this.loc(e)}},s.parseObjectTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("type");var n=this.parseName(),t=this.parseImplementsInterfaces(),u=this.parseDirectives(!0),y=this.parseFieldsDefinition();if(t.length===0&&u.length===0&&y.length===0)throw this.unexpected();return{kind:i.Kind.OBJECT_TYPE_EXTENSION,name:n,interfaces:t,directives:u,fields:y,loc:this.loc(e)}},s.parseInterfaceTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("interface");var n=this.parseName(),t=this.parseImplementsInterfaces(),u=this.parseDirectives(!0),y=this.parseFieldsDefinition();if(t.length===0&&u.length===0&&y.length===0)throw this.unexpected();return{kind:i.Kind.INTERFACE_TYPE_EXTENSION,name:n,interfaces:t,directives:u,fields:y,loc:this.loc(e)}},s.parseUnionTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("union");var n=this.parseName(),t=this.parseDirectives(!0),u=this.parseUnionMemberTypes();if(t.length===0&&u.length===0)throw this.unexpected();return{kind:i.Kind.UNION_TYPE_EXTENSION,name:n,directives:t,types:u,loc:this.loc(e)}},s.parseEnumTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("enum");var n=this.parseName(),t=this.parseDirectives(!0),u=this.parseEnumValuesDefinition();if(t.length===0&&u.length===0)throw this.unexpected();return{kind:i.Kind.ENUM_TYPE_EXTENSION,name:n,directives:t,values:u,loc:this.loc(e)}},s.parseInputObjectTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("input");var n=this.parseName(),t=this.parseDirectives(!0),u=this.parseInputFieldsDefinition();if(t.length===0&&u.length===0)throw this.unexpected();return{kind:i.Kind.INPUT_OBJECT_TYPE_EXTENSION,name:n,directives:t,fields:u,loc:this.loc(e)}},s.parseDirectiveDefinition=function(){var e=this._lexer.token,n=this.parseDescription();this.expectKeyword("directive"),this.expectToken(r.TokenKind.AT);var t=this.parseName(),u=this.parseArgumentDefs(),y=this.expectOptionalKeyword("repeatable");this.expectKeyword("on");var f=this.parseDirectiveLocations();return{kind:i.Kind.DIRECTIVE_DEFINITION,description:n,name:t,arguments:u,repeatable:y,locations:f,loc:this.loc(e)}},s.parseDirectiveLocations=function(){return this.delimitedMany(r.TokenKind.PIPE,this.parseDirectiveLocation)},s.parseDirectiveLocation=function(){var e=this._lexer.token,n=this.parseName();if(E.DirectiveLocation[n.value]!==void 0)return n;throw this.unexpected(e)},s.loc=function(e){var n;if(((n=this._options)===null||n===void 0?void 0:n.noLocation)!==!0)return new c.Location(e,this._lexer.lastToken,this._lexer.source)},s.peek=function(e){return this._lexer.token.kind===e},s.expectToken=function(e){var n=this._lexer.token;if(n.kind===e)return this._lexer.advance(),n;throw(0,d.syntaxError)(this._lexer.source,n.start,"Expected ".concat(v(e),", found ").concat(D(n),"."))},s.expectOptionalToken=function(e){var n=this._lexer.token;if(n.kind===e)return this._lexer.advance(),n},s.expectKeyword=function(e){var n=this._lexer.token;if(n.kind===r.TokenKind.NAME&&n.value===e)this._lexer.advance();else throw(0,d.syntaxError)(this._lexer.source,n.start,'Expected "'.concat(e,'", found ').concat(D(n),"."))},s.expectOptionalKeyword=function(e){var n=this._lexer.token;return n.kind===r.TokenKind.NAME&&n.value===e?(this._lexer.advance(),!0):!1},s.unexpected=function(e){var n=e!=null?e:this._lexer.token;return(0,d.syntaxError)(this._lexer.source,n.start,"Unexpected ".concat(D(n),"."))},s.any=function(e,n,t){this.expectToken(e);for(var u=[];!this.expectOptionalToken(t);)u.push(n.call(this));return u},s.optionalMany=function(e,n,t){if(this.expectOptionalToken(e)){var u=[];do u.push(n.call(this));while(!this.expectOptionalToken(t));return u}return[]},s.many=function(e,n,t){this.expectToken(e);var u=[];do u.push(n.call(this));while(!this.expectOptionalToken(t));return u},s.delimitedMany=function(e,n){this.expectOptionalToken(e);var t=[];do t.push(n.call(this));while(this.expectOptionalToken(e));return t},I}();a.Parser=g;function D(I){var s=I.value;return v(I.kind)+(s!=null?' "'.concat(s,'"'):"")}function v(I){return(0,k.isPunctuatorTokenKind)(I)?'"'.concat(I,'"'):I}}});K();var Ie=ce(),ge=ue(),{hasPragma:Se}=le(),{locStart:Ae,locEnd:De}=pe();function Ke(a){let d=[],{startToken:i}=a.loc,{next:c}=i;for(;c.kind!=="<EOF>";)c.kind==="Comment"&&(Object.assign(c,{column:c.column-1}),d.push(c)),c=c.next;return d}function ie(a){if(a&&typeof a=="object"){delete a.startToken,delete a.endToken,delete a.prev,delete a.next;for(let d in a)ie(a[d])}return a}var X={allowLegacySDLImplementsInterfaces:!1,experimentalFragmentVariables:!0};function Le(a){let{GraphQLError:d}=W();if(a instanceof d){let{message:i,locations:[c]}=a;return Ie(i,{start:c})}return a}function xe(a){let{parse:d}=Oe(),{result:i,error:c}=ge(()=>d(a,Object.assign({},X)),()=>d(a,Object.assign(Object.assign({},X),{},{allowLegacySDLImplementsInterfaces:!0})));if(!i)throw Le(c);return i.comments=Ke(i),ie(i),i}ae.exports={parsers:{graphql:{parse:xe,astFormat:"graphql",hasPragma:Se,locStart:Ae,locEnd:De}}}});return be();});