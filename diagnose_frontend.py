#!/usr/bin/env python3
"""
前端问题诊断脚本
"""
import subprocess
import os
import json
from pathlib import Path

def diagnose_frontend():
    """诊断前端问题"""
    print("🔧 前端问题诊断")
    print("=" * 50)
    
    frontend_dir = Path("frontend")
    
    # 检查前端目录
    print("📁 检查前端目录...")
    if not frontend_dir.exists():
        print("   ❌ frontend目录不存在")
        return False
    print("   ✅ frontend目录存在")
    
    # 检查package.json
    package_json = frontend_dir / "package.json"
    if not package_json.exists():
        print("   ❌ package.json不存在")
        return False
    print("   ✅ package.json存在")
    
    # 检查node_modules
    node_modules = frontend_dir / "node_modules"
    if not node_modules.exists():
        print("   ❌ node_modules不存在，需要运行 npm install")
        return False
    print("   ✅ node_modules存在")
    
    # 检查vite配置
    vite_config = frontend_dir / "vite.config.ts"
    if vite_config.exists():
        print("   ✅ vite.config.ts存在")
        try:
            with open(vite_config, 'r', encoding='utf-8') as f:
                content = f.read()
                if "3000" in content:
                    print("   ✅ 端口配置包含3000")
                else:
                    print("   ⚠️  端口配置可能有问题")
        except Exception as e:
            print(f"   ⚠️  读取vite配置失败: {e}")
    else:
        print("   ❌ vite.config.ts不存在")
    
    # 检查npm脚本
    try:
        with open(package_json, 'r', encoding='utf-8') as f:
            pkg = json.load(f)
            scripts = pkg.get('scripts', {})
            if 'dev' in scripts:
                print(f"   ✅ dev脚本: {scripts['dev']}")
            else:
                print("   ❌ 缺少dev脚本")
    except Exception as e:
        print(f"   ❌ 读取package.json失败: {e}")
    
    # 测试npm命令
    print("\n🧪 测试npm命令...")
    try:
        result = subprocess.run(
            ["npm", "--version"],
            cwd=frontend_dir,
            capture_output=True,
            text=True,
            timeout=10
        )
        if result.returncode == 0:
            print(f"   ✅ npm版本: {result.stdout.strip()}")
        else:
            print(f"   ❌ npm命令失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"   ❌ npm测试失败: {e}")
        return False
    
    # 尝试启动前端（详细输出）
    print("\n🚀 尝试启动前端服务器...")
    try:
        result = subprocess.run(
            ["npm", "run", "dev"],
            cwd=frontend_dir,
            capture_output=True,
            text=True,
            timeout=30
        )
        
        print(f"   返回码: {result.returncode}")
        if result.stdout:
            print(f"   标准输出: {result.stdout}")
        if result.stderr:
            print(f"   错误输出: {result.stderr}")
            
        if result.returncode != 0:
            print("   ❌ 前端启动失败")
            return False
        else:
            print("   ✅ 前端启动命令执行成功")
            
    except subprocess.TimeoutExpired:
        print("   ⚠️  前端启动超时（可能正常，因为dev服务器会持续运行）")
    except Exception as e:
        print(f"   ❌ 前端启动测试失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("🔍 前端问题诊断工具")
    print("=" * 60)
    
    if diagnose_frontend():
        print("\n🎉 前端诊断完成，大部分检查通过")
        print("\n💡 建议:")
        print("1. 手动在frontend目录中运行: npm run dev")
        print("2. 检查是否有端口占用问题")
        print("3. 查看详细的错误日志")
    else:
        print("\n❌ 前端诊断发现问题")
        print("\n💡 建议:")
        print("1. 重新安装前端依赖: cd frontend && npm install")
        print("2. 检查Node.js版本是否兼容")
        print("3. 查看上面的错误信息进行修复")
    
    return 0

if __name__ == "__main__":
    exit(main())
