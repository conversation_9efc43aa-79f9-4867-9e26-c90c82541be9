"""
简化版健康检查路由 - 用于基本应用启动
"""
from datetime import datetime
from fastapi import APIRouter
from typing import Dict, Any

router = APIRouter()


@router.get("/status", response_model=Dict[str, Any])
async def health_status():
    """简化版健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.0.0-simplified",
        "services": {
            "api": "healthy",
            "database": "not_configured",
            "eve_sso": "not_configured"
        },
        "message": "应用运行正常 - 简化版本"
    }


@router.get("/", response_model=Dict[str, Any])
async def health_check():
    """基础健康检查 - 重定向到status"""
    return await health_status()


@router.get("/ready")
async def readiness_check():
    """就绪检查"""
    return {
        "ready": True,
        "timestamp": datetime.utcnow().isoformat(),
        "message": "应用已就绪"
    }


@router.get("/live")
async def liveness_check():
    """存活检查"""
    return {
        "alive": True,
        "timestamp": datetime.utcnow().isoformat(),
        "message": "应用正在运行"
    }
