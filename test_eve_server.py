#!/usr/bin/env python3
"""
最小的EVE登录测试服务器
"""
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional
import uvicorn

app = FastAPI(title="EVE Login Test Server")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class LoginRequest(BaseModel):
    scopes: Optional[List[str]] = None
    redirect_url: Optional[str] = None

@app.get("/health")
async def health():
    return {"status": "ok", "message": "EVE Login Test Server is running"}

@app.post("/auth/login")
async def eve_login(request: LoginRequest):
    """EVE SSO登录接口"""
    print(f"收到EVE登录请求，scopes: {request.scopes}")
    
    # 返回模拟的EVE登录URL
    mock_login_url = "https://login.eveonline.com/v2/oauth/authorize?response_type=code&redirect_uri=http://localhost:3000/auth/callback&client_id=demo&scope=esi-characters.read_characters.v1&state=demo_state_123"
    
    return {
        "success": True,
        "data": {
            "login_url": mock_login_url,
            "state": "demo_state_123",
            "expires_in": 300,
            "scopes": request.scopes or ["esi-characters.read_characters.v1"]
        }
    }

@app.post("/auth/callback")
async def eve_callback():
    """EVE SSO回调接口"""
    return {
        "success": True,
        "data": {
            "access_token": "demo_access_token",
            "refresh_token": "demo_refresh_token",
            "token_type": "bearer",
            "expires_in": 3600,
            "character_id": 12345,
            "character_name": "Demo Character",
            "scopes": ["esi-characters.read_characters.v1"]
        }
    }

@app.get("/auth/me")
async def get_me():
    """获取当前用户信息"""
    return {
        "success": True,
        "data": {
            "id": "demo_user",
            "email": "<EMAIL>",
            "username": "demo",
            "isVerified": True,
            "characterCount": 1,
            "mainCharacterId": 12345,
            "createdAt": "2024-01-01T00:00:00Z",
            "updatedAt": "2024-01-01T00:00:00Z"
        }
    }

if __name__ == "__main__":
    print("Starting EVE Login Test Server on port 8000...")
    uvicorn.run(app, host="127.0.0.1", port=8000, log_level="info")
