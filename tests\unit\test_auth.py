"""
认证服务单元测试
"""
import pytest
from unittest.mock import Mock, patch, AsyncMock
from src.application.services.auth import AuthenticationService


@pytest.mark.unit
class TestAuthenticationService:
    """认证服务测试类"""
    
    @pytest.fixture
    def auth_service(self):
        """创建认证服务实例"""
        return AuthenticationService()
    
    def test_validate_eve_scopes_valid(self, auth_service, sample_eve_scopes):
        """测试有效的EVE权限验证"""
        result = auth_service.validate_eve_scopes(sample_eve_scopes)
        assert result is True
    
    def test_validate_eve_scopes_invalid(self, auth_service):
        """测试无效的EVE权限验证"""
        invalid_scopes = ["invalid-scope", "another-invalid-scope"]
        result = auth_service.validate_eve_scopes(invalid_scopes)
        assert result is False
    
    def test_validate_eve_scopes_empty(self, auth_service):
        """测试空权限列表"""
        result = auth_service.validate_eve_scopes([])
        assert result is False
    
    def test_validate_eve_scopes_mixed(self, auth_service):
        """测试混合权限（有效和无效）"""
        mixed_scopes = [
            "esi-characters.read_characters.v1",  # 有效
            "invalid-scope"  # 无效
        ]
        result = auth_service.validate_eve_scopes(mixed_scopes)
        assert result is False
    
    @patch('src.application.services.auth.uuid.uuid4')
    def test_generate_state_token(self, mock_uuid, auth_service):
        """测试状态令牌生成"""
        mock_uuid.return_value.hex = "test_state_token"
        
        state = auth_service.generate_state_token()
        
        assert state == "test_state_token"
        mock_uuid.assert_called_once()
    
    def test_build_eve_login_url(self, auth_service, sample_eve_scopes):
        """测试EVE登录URL构建"""
        state = "test_state"
        client_id = "test_client_id"
        redirect_uri = "http://localhost:3000/auth/callback"
        
        with patch.dict('os.environ', {'EVE_CLIENT_ID': client_id}):
            url = auth_service.build_eve_login_url(
                scopes=sample_eve_scopes,
                state=state,
                redirect_uri=redirect_uri
            )
        
        assert "login.eveonline.com" in url
        assert f"client_id={client_id}" in url
        assert f"state={state}" in url
        assert "response_type=code" in url
        assert redirect_uri in url
        
        # 验证权限参数
        for scope in sample_eve_scopes:
            assert scope in url
    
    @pytest.mark.asyncio
    async def test_initiate_eve_login_success(self, auth_service, sample_eve_scopes):
        """测试EVE登录发起成功"""
        with patch.object(auth_service, 'validate_eve_scopes', return_value=True), \
             patch.object(auth_service, 'generate_state_token', return_value="test_state"), \
             patch.object(auth_service, 'build_eve_login_url', return_value="http://test.url"):
            
            result = await auth_service.initiate_eve_login(sample_eve_scopes)
            
            assert result["success"] is True
            assert result["data"]["login_url"] == "http://test.url"
            assert result["data"]["state"] == "test_state"
            assert result["data"]["scopes"] == sample_eve_scopes
    
    @pytest.mark.asyncio
    async def test_initiate_eve_login_invalid_scopes(self, auth_service):
        """测试无效权限的EVE登录发起"""
        invalid_scopes = ["invalid-scope"]
        
        with patch.object(auth_service, 'validate_eve_scopes', return_value=False):
            result = await auth_service.initiate_eve_login(invalid_scopes)
            
            assert result["success"] is False
            assert "Invalid scopes" in result["message"]
    
    @pytest.mark.asyncio
    async def test_exchange_code_for_token_success(self, auth_service):
        """测试授权码换取令牌成功"""
        code = "test_auth_code"
        state = "test_state"
        
        mock_response = Mock()
        mock_response.json.return_value = {
            "access_token": "test_access_token",
            "refresh_token": "test_refresh_token",
            "expires_in": 3600,
            "token_type": "Bearer"
        }
        mock_response.status_code = 200
        
        with patch('httpx.AsyncClient.post', return_value=mock_response):
            result = await auth_service.exchange_code_for_token(code, state)
            
            assert result["success"] is True
            assert result["data"]["access_token"] == "test_access_token"
            assert result["data"]["refresh_token"] == "test_refresh_token"
    
    @pytest.mark.asyncio
    async def test_exchange_code_for_token_failure(self, auth_service):
        """测试授权码换取令牌失败"""
        code = "invalid_code"
        state = "test_state"
        
        mock_response = Mock()
        mock_response.status_code = 400
        mock_response.text = "Invalid authorization code"
        
        with patch('httpx.AsyncClient.post', return_value=mock_response):
            result = await auth_service.exchange_code_for_token(code, state)
            
            assert result["success"] is False
            assert "Invalid authorization code" in result["message"]
    
    @pytest.mark.asyncio
    async def test_get_character_info_success(self, auth_service, sample_character_data):
        """测试获取角色信息成功"""
        access_token = "test_access_token"
        
        mock_response = Mock()
        mock_response.json.return_value = sample_character_data
        mock_response.status_code = 200
        
        with patch('httpx.AsyncClient.get', return_value=mock_response):
            result = await auth_service.get_character_info(access_token)
            
            assert result["success"] is True
            assert result["data"]["character_id"] == sample_character_data["character_id"]
            assert result["data"]["character_name"] == sample_character_data["character_name"]
    
    @pytest.mark.asyncio
    async def test_get_character_info_failure(self, auth_service):
        """测试获取角色信息失败"""
        access_token = "invalid_token"
        
        mock_response = Mock()
        mock_response.status_code = 401
        mock_response.text = "Unauthorized"
        
        with patch('httpx.AsyncClient.get', return_value=mock_response):
            result = await auth_service.get_character_info(access_token)
            
            assert result["success"] is False
            assert "Unauthorized" in result["message"]
    
    def test_create_jwt_token(self, auth_service, sample_user_data):
        """测试JWT令牌创建"""
        with patch.dict('os.environ', {'JWT_SECRET_KEY': 'test_secret'}):
            token = auth_service.create_jwt_token(sample_user_data)
            
            assert isinstance(token, str)
            assert len(token) > 0
            
            # 验证令牌可以解码
            decoded = auth_service.decode_jwt_token(token)
            assert decoded["success"] is True
            assert decoded["data"]["id"] == sample_user_data["id"]
    
    def test_decode_jwt_token_valid(self, auth_service, sample_user_data):
        """测试有效JWT令牌解码"""
        with patch.dict('os.environ', {'JWT_SECRET_KEY': 'test_secret'}):
            token = auth_service.create_jwt_token(sample_user_data)
            result = auth_service.decode_jwt_token(token)
            
            assert result["success"] is True
            assert result["data"]["id"] == sample_user_data["id"]
    
    def test_decode_jwt_token_invalid(self, auth_service):
        """测试无效JWT令牌解码"""
        invalid_token = "invalid.jwt.token"
        
        result = auth_service.decode_jwt_token(invalid_token)
        
        assert result["success"] is False
        assert "Invalid token" in result["message"]
