#!/usr/bin/env python3
"""
前后端配置同步检查器
确保前后端配置的一致性
"""
import sys
import json
import re
from pathlib import Path
from typing import Dict, List, Any, Optional, NamedTuple
from dataclasses import dataclass
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


@dataclass
class SyncResult:
    """同步检查结果"""
    is_synced: bool
    differences: List[str]
    warnings: List[str]
    suggestions: List[str]
    details: Dict[str, Any]


@dataclass
class ConfigItem:
    """配置项"""
    key: str
    backend_value: Any
    frontend_value: Any
    source_file: str
    is_synced: bool


class ConfigSyncChecker:
    """配置同步检查器"""
    
    def __init__(self):
        self.project_root = project_root
        self.backend_config_files = [
            "src/infrastructure/config/settings.py",
            "src/infrastructure/esi/auth.py",
            ".env"
        ]
        self.frontend_config_files = [
            "frontend/src/config/api.ts",
            "frontend/src/pages/auth/LoginPage.tsx",
            "frontend/src/types/auth.ts"
        ]
    
    def check_eve_sso_scopes(self) -> SyncResult:
        """检查EVE SSO scopes的前后端同步"""
        differences = []
        warnings = []
        suggestions = []
        details = {
            'backend_scopes': [],
            'frontend_scopes': [],
            'common_scopes': [],
            'backend_only': [],
            'frontend_only': []
        }
        
        try:
            # 获取后端scopes
            backend_scopes = self._extract_backend_eve_scopes()
            details['backend_scopes'] = backend_scopes
            
            # 获取前端scopes
            frontend_scopes = self._extract_frontend_eve_scopes()
            details['frontend_scopes'] = frontend_scopes
            
            # 比较scopes
            backend_set = set(backend_scopes)
            frontend_set = set(frontend_scopes)
            
            details['common_scopes'] = list(backend_set & frontend_set)
            details['backend_only'] = list(backend_set - frontend_set)
            details['frontend_only'] = list(frontend_set - backend_set)
            
            # 检查差异
            if details['backend_only']:
                differences.append(f"后端独有的scopes: {details['backend_only']}")
                suggestions.append("考虑将后端独有的scopes添加到前端，或从后端移除不需要的scopes")
            
            if details['frontend_only']:
                differences.append(f"前端独有的scopes: {details['frontend_only']}")
                suggestions.append("考虑将前端独有的scopes添加到后端，或从前端移除不需要的scopes")
            
            # 检查无效scopes
            invalid_scopes = [
                'esi-characters.read_characters.v1',
                'esi-character.read_character.v1'
            ]
            
            for scope in invalid_scopes:
                if scope in backend_scopes:
                    differences.append(f"后端包含无效scope: {scope}")
                if scope in frontend_scopes:
                    differences.append(f"前端包含无效scope: {scope}")
            
            is_synced = len(differences) == 0
            
        except Exception as e:
            differences.append(f"检查EVE SSO scopes时出错: {str(e)}")
            is_synced = False
        
        return SyncResult(
            is_synced=is_synced,
            differences=differences,
            warnings=warnings,
            suggestions=suggestions,
            details=details
        )
    
    def _extract_backend_eve_scopes(self) -> List[str]:
        """从后端代码中提取EVE scopes"""
        scopes = []
        
        # 从环境变量中提取
        env_file = self.project_root / ".env"
        if env_file.exists():
            with open(env_file, 'r', encoding='utf-8') as f:
                content = f.read()
                # 查找EVE_SSO_SCOPES配置
                match = re.search(r'EVE_SSO_SCOPES\s*=\s*["\']([^"\']+)["\']', content)
                if match:
                    scopes.extend(match.group(1).split())
        
        # 从auth.py中提取默认scopes
        auth_file = self.project_root / "src/application/auth.py"
        if auth_file.exists():
            with open(auth_file, 'r', encoding='utf-8') as f:
                content = f.read()
                # 查找默认scopes列表
                scope_pattern = r'ESI_SCOPES\["([^"]+)"\]'
                matches = re.findall(scope_pattern, content)
                
                # 需要查找ESI_SCOPES字典来获取实际的scope值
                esi_scopes_file = self.project_root / "src/infrastructure/esi/auth.py"
                if esi_scopes_file.exists():
                    with open(esi_scopes_file, 'r', encoding='utf-8') as f:
                        esi_content = f.read()
                        for match in matches:
                            # 查找对应的scope值
                            scope_value_pattern = rf'"{match}":\s*"([^"]+)"'
                            scope_match = re.search(scope_value_pattern, esi_content)
                            if scope_match:
                                scope_value = scope_match.group(1)
                                if scope_value not in scopes:
                                    scopes.append(scope_value)
        
        return scopes
    
    def _extract_frontend_eve_scopes(self) -> List[str]:
        """从前端代码中提取EVE scopes"""
        scopes = []
        
        # 从LoginPage.tsx中提取
        login_page = self.project_root / "frontend/src/pages/auth/LoginPage.tsx"
        if login_page.exists():
            with open(login_page, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 查找scopes数组
                scope_pattern = r"'(esi-[^']+)'"
                matches = re.findall(scope_pattern, content)
                scopes.extend(matches)
        
        # 从其他配置文件中提取
        config_files = [
            "frontend/src/config/api.ts",
            "frontend/src/types/auth.ts"
        ]
        
        for config_file in config_files:
            file_path = self.project_root / config_file
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    scope_pattern = r"'(esi-[^']+)'"
                    matches = re.findall(scope_pattern, content)
                    for match in matches:
                        if match not in scopes:
                            scopes.append(match)
        
        return scopes
    
    def check_oauth_endpoints(self) -> SyncResult:
        """检查OAuth端点配置的同步"""
        differences = []
        warnings = []
        suggestions = []
        details = {
            'backend_endpoints': {},
            'frontend_endpoints': {},
            'mismatched_endpoints': []
        }
        
        try:
            # 获取后端端点配置
            backend_endpoints = self._extract_backend_oauth_endpoints()
            details['backend_endpoints'] = backend_endpoints
            
            # 获取前端端点配置
            frontend_endpoints = self._extract_frontend_oauth_endpoints()
            details['frontend_endpoints'] = frontend_endpoints
            
            # 比较端点
            for endpoint_name in set(backend_endpoints.keys()) | set(frontend_endpoints.keys()):
                backend_url = backend_endpoints.get(endpoint_name)
                frontend_url = frontend_endpoints.get(endpoint_name)
                
                if backend_url != frontend_url:
                    details['mismatched_endpoints'].append({
                        'endpoint': endpoint_name,
                        'backend': backend_url,
                        'frontend': frontend_url
                    })
                    differences.append(f"端点 {endpoint_name} 不匹配: 后端={backend_url}, 前端={frontend_url}")
            
            is_synced = len(differences) == 0
            
        except Exception as e:
            differences.append(f"检查OAuth端点时出错: {str(e)}")
            is_synced = False
        
        return SyncResult(
            is_synced=is_synced,
            differences=differences,
            warnings=warnings,
            suggestions=suggestions,
            details=details
        )
    
    def _extract_backend_oauth_endpoints(self) -> Dict[str, str]:
        """从后端代码中提取OAuth端点"""
        endpoints = {}
        
        # 从环境变量中提取
        env_file = self.project_root / ".env"
        if env_file.exists():
            with open(env_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 查找各种端点配置
                endpoint_patterns = {
                    'callback_url': r'EVE_CALLBACK_URL\s*=\s*["\']([^"\']+)["\']',
                    'base_url': r'API_BASE_URL\s*=\s*["\']([^"\']+)["\']'
                }
                
                for name, pattern in endpoint_patterns.items():
                    match = re.search(pattern, content)
                    if match:
                        endpoints[name] = match.group(1)
        
        # 从代码中提取硬编码的端点
        auth_file = self.project_root / "src/infrastructure/esi/auth.py"
        if auth_file.exists():
            with open(auth_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 查找EVE OAuth端点
                url_patterns = {
                    'authorization_url': r'https://login\.eveonline\.com/[^"\']+',
                    'token_url': r'https://login\.eveonline\.com/[^"\']+',
                    'esi_base': r'https://esi\.evetech\.net[^"\']*'
                }
                
                for name, pattern in url_patterns.items():
                    matches = re.findall(pattern, content)
                    if matches:
                        endpoints[name] = matches[0]
        
        return endpoints
    
    def _extract_frontend_oauth_endpoints(self) -> Dict[str, str]:
        """从前端代码中提取OAuth端点"""
        endpoints = {}
        
        # 从配置文件中提取
        config_files = [
            "frontend/src/config/api.ts",
            "frontend/src/pages/auth/LoginPage.tsx"
        ]
        
        for config_file in config_files:
            file_path = self.project_root / config_file
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                    # 查找API端点
                    url_patterns = {
                        'api_base': r'http://localhost:\d+',
                        'login_endpoint': r'/auth/login'
                    }
                    
                    for name, pattern in url_patterns.items():
                        matches = re.findall(pattern, content)
                        if matches:
                            endpoints[name] = matches[0]
        
        return endpoints
    
    def check_api_keys(self) -> SyncResult:
        """检查API密钥配置的同步"""
        differences = []
        warnings = []
        suggestions = []
        details = {
            'backend_keys': [],
            'frontend_keys': [],
            'missing_keys': []
        }
        
        try:
            # 检查后端必需的配置项
            required_backend_keys = [
                'EVE_CLIENT_ID',
                'EVE_CLIENT_SECRET',
                'EVE_CALLBACK_URL',
                'DATABASE_URL',
                'SECRET_KEY'
            ]
            
            env_file = self.project_root / ".env"
            configured_keys = []
            
            if env_file.exists():
                with open(env_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    for key in required_backend_keys:
                        if re.search(rf'^{key}\s*=', content, re.MULTILINE):
                            configured_keys.append(key)
                        else:
                            details['missing_keys'].append(key)
                            differences.append(f"缺少必需的配置项: {key}")
            
            details['backend_keys'] = configured_keys
            
            # 检查前端环境变量使用
            frontend_env_usage = self._check_frontend_env_usage()
            details['frontend_keys'] = frontend_env_usage
            
            is_synced = len(differences) == 0
            
        except Exception as e:
            differences.append(f"检查API密钥时出错: {str(e)}")
            is_synced = False
        
        return SyncResult(
            is_synced=is_synced,
            differences=differences,
            warnings=warnings,
            suggestions=suggestions,
            details=details
        )
    
    def _check_frontend_env_usage(self) -> List[str]:
        """检查前端环境变量使用情况"""
        env_vars = []
        
        frontend_files = [
            "frontend/src/config/api.ts",
            "frontend/src/pages/auth/LoginPage.tsx"
        ]
        
        for file_path in frontend_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                    # 查找环境变量使用
                    env_pattern = r'process\.env\.([A-Z_]+)'
                    matches = re.findall(env_pattern, content)
                    for match in matches:
                        if match not in env_vars:
                            env_vars.append(match)
        
        return env_vars
    
    def generate_sync_report(self) -> Dict[str, Any]:
        """生成完整的同步检查报告"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'checker_version': '1.0.0',
            'sync_checks': {}
        }
        
        # EVE SSO scopes同步检查
        scopes_result = self.check_eve_sso_scopes()
        report['sync_checks']['eve_sso_scopes'] = {
            'is_synced': scopes_result.is_synced,
            'differences': scopes_result.differences,
            'warnings': scopes_result.warnings,
            'suggestions': scopes_result.suggestions,
            'details': scopes_result.details
        }
        
        # OAuth端点同步检查
        endpoints_result = self.check_oauth_endpoints()
        report['sync_checks']['oauth_endpoints'] = {
            'is_synced': endpoints_result.is_synced,
            'differences': endpoints_result.differences,
            'warnings': endpoints_result.warnings,
            'suggestions': endpoints_result.suggestions,
            'details': endpoints_result.details
        }
        
        # API密钥同步检查
        keys_result = self.check_api_keys()
        report['sync_checks']['api_keys'] = {
            'is_synced': keys_result.is_synced,
            'differences': keys_result.differences,
            'warnings': keys_result.warnings,
            'suggestions': keys_result.suggestions,
            'details': keys_result.details
        }
        
        # 总体状态
        report['overall_status'] = {
            'is_synced': all(check['is_synced'] for check in report['sync_checks'].values()),
            'total_differences': sum(len(check['differences']) for check in report['sync_checks'].values()),
            'total_warnings': sum(len(check['warnings']) for check in report['sync_checks'].values())
        }
        
        return report


def main():
    """主函数"""
    print("🔄 前后端配置同步检查")
    print("=" * 50)
    
    checker = ConfigSyncChecker()
    
    # 生成同步检查报告
    report = checker.generate_sync_report()
    
    # 显示结果
    print(f"\n📊 同步检查报告 - {report['timestamp']}")
    print("-" * 50)
    
    for check_name, result in report['sync_checks'].items():
        status = "✅ 同步" if result['is_synced'] else "❌ 不同步"
        print(f"\n{check_name}: {status}")
        
        if result['differences']:
            print("  差异:")
            for diff in result['differences']:
                print(f"    - {diff}")
        
        if result['warnings']:
            print("  警告:")
            for warning in result['warnings']:
                print(f"    - {warning}")
        
        if result['suggestions']:
            print("  建议:")
            for suggestion in result['suggestions']:
                print(f"    - {suggestion}")
    
    # 总体状态
    overall = report['overall_status']
    status_icon = "🎉" if overall['is_synced'] else "⚠️"
    print(f"\n{status_icon} 总体状态: {'同步' if overall['is_synced'] else '需要修复'}")
    print(f"   差异: {overall['total_differences']}")
    print(f"   警告: {overall['total_warnings']}")
    
    # 保存报告
    report_file = project_root / "logs" / f"config_sync_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    report_file.parent.mkdir(exist_ok=True)
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 详细报告已保存到: {report_file}")
    
    return 0 if overall['is_synced'] else 1


if __name__ == "__main__":
    sys.exit(main())
