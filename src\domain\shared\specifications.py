"""
具体的规约实现
"""
from typing import Any, List, Union
from datetime import datetime, timedelta
from .base_entity import Specification


class AndSpecification(Specification):
    """AND规约 - 所有子规约都必须满足"""
    
    def __init__(self, *specifications: Specification):
        self.specifications = specifications
    
    def is_satisfied_by(self, candidate: Any) -> bool:
        """检查候选对象是否满足所有规约"""
        return all(spec.is_satisfied_by(candidate) for spec in self.specifications)


class OrSpecification(Specification):
    """OR规约 - 至少一个子规约满足即可"""
    
    def __init__(self, *specifications: Specification):
        self.specifications = specifications
    
    def is_satisfied_by(self, candidate: Any) -> bool:
        """检查候选对象是否满足任一规约"""
        return any(spec.is_satisfied_by(candidate) for spec in self.specifications)


class NotSpecification(Specification):
    """NOT规约 - 子规约不满足"""
    
    def __init__(self, specification: Specification):
        self.specification = specification
    
    def is_satisfied_by(self, candidate: Any) -> bool:
        """检查候选对象是否不满足规约"""
        return not self.specification.is_satisfied_by(candidate)


class AttributeEqualsSpecification(Specification):
    """属性等于指定值的规约"""
    
    def __init__(self, attribute_name: str, expected_value: Any):
        self.attribute_name = attribute_name
        self.expected_value = expected_value
    
    def is_satisfied_by(self, candidate: Any) -> bool:
        """检查候选对象的属性是否等于期望值"""
        try:
            actual_value = getattr(candidate, self.attribute_name)
            return actual_value == self.expected_value
        except AttributeError:
            return False


class AttributeInRangeSpecification(Specification):
    """属性在指定范围内的规约"""
    
    def __init__(self, attribute_name: str, min_value: Any, max_value: Any):
        self.attribute_name = attribute_name
        self.min_value = min_value
        self.max_value = max_value
    
    def is_satisfied_by(self, candidate: Any) -> bool:
        """检查候选对象的属性是否在指定范围内"""
        try:
            actual_value = getattr(candidate, self.attribute_name)
            return self.min_value <= actual_value <= self.max_value
        except (AttributeError, TypeError):
            return False


class DateTimeRangeSpecification(Specification):
    """日期时间范围规约"""
    
    def __init__(self, attribute_name: str, start_time: datetime, end_time: datetime):
        self.attribute_name = attribute_name
        self.start_time = start_time
        self.end_time = end_time
    
    def is_satisfied_by(self, candidate: Any) -> bool:
        """检查候选对象的日期时间属性是否在指定范围内"""
        try:
            actual_time = getattr(candidate, self.attribute_name)
            if not isinstance(actual_time, datetime):
                return False
            return self.start_time <= actual_time <= self.end_time
        except AttributeError:
            return False


class IsActiveSpecification(Specification):
    """活跃状态规约"""
    
    def is_satisfied_by(self, candidate: Any) -> bool:
        """检查候选对象是否处于活跃状态"""
        try:
            return getattr(candidate, 'is_active', False) is True
        except AttributeError:
            return False


class IsExpiredSpecification(Specification):
    """过期状态规约"""
    
    def __init__(self, attribute_name: str = 'expires_at'):
        self.attribute_name = attribute_name
    
    def is_satisfied_by(self, candidate: Any) -> bool:
        """检查候选对象是否已过期"""
        try:
            expires_at = getattr(candidate, self.attribute_name)
            if not isinstance(expires_at, datetime):
                return False
            return datetime.utcnow() > expires_at
        except AttributeError:
            return False


class HasPermissionSpecification(Specification):
    """权限检查规约"""
    
    def __init__(self, required_permissions: Union[str, List[str]]):
        if isinstance(required_permissions, str):
            self.required_permissions = [required_permissions]
        else:
            self.required_permissions = required_permissions
    
    def is_satisfied_by(self, candidate: Any) -> bool:
        """检查候选对象是否具有所需权限"""
        try:
            user_permissions = getattr(candidate, 'permissions', [])
            if isinstance(user_permissions, str):
                user_permissions = [user_permissions]
            
            return all(perm in user_permissions for perm in self.required_permissions)
        except AttributeError:
            return False


class CharacterOwnershipSpecification(Specification):
    """角色所有权规约"""
    
    def __init__(self, user_id: int):
        self.user_id = user_id
    
    def is_satisfied_by(self, candidate: Any) -> bool:
        """检查角色是否属于指定用户"""
        try:
            return getattr(candidate, 'user_id') == self.user_id
        except AttributeError:
            return False


class TokenValiditySpecification(Specification):
    """令牌有效性规约"""
    
    def is_satisfied_by(self, candidate: Any) -> bool:
        """检查令牌是否有效"""
        try:
            # 检查令牌是否存在
            if not hasattr(candidate, 'access_token') or not candidate.access_token:
                return False
            
            # 检查是否活跃
            if hasattr(candidate, 'is_active') and not candidate.is_active:
                return False
            
            # 检查是否过期
            if hasattr(candidate, 'expires_at'):
                expires_at = candidate.expires_at
                if isinstance(expires_at, datetime) and datetime.utcnow() > expires_at:
                    return False
            
            return True
        except AttributeError:
            return False


# 便利方法用于组合规约
def and_spec(*specifications: Specification) -> AndSpecification:
    """创建AND规约"""
    return AndSpecification(*specifications)


def or_spec(*specifications: Specification) -> OrSpecification:
    """创建OR规约"""
    return OrSpecification(*specifications)


def not_spec(specification: Specification) -> NotSpecification:
    """创建NOT规约"""
    return NotSpecification(specification)


# 常用的预定义规约
class CommonSpecifications:
    """常用规约集合"""
    
    @staticmethod
    def active_user() -> Specification:
        """活跃用户规约"""
        return and_spec(
            IsActiveSpecification(),
            AttributeEqualsSpecification('is_verified', True)
        )
    
    @staticmethod
    def valid_token() -> Specification:
        """有效令牌规约"""
        return and_spec(
            TokenValiditySpecification(),
            IsActiveSpecification(),
            not_spec(IsExpiredSpecification())
        )
    
    @staticmethod
    def character_accessible_by_user(user_id: int) -> Specification:
        """用户可访问的角色规约"""
        return and_spec(
            CharacterOwnershipSpecification(user_id),
            IsActiveSpecification()
        )
    
    @staticmethod
    def recent_activity(days: int = 30) -> Specification:
        """最近活跃规约"""
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        return DateTimeRangeSpecification('last_activity_at', cutoff_date, datetime.utcnow())
