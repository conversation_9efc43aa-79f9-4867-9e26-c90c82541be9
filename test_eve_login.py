#!/usr/bin/env python3
"""
测试EVE登录功能
"""
import requests
import json

def test_eve_login():
    """测试EVE登录API"""
    print("🧪 测试EVE登录功能")
    print("=" * 50)
    
    # 测试数据
    login_data = {
        "scopes": [
            "esi-characters.read_characters.v1",
            "esi-assets.read_assets.v1",
            "esi-skills.read_skills.v1",
            "esi-wallet.read_character_wallet.v1"
        ]
    }
    
    try:
        # 发送登录请求
        print("📤 发送EVE登录请求...")
        response = requests.post(
            "http://localhost:8000/auth/login",
            json=login_data,
            timeout=10
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ EVE登录请求成功")
            print(f"📋 响应数据:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # 检查响应格式
            if data.get("success"):
                login_url = data.get("data", {}).get("login_url")
                state = data.get("data", {}).get("state")
                scopes = data.get("data", {}).get("scopes")
                
                print(f"\n🔗 登录URL: {login_url}")
                print(f"🔑 状态码: {state}")
                print(f"📜 权限范围: {scopes}")
                
                # 检查是否为开发模式
                if "mock-eve-login" in login_url:
                    print("\n🛠️  检测到开发模式")
                    print("   - 使用模拟EVE登录页面")
                    print("   - 适用于本地开发和测试")
                elif "login.eveonline.com" in login_url:
                    print("\n🌐 检测到生产模式")
                    print("   - 使用真实的EVE SSO")
                    print("   - 需要有效的ngrok隧道或公网回调URL")
                
                return True
            else:
                print("❌ 登录请求失败")
                print(f"   错误信息: {data.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"   响应内容: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务器")
        print("   请确保后端服务器正在运行在 http://localhost:8000")
        return False
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_health_check():
    """测试健康检查"""
    print("\n🔍 测试健康检查...")
    
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 健康检查通过: {data}")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def main():
    """主函数"""
    print("🎮 EVE Online Assistant - 登录功能测试")
    print("=" * 60)
    
    # 健康检查
    if not test_health_check():
        print("\n❌ 后端服务器不可用，测试终止")
        return 1
    
    # EVE登录测试
    if test_eve_login():
        print("\n🎉 EVE登录功能测试通过！")
        print("\n📝 下一步:")
        print("   1. 在浏览器中访问: http://localhost:3000")
        print("   2. 点击 'EVE登录' 按钮")
        print("   3. 如果是开发模式，会跳转到模拟登录页面")
        print("   4. 如果是生产模式，会跳转到真实的EVE SSO")
        return 0
    else:
        print("\n❌ EVE登录功能测试失败")
        return 1

if __name__ == "__main__":
    exit(main())
