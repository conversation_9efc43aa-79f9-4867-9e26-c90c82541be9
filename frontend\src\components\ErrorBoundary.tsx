import React, { Component, ErrorInfo, ReactNode } from 'react'
import { Result, Button } from 'antd'
import { ReloadOutlined, HomeOutlined } from '@ant-design/icons'

interface Props {
  children: ReactNode
  fallback?: ReactNode
}

interface State {
  hasError: boolean
  error?: Error
  errorInfo?: ErrorInfo
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo)
    
    this.setState({
      error,
      errorInfo,
    })

    // 这里可以添加错误报告逻辑
    this.reportError(error, errorInfo)
  }

  reportError = (error: Error, errorInfo: ErrorInfo) => {
    // 发送错误报告到监控服务
    try {
      const errorReport = {
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
      }

      // 这里可以发送到错误监控服务
      console.error('Error Report:', errorReport)
      
      // 存储到本地存储用于调试
      const existingErrors = JSON.parse(localStorage.getItem('app_errors') || '[]')
      existingErrors.push(errorReport)
      
      // 只保留最近的10个错误
      if (existingErrors.length > 10) {
        existingErrors.splice(0, existingErrors.length - 10)
      }
      
      localStorage.setItem('app_errors', JSON.stringify(existingErrors))
    } catch (reportError) {
      console.error('Failed to report error:', reportError)
    }
  }

  handleReload = () => {
    window.location.reload()
  }

  handleGoHome = () => {
    window.location.href = '/'
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined })
  }

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义fallback，使用它
      if (this.props.fallback) {
        return this.props.fallback
      }

      // 默认错误页面
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-dark-900">
          <div className="max-w-md w-full mx-4">
            <Result
              status="error"
              title="应用程序出错"
              subTitle="抱歉，应用程序遇到了意外错误。请尝试刷新页面或返回首页。"
              extra={[
                <Button 
                  type="primary" 
                  key="retry"
                  icon={<ReloadOutlined />}
                  onClick={this.handleRetry}
                >
                  重试
                </Button>,
                <Button 
                  key="reload"
                  icon={<ReloadOutlined />}
                  onClick={this.handleReload}
                >
                  刷新页面
                </Button>,
                <Button 
                  key="home"
                  icon={<HomeOutlined />}
                  onClick={this.handleGoHome}
                >
                  返回首页
                </Button>,
              ]}
            />

            {/* 开发环境下显示错误详情 */}
            {import.meta.env.DEV && this.state.error && (
              <div className="mt-8 p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                <h3 className="text-lg font-semibold text-red-800 dark:text-red-200 mb-2">
                  错误详情 (仅开发环境显示)
                </h3>
                <div className="text-sm text-red-700 dark:text-red-300 space-y-2">
                  <div>
                    <strong>错误信息:</strong>
                    <pre className="mt-1 p-2 bg-red-100 dark:bg-red-900/40 rounded text-xs overflow-auto">
                      {this.state.error.message}
                    </pre>
                  </div>
                  {this.state.error.stack && (
                    <div>
                      <strong>错误堆栈:</strong>
                      <pre className="mt-1 p-2 bg-red-100 dark:bg-red-900/40 rounded text-xs overflow-auto max-h-40">
                        {this.state.error.stack}
                      </pre>
                    </div>
                  )}
                  {this.state.errorInfo?.componentStack && (
                    <div>
                      <strong>组件堆栈:</strong>
                      <pre className="mt-1 p-2 bg-red-100 dark:bg-red-900/40 rounded text-xs overflow-auto max-h-40">
                        {this.state.errorInfo.componentStack}
                      </pre>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

// 函数式错误边界Hook (React 18+)
export function useErrorHandler() {
  return (error: Error, errorInfo?: ErrorInfo) => {
    console.error('Unhandled error:', error, errorInfo)
    
    // 这里可以添加错误报告逻辑
    const errorReport = {
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    }

    console.error('Error Report:', errorReport)
  }
}

// 异步错误处理
export function setupGlobalErrorHandlers() {
  // 处理未捕获的Promise错误
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason)
    
    const errorReport = {
      type: 'unhandledrejection',
      reason: event.reason?.toString() || 'Unknown error',
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    }

    console.error('Promise Error Report:', errorReport)
    
    // 阻止默认的控制台错误输出
    event.preventDefault()
  })

  // 处理全局JavaScript错误
  window.addEventListener('error', (event) => {
    console.error('Global error:', event.error)
    
    const errorReport = {
      type: 'javascript',
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      stack: event.error?.stack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    }

    console.error('JavaScript Error Report:', errorReport)
  })
}
