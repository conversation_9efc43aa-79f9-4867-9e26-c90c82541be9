name: Scheduled Review

on:
  schedule:
    # 每周日晚上11点运行全面审查
    - cron: '0 23 * * 0'
    # 每天早上6点运行快速检查
    - cron: '0 6 * * *'
  workflow_dispatch:
    inputs:
      review_type:
        description: 'Type of review to run'
        required: true
        default: 'full'
        type: choice
        options:
        - full
        - quick
        - security
        - dependencies

jobs:
  full-review:
    if: github.event.schedule == '0 23 * * 0' || github.event.inputs.review_type == 'full'
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.11

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e .[dev]

    - name: Run comprehensive test suite
      run: |
        python scripts/run_tests.py

    - name: Generate detailed coverage report
      run: |
        pytest --cov=src --cov-report=html --cov-report=json --cov-report=term

    - name: Analyze code quality metrics
      run: |
        pip install radon xenon
        echo "## Code Complexity Report" > quality-report.md
        echo "### Cyclomatic Complexity" >> quality-report.md
        radon cc src --min B >> quality-report.md
        echo "### Maintainability Index" >> quality-report.md
        radon mi src --min B >> quality-report.md
        echo "### Halstead Metrics" >> quality-report.md
        radon hal src >> quality-report.md

    - name: Check test coverage trends
      run: |
        python -c "
        import json
        import os
        from datetime import datetime
        
        # 读取覆盖率数据
        try:
            with open('coverage.json') as f:
                coverage_data = json.load(f)
            
            total_coverage = coverage_data['totals']['percent_covered']
            
            # 创建趋势报告
            report = {
                'date': datetime.now().isoformat(),
                'total_coverage': total_coverage,
                'files_covered': len(coverage_data['files']),
                'lines_covered': coverage_data['totals']['covered_lines'],
                'lines_missing': coverage_data['totals']['missing_lines']
            }
            
            print(f'📊 Coverage Report:')
            print(f'   Total Coverage: {total_coverage:.2f}%')
            print(f'   Files Covered: {report[\"files_covered\"]}')
            print(f'   Lines Covered: {report[\"lines_covered\"]}')
            print(f'   Lines Missing: {report[\"lines_missing\"]}')
            
            # 保存趋势数据
            with open('coverage-trend.json', 'w') as f:
                json.dump(report, f, indent=2)
                
        except FileNotFoundError:
            print('⚠️ Coverage data not found')
        "

    - name: Security audit
      run: |
        pip install bandit safety
        bandit -r src -f json -o bandit-full-report.json
        safety check --json --output safety-full-report.json

    - name: Documentation review
      run: |
        pip install interrogate
        interrogate src --generate-badge .
        interrogate src --verbose > doc-coverage-report.txt

    - name: Upload full review reports
      uses: actions/upload-artifact@v3
      with:
        name: full-review-reports
        path: |
          htmlcov/
          quality-report.md
          coverage-trend.json
          bandit-full-report.json
          safety-full-report.json
          doc-coverage-report.txt
          interrogate_badge.svg

  quick-review:
    if: github.event.schedule == '0 6 * * *' || github.event.inputs.review_type == 'quick'
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.11

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e .

    - name: Quick module import check
      run: |
        python -c "
        print('🚀 Quick module import validation...')
        
        critical_modules = [
            'src.presentation.api.main',
            'src.application.services.auth',
            'src.infrastructure.persistence.repositories.character',
            'src.infrastructure.persistence.repositories',
        ]
        
        failed = []
        for module in critical_modules:
            try:
                __import__(module)
                print(f'✅ {module}')
            except Exception as e:
                print(f'❌ {module}: {e}')
                failed.append(module)
        
        if failed:
            print(f'\\n❌ {len(failed)} critical modules failed!')
            exit(1)
        else:
            print(f'\\n🎉 All {len(critical_modules)} critical modules OK!')
        "

    - name: Quick test run
      run: |
        pytest tests/test_module_imports.py -v --tb=short

    - name: Check for obvious issues
      run: |
        python -c "
        import ast
        from pathlib import Path
        
        print('🔍 Checking for obvious code issues...')
        
        issues = []
        
        # 检查所有Python文件的语法
        for py_file in Path('src').rglob('*.py'):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                ast.parse(content)
            except SyntaxError as e:
                issues.append(f'Syntax error in {py_file}: {e}')
            except Exception as e:
                issues.append(f'Parse error in {py_file}: {e}')
        
        if issues:
            print(f'❌ Found {len(issues)} issues:')
            for issue in issues:
                print(f'   {issue}')
            exit(1)
        else:
            print('✅ No obvious issues found')
        "

  security-review:
    if: github.event.inputs.review_type == 'security'
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.11

    - name: Install security tools
      run: |
        python -m pip install --upgrade pip
        pip install bandit safety semgrep

    - name: Run comprehensive security scan
      run: |
        echo "🔒 Running comprehensive security scan..."
        
        # Bandit扫描
        bandit -r src -f json -o bandit-security-report.json
        
        # Safety检查
        safety check --json --output safety-security-report.json
        
        # Semgrep扫描
        semgrep --config=auto src --json --output=semgrep-report.json

    - name: Upload security reports
      uses: actions/upload-artifact@v3
      with:
        name: security-review-reports
        path: |
          bandit-security-report.json
          safety-security-report.json
          semgrep-report.json

  dependency-review:
    if: github.event.inputs.review_type == 'dependencies'
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.11

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e .[dev]

    - name: Comprehensive dependency analysis
      run: |
        pip install pipdeptree pip-audit
        
        echo "📦 Dependency Analysis Report" > dependency-review.md
        echo "## Dependency Tree" >> dependency-review.md
        pipdeptree >> dependency-review.md
        
        echo "## Outdated Packages" >> dependency-review.md
        pip list --outdated >> dependency-review.md
        
        echo "## Security Audit" >> dependency-review.md
        pip-audit --format=json --output=pip-audit-report.json
        pip-audit >> dependency-review.md

    - name: Upload dependency review
      uses: actions/upload-artifact@v3
      with:
        name: dependency-review-reports
        path: |
          dependency-review.md
          pip-audit-report.json

  create-review-issue:
    needs: [full-review]
    if: always() && (github.event.schedule == '0 23 * * 0')
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Create weekly review issue
      uses: actions/github-script@v6
      with:
        script: |
          const { data: artifacts } = await github.rest.actions.listWorkflowRunArtifacts({
            owner: context.repo.owner,
            repo: context.repo.repo,
            run_id: context.runId,
          });
          
          const date = new Date().toISOString().split('T')[0];
          const title = `Weekly Code Review - ${date}`;
          
          const body = `
          ## 📋 Weekly Code Review Report
          
          **Date:** ${date}
          **Workflow Run:** [#${context.runId}](${context.payload.repository.html_url}/actions/runs/${context.runId})
          
          ### 📊 Review Items
          - [ ] Test coverage analysis
          - [ ] Code quality metrics
          - [ ] Security scan results
          - [ ] Documentation coverage
          - [ ] Dependency audit
          
          ### 📁 Generated Reports
          ${artifacts.map(artifact => `- [${artifact.name}](${context.payload.repository.html_url}/actions/runs/${context.runId})`).join('\n')}
          
          ### 🎯 Action Items
          Please review the generated reports and:
          1. Address any security vulnerabilities
          2. Improve test coverage if below 80%
          3. Update outdated dependencies
          4. Fix any code quality issues
          
          ### 📅 Next Review
          Next automated review: ${new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]}
          
          ---
          *This issue was automatically created by the scheduled review workflow.*
          `;
          
          await github.rest.issues.create({
            owner: context.repo.owner,
            repo: context.repo.repo,
            title: title,
            body: body,
            labels: ['review', 'automated', 'weekly']
          });
