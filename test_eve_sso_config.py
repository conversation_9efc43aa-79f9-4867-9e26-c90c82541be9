#!/usr/bin/env python3
"""
测试EVE SSO配置
"""
import os
import urllib.request
import urllib.parse
import json
from dotenv import load_dotenv

def load_config():
    """加载配置"""
    load_dotenv()
    
    config = {
        'client_id': os.getenv('EVE_SSO_CLIENT_ID'),
        'client_secret': os.getenv('EVE_SSO_CLIENT_SECRET'),
        'callback_url': os.getenv('EVE_SSO_CALLBACK_URL'),
        'base_url': os.getenv('EVE_SSO_BASE_URL', 'https://login.eveonline.com')
    }
    
    return config

def test_eve_sso_discovery():
    """测试EVE SSO发现端点"""
    print("🔍 测试EVE SSO发现端点...")
    
    try:
        discovery_url = "https://login.eveonline.com/.well-known/oauth_authorization_server"
        
        with urllib.request.urlopen(discovery_url, timeout=10) as response:
            if response.getcode() == 200:
                data = json.loads(response.read().decode())
                print("   ✅ EVE SSO发现端点正常")
                print(f"   📋 授权端点: {data.get('authorization_endpoint')}")
                print(f"   📋 令牌端点: {data.get('token_endpoint')}")
                return True
            else:
                print(f"   ❌ EVE SSO发现端点异常: {response.getcode()}")
                return False
                
    except Exception as e:
        print(f"   ❌ EVE SSO发现端点测试失败: {e}")
        return False

def test_authorization_url_generation(config):
    """测试授权URL生成"""
    print("\n🔗 测试授权URL生成...")
    
    try:
        # 基本参数
        params = {
            'response_type': 'code',
            'redirect_uri': config['callback_url'],
            'client_id': config['client_id'],
            'scope': 'esi-characters.read_characters.v1',
            'state': 'test_state_12345'
        }
        
        # 生成授权URL
        auth_url = f"{config['base_url']}/v2/oauth/authorize/?" + urllib.parse.urlencode(params)
        
        print("   ✅ 授权URL生成成功")
        print(f"   📋 Client ID: {config['client_id']}")
        print(f"   📋 回调URL: {config['callback_url']}")
        print(f"   📋 完整授权URL:")
        print(f"      {auth_url}")
        
        return auth_url
        
    except Exception as e:
        print(f"   ❌ 授权URL生成失败: {e}")
        return None

def test_client_validation(config):
    """测试客户端验证（通过尝试获取令牌）"""
    print("\n🔐 测试客户端配置...")
    
    # 注意：这个测试会失败，因为我们没有有效的授权码
    # 但错误信息可以帮助我们判断客户端配置是否正确
    
    try:
        token_url = f"{config['base_url']}/v2/oauth/token"
        
        # 构造测试请求数据
        data = {
            'grant_type': 'authorization_code',
            'code': 'test_invalid_code',  # 故意使用无效代码
            'redirect_uri': config['callback_url'],
            'client_id': config['client_id'],
            'client_secret': config['client_secret']
        }
        
        # 编码数据
        post_data = urllib.parse.urlencode(data).encode('utf-8')
        
        # 创建请求
        req = urllib.request.Request(
            token_url,
            data=post_data,
            headers={
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': 'EVE-Assistant-Test/1.0'
            }
        )
        
        # 发送请求
        try:
            with urllib.request.urlopen(req, timeout=10) as response:
                result = json.loads(response.read().decode())
                print(f"   ⚠️  意外成功: {result}")
        except urllib.error.HTTPError as e:
            error_data = json.loads(e.read().decode())
            error_code = error_data.get('error', 'unknown')
            error_desc = error_data.get('error_description', 'No description')
            
            print(f"   📋 HTTP状态码: {e.code}")
            print(f"   📋 错误代码: {error_code}")
            print(f"   📋 错误描述: {error_desc}")
            
            # 分析错误类型
            if error_code == 'invalid_client':
                print("   ❌ 客户端ID或密钥无效")
                print("      请检查EVE开发者门户中的应用配置")
                return False
            elif error_code == 'invalid_request':
                if 'client could not be found' in error_desc.lower():
                    print("   ❌ 客户端未找到")
                    print("      请检查Client ID是否正确")
                    return False
                else:
                    print("   ✅ 客户端配置可能正确（授权码无效是预期的）")
                    return True
            elif error_code == 'invalid_grant':
                print("   ✅ 客户端配置正确（授权码无效是预期的）")
                return True
            else:
                print(f"   ⚠️  未知错误类型: {error_code}")
                return False
                
    except Exception as e:
        print(f"   ❌ 客户端验证测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 EVE SSO配置测试")
    print("=" * 60)
    
    # 加载配置
    config = load_config()
    
    print("📋 当前配置:")
    print(f"   Client ID: {config['client_id']}")
    print(f"   回调URL: {config['callback_url']}")
    print(f"   基础URL: {config['base_url']}")
    print()
    
    # 执行测试
    tests = [
        ("EVE SSO发现端点", lambda: test_eve_sso_discovery()),
        ("授权URL生成", lambda: test_authorization_url_generation(config)),
        ("客户端配置", lambda: test_client_validation(config)),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} 测试通过\n")
            else:
                print(f"❌ {test_name} 测试失败\n")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}\n")
    
    print("=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed < total:
        print("\n🔧 可能的解决方案:")
        print("1. 检查EVE开发者门户中的应用配置")
        print("2. 确认Client ID和Client Secret正确")
        print("3. 确认回调URL已在EVE门户中注册")
        print("4. 检查应用是否处于活跃状态")
        print("\n📝 EVE开发者门户: https://developers.eveonline.com/")
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    exit(main())
